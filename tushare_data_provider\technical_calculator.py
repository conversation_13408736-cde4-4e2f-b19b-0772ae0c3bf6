"""
技术指标计算器
基于pandas实现各种技术指标的计算
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')


class TechnicalCalculator:
    """技术指标计算器"""
    
    @staticmethod
    def calculate_ma(data: pd.Series, periods: list) -> Dict[str, pd.Series]:
        """计算移动平均线"""
        result = {}
        for period in periods:
            result[f'ma{period}'] = data.rolling(window=period).mean()
        return result
    
    @staticmethod
    def calculate_ema(data: pd.Series, periods: list) -> Dict[str, pd.Series]:
        """计算指数移动平均线"""
        result = {}
        for period in periods:
            result[f'ema{period}'] = data.ewm(span=period).mean()
        return result
    
    @staticmethod
    def calculate_macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """计算MACD指标"""
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        
        dif = ema_fast - ema_slow
        dea = dif.ewm(span=signal).mean()
        hist = (dif - dea) * 2
        
        return {
            'macd_dif': dif,
            'macd_dea': dea,
            'macd_hist': hist
        }
    
    @staticmethod
    def calculate_rsi(data: pd.Series, periods: list = [6, 12, 14, 24]) -> Dict[str, pd.Series]:
        """计算RSI指标"""
        result = {}
        
        for period in periods:
            delta = data.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            if period == 14:
                result['rsi'] = rsi
            else:
                result[f'rsi{period}'] = rsi
                
        return result
    
    @staticmethod
    def calculate_kdj(high: pd.Series, low: pd.Series, close: pd.Series, 
                     period: int = 9, k_period: int = 3, d_period: int = 3) -> Dict[str, pd.Series]:
        """计算KDJ指标"""
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        
        k = rsv.ewm(com=k_period-1).mean()
        d = k.ewm(com=d_period-1).mean()
        j = 3 * k - 2 * d
        
        return {
            'kdj_k': k,
            'kdj_d': d,
            'kdj_j': j
        }
    
    @staticmethod
    def calculate_bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """计算布林带"""
        ma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()
        
        upper = ma + (std * std_dev)
        lower = ma - (std * std_dev)
        
        return {
            'boll_upper': upper,
            'boll_mid': ma,
            'boll_lower': lower
        }
    
    @staticmethod
    def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """计算平均真实波幅(ATR)"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    @staticmethod
    def calculate_williams_r(high: pd.Series, low: pd.Series, close: pd.Series, 
                           periods: list = [6, 10, 14]) -> Dict[str, pd.Series]:
        """计算威廉指标"""
        result = {}
        
        for period in periods:
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            
            wr = -100 * (highest_high - close) / (highest_high - lowest_low)
            
            if period == 14:
                result['wr'] = wr
            else:
                result[f'wr{period}'] = wr
                
        return result
    
    @staticmethod
    def calculate_cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """计算顺势指标(CCI)"""
        typical_price = (high + low + close) / 3
        ma = typical_price.rolling(window=period).mean()
        mad = typical_price.rolling(window=period).apply(lambda x: np.abs(x - x.mean()).mean())
        
        cci = (typical_price - ma) / (0.015 * mad)
        
        return cci
    
    @staticmethod
    def calculate_obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """计算能量潮(OBV)"""
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
                
        return obv
    
    @staticmethod
    def calculate_vr(close: pd.Series, volume: pd.Series, period: int = 26) -> pd.Series:
        """计算成交量比率(VR)"""
        price_change = close.diff()
        
        up_volume = volume.where(price_change > 0, 0).rolling(window=period).sum()
        down_volume = volume.where(price_change < 0, 0).rolling(window=period).sum()
        equal_volume = volume.where(price_change == 0, 0).rolling(window=period).sum()
        
        vr = (up_volume + equal_volume/2) / (down_volume + equal_volume/2) * 100
        
        return vr
    
    @staticmethod
    def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """计算平均趋向指数(ADX) - 简化版本"""
        # 计算真实波幅
        atr = TechnicalCalculator.calculate_atr(high, low, close, period)
        
        # 计算方向移动
        up_move = high.diff()
        down_move = -low.diff()
        
        plus_dm = up_move.where((up_move > down_move) & (up_move > 0), 0)
        minus_dm = down_move.where((down_move > up_move) & (down_move > 0), 0)
        
        # 平滑处理
        plus_di = 100 * (plus_dm.rolling(window=period).mean() / atr)
        minus_di = 100 * (minus_dm.rolling(window=period).mean() / atr)
        
        # 计算ADX
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()
        
        return adx
    
    @staticmethod
    def calculate_momentum(data: pd.Series, period: int = 10) -> pd.Series:
        """计算动量指标"""
        return data.diff(period)
    
    @staticmethod
    def calculate_roc(data: pd.Series, period: int = 12) -> pd.Series:
        """计算变动率指标(ROC)"""
        return (data / data.shift(period) - 1) * 100
    
    @staticmethod
    def calculate_volatility(data: pd.Series, period: int = 20) -> pd.Series:
        """计算历史波动率"""
        returns = data.pct_change()
        volatility = returns.rolling(window=period).std() * np.sqrt(252)  # 年化波动率
        return volatility
    
    @staticmethod
    def calculate_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        result_df = df.copy()
        
        # 确保必要的列存在
        required_cols = ['open', 'high', 'low', 'close', 'vol']
        for col in required_cols:
            if col not in df.columns:
                print(f"Warning: Missing column {col}")
                return result_df
        
        try:
            # 移动平均线
            ma_dict = TechnicalCalculator.calculate_ma(df['close'], [5, 10, 20, 60])
            for key, value in ma_dict.items():
                result_df[key] = value
            
            # 指数移动平均线
            ema_dict = TechnicalCalculator.calculate_ema(df['close'], [12, 26])
            for key, value in ema_dict.items():
                result_df[key] = value
            
            # MACD
            macd_dict = TechnicalCalculator.calculate_macd(df['close'])
            for key, value in macd_dict.items():
                result_df[key] = value
            
            # RSI
            rsi_dict = TechnicalCalculator.calculate_rsi(df['close'])
            for key, value in rsi_dict.items():
                result_df[key] = value
            
            # KDJ
            kdj_dict = TechnicalCalculator.calculate_kdj(df['high'], df['low'], df['close'])
            for key, value in kdj_dict.items():
                result_df[key] = value
            
            # 布林带
            boll_dict = TechnicalCalculator.calculate_bollinger_bands(df['close'])
            for key, value in boll_dict.items():
                result_df[key] = value
            
            # ATR
            result_df['atr'] = TechnicalCalculator.calculate_atr(df['high'], df['low'], df['close'])
            
            # 威廉指标
            wr_dict = TechnicalCalculator.calculate_williams_r(df['high'], df['low'], df['close'])
            for key, value in wr_dict.items():
                result_df[key] = value
            
            # CCI
            result_df['cci'] = TechnicalCalculator.calculate_cci(df['high'], df['low'], df['close'])
            
            # OBV
            result_df['obv'] = TechnicalCalculator.calculate_obv(df['close'], df['vol'])
            
            # VR
            result_df['vr'] = TechnicalCalculator.calculate_vr(df['close'], df['vol'])
            
            # ADX
            result_df['adx'] = TechnicalCalculator.calculate_adx(df['high'], df['low'], df['close'])
            
            # 动量指标
            result_df['momentum'] = TechnicalCalculator.calculate_momentum(df['close'])
            
            # ROC
            result_df['roc'] = TechnicalCalculator.calculate_roc(df['close'])
            
            # 波动率
            result_df['volatility'] = TechnicalCalculator.calculate_volatility(df['close'])
            
        except Exception as e:
            print(f"Error calculating technical indicators: {e}")
        
        return result_df