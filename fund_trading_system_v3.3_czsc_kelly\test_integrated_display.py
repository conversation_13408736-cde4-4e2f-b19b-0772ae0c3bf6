"""
测试整合后的凯利显示效果
验证凯利公式结果是否正确整合到LLM输出中
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_integrated_display():
    """测试整合后的显示效果"""
    
    # 模拟完整的分析结果（包含凯利分析）
    mock_analysis_result = {
        'fund_code': '518880',
        'enhanced_decision': {
            'final_decision': 'hold',
            'decision_confidence': 0.66,
            'coordination_score': 0.85,
            'weighted_score': 0.274,
            'market_classification': {
                'primary_classification': '震荡整理'
            }
        },
        'final_decision': 'hold',
        'final_confidence': 0.66,
        'llm_analysis': {
            'market_sentiment': '谨慎',
            'confidence_level': 0.65,
            'strategy_suggestion': '采用CZSC缠论区间套操作：1) 7.5250-7.5480箱体震荡时保持30%底仓；2) 若放量突破7.5480且MACD翻红，加仓至50%目标7.60；3)跌破7.3860支撑位则止损至20%仓位，等待7.35附近三买信号。严格遵循笔破坏确认原则，30分钟级别分型确认后操作',
            'market_drivers': [
                'CZSC缠论显示MA5(7.4536)已上穿MA20(7.4024)形成短期多头排列，但MACD柱状体0.0165与信号线0.0020的背离提示动能衰减',
                '分型结构呈现7.5480-7.5250的下降笔(BI5)正在构建，结合成交量比率1.1473的放大，显示短线抛压增强'
            ]
        },
        'kelly_analysis': {
            'fund_code': '518880',
            'kelly_calculation': {
                'win_probability': 0.50,
                'risk_reward_ratio': 1.00,
                'confidence': 0.95,
                'kelly_fraction': 0.0,
                'optimal_position': 0.0,  # 0%
                'adjusted_position': 0.0,
                'risk_level': '无风险',
                'recommendation': '建议观望，暂不建仓'
            },
            'position_reasoning': '交易决策：HOLD | 胜率评估：50.0% | 风险收益比：1.00 | 综合置信度：95.0% | 凯利分数：0.00% | 建议仓位：0.00% | 风险等级：无风险 | 建议暂不建仓，保持观望',
            'timestamp': '2024-01-01T10:00:00'
        }
    }
    
    print("🧪 测试整合后的显示效果")
    print("=" * 60)
    
    # 导入显示方法
    try:
        # 尝试从主系统导入
        sys.path.append('..')
        from new_auto_trade_fund_example4_v3 import EnhancedFundTradingSystemV4
        
        # 创建系统实例
        trading_system = EnhancedFundTradingSystemV4()
        
        print("📺 整合后的显示输出:")
        print("-" * 40)
        
        # 调用修改后的显示方法
        trading_system.display_detailed_analysis_result(mock_analysis_result)
        
        print("-" * 40)
        print("✅ 整合显示测试完成")
        
        print("\n🎯 预期效果:")
        print("1. 不再显示冗长的凯利计算过程")
        print("2. 凯利仓位建议直接整合到策略建议中")
        print("3. 显示简洁的凯利仓位和风险等级")
        print("4. 保留重要的概率分析信息")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("尝试使用简化的显示方法...")
        
        # 使用简化的显示方法
        display_simplified_result(mock_analysis_result)
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def display_simplified_result(analysis_result):
    """简化的显示方法，用于测试"""
    
    fund_code = analysis_result.get('fund_code', 'UNKNOWN')
    enhanced_decision = analysis_result.get('enhanced_decision', {})
    kelly_analysis = analysis_result.get('kelly_analysis', {})
    kelly_calc = kelly_analysis.get('kelly_calculation', {})
    llm_analysis = analysis_result.get('llm_analysis', {})
    
    # 核心决策结果
    final_decision = enhanced_decision.get('final_decision', 'hold')
    decision_confidence = enhanced_decision.get('decision_confidence', 0)
    weighted_score = enhanced_decision.get('weighted_score', 0)
    
    decision_icon = "🟢" if final_decision == "buy" else ("🔴" if final_decision == "sell" else "🟡")
    print(f"\n{decision_icon} {fund_code}: {final_decision.upper()} | 置信度:{decision_confidence:.2f} | 评分:{weighted_score:.3f}")
    
    # 市场分类
    market_classification = enhanced_decision.get('market_classification', {})
    if market_classification:
        primary_class = market_classification.get('primary_classification', '未知')
        print(f"   📊 市场分类: {primary_class}")
    
    # LLM分析
    if llm_analysis:
        market_sentiment = llm_analysis.get('market_sentiment', '未知')
        llm_confidence = llm_analysis.get('confidence_level', 0)
        print(f"   🤖 LLM分析: 情绪={market_sentiment} | 置信度={llm_confidence:.2f}")
    
    # 凯利仓位 - 简化显示
    kelly_position = kelly_calc.get('optimal_position', 0)
    kelly_risk_level = kelly_calc.get('risk_level', '未知')
    win_prob = kelly_calc.get('win_probability', 0)
    risk_reward = kelly_calc.get('risk_reward_ratio', 0)
    
    position_icon = "💰" if kelly_position > 0 else "⚪"
    print(f"   {position_icon} 凯利仓位: {kelly_position:.1%} | 风险等级: {kelly_risk_level}")
    print(f"   📊 概率分析: 胜率={win_prob:.1%} | 盈亏比={risk_reward:.2f}")
    
    # 整合策略建议
    strategy_suggestion = llm_analysis.get('strategy_suggestion', '')
    if strategy_suggestion:
        if kelly_position > 0:
            kelly_prefix = f"【凯利建议: {kelly_position:.1%}仓位 | {kelly_risk_level}】"
        else:
            kelly_prefix = f"【凯利建议观望 | {kelly_risk_level}】"
        
        integrated_strategy = f"{kelly_prefix} {strategy_suggestion}"
        print(f"   💡 策略建议: {integrated_strategy}")
    
    # 市场驱动
    market_drivers = llm_analysis.get('market_drivers', [])
    if market_drivers:
        print(f"   📈 市场驱动:")
        for driver in market_drivers[:2]:
            print(f"     • {driver}")

def compare_before_after():
    """对比修改前后的效果"""
    
    print("\n" + "=" * 80)
    print("📊 修改前后对比")
    print("=" * 80)
    
    print("\n❌ 修改前的问题:")
    print("1. 显示了60多行的凯利计算详细过程")
    print("2. 凯利结果与LLM分析分离，信息杂乱")
    print("3. 用户需要在大量信息中寻找有用内容")
    print("4. 凯利建议没有整合到策略建议中")
    
    print("\n✅ 修改后的改进:")
    print("1. 凯利计算过程不再在控制台显示")
    print("2. 凯利仓位建议直接整合到策略建议中")
    print("3. 保留关键信息：仓位、风险等级、胜率、盈亏比")
    print("4. 整体输出更加简洁和有用")
    
    print("\n🎯 预期用户体验:")
    print("- 一眼就能看到凯利建议的仓位")
    print("- 策略建议中包含凯利仓位信息")
    print("- 不再被冗长的计算过程干扰")
    print("- 信息更加整合和连贯")

if __name__ == "__main__":
    print("🚀 凯利显示整合测试")
    print("=" * 60)
    
    # 测试整合显示
    success = test_integrated_display()
    
    # 对比说明
    compare_before_after()
    
    if success:
        print("\n🎉 测试成功！凯利公式已成功整合到LLM输出中")
        print("\n💡 现在运行主系统应该能看到简洁整合的输出")
    else:
        print("\n⚠️ 测试失败，请检查代码")
    
    print("=" * 60)