"""
混合决策系统 - RL与LLM协作的核心决策框架
实现分工明确的双引擎模式，LLM负责市场理解，RL负责精准执行
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from analyzers.llm_market_analyzer import LLMMarketAnalyzer
from .decision_fusion_layer import DecisionFusionLayer


class HybridDecisionSystem:
    """
    @class HybridDecisionSystem
    @brief RL + LLM 混合决策系统
    @details 实现分工明确的双引擎模式，融合深度理解与精准执行
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 初始化核心组件
        self.llm_analyzer = LLMMarketAnalyzer()         # LLM市场分析引擎
        self.rl_optimizer = None                        # RL决策优化器（延迟初始化）
        self.fusion_layer = DecisionFusionLayer()       # 决策融合层
        
        # 协作配置
        self.collaboration_mode = self.config.get('collaboration_mode', 'adaptive')  # adaptive/llm_primary/rl_primary
        self.decision_threshold = self.config.get('decision_threshold', 0.6)
        self.conflict_resolution = self.config.get('conflict_resolution', 'weighted_average')
        
        # 性能监控
        self.decision_history = []
        self.performance_metrics = {
            'total_decisions': 0,
            'llm_primary_decisions': 0,
            'rl_primary_decisions': 0,
            'hybrid_decisions': 0,
            'success_rate': 0.0
        }
        
        self.logger.info("混合决策系统初始化完成")
    
    def make_decision(self, market_data: Dict[str, Any], fund_code: str = None) -> Dict[str, Any]:
        """
        @brief 执行混合决策流程
        @param market_data: 市场数据
        @param fund_code: 基金代码
        @return: 混合决策结果
        """
        try:
            decision_id = f"decision_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.logger.info(f"开始混合决策流程 - {decision_id}, 基金: {fund_code}")
            
            # 第一阶段：LLM市场理解和分析
            llm_analysis = self._perform_llm_analysis(market_data, fund_code)
            
            # 第二阶段：RL精准决策优化
            rl_decision = self._perform_rl_optimization(market_data, llm_analysis, fund_code)
            
            # 第三阶段：决策融合
            final_decision = self._fuse_decisions(llm_analysis, rl_decision, market_data)
            
            # 第四阶段：决策验证和解释
            validated_decision = self._validate_and_explain_decision(final_decision, llm_analysis, rl_decision)
            
            # 记录决策历史
            self._record_decision(decision_id, llm_analysis, rl_decision, validated_decision, fund_code)
            
            return validated_decision
            
        except Exception as e:
            self.logger.error(f"混合决策系统异常: {str(e)}")
            return self._get_fallback_decision(market_data, fund_code, error=str(e))
    
    def _perform_llm_analysis(self, market_data: Dict[str, Any], fund_code: str) -> Dict[str, Any]:
        """执行LLM市场分析"""
        try:
            self.logger.info("执行LLM市场分析...")
            
            # 调用LLM分析器进行深度市场理解
            llm_result = self.llm_analyzer.analyze_market_narrative(market_data, fund_code)
            
            # 增强LLM分析结果
            enhanced_analysis = self._enhance_llm_analysis(llm_result, market_data)
            
            self.logger.info(f"LLM分析完成: 市场情绪={enhanced_analysis.get('market_sentiment', '未知')}")
            return enhanced_analysis
            
        except Exception as e:
            self.logger.error(f"LLM分析失败: {str(e)}")
            return {
                'analysis_type': 'llm_analysis_error',
                'error': str(e),
                'market_sentiment': '未知',
                'confidence_level': 0.0,
                'strategy_suggestion': '使用传统分析方法'
            }
    
    def _perform_rl_optimization(self, market_data: Dict[str, Any], llm_analysis: Dict[str, Any], fund_code: str) -> Dict[str, Any]:
        """执行RL决策优化"""
        try:
            self.logger.info("执行RL决策优化...")
            
            # 如果RL优化器未初始化，使用传统决策
            if self.rl_optimizer is None:
                self.logger.warning("RL优化器未初始化，使用传统决策逻辑")
                return self._get_traditional_rl_decision(market_data, llm_analysis)
            
            # 构建增强状态向量（融合LLM分析结果）
            enhanced_state = self._build_enhanced_state_vector(market_data, llm_analysis)
            
            # RL决策优化
            rl_decision = self.rl_optimizer.optimize_decision(enhanced_state)
            
            self.logger.info(f"RL优化完成: 动作={rl_decision.get('action', '未知')}")
            return rl_decision
            
        except Exception as e:
            self.logger.error(f"RL优化失败: {str(e)}")
            return self._get_traditional_rl_decision(market_data, llm_analysis, error=str(e))
    
    def _fuse_decisions(self, llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """融合LLM和RL的决策结果"""
        try:
            self.logger.info("开始决策融合...")
            
            # 使用决策融合层
            fusion_result = self.fusion_layer.fuse_decisions(
                llm_analysis=llm_analysis,
                rl_decision=rl_decision,
                market_context=market_data,
                fusion_mode=self.collaboration_mode
            )
            
            self.logger.info(f"决策融合完成: 最终决策={fusion_result.get('final_decision', '未知')}")
            return fusion_result
            
        except Exception as e:
            self.logger.error(f"决策融合失败: {str(e)}")
            # 兜底逻辑：优先使用LLM分析结果
            return self._create_fallback_fusion(llm_analysis, rl_decision, error=str(e))
    
    def _validate_and_explain_decision(self, fused_decision: Dict[str, Any], llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any]) -> Dict[str, Any]:
        """验证并解释最终决策"""
        try:
            # 决策一致性检查
            consistency_score = self._calculate_decision_consistency(llm_analysis, rl_decision, fused_decision)
            
            # 风险评估
            risk_assessment = self._assess_decision_risk(fused_decision, llm_analysis)
            
            # 生成决策解释
            explanation = self._generate_decision_explanation(fused_decision, llm_analysis, rl_decision, consistency_score)
            
            # 构建最终结果
            validated_decision = {
                **fused_decision,
                'consistency_score': consistency_score,
                'risk_assessment': risk_assessment,
                'explanation': explanation,
                'validation_timestamp': datetime.now().isoformat(),
                'decision_quality': self._evaluate_decision_quality(consistency_score, risk_assessment)
            }
            
            return validated_decision
            
        except Exception as e:
            self.logger.error(f"决策验证失败: {str(e)}")
            return {**fused_decision, 'validation_error': str(e)}
    
    def _enhance_llm_analysis(self, llm_result: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """增强LLM分析结果"""
        enhanced = llm_result.copy()
        
        # 添加数值化的情绪评分
        sentiment = llm_result.get('market_sentiment', '中性')
        sentiment_mapping = {'积极': 0.8, '乐观': 0.7, '中性': 0.5, '谨慎': 0.3, '悲观': 0.2}
        enhanced['market_sentiment_score'] = sentiment_mapping.get(sentiment, 0.5)
        
        # 添加趋势置信度
        confidence = llm_result.get('confidence_level', 0.5)
        enhanced['trend_confidence'] = confidence
        
        # 添加波动率预期
        if 'volatility' in str(llm_result).lower():
            enhanced['volatility_expectation'] = 0.7 if '高波动' in str(llm_result) else 0.3
        else:
            enhanced['volatility_expectation'] = 0.5
        
        # 添加风险等级评分
        risk_keywords = ['风险', 'risk', '谨慎', '不确定']
        risk_score = sum(1 for keyword in risk_keywords if keyword in str(llm_result).lower()) / len(risk_keywords)
        enhanced['risk_level'] = min(risk_score, 1.0)
        
        # 添加机会评分
        opportunity_keywords = ['机会', 'opportunity', '买入', '积极', '看好']
        opportunity_score = sum(1 for keyword in opportunity_keywords if keyword in str(llm_result).lower()) / len(opportunity_keywords)
        enhanced['opportunity_score'] = min(opportunity_score, 1.0)
        
        return enhanced
    
    def _build_enhanced_state_vector(self, market_data: Dict[str, Any], llm_analysis: Dict[str, Any]) -> np.ndarray:
        """构建增强状态向量（50维）"""
        try:
            features = []
            
            # 基础特征 (13维) - 模拟数据
            basic_features = [
                market_data.get('price_data', {}).get('current_price', 1.0),
                market_data.get('price_data', {}).get('change_pct', 0.0) / 100,
                market_data.get('volume', 1000000) / 1000000,  # 标准化
                0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5  # 占位符
            ]
            features.extend(basic_features[:13])
            
            # 技术指标 (15维) - 从市场数据提取
            technical_indicators = market_data.get('technical_analysis', {}).get('indicators', {})
            tech_features = [
                technical_indicators.get('rsi', 50) / 100,
                technical_indicators.get('macd', 0.0),
                (technical_indicators.get('ma5', 1.0) - technical_indicators.get('ma20', 1.0)) / technical_indicators.get('ma20', 1.0),
                0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5  # 占位符
            ]
            features.extend(tech_features[:15])
            
            # 六大维度评估 (6维)
            evaluations = market_data.get('evaluations', {})
            dimension_features = []
            for dim_name in ['趋势', '波动性', '流动性', '情绪', '结构', '转换']:
                eval_result = evaluations.get(dim_name)
                if eval_result and hasattr(eval_result, 'score'):
                    dimension_features.append(eval_result.score)
                else:
                    dimension_features.append(0.5)  # 默认值
            features.extend(dimension_features[:6])
            
            # LLM语义特征 (8维)
            llm_features = [
                llm_analysis.get('market_sentiment_score', 0.5),
                llm_analysis.get('trend_confidence', 0.5),
                llm_analysis.get('volatility_expectation', 0.5),
                llm_analysis.get('risk_level', 0.5),
                llm_analysis.get('opportunity_score', 0.5),
                llm_analysis.get('confidence_level', 0.5),
                0.5, 0.5  # 占位符
            ]
            features.extend(llm_features[:8])
            
            # 宏观因子 (8维) - 占位符
            macro_features = [0.5] * 8
            features.extend(macro_features)
            
            # 确保精确50维
            features = features[:50]
            while len(features) < 50:
                features.append(0.5)
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"构建状态向量失败: {str(e)}")
            return np.full(50, 0.5, dtype=np.float32)  # 返回默认向量
    
    def _get_traditional_rl_decision(self, market_data: Dict[str, Any], llm_analysis: Dict[str, Any], error: str = None) -> Dict[str, Any]:
        """获取传统RL决策（兜底方案）"""
        # 基于LLM分析的简化RL决策
        sentiment_score = llm_analysis.get('market_sentiment_score', 0.5)
        confidence = llm_analysis.get('confidence_level', 0.5)
        
        if sentiment_score > 0.6 and confidence > 0.6:
            action = 'buy'
            position_change = 0.3
        elif sentiment_score < 0.4 and confidence > 0.6:
            action = 'sell'
            position_change = -0.3
        else:
            action = 'hold'
            position_change = 0.0
        
        return {
            'action': action,
            'position_change': position_change,
            'confidence': confidence,
            'decision_type': 'traditional_rl',
            'error': error
        }
    
    def _calculate_decision_consistency(self, llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any], fused_decision: Dict[str, Any]) -> float:
        """计算决策一致性评分"""
        try:
            # 提取决策方向
            llm_sentiment = llm_analysis.get('market_sentiment_score', 0.5)
            rl_action = rl_decision.get('action', 'hold')
            final_action = fused_decision.get('final_decision', 'hold')
            
            # 将LLM情绪转换为动作
            if llm_sentiment > 0.6:
                llm_action = 'buy'
            elif llm_sentiment < 0.4:
                llm_action = 'sell'
            else:
                llm_action = 'hold'
            
            # 计算一致性
            consistency_score = 0.0
            if llm_action == rl_action == final_action:
                consistency_score = 1.0
            elif (llm_action == final_action) or (rl_action == final_action):
                consistency_score = 0.7
            elif llm_action == rl_action:
                consistency_score = 0.5
            else:
                consistency_score = 0.3
            
            return consistency_score
            
        except Exception as e:
            self.logger.error(f"计算决策一致性失败: {str(e)}")
            return 0.5
    
    def _assess_decision_risk(self, fused_decision: Dict[str, Any], llm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """评估决策风险"""
        risk_level = llm_analysis.get('risk_level', 0.5)
        confidence = fused_decision.get('confidence', 0.5)
        
        return {
            'risk_level': risk_level,
            'confidence_risk': 1.0 - confidence,
            'overall_risk': (risk_level + (1.0 - confidence)) / 2,
            'risk_category': 'high' if risk_level > 0.7 else 'medium' if risk_level > 0.4 else 'low'
        }
    
    def _generate_decision_explanation(self, fused_decision: Dict[str, Any], llm_analysis: Dict[str, Any], 
                                     rl_decision: Dict[str, Any], consistency_score: float) -> Dict[str, Any]:
        """生成决策解释"""
        return {
            'llm_reasoning': llm_analysis.get('strategy_suggestion', '基于LLM的市场分析'),
            'rl_reasoning': f"基于量化模型的{rl_decision.get('action', '未知')}决策",
            'fusion_reasoning': f"综合考虑市场情绪和量化信号，决策一致性{consistency_score:.2f}",
            'key_factors': [
                f"市场情绪: {llm_analysis.get('market_sentiment', '中性')}",
                f"RL动作: {rl_decision.get('action', '未知')}",
                f"最终决策: {fused_decision.get('final_decision', '未知')}"
            ]
        }
    
    def _evaluate_decision_quality(self, consistency_score: float, risk_assessment: Dict[str, Any]) -> str:
        """评估决策质量"""
        overall_risk = risk_assessment.get('overall_risk', 0.5)
        
        if consistency_score > 0.8 and overall_risk < 0.3:
            return 'excellent'
        elif consistency_score > 0.6 and overall_risk < 0.5:
            return 'good'
        elif consistency_score > 0.4 and overall_risk < 0.7:
            return 'acceptable'
        else:
            return 'poor'
    
    def _create_fallback_fusion(self, llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any], error: str = None) -> Dict[str, Any]:
        """创建兜底融合决策"""
        # 优先使用LLM分析结果
        sentiment_score = llm_analysis.get('market_sentiment_score', 0.5)
        
        if sentiment_score > 0.6:
            final_decision = 'buy'
        elif sentiment_score < 0.4:
            final_decision = 'sell'
        else:
            final_decision = 'hold'
        
        return {
            'final_decision': final_decision,
            'confidence': llm_analysis.get('confidence_level', 0.5),
            'decision_source': 'llm_primary',
            'fusion_error': error,
            'fallback_mode': True
        }
    
    def _record_decision(self, decision_id: str, llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any], 
                        final_decision: Dict[str, Any], fund_code: str) -> None:
        """记录决策历史"""
        decision_record = {
            'decision_id': decision_id,
            'fund_code': fund_code,
            'timestamp': datetime.now().isoformat(),
            'llm_analysis': llm_analysis,
            'rl_decision': rl_decision,
            'final_decision': final_decision,
            'decision_quality': final_decision.get('decision_quality', 'unknown')
        }
        
        self.decision_history.append(decision_record)
        
        # 更新性能指标
        self.performance_metrics['total_decisions'] += 1
        
        # 保持历史记录数量限制
        if len(self.decision_history) > 100:
            self.decision_history = self.decision_history[-100:]
    
    def _get_fallback_decision(self, market_data: Dict[str, Any], fund_code: str, error: str = None) -> Dict[str, Any]:
        """获取兜底决策"""
        return {
            'final_decision': 'hold',
            'confidence': 0.3,
            'decision_source': 'fallback',
            'error': error,
            'fund_code': fund_code,
            'timestamp': datetime.now().isoformat(),
            'explanation': {
                'reasoning': '系统异常，采用保守策略',
                'key_factors': ['系统错误', '风险控制']
            }
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'collaboration_mode': 'adaptive',
            'decision_threshold': 0.6,
            'conflict_resolution': 'weighted_average',
            'llm_weight': 0.6,
            'rl_weight': 0.4,
            'enable_fallback': True,
            'max_decision_history': 100
        }
    
    def set_rl_optimizer(self, rl_optimizer) -> None:
        """设置RL优化器"""
        self.rl_optimizer = rl_optimizer
        self.logger.info("RL优化器已设置")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_metrics.copy()
    
    def get_decision_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取决策历史"""
        return self.decision_history[-limit:] 