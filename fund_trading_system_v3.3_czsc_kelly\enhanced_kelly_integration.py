"""
增强版凯利公式集成模块
确保凯利仓位计算结果能够正确显示和应用
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json

@dataclass
class EnhancedTradingSignal:
    """增强版交易信号，包含详细的凯利计算信息"""
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    
    # 价格信息
    current_price: float
    entry_price: float
    stop_loss: float
    take_profit: float
    
    # 概率和比率
    win_probability: float
    risk_reward_ratio: float
    confidence: float
    
    # 凯利公式计算结果
    kelly_fraction: float      # 原始凯利分数
    kelly_position: float      # 最终建议仓位
    kelly_method: str          # 使用的凯利方法
    kelly_risk_level: str      # 凯利风险等级
    
    # 分析详情
    czsc_analysis: Dict
    llm_analysis: Dict
    integration_details: Dict
    
    # 推理说明
    reasoning: str
    position_reasoning: str    # 专门的仓位推理

class EnhancedKellyIntegrator:
    """增强版凯利公式集成器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or self._default_config()
        
    def _default_config(self) -> Dict:
        return {
            'kelly_method': 'fractional',
            'kelly_fraction': 0.25,
            'max_position': 0.25,
            'min_position': 0.01,
            'confidence_threshold': 0.5,
            'min_win_probability': 0.51,
            'min_risk_reward_ratio': 1.2
        }
    
    def calculate_enhanced_kelly_position(self, 
                                        win_probability: float,
                                        risk_reward_ratio: float,
                                        confidence: float,
                                        signal_type: str) -> Dict:
        """
        增强版凯利公式计算，返回详细信息
        
        Returns:
            包含所有计算细节的字典
        """
        
        # 1. 输入验证和调整
        adjusted_win_prob = self._adjust_win_probability(win_probability, confidence)
        adjusted_risk_reward = self._adjust_risk_reward_ratio(risk_reward_ratio, signal_type)
        
        # 2. 经典凯利公式计算
        classic_kelly = self._calculate_classic_kelly(adjusted_win_prob, adjusted_risk_reward)
        
        # 3. 应用凯利方法调整
        if self.config['kelly_method'] == 'fractional':
            adjusted_kelly = classic_kelly * self.config['kelly_fraction']
            method_description = f"分数凯利({self.config['kelly_fraction']:.0%})"
        elif self.config['kelly_method'] == 'adaptive':
            adjusted_kelly = self._calculate_adaptive_kelly(classic_kelly, confidence, win_probability)
            method_description = "自适应凯利"
        else:
            adjusted_kelly = classic_kelly
            method_description = "经典凯利"
        
        # 4. 应用仓位限制
        final_position = np.clip(adjusted_kelly, 0, self.config['max_position'])
        
        # 5. 最小仓位过滤
        if final_position < self.config['min_position']:
            final_position = 0.0
            position_status = "低于最小仓位阈值，建议观望"
        elif final_position == 0:
            position_status = "计算结果为零仓位"
        else:
            position_status = "正常仓位"
        
        # 6. 风险等级评估
        risk_level = self._assess_kelly_risk_level(classic_kelly, final_position)
        
        # 7. 生成详细说明
        calculation_details = {
            'original_win_prob': win_probability,
            'adjusted_win_prob': adjusted_win_prob,
            'original_risk_reward': risk_reward_ratio,
            'adjusted_risk_reward': adjusted_risk_reward,
            'classic_kelly_fraction': classic_kelly,
            'method_adjusted_kelly': adjusted_kelly,
            'final_position': final_position,
            'kelly_method': self.config['kelly_method'],
            'method_description': method_description,
            'risk_level': risk_level,
            'position_status': position_status,
            'confidence_factor': confidence
        }
        
        return calculation_details
    
    def _adjust_win_probability(self, win_prob: float, confidence: float) -> float:
        """根据置信度调整胜率"""
        # 置信度越低，胜率越保守
        confidence_factor = 0.8 + 0.2 * confidence  # 0.8-1.0之间
        adjusted = win_prob * confidence_factor
        return np.clip(adjusted, 0.01, 0.99)
    
    def _adjust_risk_reward_ratio(self, risk_reward: float, signal_type: str) -> float:
        """根据信号类型调整风险收益比"""
        if signal_type == 'hold':
            return 1.0  # 观望时使用中性比率
        
        # 确保最小风险收益比
        return max(risk_reward, 1.0)
    
    def _calculate_classic_kelly(self, win_prob: float, risk_reward: float) -> float:
        """计算经典凯利分数"""
        if win_prob <= 0 or win_prob >= 1 or risk_reward <= 0:
            return 0.0
        
        lose_prob = 1 - win_prob
        kelly_fraction = (win_prob * risk_reward - lose_prob) / risk_reward
        
        return max(kelly_fraction, 0.0)
    
    def _calculate_adaptive_kelly(self, classic_kelly: float, confidence: float, win_prob: float) -> float:
        """计算自适应凯利分数"""
        # 基于置信度和胜率的自适应调整
        uncertainty_factor = abs(win_prob - 0.5) * 2  # 胜率偏离50%的程度
        confidence_factor = confidence
        
        adaptive_multiplier = 0.3 + 0.4 * uncertainty_factor + 0.3 * confidence_factor
        adaptive_multiplier = np.clip(adaptive_multiplier, 0.1, 0.8)
        
        return classic_kelly * adaptive_multiplier
    
    def _assess_kelly_risk_level(self, classic_kelly: float, final_position: float) -> str:
        """评估凯利风险等级"""
        if final_position == 0:
            return "无风险"
        elif classic_kelly <= 0.05:
            return "极低风险"
        elif classic_kelly <= 0.10:
            return "低风险"
        elif classic_kelly <= 0.20:
            return "中等风险"
        elif classic_kelly <= 0.30:
            return "较高风险"
        else:
            return "高风险"
    
    def generate_position_reasoning(self, kelly_details: Dict, signal_type: str) -> str:
        """生成仓位推理说明"""
        
        reasoning_parts = []
        
        # 凯利计算过程
        reasoning_parts.append(f"凯利公式计算：")
        reasoning_parts.append(f"  • 调整后胜率：{kelly_details['adjusted_win_prob']:.1%}")
        reasoning_parts.append(f"  • 调整后盈亏比：{kelly_details['adjusted_risk_reward']:.2f}")
        reasoning_parts.append(f"  • 经典凯利分数：{kelly_details['classic_kelly_fraction']:.2%}")
        reasoning_parts.append(f"  • {kelly_details['method_description']}：{kelly_details['method_adjusted_kelly']:.2%}")
        reasoning_parts.append(f"  • 最终建议仓位：{kelly_details['final_position']:.2%}")
        
        # 风险评估
        reasoning_parts.append(f"风险评估：{kelly_details['risk_level']}")
        
        # 仓位状态
        reasoning_parts.append(f"仓位状态：{kelly_details['position_status']}")
        
        # 具体建议
        if kelly_details['final_position'] > 0:
            if signal_type == 'buy':
                reasoning_parts.append(f"建议：以{kelly_details['final_position']:.1%}仓位做多")
            elif signal_type == 'sell':
                reasoning_parts.append(f"建议：以{kelly_details['final_position']:.1%}仓位做空")
        else:
            reasoning_parts.append("建议：暂不建仓，保持观望")
        
        return "\n".join(reasoning_parts)

def create_enhanced_signal_with_kelly(symbol: str,
                                    current_price: float,
                                    signal_type: str,
                                    entry_price: float,
                                    stop_loss: float,
                                    take_profit: float,
                                    win_probability: float,
                                    confidence: float,
                                    czsc_analysis: Dict,
                                    llm_analysis: Dict,
                                    config: Dict = None) -> EnhancedTradingSignal:
    """
    创建包含详细凯利计算的增强交易信号
    """
    
    # 初始化凯利集成器
    kelly_integrator = EnhancedKellyIntegrator(config)
    
    # 计算风险收益比
    if signal_type != 'hold' and stop_loss != 0:
        if signal_type == 'buy':
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
        else:  # sell
            risk = abs(stop_loss - entry_price)
            reward = abs(entry_price - take_profit)
        
        risk_reward_ratio = reward / risk if risk > 0 else 1.0
    else:
        risk_reward_ratio = 1.0
    
    # 计算凯利仓位
    kelly_details = kelly_integrator.calculate_enhanced_kelly_position(
        win_probability=win_probability,
        risk_reward_ratio=risk_reward_ratio,
        confidence=confidence,
        signal_type=signal_type
    )
    
    # 生成仓位推理
    position_reasoning = kelly_integrator.generate_position_reasoning(kelly_details, signal_type)
    
    # 生成综合推理
    comprehensive_reasoning = _generate_comprehensive_reasoning(
        signal_type, czsc_analysis, llm_analysis, kelly_details
    )
    
    # 整合详情
    integration_details = {
        'signal_consistency': _calculate_signal_consistency(czsc_analysis, llm_analysis),
        'confidence_breakdown': {
            'czsc_confidence': czsc_analysis.get('confidence', 0),
            'llm_confidence': llm_analysis.get('confidence', 0),
            'combined_confidence': confidence
        },
        'kelly_calculation': kelly_details
    }
    
    return EnhancedTradingSignal(
        symbol=symbol,
        signal_type=signal_type,
        current_price=current_price,
        entry_price=entry_price,
        stop_loss=stop_loss,
        take_profit=take_profit,
        win_probability=win_probability,
        risk_reward_ratio=risk_reward_ratio,
        confidence=confidence,
        kelly_fraction=kelly_details['classic_kelly_fraction'],
        kelly_position=kelly_details['final_position'],
        kelly_method=kelly_details['method_description'],
        kelly_risk_level=kelly_details['risk_level'],
        czsc_analysis=czsc_analysis,
        llm_analysis=llm_analysis,
        integration_details=integration_details,
        reasoning=comprehensive_reasoning,
        position_reasoning=position_reasoning
    )

def _calculate_signal_consistency(czsc_analysis: Dict, llm_analysis: Dict) -> float:
    """计算信号一致性"""
    czsc_signal = czsc_analysis.get('signal', 'hold')
    llm_signal = llm_analysis.get('signal', 'hold')
    
    if czsc_signal == llm_signal:
        return 1.0
    elif (czsc_signal == 'hold' and llm_signal in ['buy', 'sell']) or \
         (llm_signal == 'hold' and czsc_signal in ['buy', 'sell']):
        return 0.5
    else:
        return 0.2

def _generate_comprehensive_reasoning(signal_type: str, 
                                    czsc_analysis: Dict, 
                                    llm_analysis: Dict, 
                                    kelly_details: Dict) -> str:
    """生成综合推理说明"""
    
    reasoning_parts = []
    
    # 信号分析
    reasoning_parts.append(f"交易信号：{signal_type.upper()}")
    
    # CZSC分析
    czsc_signal = czsc_analysis.get('signal', 'unknown')
    czsc_confidence = czsc_analysis.get('confidence', 0)
    reasoning_parts.append(f"CZSC分析：{czsc_signal}信号，置信度{czsc_confidence:.1%}")
    
    # LLM分析
    llm_signal = llm_analysis.get('signal', 'unknown')
    llm_confidence = llm_analysis.get('confidence', 0)
    reasoning_parts.append(f"LLM分析：{llm_signal}信号，置信度{llm_confidence:.1%}")
    
    # 凯利仓位
    kelly_position = kelly_details['final_position']
    kelly_method = kelly_details['method_description']
    kelly_risk = kelly_details['risk_level']
    
    reasoning_parts.append(f"凯利仓位：{kelly_position:.2%}（{kelly_method}，{kelly_risk}）")
    
    # 最终建议
    if kelly_position > 0:
        reasoning_parts.append(f"最终建议：{signal_type}仓位{kelly_position:.1%}")
    else:
        reasoning_parts.append("最终建议：暂不建仓")
    
    return "；".join(reasoning_parts)

# 使用示例
if __name__ == "__main__":
    # 模拟数据
    czsc_analysis = {
        'signal': 'buy',
        'confidence': 0.6,
        'strength': 3,
        'reasoning': 'CZSC显示买入信号'
    }
    
    llm_analysis = {
        'signal': 'buy',
        'confidence': 0.7,
        'reasoning': 'LLM分析显示市场情绪积极'
    }
    
    # 创建增强信号
    enhanced_signal = create_enhanced_signal_with_kelly(
        symbol='000001.SZ',
        current_price=10.50,
        signal_type='buy',
        entry_price=10.45,
        stop_loss=10.00,
        take_profit=11.50,
        win_probability=0.65,
        confidence=0.65,
        czsc_analysis=czsc_analysis,
        llm_analysis=llm_analysis,
        config={'kelly_method': 'fractional', 'kelly_fraction': 0.25}
    )
    
    print("=== 增强版交易信号 ===")
    print(f"标的：{enhanced_signal.symbol}")
    print(f"信号：{enhanced_signal.signal_type}")
    print(f"凯利仓位：{enhanced_signal.kelly_position:.2%}")
    print(f"凯利方法：{enhanced_signal.kelly_method}")
    print(f"风险等级：{enhanced_signal.kelly_risk_level}")
    print(f"\n仓位推理：")
    print(enhanced_signal.position_reasoning)
    print(f"\n综合推理：")
    print(enhanced_signal.reasoning)