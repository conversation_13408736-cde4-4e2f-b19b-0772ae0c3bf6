"""
数据结构定义
定义所有数据获取相关的数据类
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import pandas as pd
import numpy as np


@dataclass
class MarketData:
    """市场基础数据"""
    ts_code: str                    # 股票代码
    trade_date: str                 # 交易日期
    open: float                     # 开盘价
    high: float                     # 最高价
    low: float                      # 最低价
    close: float                    # 收盘价
    pre_close: float               # 前收盘价
    change: float                  # 涨跌额
    pct_chg: float                 # 涨跌幅
    vol: float                     # 成交量(手)
    amount: float                  # 成交额(千元)
    
    # 扩展字段
    turnover_rate: Optional[float] = None      # 换手率
    turnover_rate_f: Optional[float] = None    # 换手率(自由流通股)
    volume_ratio: Optional[float] = None       # 量比
    pe: Optional[float] = None                 # 市盈率
    pb: Optional[float] = None                 # 市净率


@dataclass
class TechnicalIndicators:
    """技术指标数据"""
    ts_code: str
    trade_date: str
    
    # 趋势指标
    ma5: Optional[float] = None        # 5日均线
    ma10: Optional[float] = None       # 10日均线
    ma20: Optional[float] = None       # 20日均线
    ma60: Optional[float] = None       # 60日均线
    ema12: Optional[float] = None      # 12日指数移动平均
    ema26: Optional[float] = None      # 26日指数移动平均
    
    # MACD指标
    macd_dif: Optional[float] = None   # MACD DIF
    macd_dea: Optional[float] = None   # MACD DEA
    macd_hist: Optional[float] = None  # MACD柱状图
    
    # 动量指标
    rsi: Optional[float] = None        # RSI
    rsi6: Optional[float] = None       # 6日RSI
    rsi12: Optional[float] = None      # 12日RSI
    rsi24: Optional[float] = None      # 24日RSI
    
    # 随机指标
    kdj_k: Optional[float] = None      # KDJ K值
    kdj_d: Optional[float] = None      # KDJ D值
    kdj_j: Optional[float] = None      # KDJ J值
    
    # 布林带
    boll_upper: Optional[float] = None # 布林带上轨
    boll_mid: Optional[float] = None   # 布林带中轨
    boll_lower: Optional[float] = None # 布林带下轨
    
    # 波动率指标
    atr: Optional[float] = None        # 平均真实波幅
    volatility: Optional[float] = None # 历史波动率
    
    # 成交量指标
    obv: Optional[float] = None        # 能量潮
    vr: Optional[float] = None         # 成交量比率
    
    # 威廉指标
    wr: Optional[float] = None         # 威廉指标
    wr6: Optional[float] = None        # 6日威廉指标
    wr10: Optional[float] = None       # 10日威廉指标
    
    # CCI指标
    cci: Optional[float] = None        # 顺势指标
    
    # 其他指标
    adx: Optional[float] = None        # 平均趋向指数
    momentum: Optional[float] = None   # 动量指标
    roc: Optional[float] = None        # 变动率指标


@dataclass
class MacroData:
    """宏观经济数据"""
    trade_date: str
    
    # GDP相关
    gdp_yoy: Optional[float] = None           # GDP同比增长率
    gdp_quarter: Optional[float] = None       # 季度GDP
    
    # 通胀数据
    cpi: Optional[float] = None               # 消费者价格指数
    ppi: Optional[float] = None               # 生产者价格指数
    
    # 货币供应量
    m0: Optional[float] = None                # 流通中现金
    m1: Optional[float] = None                # 狭义货币供应量
    m2: Optional[float] = None                # 广义货币供应量
    
    # 利率数据
    shibor_on: Optional[float] = None         # 隔夜Shibor
    shibor_1w: Optional[float] = None         # 1周Shibor
    shibor_2w: Optional[float] = None         # 2周Shibor
    shibor_1m: Optional[float] = None         # 1月Shibor
    shibor_3m: Optional[float] = None         # 3月Shibor
    shibor_6m: Optional[float] = None         # 6月Shibor
    shibor_9m: Optional[float] = None         # 9月Shibor
    shibor_1y: Optional[float] = None         # 1年Shibor
    
    # LPR数据
    lpr_1y: Optional[float] = None            # 1年期LPR
    lpr_5y: Optional[float] = None            # 5年期LPR
    
    # 汇率数据
    usd_cny: Optional[float] = None           # 美元兑人民币
    eur_cny: Optional[float] = None           # 欧元兑人民币
    
    # 大宗商品
    gold_price: Optional[float] = None        # 黄金价格
    oil_price: Optional[float] = None         # 原油价格


@dataclass
class NewsData:
    """新闻数据"""
    datetime: str                    # 新闻时间
    title: str                      # 新闻标题
    content: Optional[str] = None   # 新闻内容
    source: Optional[str] = None    # 新闻来源
    url: Optional[str] = None       # 新闻链接
    
    # 情感分析结果
    sentiment_score: Optional[float] = None    # 情感得分 (-1到1)
    sentiment_label: Optional[str] = None      # 情感标签 (positive/negative/neutral)
    
    # 关键词
    keywords: Optional[List[str]] = None       # 关键词列表
    
    # 相关股票
    related_stocks: Optional[List[str]] = None # 相关股票代码


@dataclass
class FundFlowData:
    """资金流向数据"""
    ts_code: str
    trade_date: str
    
    # 主力资金
    main_net_inflow: Optional[float] = None      # 主力净流入
    main_net_inflow_rate: Optional[float] = None # 主力净流入率
    
    # 超大单
    xl_net_inflow: Optional[float] = None        # 超大单净流入
    xl_net_inflow_rate: Optional[float] = None   # 超大单净流入率
    
    # 大单
    l_net_inflow: Optional[float] = None         # 大单净流入
    l_net_inflow_rate: Optional[float] = None    # 大单净流入率
    
    # 中单
    m_net_inflow: Optional[float] = None         # 中单净流入
    m_net_inflow_rate: Optional[float] = None    # 中单净流入率
    
    # 小单
    s_net_inflow: Optional[float] = None         # 小单净流入
    s_net_inflow_rate: Optional[float] = None    # 小单净流入率


@dataclass
class ComprehensiveData:
    """综合数据 - 50维度特征"""
    ts_code: str
    trade_date: str
    
    # 基础数据 (6维)
    market_data: MarketData
    
    # 技术指标 (20维)
    technical_indicators: TechnicalIndicators
    
    # 宏观数据 (15维)
    macro_data: MacroData
    
    # 资金流向 (5维)
    fund_flow: FundFlowData
    
    # 新闻情感 (4维)
    news_sentiment: Optional[float] = None       # 新闻情感得分
    news_count: Optional[int] = None             # 新闻数量
    positive_news_ratio: Optional[float] = None  # 正面新闻比例
    negative_news_ratio: Optional[float] = None  # 负面新闻比例
    
    def to_feature_vector(self) -> np.ndarray:
        """转换为50维特征向量"""
        features = []
        
        # 基础数据 (6维)
        features.extend([
            self.market_data.open,
            self.market_data.high,
            self.market_data.low,
            self.market_data.close,
            self.market_data.vol,
            self.market_data.amount
        ])
        
        # 技术指标 (20维)
        tech = self.technical_indicators
        features.extend([
            tech.ma5 or 0, tech.ma10 or 0, tech.ma20 or 0, tech.ma60 or 0,
            tech.ema12 or 0, tech.ema26 or 0,
            tech.macd_dif or 0, tech.macd_dea or 0, tech.macd_hist or 0,
            tech.rsi or 0, tech.rsi6 or 0, tech.rsi12 or 0,
            tech.kdj_k or 0, tech.kdj_d or 0, tech.kdj_j or 0,
            tech.boll_upper or 0, tech.boll_mid or 0, tech.boll_lower or 0,
            tech.atr or 0, tech.volatility or 0
        ])
        
        # 宏观数据 (15维)
        macro = self.macro_data
        features.extend([
            macro.gdp_yoy or 0, macro.cpi or 0, macro.ppi or 0,
            macro.m0 or 0, macro.m1 or 0, macro.m2 or 0,
            macro.shibor_on or 0, macro.shibor_1w or 0, macro.shibor_1m or 0,
            macro.shibor_3m or 0, macro.shibor_6m or 0, macro.shibor_1y or 0,
            macro.lpr_1y or 0, macro.usd_cny or 0, macro.gold_price or 0
        ])
        
        # 资金流向 (5维)
        fund = self.fund_flow
        features.extend([
            fund.main_net_inflow or 0,
            fund.xl_net_inflow or 0,
            fund.l_net_inflow or 0,
            fund.m_net_inflow or 0,
            fund.s_net_inflow or 0
        ])
        
        # 新闻情感 (4维)
        features.extend([
            self.news_sentiment or 0,
            self.news_count or 0,
            self.positive_news_ratio or 0,
            self.negative_news_ratio or 0
        ])
        
        return np.array(features, dtype=np.float32)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'ts_code': self.ts_code,
            'trade_date': self.trade_date,
            'market_data': self.market_data.__dict__,
            'technical_indicators': self.technical_indicators.__dict__,
            'macro_data': self.macro_data.__dict__,
            'fund_flow': self.fund_flow.__dict__,
            'news_sentiment': self.news_sentiment,
            'news_count': self.news_count,
            'positive_news_ratio': self.positive_news_ratio,
            'negative_news_ratio': self.negative_news_ratio
        }


@dataclass
class DataRequest:
    """数据请求参数"""
    ts_codes: List[str]              # 股票代码列表
    start_date: str                  # 开始日期
    end_date: str                    # 结束日期
    fields: Optional[List[str]] = None    # 指定字段
    freq: str = 'D'                  # 频率: D日线, W周线, M月线
    
    # 数据类型开关
    include_basic: bool = True       # 包含基础数据
    include_technical: bool = True   # 包含技术指标
    include_macro: bool = True       # 包含宏观数据
    include_news: bool = True        # 包含新闻数据
    include_fund_flow: bool = True   # 包含资金流向


@dataclass
class DataResponse:
    """数据响应结果"""
    success: bool                    # 是否成功
    data: Optional[List[ComprehensiveData]] = None  # 数据列表
    error_message: Optional[str] = None             # 错误信息
    request_time: Optional[datetime] = None         # 请求时间
    data_count: int = 0             # 数据条数
    
    def to_dataframe(self) -> Optional[pd.DataFrame]:
        """转换为DataFrame"""
        if not self.success or not self.data:
            return None
            
        records = []
        for item in self.data:
            record = item.to_dict()
            records.append(record)
            
        return pd.DataFrame(records)