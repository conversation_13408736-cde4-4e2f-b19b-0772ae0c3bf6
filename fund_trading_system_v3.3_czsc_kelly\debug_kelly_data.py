#!/usr/bin/env python3
"""
调试凯利协调器接收到的数据结构
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3

def debug_kelly_data():
    """调试凯利数据结构"""
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    print("🔍 开始调试凯利协调器数据结构")
    print("=" * 60)
    
    try:
        # 初始化协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 执行分析
        result = coordinator.coordinate_analysis('518880')
        
        print("\n✅ 调试完成")
        print("=" * 60)
        
        # 显示凯利分析结果
        kelly_analysis = result.get('kelly_analysis', {})
        if kelly_analysis:
            kelly_calc = kelly_analysis.get('kelly_calculation', {})
            print(f"凯利仓位: {kelly_calc.get('optimal_position', 0):.2%}")
            print(f"胜率: {kelly_calc.get('win_probability', 0):.1%}")
            print(f"盈亏比: {kelly_calc.get('risk_reward_ratio', 0):.2f}")
            print(f"风险等级: {kelly_calc.get('risk_level', 'N/A')}")
        else:
            print("❌ 未找到凯利分析结果")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_kelly_data()