"""
Backtest Modules Integration Package
回测模块集成包，用于增强CZSC信号质量
"""

import sys
import os
import logging

# 添加backtest_modules路径到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 设置日志
logger = logging.getLogger(__name__)

# 尝试导入各个模块
try:
    from .signal_quality_enhancer import SignalQualityEnhancer
    SIGNAL_ENHANCER_AVAILABLE = True
    logger.info("SignalQualityEnhancer 模块加载成功")
except ImportError as e:
    SIGNAL_ENHANCER_AVAILABLE = False
    logger.warning(f"SignalQualityEnhancer 模块加载失败: {e}")

try:
    from .risk_enhanced_result import RiskEnhancedEvaluationResult
    RISK_ENHANCED_RESULT_AVAILABLE = True
    logger.info("RiskEnhancedEvaluationResult 模块加载成功")
except ImportError as e:
    RISK_ENHANCED_RESULT_AVAILABLE = False
    logger.warning(f"RiskEnhancedEvaluationResult 模块加载失败: {e}")

# 尝试导入原始backtest_modules中的模块
try:
    # 假设原始模块在上级目录的backtest_modules中
    parent_dir = os.path.dirname(current_dir)
    backtest_modules_dir = os.path.join(parent_dir, 'backtest_modules')
    
    if os.path.exists(backtest_modules_dir) and backtest_modules_dir not in sys.path:
        sys.path.insert(0, backtest_modules_dir)
    
    from advanced_backtest_system import AdvancedBacktestSystem
    ADVANCED_BACKTEST_AVAILABLE = True
    logger.info("AdvancedBacktestSystem 模块加载成功")
except ImportError as e:
    ADVANCED_BACKTEST_AVAILABLE = False
    logger.warning(f"AdvancedBacktestSystem 模块加载失败: {e}")

try:
    from data_preprocessor import DataPreprocessor
    DATA_PREPROCESSOR_AVAILABLE = True
    logger.info("DataPreprocessor 模块加载成功")
except ImportError as e:
    DATA_PREPROCESSOR_AVAILABLE = False
    logger.warning(f"DataPreprocessor 模块加载失败: {e}")

try:
    from feature_engineer import FeatureEngineer
    FEATURE_ENGINEER_AVAILABLE = True
    logger.info("FeatureEngineer 模块加载成功")
except ImportError as e:
    FEATURE_ENGINEER_AVAILABLE = False
    logger.warning(f"FeatureEngineer 模块加载失败: {e}")

try:
    from model_trainer import ModelTrainer
    MODEL_TRAINER_AVAILABLE = True
    logger.info("ModelTrainer 模块加载成功")
except ImportError as e:
    MODEL_TRAINER_AVAILABLE = False
    logger.warning(f"ModelTrainer 模块加载失败: {e}")

try:
    from risk_manager import RiskManager
    RISK_MANAGER_AVAILABLE = True
    logger.info("RiskManager 模块加载成功")
except ImportError as e:
    RISK_MANAGER_AVAILABLE = False
    logger.warning(f"RiskManager 模块加载失败: {e}")

try:
    from backtest_engine import BacktestEngine
    BACKTEST_ENGINE_AVAILABLE = True
    logger.info("BacktestEngine 模块加载成功")
except ImportError as e:
    BACKTEST_ENGINE_AVAILABLE = False
    logger.warning(f"BacktestEngine 模块加载失败: {e}")

# 模块可用性检查
def check_module_availability():
    """检查模块可用性"""
    availability = {
        'signal_enhancer': SIGNAL_ENHANCER_AVAILABLE,
        'risk_enhanced_result': RISK_ENHANCED_RESULT_AVAILABLE,
        'advanced_backtest': ADVANCED_BACKTEST_AVAILABLE,
        'data_preprocessor': DATA_PREPROCESSOR_AVAILABLE,
        'feature_engineer': FEATURE_ENGINEER_AVAILABLE,
        'model_trainer': MODEL_TRAINER_AVAILABLE,
        'risk_manager': RISK_MANAGER_AVAILABLE,
        'backtest_engine': BACKTEST_ENGINE_AVAILABLE
    }
    
    available_count = sum(availability.values())
    total_count = len(availability)
    
    logger.info(f"模块可用性检查: {available_count}/{total_count} 个模块可用")
    
    for module, available in availability.items():
        status = "✓" if available else "✗"
        logger.info(f"  {status} {module}")
    
    return availability

# 创建集成工厂类
class BacktestIntegrationFactory:
    """回测集成工厂类"""
    
    @staticmethod
    def create_enhanced_system(config=None):
        """创建增强的回测系统"""
        if not ADVANCED_BACKTEST_AVAILABLE:
            raise ImportError("AdvancedBacktestSystem 不可用，无法创建增强系统")
        
        default_config = {
            'data_preprocessor': {
                'missing_strategy': 'interpolate',
                'outlier_threshold': 3.0
            },
            'feature_engineer': {
                'correlation_threshold': 0.8,
                'variance_threshold': 0.01
            },
            'backtest_engine': {
                'initial_capital': 100000,
                'transaction_cost': 0.001,
                'signal_method': 'threshold',
                'signal_threshold': 0.6
            },
            'risk_manager': {
                'max_position_size': 0.1,
                'stop_loss_pct': 0.05,
                'take_profit_pct': 0.15,
                'max_drawdown_limit': 0.2
            }
        }
        
        if config:
            # 递归更新配置
            def update_config(base, update):
                for key, value in update.items():
                    if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                        update_config(base[key], value)
                    else:
                        base[key] = value
            
            update_config(default_config, config)
        
        return AdvancedBacktestSystem(default_config)
    
    @staticmethod
    def create_signal_enhancer(backtest_system=None):
        """创建信号质量增强器"""
        if not SIGNAL_ENHANCER_AVAILABLE:
            raise ImportError("SignalQualityEnhancer 不可用")
        
        if backtest_system is None:
            backtest_system = BacktestIntegrationFactory.create_enhanced_system()
        
        return SignalQualityEnhancer(backtest_system)
    
    @staticmethod
    def create_risk_manager(config=None):
        """创建风险管理器"""
        if not RISK_MANAGER_AVAILABLE:
            raise ImportError("RiskManager 不可用")
        
        default_config = {
            'max_position_size': 0.1,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.15,
            'max_drawdown_limit': 0.2,
            'max_daily_loss': 0.02,
            'position_concentration_limit': 0.3
        }
        
        if config:
            default_config.update(config)
        
        return RiskManager(**default_config)

# 便捷函数
def get_available_modules():
    """获取可用模块列表"""
    return check_module_availability()

def is_fully_integrated():
    """检查是否完全集成"""
    availability = check_module_availability()
    return all(availability.values())

# 导出主要类和函数
__all__ = [
    'SignalQualityEnhancer',
    'RiskEnhancedEvaluationResult', 
    'BacktestIntegrationFactory',
    'check_module_availability',
    'get_available_modules',
    'is_fully_integrated',
    # 可用性标志
    'SIGNAL_ENHANCER_AVAILABLE',
    'RISK_ENHANCED_RESULT_AVAILABLE',
    'ADVANCED_BACKTEST_AVAILABLE',
    'DATA_PREPROCESSOR_AVAILABLE',
    'FEATURE_ENGINEER_AVAILABLE',
    'MODEL_TRAINER_AVAILABLE',
    'RISK_MANAGER_AVAILABLE',
    'BACKTEST_ENGINE_AVAILABLE'
]

# 条件导出
if ADVANCED_BACKTEST_AVAILABLE:
    __all__.append('AdvancedBacktestSystem')

if DATA_PREPROCESSOR_AVAILABLE:
    __all__.append('DataPreprocessor')

if FEATURE_ENGINEER_AVAILABLE:
    __all__.append('FeatureEngineer')

if MODEL_TRAINER_AVAILABLE:
    __all__.append('ModelTrainer')

if RISK_MANAGER_AVAILABLE:
    __all__.append('RiskManager')

if BACKTEST_ENGINE_AVAILABLE:
    __all__.append('BacktestEngine')

# 初始化日志
logger.info("Backtest Modules Integration Package 初始化完成")
logger.info(f"可用模块: {sum(check_module_availability().values())}/8")