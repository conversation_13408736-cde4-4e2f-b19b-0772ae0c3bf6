"""
动态规则引擎
根据市场环境和历史表现动态调整风控规则
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict

from .data_structures import MarketEnvironmentAssessment, PerformanceMetrics
from .risk_control_config import RiskControlConfig


@dataclass
class RuleAdjustment:
    """规则调整记录"""
    rule_name: str
    original_value: float
    adjusted_value: float
    adjustment_factor: float
    reason: str
    timestamp: datetime
    market_condition: str


class DynamicRuleEngine:
    """
    @class DynamicRuleEngine
    @brief 动态规则引擎
    @details 根据市场环境和系统表现动态调整风控规则
    """
    
    def __init__(self, base_config: RiskControlConfig):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.base_config = base_config
        self.current_adjustments = {}  # {rule_path: RuleAdjustment}
        self.adjustment_history = []
        self.performance_tracker = {}
        self.last_adjustment_time = datetime.now()
        
        # 调整策略配置
        self.adjustment_strategies = self._initialize_adjustment_strategies()
        
    def _initialize_adjustment_strategies(self) -> Dict[str, Dict[str, Any]]:
        """初始化调整策略"""
        return {
            'market_regime_based': {
                'bull_market': {
                    'rsi_threshold_multiplier': 1.1,  # 牛市时RSI阈值放宽10%
                    'volume_requirement_multiplier': 0.9,  # 成交量要求降低10%
                    'bb_tolerance_multiplier': 1.2,  # 布林线容忍度放宽20%
                    'confidence_threshold_multiplier': 0.95  # 置信度要求略微降低
                },
                'bear_market': {
                    'rsi_threshold_multiplier': 0.85,  # 熊市时RSI阈值收紧15%
                    'volume_requirement_multiplier': 1.3,  # 成交量要求提高30%
                    'bb_tolerance_multiplier': 0.7,  # 布林线容忍度收紧30%
                    'confidence_threshold_multiplier': 1.1  # 置信度要求提高
                },
                'sideways_market': {
                    'rsi_threshold_multiplier': 0.95,  # 震荡市略微收紧
                    'volume_requirement_multiplier': 1.1,
                    'bb_tolerance_multiplier': 0.9,
                    'confidence_threshold_multiplier': 1.05
                }
            },
            'volatility_based': {
                'high_volatility': {
                    'all_thresholds_multiplier': 0.8,  # 高波动时全面收紧20%
                    'position_limit_multiplier': 0.7,  # 仓位限制收紧30%
                    'stop_loss_multiplier': 0.9  # 止损更严格
                },
                'medium_volatility': {
                    'all_thresholds_multiplier': 1.0,  # 保持标准
                    'position_limit_multiplier': 1.0,
                    'stop_loss_multiplier': 1.0
                },
                'low_volatility': {
                    'all_thresholds_multiplier': 1.15,  # 低波动时适度放宽
                    'position_limit_multiplier': 1.2,
                    'stop_loss_multiplier': 1.1
                }
            },
            'performance_based': {
                'poor_performance': {
                    'all_thresholds_multiplier': 0.7,  # 表现差时大幅收紧
                    'confidence_threshold_multiplier': 1.2,
                    'position_limit_multiplier': 0.6
                },
                'good_performance': {
                    'all_thresholds_multiplier': 1.1,  # 表现好时适度放宽
                    'confidence_threshold_multiplier': 0.95,
                    'position_limit_multiplier': 1.1
                }
            },
            'liquidity_based': {
                'poor_liquidity': {
                    'volume_requirement_multiplier': 1.5,  # 流动性差时大幅提高成交量要求
                    'position_limit_multiplier': 0.8,  # 降低仓位限制
                    'bb_tolerance_multiplier': 0.8  # 收紧布林线要求
                },
                'good_liquidity': {
                    'volume_requirement_multiplier': 0.9,
                    'position_limit_multiplier': 1.1,
                    'bb_tolerance_multiplier': 1.1
                }
            }
        }
    
    def adjust_rules(self, market_assessment: MarketEnvironmentAssessment,
                    performance_metrics: Optional[PerformanceMetrics] = None) -> Dict[str, Any]:
        """
        @brief 根据市场环境调整规则
        @param market_assessment: 市场环境评估
        @param performance_metrics: 系统表现指标
        @return: 调整后的规则配置
        """
        try:
            self.logger.info(f"开始动态规则调整 - 市场状态: {market_assessment.market_regime}")
            
            # 计算调整因子
            adjustment_factors = self._calculate_adjustment_factors(
                market_assessment, performance_metrics
            )
            
            # 应用调整
            adjusted_config = self._apply_adjustments(adjustment_factors, market_assessment)
            
            # 记录调整历史
            self._record_adjustments(adjustment_factors, market_assessment)
            
            # 验证调整结果
            validation_result = self._validate_adjusted_config(adjusted_config)
            
            return {
                'adjusted_config': adjusted_config,
                'adjustment_factors': adjustment_factors,
                'validation_result': validation_result,
                'adjustment_time': datetime.now().isoformat(),
                'market_condition': {
                    'regime': market_assessment.market_regime,
                    'volatility': market_assessment.volatility_level,
                    'liquidity': market_assessment.liquidity_condition,
                    'risk_level': market_assessment.overall_risk_level
                }
            }
            
        except Exception as e:
            self.logger.error(f"动态规则调整失败: {str(e)}")
            return {
                'adjusted_config': self.base_config.export_config(),
                'error': str(e)
            }
    
    def _calculate_adjustment_factors(self, market_assessment: MarketEnvironmentAssessment,
                                    performance_metrics: Optional[PerformanceMetrics]) -> Dict[str, float]:
        """计算调整因子"""
        factors = {
            'rsi_threshold_multiplier': 1.0,
            'volume_requirement_multiplier': 1.0,
            'bb_tolerance_multiplier': 1.0,
            'confidence_threshold_multiplier': 1.0,
            'position_limit_multiplier': 1.0,
            'all_thresholds_multiplier': 1.0,
            'stop_loss_multiplier': 1.0
        }
        
        # 基于市场状态调整
        regime_adjustments = self.adjustment_strategies['market_regime_based'].get(
            market_assessment.market_regime, {}
        )
        for key, value in regime_adjustments.items():
            factors[key] = factors.get(key, 1.0) * value
        
        # 基于波动性调整
        volatility_adjustments = self.adjustment_strategies['volatility_based'].get(
            market_assessment.volatility_level, {}
        )
        for key, value in volatility_adjustments.items():
            factors[key] = factors.get(key, 1.0) * value
        
        # 基于流动性调整
        liquidity_adjustments = self.adjustment_strategies['liquidity_based'].get(
            market_assessment.liquidity_condition, {}
        )
        for key, value in liquidity_adjustments.items():
            factors[key] = factors.get(key, 1.0) * value
        
        # 基于系统表现调整
        if performance_metrics:
            performance_adjustments = self._get_performance_adjustments(performance_metrics)
            for key, value in performance_adjustments.items():
                factors[key] = factors.get(key, 1.0) * value
        
        # 应用全局调整因子
        global_multiplier = factors.get('all_thresholds_multiplier', 1.0)
        if global_multiplier != 1.0:
            for key in factors:
                if key != 'all_thresholds_multiplier':
                    factors[key] *= global_multiplier
        
        return factors
    
    def _get_performance_adjustments(self, performance_metrics: PerformanceMetrics) -> Dict[str, float]:
        """基于系统表现获取调整因子"""
        adjustments = {}
        
        # 基于夏普比率调整
        if performance_metrics.sharpe_ratio < 0.5:
            performance_category = 'poor_performance'
        elif performance_metrics.sharpe_ratio > 1.5:
            performance_category = 'good_performance'
        else:
            return adjustments  # 中等表现，不调整
        
        strategy_adjustments = self.adjustment_strategies['performance_based'].get(
            performance_category, {}
        )
        adjustments.update(strategy_adjustments)
        
        # 基于最大回撤调整
        if performance_metrics.max_drawdown > 0.15:  # 回撤超过15%
            adjustments['position_limit_multiplier'] = adjustments.get('position_limit_multiplier', 1.0) * 0.8
            adjustments['confidence_threshold_multiplier'] = adjustments.get('confidence_threshold_multiplier', 1.0) * 1.1
        
        # 基于胜率调整
        if performance_metrics.win_rate < 0.4:  # 胜率低于40%
            adjustments['all_thresholds_multiplier'] = adjustments.get('all_thresholds_multiplier', 1.0) * 0.9
        
        return adjustments
    
    def _apply_adjustments(self, adjustment_factors: Dict[str, float],
                          market_assessment: MarketEnvironmentAssessment) -> Dict[str, Any]:
        """应用调整因子到配置"""
        adjusted_config = self.base_config.export_config()
        
        # 调整技术指标配置
        tech_config = adjusted_config['technical_indicators']
        
        # RSI调整
        if 'rsi_threshold_multiplier' in adjustment_factors:
            multiplier = adjustment_factors['rsi_threshold_multiplier']
            tech_config['rsi']['buy_max_threshold'] = int(
                tech_config['rsi']['buy_max_threshold'] * multiplier
            )
            tech_config['rsi']['sell_min_threshold'] = int(
                tech_config['rsi']['sell_min_threshold'] * multiplier
            )
        
        # 成交量调整
        if 'volume_requirement_multiplier' in adjustment_factors:
            multiplier = adjustment_factors['volume_requirement_multiplier']
            tech_config['volume']['min_volume_ratio'] *= multiplier
        
        # 布林线调整
        if 'bb_tolerance_multiplier' in adjustment_factors:
            multiplier = adjustment_factors['bb_tolerance_multiplier']
            tech_config['bollinger_bands']['tolerance'] *= multiplier
        
        # 仓位限制调整
        if 'position_limit_multiplier' in adjustment_factors:
            multiplier = adjustment_factors['position_limit_multiplier']
            portfolio_config = adjusted_config['portfolio_risk']
            portfolio_config['position_limits']['max_single_position'] *= multiplier
            portfolio_config['position_limits']['max_sector_exposure'] *= multiplier
        
        # 置信度阈值调整
        if 'confidence_threshold_multiplier' in adjustment_factors:
            multiplier = adjustment_factors['confidence_threshold_multiplier']
            # 这里可以调整系统中的置信度要求
            adjusted_config['min_confidence_threshold'] = 0.6 * multiplier
        
        return adjusted_config
    
    def _record_adjustments(self, adjustment_factors: Dict[str, float],
                           market_assessment: MarketEnvironmentAssessment):
        """记录调整历史"""
        timestamp = datetime.now()
        
        for rule_name, factor in adjustment_factors.items():
            if factor != 1.0:  # 只记录有实际调整的规则
                adjustment = RuleAdjustment(
                    rule_name=rule_name,
                    original_value=1.0,  # 基准值
                    adjusted_value=factor,
                    adjustment_factor=factor,
                    reason=f"市场状态: {market_assessment.market_regime}, 波动性: {market_assessment.volatility_level}",
                    timestamp=timestamp,
                    market_condition=f"{market_assessment.market_regime}_{market_assessment.volatility_level}"
                )
                
                self.current_adjustments[rule_name] = adjustment
                self.adjustment_history.append(adjustment)
        
        self.last_adjustment_time = timestamp
    
    def _validate_adjusted_config(self, adjusted_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证调整后的配置"""
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        try:
            # 检查RSI阈值合理性
            rsi_config = adjusted_config['technical_indicators']['rsi']
            if rsi_config['buy_max_threshold'] < 30:
                validation_result['warnings'].append("RSI买入阈值过低，可能错过买入机会")
            elif rsi_config['buy_max_threshold'] > 80:
                validation_result['warnings'].append("RSI买入阈值过高，风险控制可能不足")
            
            # 检查成交量要求
            volume_config = adjusted_config['technical_indicators']['volume']
            if volume_config['min_volume_ratio'] < 0.5:
                validation_result['warnings'].append("成交量要求过低，流动性风险增加")
            elif volume_config['min_volume_ratio'] > 3.0:
                validation_result['warnings'].append("成交量要求过高，可能难以满足")
            
            # 检查仓位限制
            position_config = adjusted_config['portfolio_risk']['position_limits']
            if position_config['max_single_position'] > 0.3:
                validation_result['errors'].append("单仓位限制过高，集中度风险过大")
                validation_result['valid'] = False
            elif position_config['max_single_position'] < 0.05:
                validation_result['warnings'].append("单仓位限制过低，可能影响收益潜力")
            
        except Exception as e:
            validation_result['errors'].append(f"配置验证异常: {str(e)}")
            validation_result['valid'] = False
        
        return validation_result
    
    def get_current_adjustments(self) -> Dict[str, RuleAdjustment]:
        """获取当前生效的调整"""
        return self.current_adjustments.copy()
    
    def get_adjustment_history(self, days: int = 30) -> List[RuleAdjustment]:
        """获取调整历史"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return [adj for adj in self.adjustment_history if adj.timestamp >= cutoff_date]
    
    def reset_adjustments(self):
        """重置所有调整"""
        self.current_adjustments.clear()
        self.logger.info("所有规则调整已重置")
    
    def should_readjust(self, market_assessment: MarketEnvironmentAssessment) -> bool:
        """判断是否需要重新调整"""
        # 检查时间间隔
        time_since_last = datetime.now() - self.last_adjustment_time
        if time_since_last < timedelta(hours=1):  # 至少间隔1小时
            return False
        
        # 检查市场环境是否有显著变化
        if not self.current_adjustments:
            return True
        
        # 检查风险水平变化
        current_risk = market_assessment.overall_risk_level
        if current_risk in ['high', 'critical']:
            return True
        
        return False
    
    def get_rule_effectiveness(self) -> Dict[str, Any]:
        """评估规则调整的有效性"""
        if not self.adjustment_history:
            return {'message': '暂无调整历史数据'}
        
        # 统计调整频率
        recent_adjustments = self.get_adjustment_history(7)  # 最近7天
        adjustment_frequency = len(recent_adjustments)
        
        # 统计调整类型
        adjustment_types = {}
        for adj in recent_adjustments:
            adj_type = adj.rule_name
            adjustment_types[adj_type] = adjustment_types.get(adj_type, 0) + 1
        
        # 计算平均调整幅度
        adjustment_magnitudes = [abs(adj.adjustment_factor - 1.0) for adj in recent_adjustments]
        avg_magnitude = np.mean(adjustment_magnitudes) if adjustment_magnitudes else 0
        
        return {
            'adjustment_frequency_7days': adjustment_frequency,
            'adjustment_types': adjustment_types,
            'average_adjustment_magnitude': avg_magnitude,
            'most_adjusted_rule': max(adjustment_types.items(), key=lambda x: x[1])[0] if adjustment_types else None,
            'last_adjustment_time': self.last_adjustment_time.isoformat()
        }
