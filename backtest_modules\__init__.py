"""
高级回测系统模块
包含数据预处理、特征工程、时间序列处理、网络架构、模型训练、回测引擎和风险管理模块
"""

from .data_preprocessor import DataPreprocessor
from .feature_engineer import FeatureEngineer
from .timeseries_processor import TimeSeriesProcessor
from .network_architecture import NetworkArchitecture
from .model_trainer import ModelTrainer
from .backtest_engine import BacktestEngine
from .risk_manager import RiskManager
from .advanced_backtest_system import AdvancedBacktestSystem

__version__ = "1.0.0"
__author__ = "Advanced Backtest System"

__all__ = [
    'DataPreprocessor',
    'FeatureEngineer', 
    'TimeSeriesProcessor',
    'NetworkArchitecture',
    'ModelTrainer',
    'BacktestEngine',
    'RiskManager',
    'AdvancedBacktestSystem'
] 