import pandas as pd
import numpy as np
from typing import Optional, Dict, Any
from scipy import stats
import warnings
warnings.filterwarnings('ignore')


class DataPreprocessor:
    """
    数据预处理模块
    负责处理缺失值、异常值检测、时间序列连续性保证
    """
    
    def __init__(self, missing_strategy: str = 'interpolate', outlier_threshold: float = 3.0):
        """
        初始化数据预处理器
        
        Args:
            missing_strategy: 缺失值处理策略 ('interpolate', 'forward_fill', 'drop', 'mean')
            outlier_threshold: 异常值检测阈值（Z-score标准）
        """
        self.missing_strategy = missing_strategy
        self.outlier_threshold = outlier_threshold
        self.outlier_indices = []
        self.processing_log = []
        
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理缺失值
        
        Args:
            df: 输入数据框
            
        Returns:
            处理后的数据框
        """
        df_processed = df.copy()
        
        # 记录原始缺失值情况
        missing_before = df_processed.isnull().sum().sum()
        self.processing_log.append(f"Missing values before processing: {missing_before}")
        
        if self.missing_strategy == 'interpolate':
            # 使用线性插值填充数值列
            numeric_cols = df_processed.select_dtypes(include=[np.number]).columns
            df_processed[numeric_cols] = df_processed[numeric_cols].interpolate(method='linear')
            
        elif self.missing_strategy == 'forward_fill':
            # 前向填充
            df_processed = df_processed.fillna(method='ffill')
            
        elif self.missing_strategy == 'mean':
            # 用均值填充数值列
            numeric_cols = df_processed.select_dtypes(include=[np.number]).columns
            df_processed[numeric_cols] = df_processed[numeric_cols].fillna(df_processed[numeric_cols].mean())
            
        elif self.missing_strategy == 'drop':
            # 删除包含缺失值的行
            df_processed = df_processed.dropna()
            
        # 记录处理后缺失值情况
        missing_after = df_processed.isnull().sum().sum()
        self.processing_log.append(f"Missing values after processing: {missing_after}")
        
        return df_processed
    
    def detect_outliers(self, df: pd.DataFrame, columns: Optional[list] = None) -> pd.DataFrame:
        """
        检测并处理异常值
        
        Args:
            df: 输入数据框
            columns: 需要检测异常值的列名列表，如果为None则检测所有数值列
            
        Returns:
            处理后的数据框
        """
        df_processed = df.copy()
        
        if columns is None:
            columns = df_processed.select_dtypes(include=[np.number]).columns.tolist()
        
        outlier_count = 0
        
        for col in columns:
            if col in df_processed.columns:
                # 使用Z-score方法检测异常值
                z_scores = np.abs(stats.zscore(df_processed[col].dropna()))
                outlier_mask = z_scores > self.outlier_threshold
                
                # 记录异常值位置
                outlier_indices = df_processed[df_processed[col].notna()].index[outlier_mask]
                self.outlier_indices.extend([(col, idx) for idx in outlier_indices])
                outlier_count += len(outlier_indices)
                
                # 使用中位数替换异常值
                median_value = df_processed[col].median()
                df_processed.loc[outlier_indices, col] = median_value
        
        self.processing_log.append(f"Detected and processed {outlier_count} outliers")
        
        return df_processed
    
    def ensure_continuity(self, df: pd.DataFrame, time_col: str = 'dt') -> pd.DataFrame:
        """
        确保时间序列的连续性
        
        Args:
            df: 输入数据框
            time_col: 时间列名
            
        Returns:
            连续性处理后的数据框
        """
        df_processed = df.copy()
        
        if time_col not in df_processed.columns:
            self.processing_log.append(f"Warning: Time column '{time_col}' not found")
            return df_processed
        
        # 确保时间列为datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df_processed[time_col]):
            df_processed[time_col] = pd.to_datetime(df_processed[time_col])
        
        # 按时间排序
        df_processed = df_processed.sort_values(time_col).reset_index(drop=True)
        
        # 检查时间间隔
        time_diffs = df_processed[time_col].diff()
        
        # 记录时间连续性信息
        min_interval = time_diffs.min()
        max_interval = time_diffs.max()
        median_interval = time_diffs.median()
        
        self.processing_log.append(f"Time continuity check - Min: {min_interval}, Max: {max_interval}, Median: {median_interval}")
        
        return df_processed
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        验证数据质量
        
        Args:
            df: 输入数据框
            
        Returns:
            数据质量报告字典
        """
        quality_report = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'missing_values': df.isnull().sum().to_dict(),
            'data_types': df.dtypes.to_dict(),
            'memory_usage': df.memory_usage(deep=True).to_dict(),
            'outlier_count': len(self.outlier_indices),
            'processing_log': self.processing_log.copy()
        }
        
        # 数值列统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            quality_report['numeric_summary'] = df[numeric_cols].describe().to_dict()
        
        return quality_report
    
    def preprocess_pipeline(self, df: pd.DataFrame, time_col: str = 'dt', 
                          outlier_columns: Optional[list] = None) -> tuple:
        """
        完整的数据预处理流水线
        
        Args:
            df: 输入数据框
            time_col: 时间列名
            outlier_columns: 需要检测异常值的列
            
        Returns:
            (处理后的数据框, 质量报告)
        """
        self.processing_log.clear()
        self.outlier_indices.clear()
        
        self.processing_log.append("Starting data preprocessing pipeline")
        
        # 1. 确保时间序列连续性
        df_processed = self.ensure_continuity(df, time_col)
        
        # 2. 处理缺失值
        df_processed = self.handle_missing_values(df_processed)
        
        # 3. 检测和处理异常值
        df_processed = self.detect_outliers(df_processed, outlier_columns)
        
        # 4. 生成质量报告
        quality_report = self.validate_data_quality(df_processed)
        
        self.processing_log.append("Data preprocessing pipeline completed")
        
        return df_processed, quality_report 