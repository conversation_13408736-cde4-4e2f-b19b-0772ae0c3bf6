"""
决策融合层 - 整合LLM和RL的决策结果
实现多种融合策略，包括权重平均、置信度加权、动态融合等
"""

import logging
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum


class FusionMode(Enum):
    """融合模式枚举"""
    WEIGHTED_AVERAGE = "weighted_average"      # 权重平均
    CONFIDENCE_BASED = "confidence_based"      # 基于置信度
    ADAPTIVE = "adaptive"                      # 自适应融合
    LLM_PRIMARY = "llm_primary"               # LLM主导
    RL_PRIMARY = "rl_primary"                 # RL主导
    MAJORITY_VOTE = "majority_vote"           # 多数投票


class DecisionFusionLayer:
    """
    @class DecisionFusionLayer
    @brief 决策融合层
    @details 整合LLM和RL的决策结果，提供多种融合策略
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 融合参数
        self.llm_base_weight = self.config.get('llm_base_weight', 0.6)
        self.rl_base_weight = self.config.get('rl_base_weight', 0.4)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)
        self.conflict_threshold = self.config.get('conflict_threshold', 0.3)
        
        # 融合历史和学习
        self.fusion_history = []
        self.adaptive_weights = {'llm': self.llm_base_weight, 'rl': self.rl_base_weight}
        
        self.logger.info("决策融合层初始化完成")
    
    def fuse_decisions(self, llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any], 
                      market_context: Dict[str, Any], fusion_mode: str = 'adaptive') -> Dict[str, Any]:
        """
        @brief 融合LLM和RL决策
        @param llm_analysis: LLM分析结果
        @param rl_decision: RL决策结果
        @param market_context: 市场上下文
        @param fusion_mode: 融合模式
        @return: 融合决策结果
        """
        try:
            self.logger.info(f"开始决策融合 - 模式: {fusion_mode}")
            
            # 预处理决策输入
            normalized_llm = self._normalize_llm_decision(llm_analysis)
            normalized_rl = self._normalize_rl_decision(rl_decision)
            
            # 检测决策冲突
            conflict_info = self._detect_decision_conflict(normalized_llm, normalized_rl)
            
            # 根据融合模式执行融合
            fusion_result = self._execute_fusion(
                normalized_llm, normalized_rl, market_context, 
                FusionMode(fusion_mode), conflict_info
            )
            
            # 后处理和验证
            final_result = self._post_process_fusion(fusion_result, conflict_info, llm_analysis, rl_decision)
            
            # 记录融合历史
            self._record_fusion_history(normalized_llm, normalized_rl, final_result, fusion_mode)
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"决策融合失败: {str(e)}")
            return self._get_fallback_fusion(llm_analysis, rl_decision, error=str(e))
    
    def _normalize_llm_decision(self, llm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """标准化LLM决策"""
        sentiment_score = llm_analysis.get('market_sentiment_score', 0.5)
        confidence = llm_analysis.get('confidence_level', 0.5)
        
        # 将LLM分析转换为标准化决策
        if sentiment_score > 0.6:
            action = 'buy'
            position_strength = (sentiment_score - 0.5) * 2  # 映射到[0,1]
        elif sentiment_score < 0.4:
            action = 'sell'
            position_strength = (0.5 - sentiment_score) * 2  # 映射到[0,1]
        else:
            action = 'hold'
            position_strength = 0.0
        
        return {
            'action': action,
            'position_strength': position_strength,
            'confidence': confidence,
            'source': 'llm',
            'raw_sentiment_score': sentiment_score,
            'strategy_reasoning': llm_analysis.get('strategy_suggestion', '')
        }
    
    def _normalize_rl_decision(self, rl_decision: Dict[str, Any]) -> Dict[str, Any]:
        """标准化RL决策"""
        action = rl_decision.get('action', 'hold')
        position_change = rl_decision.get('position_change', 0.0)
        confidence = rl_decision.get('confidence', 0.5)
        
        # 计算仓位强度
        position_strength = abs(position_change) if action != 'hold' else 0.0
        
        return {
            'action': action,
            'position_strength': min(position_strength, 1.0),
            'confidence': confidence,
            'source': 'rl',
            'raw_position_change': position_change,
            'decision_type': rl_decision.get('decision_type', 'rl')
        }
    
    def _detect_decision_conflict(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any]) -> Dict[str, Any]:
        """检测决策冲突"""
        llm_action = normalized_llm['action']
        rl_action = normalized_rl['action']
        
        # 判断冲突类型
        if llm_action == rl_action:
            conflict_type = 'no_conflict'
            conflict_severity = 0.0
        elif (llm_action == 'hold') or (rl_action == 'hold'):
            conflict_type = 'mild_conflict'
            conflict_severity = 0.3
        elif (llm_action == 'buy' and rl_action == 'sell') or (llm_action == 'sell' and rl_action == 'buy'):
            conflict_type = 'strong_conflict'
            conflict_severity = 1.0
        else:
            conflict_type = 'unknown_conflict'
            conflict_severity = 0.5
        
        # 计算置信度差异
        confidence_diff = abs(normalized_llm['confidence'] - normalized_rl['confidence'])
        
        return {
            'conflict_type': conflict_type,
            'conflict_severity': conflict_severity,
            'confidence_difference': confidence_diff,
            'llm_action': llm_action,
            'rl_action': rl_action,
            'requires_resolution': conflict_severity > self.conflict_threshold
        }
    
    def _execute_fusion(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any], 
                       market_context: Dict[str, Any], fusion_mode: FusionMode, 
                       conflict_info: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的融合策略"""
        
        if fusion_mode == FusionMode.WEIGHTED_AVERAGE:
            return self._weighted_average_fusion(normalized_llm, normalized_rl)
        
        elif fusion_mode == FusionMode.CONFIDENCE_BASED:
            return self._confidence_based_fusion(normalized_llm, normalized_rl)
        
        elif fusion_mode == FusionMode.ADAPTIVE:
            return self._adaptive_fusion(normalized_llm, normalized_rl, market_context, conflict_info)
        
        elif fusion_mode == FusionMode.LLM_PRIMARY:
            return self._llm_primary_fusion(normalized_llm, normalized_rl, conflict_info)
        
        elif fusion_mode == FusionMode.RL_PRIMARY:
            return self._rl_primary_fusion(normalized_llm, normalized_rl, conflict_info)
        
        elif fusion_mode == FusionMode.MAJORITY_VOTE:
            return self._majority_vote_fusion(normalized_llm, normalized_rl)
        
        else:
            self.logger.warning(f"未知融合模式: {fusion_mode}, 使用自适应融合")
            return self._adaptive_fusion(normalized_llm, normalized_rl, market_context, conflict_info)
    
    def _weighted_average_fusion(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any]) -> Dict[str, Any]:
        """权重平均融合"""
        llm_weight = self.adaptive_weights['llm']
        rl_weight = self.adaptive_weights['rl']
        
        # 加权平均置信度
        weighted_confidence = (llm_weight * normalized_llm['confidence'] + 
                              rl_weight * normalized_rl['confidence'])
        
        # 决策逻辑：如果两个决策一致，采用该决策；否则根据权重和置信度决定
        if normalized_llm['action'] == normalized_rl['action']:
            final_action = normalized_llm['action']
            position_strength = (llm_weight * normalized_llm['position_strength'] + 
                               rl_weight * normalized_rl['position_strength'])
        else:
            # 冲突情况：选择权重*置信度更高的决策
            llm_score = llm_weight * normalized_llm['confidence']
            rl_score = rl_weight * normalized_rl['confidence']
            
            if llm_score > rl_score:
                final_action = normalized_llm['action']
                position_strength = normalized_llm['position_strength'] * (llm_score / (llm_score + rl_score))
            else:
                final_action = normalized_rl['action']
                position_strength = normalized_rl['position_strength'] * (rl_score / (llm_score + rl_score))
        
        return {
            'final_decision': final_action,
            'position_strength': position_strength,
            'confidence': weighted_confidence,
            'fusion_method': 'weighted_average',
            'weights_used': {'llm': llm_weight, 'rl': rl_weight}
        }
    
    def _confidence_based_fusion(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any]) -> Dict[str, Any]:
        """基于置信度的融合"""
        llm_confidence = normalized_llm['confidence']
        rl_confidence = normalized_rl['confidence']
        
        # 根据置信度动态调整权重
        total_confidence = llm_confidence + rl_confidence
        if total_confidence > 0:
            dynamic_llm_weight = llm_confidence / total_confidence
            dynamic_rl_weight = rl_confidence / total_confidence
        else:
            dynamic_llm_weight = 0.5
            dynamic_rl_weight = 0.5
        
        # 如果一方置信度明显高于另一方，采用高置信度方的决策
        confidence_diff = abs(llm_confidence - rl_confidence)
        if confidence_diff > 0.3:
            if llm_confidence > rl_confidence:
                final_action = normalized_llm['action']
                position_strength = normalized_llm['position_strength']
                final_confidence = llm_confidence
                decision_source = 'llm_high_confidence'
            else:
                final_action = normalized_rl['action']
                position_strength = normalized_rl['position_strength']
                final_confidence = rl_confidence
                decision_source = 'rl_high_confidence'
        else:
            # 置信度相近，使用加权融合
            if normalized_llm['action'] == normalized_rl['action']:
                final_action = normalized_llm['action']
                position_strength = (dynamic_llm_weight * normalized_llm['position_strength'] + 
                                   dynamic_rl_weight * normalized_rl['position_strength'])
            else:
                # 选择置信度更高的
                if llm_confidence >= rl_confidence:
                    final_action = normalized_llm['action']
                    position_strength = normalized_llm['position_strength']
                else:
                    final_action = normalized_rl['action']
                    position_strength = normalized_rl['position_strength']
            
            final_confidence = max(llm_confidence, rl_confidence)
            decision_source = 'confidence_based_fusion'
        
        return {
            'final_decision': final_action,
            'position_strength': position_strength,
            'confidence': final_confidence,
            'fusion_method': 'confidence_based',
            'decision_source': decision_source,
            'dynamic_weights': {'llm': dynamic_llm_weight, 'rl': dynamic_rl_weight}
        }
    
    def _adaptive_fusion(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any], 
                        market_context: Dict[str, Any], conflict_info: Dict[str, Any]) -> Dict[str, Any]:
        """自适应融合"""
        # 根据市场环境和冲突情况选择最佳融合策略
        conflict_severity = conflict_info['conflict_severity']
        
        # 市场波动性评估
        market_volatility = self._assess_market_volatility(market_context)
        
        # 决策融合策略选择
        if conflict_severity < 0.2:
            # 无冲突或轻微冲突，使用权重平均
            result = self._weighted_average_fusion(normalized_llm, normalized_rl)
            result['adaptive_strategy'] = 'weighted_average_no_conflict'
        
        elif conflict_severity > 0.7:
            # 强冲突，使用置信度决策
            result = self._confidence_based_fusion(normalized_llm, normalized_rl)
            result['adaptive_strategy'] = 'confidence_based_high_conflict'
        
        elif market_volatility > 0.7:
            # 高波动市场，偏向RL决策
            result = self._rl_primary_fusion(normalized_llm, normalized_rl, conflict_info)
            result['adaptive_strategy'] = 'rl_primary_high_volatility'
        
        else:
            # 一般情况，偏向LLM决策
            result = self._llm_primary_fusion(normalized_llm, normalized_rl, conflict_info)
            result['adaptive_strategy'] = 'llm_primary_stable_market'
        
        result['fusion_method'] = 'adaptive'
        result['market_volatility'] = market_volatility
        result['conflict_severity'] = conflict_severity
        
        return result
    
    def _llm_primary_fusion(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any], 
                           conflict_info: Dict[str, Any]) -> Dict[str, Any]:
        """LLM主导融合"""
        # 以LLM决策为主，RL作为调节
        final_action = normalized_llm['action']
        base_strength = normalized_llm['position_strength']
        
        # RL调节因子
        if normalized_llm['action'] == normalized_rl['action']:
            # 一致时增强信号
            adjustment_factor = 1.0 + 0.2 * normalized_rl['confidence']
        else:
            # 冲突时减弱信号
            adjustment_factor = 1.0 - 0.3 * normalized_rl['confidence']
        
        adjusted_strength = base_strength * max(0.1, min(1.0, adjustment_factor))
        
        return {
            'final_decision': final_action,
            'position_strength': adjusted_strength,
            'confidence': normalized_llm['confidence'],
            'fusion_method': 'llm_primary',
            'rl_adjustment_factor': adjustment_factor
        }
    
    def _rl_primary_fusion(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any], 
                          conflict_info: Dict[str, Any]) -> Dict[str, Any]:
        """RL主导融合"""
        # 以RL决策为主，LLM作为调节
        final_action = normalized_rl['action']
        base_strength = normalized_rl['position_strength']
        
        # LLM调节因子
        if normalized_llm['action'] == normalized_rl['action']:
            # 一致时增强信号
            adjustment_factor = 1.0 + 0.2 * normalized_llm['confidence']
        else:
            # 冲突时减弱信号
            adjustment_factor = 1.0 - 0.3 * normalized_llm['confidence']
        
        adjusted_strength = base_strength * max(0.1, min(1.0, adjustment_factor))
        
        return {
            'final_decision': final_action,
            'position_strength': adjusted_strength,
            'confidence': normalized_rl['confidence'],
            'fusion_method': 'rl_primary',
            'llm_adjustment_factor': adjustment_factor
        }
    
    def _majority_vote_fusion(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any]) -> Dict[str, Any]:
        """多数投票融合（扩展版本，可加入更多决策源）"""
        # 当前只有两个决策源，简化为置信度比较
        if normalized_llm['confidence'] >= normalized_rl['confidence']:
            return {
                'final_decision': normalized_llm['action'],
                'position_strength': normalized_llm['position_strength'],
                'confidence': normalized_llm['confidence'],
                'fusion_method': 'majority_vote',
                'winning_source': 'llm'
            }
        else:
            return {
                'final_decision': normalized_rl['action'],
                'position_strength': normalized_rl['position_strength'],
                'confidence': normalized_rl['confidence'],
                'fusion_method': 'majority_vote',
                'winning_source': 'rl'
            }
    
    def _assess_market_volatility(self, market_context: Dict[str, Any]) -> float:
        """评估市场波动性"""
        try:
            # 从市场数据中提取波动性指标
            evaluations = market_context.get('evaluations', {})
            volatility_eval = evaluations.get('波动性')
            
            if volatility_eval and hasattr(volatility_eval, 'score'):
                return volatility_eval.score
            
            # 兜底：从技术指标估算波动性
            technical_data = market_context.get('technical_analysis', {})
            atr = technical_data.get('indicators', {}).get('atr', 0.5)
            return min(atr / 100, 1.0)  # 标准化到[0,1]
            
        except Exception as e:
            self.logger.warning(f"评估市场波动性失败: {str(e)}")
            return 0.5  # 默认中等波动性
    
    def _post_process_fusion(self, fusion_result: Dict[str, Any], conflict_info: Dict[str, Any], 
                           llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any]) -> Dict[str, Any]:
        """后处理融合结果"""
        # 添加元信息
        fusion_result.update({
            'fusion_timestamp': datetime.now().isoformat(),
            'conflict_info': conflict_info,
            'input_sources': {
                'llm': {
                    'market_sentiment': llm_analysis.get('market_sentiment', '未知'),
                    'confidence': llm_analysis.get('confidence_level', 0.5)
                },
                'rl': {
                    'action': rl_decision.get('action', '未知'),
                    'confidence': rl_decision.get('confidence', 0.5)
                }
            }
        })
        
        # 安全性检查
        if fusion_result.get('position_strength', 0) > 1.0:
            fusion_result['position_strength'] = 1.0
            fusion_result['position_strength_capped'] = True
        
        # 置信度合理性检查
        if fusion_result.get('confidence', 0) > 1.0:
            fusion_result['confidence'] = 1.0
        elif fusion_result.get('confidence', 0) < 0.0:
            fusion_result['confidence'] = 0.0
        
        return fusion_result
    
    def _record_fusion_history(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any], 
                             final_result: Dict[str, Any], fusion_mode: str) -> None:
        """记录融合历史"""
        fusion_record = {
            'timestamp': datetime.now().isoformat(),
            'fusion_mode': fusion_mode,
            'llm_input': normalized_llm.copy(),
            'rl_input': normalized_rl.copy(),
            'final_result': final_result.copy()
        }
        
        self.fusion_history.append(fusion_record)
        
        # 保持历史记录限制
        if len(self.fusion_history) > 50:
            self.fusion_history = self.fusion_history[-50:]
        
        # 更新自适应权重（简化版本）
        self._update_adaptive_weights(normalized_llm, normalized_rl, final_result)
    
    def _update_adaptive_weights(self, normalized_llm: Dict[str, Any], normalized_rl: Dict[str, Any], 
                               final_result: Dict[str, Any]) -> None:
        """更新自适应权重"""
        # 简化的权重更新逻辑
        learning_rate = 0.01
        
        # 如果LLM和RL决策一致且置信度高，增加相应权重
        if (normalized_llm['action'] == normalized_rl['action'] and 
            normalized_llm['confidence'] > 0.7 and normalized_rl['confidence'] > 0.7):
            # 两者都表现良好，保持当前权重
            pass
        elif normalized_llm['confidence'] > normalized_rl['confidence']:
            # LLM表现更好，增加LLM权重
            self.adaptive_weights['llm'] = min(0.8, self.adaptive_weights['llm'] + learning_rate)
            self.adaptive_weights['rl'] = 1.0 - self.adaptive_weights['llm']
        else:
            # RL表现更好，增加RL权重
            self.adaptive_weights['rl'] = min(0.8, self.adaptive_weights['rl'] + learning_rate)
            self.adaptive_weights['llm'] = 1.0 - self.adaptive_weights['rl']
    
    def _get_fallback_fusion(self, llm_analysis: Dict[str, Any], rl_decision: Dict[str, Any], 
                           error: str = None) -> Dict[str, Any]:
        """获取兜底融合结果"""
        # 优先使用LLM分析结果
        sentiment_score = llm_analysis.get('market_sentiment_score', 0.5)
        
        if sentiment_score > 0.6:
            final_decision = 'buy'
        elif sentiment_score < 0.4:
            final_decision = 'sell'
        else:
            final_decision = 'hold'
        
        return {
            'final_decision': final_decision,
            'position_strength': abs(sentiment_score - 0.5) * 2,
            'confidence': llm_analysis.get('confidence_level', 0.3),
            'fusion_method': 'fallback',
            'error': error,
            'decision_source': 'llm_fallback'
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'llm_base_weight': 0.6,
            'rl_base_weight': 0.4,
            'confidence_threshold': 0.7,
            'conflict_threshold': 0.3,
            'enable_adaptive_weights': True,
            'max_history_length': 50
        }
    
    def get_fusion_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取融合历史"""
        return self.fusion_history[-limit:]
    
    def get_adaptive_weights(self) -> Dict[str, float]:
        """获取当前自适应权重"""
        return self.adaptive_weights.copy() 