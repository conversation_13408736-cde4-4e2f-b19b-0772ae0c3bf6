# Tushare数据提供器

独立的Tushare数据获取项目，提供50维度金融数据获取服务。

## 功能特性

- **50维度数据获取**: 基础市场数据 + 技术指标 + 宏观数据 + 资金流向 + 新闻情感
- **统一数据入口**: 使用项目中的`dataNew.py`作为数据入口，无需额外配置token
- **技术指标计算**: 内置20+种技术指标计算
- **数据缓存**: 支持内存缓存，提高数据获取效率
- **错误重试**: 自动重试机制，提高数据获取稳定性
- **灵活配置**: 支持多种配置模式（实时、历史、5年数据等）

## 快速开始

### 1. 基本使用

```python
from tushare_data_provider import TushareDataProvider, DataRequest

# 初始化数据提供器
provider = TushareDataProvider()

# 获取实时数据
response = provider.get_realtime_data(['000001.SZ', '600000.SH'])

if response.success:
    print(f"获取到 {response.data_count} 条数据")
    
    # 获取第一条数据
    first_data = response.data[0]
    print(f"股票: {first_data.ts_code}")
    print(f"收盘价: {first_data.market_data.close}")
    print(f"RSI: {first_data.technical_indicators.rsi}")
    
    # 转换为50维特征向量
    feature_vector = first_data.to_feature_vector()
    print(f"特征向量维度: {len(feature_vector)}")
```

### 2. 获取历史数据

```python
from datetime import datetime, timedelta

# 获取最近30天数据
end_date = datetime.now().strftime("%Y%m%d")
start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")

request = DataRequest(
    ts_codes=['000001.SZ', '600000.SH'],
    start_date=start_date,
    end_date=end_date,
    include_basic=True,
    include_technical=True,
    include_macro=True,
    include_fund_flow=True
)

response = provider.get_comprehensive_data(request)
```

### 3. 获取5年历史数据

```python
# 直接获取5年数据
response = provider.get_5year_data(['000001.SZ', '600000.SH'])

if response.success:
    # 转换为DataFrame
    df = response.to_dataframe()
    print(f"数据形状: {df.shape}")
```

## 数据结构

### 50维特征向量构成

1. **基础数据 (6维)**: 开盘价、最高价、最低价、收盘价、成交量、成交额
2. **技术指标 (20维)**: MA、EMA、MACD、RSI、KDJ、布林带、ATR等
3. **宏观数据 (15维)**: GDP、CPI、货币供应量、Shibor、LPR、汇率等
4. **资金流向 (5维)**: 主力资金、超大单、大单、中单、小单净流入
5. **新闻情感 (4维)**: 情感得分、新闻数量、正面/负面新闻比例

### 主要数据类

- `MarketData`: 市场基础数据
- `TechnicalIndicators`: 技术指标数据
- `MacroData`: 宏观经济数据
- `FundFlowData`: 资金流向数据
- `NewsData`: 新闻数据
- `ComprehensiveData`: 综合数据（包含所有维度）

## 配置选项

### 预定义配置

```python
from tushare_data_provider import FIVE_YEAR_CONFIG, REALTIME_CONFIG, DEFAULT_CONFIG

# 5年数据配置
provider = TushareDataProvider(FIVE_YEAR_CONFIG)

# 实时数据配置
provider = TushareDataProvider(REALTIME_CONFIG)

# 默认配置
provider = TushareDataProvider(DEFAULT_CONFIG)
```

### 自定义配置

```python
from tushare_data_provider import DataConfig

config = DataConfig(
    enable_cache=True,
    cache_expire_hours=2,
    request_delay=0.2,
    max_retries=5,
    ma_periods=[5, 10, 20, 60],
    rsi_period=14
)

provider = TushareDataProvider(config)
```

## 技术指标

支持的技术指标包括：

### 趋势指标
- MA (移动平均线): 5, 10, 20, 60日
- EMA (指数移动平均): 12, 26日
- MACD: DIF, DEA, HIST
- ADX (平均趋向指数)

### 动量指标
- RSI (相对强弱指数): 6, 12, 14, 24日
- KDJ (随机指标): K, D, J值
- Williams %R: 6, 10, 14日
- CCI (顺势指标)
- Momentum (动量指标)
- ROC (变动率指标)

### 波动率指标
- ATR (平均真实波幅)
- Bollinger Bands (布林带): 上轨、中轨、下轨
- Volatility (历史波动率)

### 成交量指标
- OBV (能量潮)
- VR (成交量比率)

## 缓存机制

数据提供器内置缓存机制，可以显著提高重复请求的响应速度：

```python
# 查看缓存信息
cache_info = provider.get_cache_info()
print(f"缓存大小: {cache_info['cache_size']}")

# 清空缓存
provider.clear_cache()
```

## 错误处理

内置重试机制和错误处理：

- 自动重试失败的请求（默认3次）
- 指数退避策略
- 详细的错误日志
- 优雅的错误降级

## 性能优化

- 内存缓存减少重复请求
- 并发请求支持
- 数据预处理和清洗
- 合理的请求间隔控制

## 示例代码

查看 `examples/basic_usage.py` 获取完整的使用示例。

## 依赖项

- pandas >= 1.3.0
- numpy >= 1.20.0
- 项目中的 `dataNew.py` (Tushare数据接口)

## 注意事项

1. 确保项目根目录下有 `dataNew.py` 文件
2. Tushare接口有频率限制，建议合理设置请求间隔
3. 大量历史数据获取可能需要较长时间
4. 部分宏观数据更新频率较低（月度、季度）
5. 新闻数据获取可能受到接口限制

## 更新日志

### v1.0.0
- 初始版本发布
- 支持50维度数据获取
- 内置技术指标计算
- 缓存和重试机制
- 完整的示例代码