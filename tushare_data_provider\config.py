"""
数据获取配置文件
"""

from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime, timedelta


@dataclass
class DataConfig:
    """数据获取配置"""
    
    # 基础配置
    default_start_date: str = "20190101"  # 默认5年前
    default_end_date: str = datetime.now().strftime("%Y%m%d")
    
    # 数据维度配置
    enable_basic_data: bool = True      # 基础OHLCV数据
    enable_technical: bool = True       # 技术指标
    enable_macro: bool = True          # 宏观数据
    enable_news: bool = True           # 新闻数据
    enable_fund_flow: bool = True      # 资金流向
    
    # 技术指标参数
    ma_periods: List[int] = None       # 移动平均周期
    ema_periods: List[int] = None      # 指数移动平均周期
    rsi_period: int = 14               # RSI周期
    macd_params: Dict[str, int] = None # MACD参数
    bollinger_period: int = 20         # 布林带周期
    atr_period: int = 14               # ATR周期
    
    # 数据缓存配置
    enable_cache: bool = True
    cache_expire_hours: int = 1        # 缓存过期时间（小时）
    
    # 请求配置
    request_delay: float = 0.1         # 请求间隔（秒）
    max_retries: int = 3               # 最大重试次数
    timeout: int = 30                  # 请求超时时间
    
    def __post_init__(self):
        """初始化默认参数"""
        if self.ma_periods is None:
            self.ma_periods = [5, 10, 20, 60]
            
        if self.ema_periods is None:
            self.ema_periods = [12, 26]
            
        if self.macd_params is None:
            self.macd_params = {
                'fast': 12,
                'slow': 26, 
                'signal': 9
            }
    
    @classmethod
    def create_5year_config(cls) -> 'DataConfig':
        """创建5年数据获取配置"""
        start_date = (datetime.now() - timedelta(days=5*365)).strftime("%Y%m%d")
        return cls(
            default_start_date=start_date,
            enable_basic_data=True,
            enable_technical=True,
            enable_macro=True,
            enable_news=True,
            enable_fund_flow=True
        )
    
    @classmethod
    def create_realtime_config(cls) -> 'DataConfig':
        """创建实时数据获取配置"""
        today = datetime.now().strftime("%Y%m%d")
        return cls(
            default_start_date=today,
            default_end_date=today,
            enable_basic_data=True,
            enable_technical=True,
            enable_macro=False,
            enable_news=True,
            enable_fund_flow=True,
            cache_expire_hours=0.1  # 实时数据缓存6分钟
        )


# 预定义配置
DEFAULT_CONFIG = DataConfig()
FIVE_YEAR_CONFIG = DataConfig.create_5year_config()
REALTIME_CONFIG = DataConfig.create_realtime_config()