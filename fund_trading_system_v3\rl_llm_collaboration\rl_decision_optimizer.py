"""
RL决策优化器 - 强化学习决策优化核心组件
支持多种RL算法(PPO, SAC, A3C)，提供高精度的交易决策优化
"""

import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List
from enum import Enum
import json
import os


class RLAlgorithm(Enum):
    """RL算法枚举"""
    PPO = "ppo"
    SAC = "sac"
    A3C = "a3c"
    DDPG = "ddpg"


class PolicyNetwork(nn.Module):
    """策略网络"""
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(PolicyNetwork, self).__init__()
        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.<PERSON>(),
            nn.Linear(hidden_dim, action_dim),
            nn.Tanh()  # 输出[-1, 1]范围的动作
        )
        
    def forward(self, state):
        return self.network(state)


class ValueNetwork(nn.Module):
    """价值网络"""
    def __init__(self, state_dim: int, hidden_dim: int = 256):
        super(ValueNetwork, self).__init__()
        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, state):
        return self.network(state)


class PPOAgent:
    """PPO算法智能体"""
    def __init__(self, state_dim: int = 50, action_dim: int = 2, hidden_dim: int = 256,
                 lr: float = 3e-4, gamma: float = 0.99, eps_clip: float = 0.2):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.eps_clip = eps_clip
        
        # 网络初始化
        self.policy_net = PolicyNetwork(state_dim, action_dim, hidden_dim)
        self.value_net = ValueNetwork(state_dim, hidden_dim)
        
        # 优化器
        self.policy_optimizer = optim.Adam(self.policy_net.parameters(), lr=lr)
        self.value_optimizer = optim.Adam(self.value_net.parameters(), lr=lr)
        
        # 经验缓冲
        self.memory = []
        
    def predict(self, state: np.ndarray) -> np.ndarray:
        """预测动作"""
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            action = self.policy_net(state_tensor)
            return action.numpy().flatten()
    
    def act(self, state: np.ndarray) -> Tuple[np.ndarray, float]:
        """执行动作并返回动作和价值"""
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            action = self.policy_net(state_tensor)
            value = self.value_net(state_tensor)
            return action.numpy().flatten(), value.item()


class RLDecisionOptimizer:
    """
    @class RLDecisionOptimizer
    @brief RL决策优化器
    @details 强化学习决策优化的核心组件，支持多种RL算法
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 模型参数
        self.state_dim = self.config.get('state_dim', 50)
        self.action_dim = self.config.get('action_dim', 2)
        self.hidden_dim = self.config.get('hidden_dim', 256)
        self.algorithm = RLAlgorithm(self.config.get('algorithm', 'ppo'))
        
        # 初始化RL智能体
        self.agent = None
        self.is_trained = False
        self.model_path = self.config.get('model_path', 'models/rl_model.pth')
        
        # 决策相关
        self.decision_history = []
        self.performance_metrics = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'average_confidence': 0.0,
            'last_update': datetime.now()
        }
        
        # 风险控制
        self.max_position_change = self.config.get('max_position_change', 0.3)
        self.min_confidence_threshold = self.config.get('min_confidence_threshold', 0.4)
        
        self._initialize_agent()
        self.logger.info(f"RL决策优化器初始化完成 - 算法: {self.algorithm.value}")
    
    def optimize_decision(self, state_vector: np.ndarray, 
                         market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        @brief 基于强化学习优化决策
        @param state_vector: 状态向量(50维)
        @param market_context: 市场上下文信息
        @return: 优化决策结果
        """
        try:
            self.logger.info("开始RL决策优化...")
            
            # 状态向量预处理
            processed_state = self._preprocess_state(state_vector)
            
            # RL决策推理
            if self.agent and self.is_trained:
                raw_action, estimated_value = self.agent.act(processed_state)
                decision_result = self._process_raw_action(raw_action, estimated_value, processed_state)
            else:
                self.logger.warning("RL模型未训练，使用规则化决策")
                decision_result = self._rule_based_decision(processed_state, market_context)
            
            # 决策后处理和验证
            validated_decision = self._validate_decision(decision_result, market_context)
            
            # 生成决策解释
            explanation = self._generate_decision_reasoning(
                processed_state, decision_result, market_context
            )
            validated_decision['reasoning'] = explanation
            
            # 记录决策历史
            self._record_decision(processed_state, validated_decision)
            
            self.logger.info(f"RL决策完成: {validated_decision.get('action', '未知')}")
            return validated_decision
            
        except Exception as e:
            self.logger.error(f"RL决策优化失败: {str(e)}")
            return self._get_fallback_decision(state_vector, error=str(e))
    
    def _initialize_agent(self) -> None:
        """初始化RL智能体"""
        try:
            if self.algorithm == RLAlgorithm.PPO:
                self.agent = PPOAgent(
                    state_dim=self.state_dim,
                    action_dim=self.action_dim,
                    hidden_dim=self.hidden_dim,
                    lr=self.config.get('learning_rate', 3e-4),
                    gamma=self.config.get('gamma', 0.99),
                    eps_clip=self.config.get('eps_clip', 0.2)
                )
            else:
                self.logger.warning(f"算法 {self.algorithm.value} 暂未实现，使用PPO")
                self.agent = PPOAgent(self.state_dim, self.action_dim, self.hidden_dim)
            
            # 尝试加载预训练模型
            self._load_pretrained_model()
            
        except Exception as e:
            self.logger.error(f"初始化RL智能体失败: {str(e)}")
            self.agent = None
    
    def _preprocess_state(self, state_vector: np.ndarray) -> np.ndarray:
        """预处理状态向量"""
        try:
            # 确保状态向量维度正确
            if len(state_vector) != self.state_dim:
                self.logger.warning(f"状态向量维度不匹配: {len(state_vector)} vs {self.state_dim}")
                # 填充或截断到正确维度
                if len(state_vector) < self.state_dim:
                    padded_state = np.full(self.state_dim, 0.5)
                    padded_state[:len(state_vector)] = state_vector
                    state_vector = padded_state
                else:
                    state_vector = state_vector[:self.state_dim]
            
            # 数值稳定性处理
            state_vector = np.clip(state_vector, -5.0, 5.0)
            
            # 检查NaN和Inf
            if np.any(np.isnan(state_vector)) or np.any(np.isinf(state_vector)):
                self.logger.warning("状态向量包含NaN或Inf，使用默认值")
                state_vector = np.full(self.state_dim, 0.5)
            
            return state_vector.astype(np.float32)
            
        except Exception as e:
            self.logger.error(f"状态预处理失败: {str(e)}")
            return np.full(self.state_dim, 0.5, dtype=np.float32)
    
    def _process_raw_action(self, raw_action: np.ndarray, estimated_value: float, 
                           state: np.ndarray) -> Dict[str, Any]:
        """处理原始动作输出"""
        try:
            # 解析动作 [position_change, confidence_level]
            if len(raw_action) >= 2:
                position_change = float(raw_action[0])  # [-1, 1]
                confidence_raw = float(raw_action[1])   # [-1, 1]
            else:
                position_change = float(raw_action[0]) if len(raw_action) > 0 else 0.0
                confidence_raw = 0.5
            
            # 转换置信度到[0, 1]
            confidence = (confidence_raw + 1.0) / 2.0
            confidence = np.clip(confidence, 0.0, 1.0)
            
            # 限制仓位变化幅度
            position_change = np.clip(position_change, -self.max_position_change, self.max_position_change)
            
            # 确定具体动作
            if abs(position_change) < 0.05:  # 小幅度变化视为持仓
                action = 'hold'
                position_change = 0.0
            elif position_change > 0:
                action = 'buy'
            else:
                action = 'sell'
                position_change = abs(position_change)  # 转为正值表示卖出强度
            
            return {
                'action': action,
                'position_change': position_change,
                'confidence': confidence,
                'estimated_value': estimated_value,
                'raw_action': raw_action.tolist(),
                'decision_type': 'rl_optimized'
            }
            
        except Exception as e:
            self.logger.error(f"处理原始动作失败: {str(e)}")
            return {
                'action': 'hold',
                'position_change': 0.0,
                'confidence': 0.3,
                'estimated_value': 0.0,
                'error': str(e),
                'decision_type': 'rl_error'
            }
    
    def _rule_based_decision(self, state: np.ndarray, market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """基于规则的决策（兜底方案）"""
        try:
            # 提取关键特征进行简单决策
            # 假设前几维是价格相关特征
            price_momentum = state[1] if len(state) > 1 else 0.0  # 价格变化
            rsi_like = state[13] if len(state) > 13 else 0.5     # RSI类指标
            
            # 简单决策逻辑
            if price_momentum > 0.02 and rsi_like < 0.7:
                action = 'buy'
                position_change = min(0.2, price_momentum * 10)
                confidence = 0.6
            elif price_momentum < -0.02 and rsi_like > 0.3:
                action = 'sell'
                position_change = min(0.2, abs(price_momentum) * 10)
                confidence = 0.6
            else:
                action = 'hold'
                position_change = 0.0
                confidence = 0.4
            
            return {
                'action': action,
                'position_change': position_change,
                'confidence': confidence,
                'estimated_value': 0.0,
                'decision_type': 'rule_based',
                'rule_factors': {
                    'price_momentum': price_momentum,
                    'rsi_like': rsi_like
                }
            }
            
        except Exception as e:
            self.logger.error(f"规则化决策失败: {str(e)}")
            return {
                'action': 'hold',
                'position_change': 0.0,
                'confidence': 0.3,
                'estimated_value': 0.0,
                'error': str(e),
                'decision_type': 'rule_error'
            }
    
    def _validate_decision(self, decision: Dict[str, Any], market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """验证决策合理性"""
        validated = decision.copy()
        
        # 置信度下限检查
        if validated.get('confidence', 0) < self.min_confidence_threshold:
            validated['action'] = 'hold'
            validated['position_change'] = 0.0
            validated['confidence_override'] = True
            validated['original_confidence'] = decision.get('confidence', 0)
        
        # 极端市场条件检查
        if market_context:
            market_volatility = self._extract_market_volatility(market_context)
            if market_volatility > 0.8:  # 极高波动
                validated['position_change'] *= 0.5  # 减半仓位变化
                validated['volatility_adjustment'] = True
        
        # 最终安全检查
        validated['position_change'] = max(0.0, min(1.0, validated.get('position_change', 0)))
        validated['confidence'] = max(0.0, min(1.0, validated.get('confidence', 0)))
        
        return validated
    
    def _generate_decision_reasoning(self, state: np.ndarray, decision: Dict[str, Any], 
                                   market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成决策推理说明"""
        try:
            # 分析关键特征
            key_factors = self._analyze_key_factors(state)
            
            # 技术信号分析
            technical_signals = self._analyze_technical_indicators(state)
            
            # 风险评估
            risk_assessment = self._assess_risk_level(state, decision)
            
            reasoning = {
                'decision_summary': f"基于RL模型分析，建议{decision.get('action', '持仓')}",
                'key_factors': key_factors,
                'technical_signals': technical_signals,
                'risk_assessment': risk_assessment,
                'confidence_explanation': self._explain_confidence(decision.get('confidence', 0)),
                'model_type': decision.get('decision_type', 'unknown')
            }
            
            return reasoning
            
        except Exception as e:
            self.logger.error(f"生成决策推理失败: {str(e)}")
            return {
                'decision_summary': '决策推理生成失败',
                'error': str(e)
            }
    
    def _analyze_key_factors(self, state: np.ndarray) -> List[str]:
        """分析关键影响因素"""
        factors = []
        
        try:
            # 价格趋势
            if len(state) > 1:
                price_change = state[1]
                if abs(price_change) > 0.02:
                    trend = "上涨" if price_change > 0 else "下跌"
                    factors.append(f"价格呈现{trend}趋势 ({price_change:.3f})")
            
            # 技术指标
            if len(state) > 13:
                rsi_like = state[13]
                if rsi_like > 0.7:
                    factors.append("技术指标显示超买状态")
                elif rsi_like < 0.3:
                    factors.append("技术指标显示超卖状态")
            
            # LLM情绪因子
            if len(state) > 28:
                sentiment_score = state[28]
                if sentiment_score > 0.6:
                    factors.append("市场情绪积极")
                elif sentiment_score < 0.4:
                    factors.append("市场情绪谨慎")
            
            return factors if factors else ["基于综合技术分析"]
            
        except Exception as e:
            return [f"因素分析异常: {str(e)}"]
    
    def _analyze_technical_indicators(self, state: np.ndarray) -> Dict[str, str]:
        """分析技术指标"""
        indicators = {}
        
        try:
            if len(state) > 13:
                # RSI类指标
                rsi_like = state[13]
                if rsi_like > 0.7:
                    indicators['momentum'] = "超买"
                elif rsi_like < 0.3:
                    indicators['momentum'] = "超卖"
                else:
                    indicators['momentum'] = "中性"
            
            if len(state) > 14:
                # MACD类指标
                macd_like = state[14]
                indicators['trend'] = "看涨" if macd_like > 0 else "看跌"
            
            return indicators
            
        except Exception as e:
            return {'error': str(e)}
    
    def _assess_risk_level(self, state: np.ndarray, decision: Dict[str, Any]) -> Dict[str, Any]:
        """评估风险水平"""
        try:
            position_change = decision.get('position_change', 0)
            confidence = decision.get('confidence', 0)
            
            # 基础风险评估
            position_risk = position_change * 2  # 仓位变化风险
            confidence_risk = 1.0 - confidence  # 置信度风险
            
            # 市场风险（从状态向量推断）
            market_risk = 0.5
            if len(state) > 30:
                volatility_proxy = abs(state[30] - 0.5) * 2
                market_risk = min(volatility_proxy, 1.0)
            
            overall_risk = (position_risk + confidence_risk + market_risk) / 3
            
            return {
                'overall_risk': overall_risk,
                'position_risk': position_risk,
                'confidence_risk': confidence_risk,
                'market_risk': market_risk,
                'risk_level': self._categorize_risk(overall_risk)
            }
            
        except Exception as e:
            return {'error': str(e), 'overall_risk': 0.5}
    
    def _categorize_risk(self, risk_score: float) -> str:
        """风险等级分类"""
        if risk_score < 0.3:
            return "低"
        elif risk_score < 0.6:
            return "中"
        else:
            return "高"
    
    def _explain_confidence(self, confidence: float) -> str:
        """解释置信度"""
        if confidence > 0.8:
            return "模型对此决策非常确信"
        elif confidence > 0.6:
            return "模型对此决策较为确信"
        elif confidence > 0.4:
            return "模型对此决策存在一定不确定性"
        else:
            return "模型对此决策不够确信，建议谨慎"
    
    def _extract_market_volatility(self, market_context: Dict[str, Any]) -> float:
        """从市场上下文提取波动性"""
        try:
            evaluations = market_context.get('evaluations', {})
            volatility_eval = evaluations.get('波动性')
            
            if volatility_eval and hasattr(volatility_eval, 'score'):
                return volatility_eval.score
            return 0.5
        except:
            return 0.5
    
    def _load_pretrained_model(self) -> None:
        """加载预训练模型"""
        try:
            if os.path.exists(self.model_path) and self.agent:
                checkpoint = torch.load(self.model_path, map_location='cpu')
                
                if hasattr(self.agent, 'policy_net'):
                    self.agent.policy_net.load_state_dict(checkpoint.get('policy_net', {}))
                if hasattr(self.agent, 'value_net'):
                    self.agent.value_net.load_state_dict(checkpoint.get('value_net', {}))
                
                self.is_trained = True
                self.logger.info(f"已加载预训练模型: {self.model_path}")
            else:
                self.logger.info("未找到预训练模型，使用随机初始化")
                
        except Exception as e:
            self.logger.warning(f"加载预训练模型失败: {str(e)}")
    
    def _record_decision(self, state: np.ndarray, decision: Dict[str, Any]) -> None:
        """记录决策历史"""
        decision_record = {
            'timestamp': datetime.now().isoformat(),
            'state_summary': {
                'dimension': len(state),
                'key_features': state[:5].tolist() if len(state) >= 5 else state.tolist()
            },
            'decision': decision.copy()
        }
        
        self.decision_history.append(decision_record)
        
        # 更新性能指标
        self.performance_metrics['total_decisions'] += 1
        confidence = decision.get('confidence', 0)
        self.performance_metrics['average_confidence'] = (
            (self.performance_metrics['average_confidence'] * (self.performance_metrics['total_decisions'] - 1) + confidence) /
            self.performance_metrics['total_decisions']
        )
        
        # 保持历史记录限制
        if len(self.decision_history) > 100:
            self.decision_history = self.decision_history[-100:]
    
    def _get_fallback_decision(self, state_vector: np.ndarray, error: str = None) -> Dict[str, Any]:
        """获取兜底决策"""
        return {
            'action': 'hold',
            'position_change': 0.0,
            'confidence': 0.2,
            'estimated_value': 0.0,
            'decision_type': 'fallback',
            'error': error,
            'reasoning': {
                'decision_summary': '系统异常，采用保守策略',
                'error': error
            }
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'state_dim': 50,
            'action_dim': 2,
            'hidden_dim': 256,
            'algorithm': 'ppo',
            'learning_rate': 3e-4,
            'gamma': 0.99,
            'eps_clip': 0.2,
            'max_position_change': 0.3,
            'min_confidence_threshold': 0.4,
            'model_path': 'models/rl_model.pth'
        }
    
    def save_model(self, path: str = None) -> bool:
        """保存模型"""
        try:
            save_path = path or self.model_path
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            if self.agent and hasattr(self.agent, 'policy_net'):
                checkpoint = {
                    'policy_net': self.agent.policy_net.state_dict(),
                    'value_net': self.agent.value_net.state_dict() if hasattr(self.agent, 'value_net') else {},
                    'config': self.config,
                    'performance_metrics': self.performance_metrics
                }
                torch.save(checkpoint, save_path)
                self.logger.info(f"模型已保存到: {save_path}")
                return True
            else:
                self.logger.warning("没有可保存的模型")
                return False
                
        except Exception as e:
            self.logger.error(f"保存模型失败: {str(e)}")
            return False
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_metrics.copy()
    
    def get_decision_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取决策历史"""
        return self.decision_history[-limit:] 