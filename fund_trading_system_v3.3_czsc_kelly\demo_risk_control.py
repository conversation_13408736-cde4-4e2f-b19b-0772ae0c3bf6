"""
风控系统演示脚本
展示风控agent和资金风控模块的功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.enhanced.risk_control_agent import RiskControlAgent
from core.position_tracker import PositionTracker
from core.pnl_calculator import PnLCalculator
from core.market_environment_assessor import MarketEnvironmentAssessor
from core.decision_explainer import DecisionExplainer
from core.data_structures import DimensionEvaluationResult, PositionInfo


def demo_technical_risk_control():
    """演示技术指标风控"""
    print("=" * 60)
    print("🛡️  技术指标风控演示")
    print("=" * 60)
    
    risk_agent = RiskControlAgent()
    
    # 场景1：符合买入条件的情况
    print("\n📈 场景1：符合买入条件")
    good_data = {
        'fund_code': '513500',
        'analysis_result': {
            'technical_analysis': {
                'indicators': {
                    'bb_position': 0.1,  # 低于下轨
                    'rsi': 40,           # 适中RSI
                    'volume_ratio': 1.5,  # 充足成交量
                    'close': 100,
                    'bb_upper': 105,
                    'bb_middle': 100,
                    'bb_lower': 95
                }
            },
            'dimension_evaluations': {
                '波动性': DimensionEvaluationResult('波动性', 'low', 0.3, 0.8, [], 'good'),
                '流动性': DimensionEvaluationResult('流动性', 'good', 0.8, 0.9, [], 'good'),
                '情绪': DimensionEvaluationResult('情绪', 'positive', 0.7, 0.8, [], 'good')
            }
        },
        'proposed_decision': 'buy'
    }
    
    result = risk_agent.process(good_data)
    print(f"✅ 基金: {result['fund_code']}")
    print(f"✅ 原始决策: {result['original_decision']}")
    print(f"✅ 最终决策: {result['final_decision']}")
    print(f"✅ 风险等级: {result['risk_level']}")
    
    # 场景2：不符合买入条件的情况
    print("\n📉 场景2：不符合买入条件")
    bad_data = {
        'fund_code': '513080',
        'analysis_result': {
            'technical_analysis': {
                'indicators': {
                    'bb_position': 0.9,  # 高于上轨
                    'rsi': 75,           # RSI过高
                    'volume_ratio': 0.8,  # 成交量不足
                    'close': 100,
                    'bb_upper': 105,
                    'bb_middle': 100,
                    'bb_lower': 95
                }
            }
        },
        'proposed_decision': 'buy'
    }
    
    result = risk_agent.process(bad_data)
    print(f"❌ 基金: {result['fund_code']}")
    print(f"❌ 原始决策: {result['original_decision']}")
    print(f"❌ 最终决策: {result['final_decision']}")
    print(f"❌ 风险等级: {result['risk_level']}")
    
    # 显示违规详情
    violations = result.get('risk_validation', {}).get('technical_violations', {})
    if violations:
        print("❌ 技术指标违规:")
        for indicator, violation in violations.items():
            print(f"   - {indicator}: {violation}")


def demo_position_tracking():
    """演示持仓跟踪和回撤监控"""
    print("\n" + "=" * 60)
    print("📊 持仓跟踪和回撤监控演示")
    print("=" * 60)
    
    tracker = PositionTracker()
    calculator = PnLCalculator()
    
    # 创建测试持仓
    print("\n📈 创建测试持仓")
    tracker.update_position('513500', 1000, 1.0, 'buy')  # 买入1000份，成本1.0
    tracker.update_position('513080', 800, 1.2, 'buy')   # 买入800份，成本1.2
    
    # 模拟价格上涨
    print("\n📈 模拟价格上涨阶段")
    tracker.update_position('513500', 1000, 1.3, 'update')  # 价格涨到1.3，盈利30%
    tracker.update_position('513080', 800, 1.5, 'update')   # 价格涨到1.5，盈利25%
    
    # 显示盈利状况
    positions = tracker.get_all_positions()
    for fund_code, position in positions.items():
        pnl_data = calculator.calculate_position_pnl(position)
        print(f"✅ {fund_code}: 盈利 {pnl_data['unrealized_pnl_pct']:.1f}%")
    
    # 模拟价格回撤
    print("\n📉 模拟价格回撤阶段")
    tracker.update_position('513500', 1000, 1.1, 'update')  # 价格回撤到1.1
    tracker.update_position('513080', 800, 1.25, 'update')  # 价格回撤到1.25
    
    # 检查回撤触发
    print("\n🚨 检查回撤触发条件")
    for fund_code in positions.keys():
        drawdown_result = tracker.check_drawdown_trigger(fund_code)
        print(f"📊 {fund_code}:")
        print(f"   当前盈利: {drawdown_result.current_profit_pct:.1%}")
        print(f"   最高盈利: {drawdown_result.max_profit_pct:.1%}")
        print(f"   回撤幅度: {drawdown_result.drawdown_from_peak:.1%}")
        print(f"   是否卖出: {'是' if drawdown_result.should_sell else '否'}")
        if drawdown_result.should_sell:
            print(f"   卖出比例: {drawdown_result.recommended_sell_ratio:.0%}")
            print(f"   紧急程度: {drawdown_result.urgency_level}")
            print(f"   卖出原因: {drawdown_result.sell_reason}")


def demo_market_environment_assessment():
    """演示市场环境评估"""
    print("\n" + "=" * 60)
    print("🌍 市场环境评估演示")
    print("=" * 60)
    
    assessor = MarketEnvironmentAssessor()
    
    # 场景1：良好市场环境
    print("\n📈 场景1：良好市场环境")
    good_dimensions = {
        '趋势': DimensionEvaluationResult('趋势', 'up', 0.8, 0.9, [], 'good'),
        '波动性': DimensionEvaluationResult('波动性', 'low', 0.3, 0.8, [], 'good'),
        '流动性': DimensionEvaluationResult('流动性', 'good', 0.9, 0.9, [], 'good'),
        '情绪': DimensionEvaluationResult('情绪', 'positive', 0.8, 0.8, [], 'good'),
        '结构': DimensionEvaluationResult('结构', 'stable', 0.8, 0.9, [], 'good'),
        '转换': DimensionEvaluationResult('转换', 'low', 0.2, 0.7, [], 'good')
    }
    
    assessment = assessor.assess_market_environment(good_dimensions)
    print(f"✅ 市场状态: {assessment.market_regime}")
    print(f"✅ 波动性水平: {assessment.volatility_level}")
    print(f"✅ 流动性条件: {assessment.liquidity_condition}")
    print(f"✅ 整体风险: {assessment.overall_risk_level}")
    print(f"✅ 策略建议: {assessment.recommended_strategy}")
    
    # 场景2：恶劣市场环境
    print("\n📉 场景2：恶劣市场环境")
    bad_dimensions = {
        '趋势': DimensionEvaluationResult('趋势', 'down', 0.2, 0.4, [], 'poor'),
        '波动性': DimensionEvaluationResult('波动性', 'high', 0.9, 0.3, [], 'poor'),
        '流动性': DimensionEvaluationResult('流动性', 'poor', 0.2, 0.4, [], 'poor'),
        '情绪': DimensionEvaluationResult('情绪', 'negative', 0.1, 0.3, [], 'poor'),
        '结构': DimensionEvaluationResult('结构', 'unstable', 0.3, 0.4, [], 'poor'),
        '转换': DimensionEvaluationResult('转换', 'high', 0.8, 0.4, [], 'poor')
    }
    
    assessment = assessor.assess_market_environment(bad_dimensions)
    print(f"❌ 市场状态: {assessment.market_regime}")
    print(f"❌ 波动性水平: {assessment.volatility_level}")
    print(f"❌ 流动性条件: {assessment.liquidity_condition}")
    print(f"❌ 整体风险: {assessment.overall_risk_level}")
    print(f"❌ 策略建议: {assessment.recommended_strategy}")
    
    # 检查是否应该暂停交易
    should_halt = assessor.should_halt_trading(assessment)
    print(f"🚨 是否暂停交易: {'是' if should_halt else '否'}")


def demo_decision_explanation():
    """演示决策解释功能"""
    print("\n" + "=" * 60)
    print("💡 决策解释功能演示")
    print("=" * 60)
    
    explainer = DecisionExplainer()
    risk_agent = RiskControlAgent()
    
    # 创建一个复杂的风控场景
    complex_data = {
        'fund_code': '513030',
        'analysis_result': {
            'technical_analysis': {
                'indicators': {
                    'bb_position': 0.7,  # 偏高位置
                    'rsi': 68,           # 接近超买
                    'volume_ratio': 1.1,  # 成交量略低
                    'close': 100,
                    'bb_upper': 105,
                    'bb_middle': 100,
                    'bb_lower': 95,
                    'macd_dif': 0.2,
                    'macd_dea': 0.1,
                    'macd_bullish': True
                }
            }
        },
        'proposed_decision': 'buy'
    }
    
    # 执行风控验证
    risk_result = risk_agent.process(complex_data)
    
    # 生成详细解释（简化处理）
    print(f"📊 基金: {complex_data['fund_code']}")
    print(f"📊 原始决策: {risk_result['original_decision']}")
    print(f"📊 最终决策: {risk_result['final_decision']}")
    print(f"📊 风险等级: {risk_result['risk_level']}")

    # 显示技术指标违规
    violations = risk_result.get('risk_validation', {}).get('technical_violations', {})
    if violations:
        print("\n🔍 技术指标违规详情:")
        for indicator, violation in violations.items():
            print(f"   ❌ {indicator}: {violation}")

    return  # 简化演示，跳过复杂的解释生成
    
    print(f"📊 基金: {explanation.get('fund_code', 'N/A')}")
    print(f"📊 决策摘要: {explanation.get('overall_summary', 'N/A')}")
    
    # 显示详细分析
    detailed = explanation.get('detailed_analysis', {})
    if 'technical_analysis' in detailed:
        print("\n🔍 技术分析详情:")
        tech_analysis = detailed['technical_analysis']
        for indicator, details in tech_analysis.items():
            if isinstance(details, dict) and 'violation' in details:
                print(f"   ❌ {indicator}: {details['violation']}")
                print(f"      建议: {details.get('suggestion', 'N/A')}")
    
    # 显示建议
    recommendations = explanation.get('recommendations', [])
    if recommendations:
        print("\n💡 操作建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")


def main():
    """主演示函数"""
    print("🚀 基金交易系统风控模块演示")
    print("=" * 60)
    print("本演示将展示以下功能:")
    print("1. 技术指标风控验证")
    print("2. 持仓跟踪和回撤监控")
    print("3. 市场环境评估")
    print("4. 决策解释功能")
    print("=" * 60)
    
    try:
        # 执行各个演示
        demo_technical_risk_control()
        demo_position_tracking()
        demo_market_environment_assessment()
        demo_decision_explanation()
        
        print("\n" + "=" * 60)
        print("✅ 风控系统演示完成！")
        print("=" * 60)
        print("🎯 主要特性:")
        print("   ✅ 预防性风控 - 买入前严格验证")
        print("   ✅ 动态回撤监控 - 保护盈利成果")
        print("   ✅ 市场环境适应 - 根据环境调整策略")
        print("   ✅ 透明决策解释 - 提供详细说明")
        print("   ✅ 多维度风险控制 - 技术+市场+组合")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
