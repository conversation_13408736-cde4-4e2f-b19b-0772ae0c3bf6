"""
简化的数据管理器测试
"""

import sys
import os

# 添加路径
sys.path.append('fund_trading_system_v3')
sys.path.append('tushare_data_provider')

def test_imports():
    """测试导入"""
    print("=== 测试导入 ===")
    
    try:
        # 测试tushare_data_provider导入
        from tushare_data_provider import TushareDataProvider, DataRequest, FIVE_YEAR_CONFIG, REALTIME_CONFIG
        print("✅ Tushare数据提供器导入成功")
        
        # 测试数据管理器导入
        from fund_trading_system_v3.core.data_manager import DataManager
        print("✅ 数据管理器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        from fund_trading_system_v3.core.data_manager import DataManager
        from datetime import datetime, timedelta
        
        # 初始化数据管理器
        data_manager = DataManager(config_type='default')
        print("✅ 数据管理器初始化成功")
        
        # 测试股票代码
        test_symbols = ['000001.SZ']
        
        # 获取最近3天的市场数据
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=3)).strftime("%Y%m%d")
        
        print(f"📊 获取市场数据: {test_symbols[0]} ({start_date} - {end_date})")
        
        market_data = data_manager.get_market_data(
            symbols=test_symbols,
            start_date=start_date,
            end_date=end_date
        )
        
        if market_data and test_symbols[0] in market_data:
            bars = market_data[test_symbols[0]]
            print(f"✅ 成功获取 {len(bars)} 条K线数据")
            
            if bars:
                latest_bar = bars[-1]
                print(f"最新K线数据:")
                print(f"  股票代码: {latest_bar.symbol}")
                print(f"  交易时间: {latest_bar.dt}")
                print(f"  收盘价: {latest_bar.close}")
                print(f"  成交量: {latest_bar.vol}")
        else:
            print("❌ 未获取到市场数据")
        
        # 测试缓存信息
        cache_info = data_manager.get_cache_info()
        print(f"\n💾 缓存信息:")
        print(f"  管理器缓存: {cache_info['manager_cache_size']}")
        print(f"  提供器缓存: {cache_info['provider_cache_size']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comprehensive_data():
    """测试综合数据获取"""
    print("\n=== 测试综合数据获取 ===")
    
    try:
        from fund_trading_system_v3.core.data_manager import DataManager
        from datetime import datetime, timedelta
        
        data_manager = DataManager()
        test_symbols = ['000001.SZ']
        
        # 获取最近3天的综合数据
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=3)).strftime("%Y%m%d")
        
        print(f"📈 获取综合分析数据: {test_symbols[0]}")
        
        comprehensive_data = data_manager.get_comprehensive_analysis_data(
            symbols=test_symbols,
            start_date=start_date,
            end_date=end_date
        )
        
        if comprehensive_data and test_symbols[0] in comprehensive_data:
            data_list = comprehensive_data[test_symbols[0]]
            print(f"✅ 成功获取 {len(data_list)} 条综合数据")
            
            if data_list:
                latest_data = data_list[-1]
                print(f"最新综合数据:")
                print(f"  股票代码: {latest_data.ts_code}")
                print(f"  交易日期: {latest_data.trade_date}")
                print(f"  收盘价: {latest_data.market_data.close}")
                
                # 测试特征向量
                feature_vector = latest_data.to_feature_vector()
                print(f"  特征向量维度: {len(feature_vector)}")
                print(f"  非零特征数: {(feature_vector != 0).sum()}")
                
                # 测试多维度分析转换
                multi_state = data_manager.convert_to_multidimensional_state(latest_data)
                print(f"  多维度分析:")
                print(f"    综合得分: {multi_state.composite_score:.4f}")
                print(f"    推荐行动: {multi_state.recommended_action}")
                print(f"    风险等级: {multi_state.risk_level}")
        else:
            print("❌ 未获取到综合数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始简化数据管理器测试")
    print("=" * 50)
    
    # 运行测试
    success_count = 0
    total_tests = 3
    
    if test_imports():
        success_count += 1
    
    if test_basic_functionality():
        success_count += 1
        
    if test_comprehensive_data():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎉 测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！数据管理器集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")