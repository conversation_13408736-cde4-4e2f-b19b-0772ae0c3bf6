"""
调试主系统中的凯利分析结果
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from system.enhanced_trading_system import EnhancedFundTradingSystemV3


def debug_main_kelly():
    """调试主系统中的凯利分析"""
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        print(f"\n🔍 调试主系统凯利分析")
        print("-" * 60)
        
        # 初始化交易系统
        trading_system = EnhancedFundTradingSystemV3(title='调试模式')
        
        # 手动分析一个基金
        fund_code = '518880'
        print(f"\n📊 分析基金: {fund_code}")
        
        # 获取分析结果
        analysis_result = trading_system.coordinator.coordinate_analysis(fund_code)
        
        # 调试凯利分析结果
        print(f"\n🔍 调试凯利分析结果:")
        kelly_analysis = analysis_result.get('kelly_analysis', {})
        print(f"kelly_analysis存在: {bool(kelly_analysis)}")
        
        if kelly_analysis:
            print(f"kelly_analysis内容: {kelly_analysis.keys()}")
            
            kelly_calc = kelly_analysis.get('kelly_calculation', {})
            print(f"kelly_calculation存在: {bool(kelly_calc)}")
            
            if kelly_calc:
                print(f"kelly_calculation内容:")
                for key, value in kelly_calc.items():
                    print(f"  {key}: {value}")
                
                # 检查关键字段
                optimal_position = kelly_calc.get('optimal_position', 0)
                risk_level = kelly_calc.get('risk_level', '未知')
                win_probability = kelly_calc.get('win_probability', 0)
                risk_reward_ratio = kelly_calc.get('risk_reward_ratio', 0)
                
                print(f"\n📊 关键字段:")
                print(f"  optimal_position: {optimal_position}")
                print(f"  risk_level: {risk_level}")
                print(f"  win_probability: {win_probability}")
                print(f"  risk_reward_ratio: {risk_reward_ratio}")
        
        # 调用系统的显示方法
        print(f"\n🖥️ 系统显示结果:")
        trading_system._display_analysis_result(analysis_result)
        
    except Exception as e:
        print(f"\n❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    debug_main_kelly()