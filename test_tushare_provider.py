"""
测试Tushare数据提供器
"""

import sys
import os
sys.path.append('tushare_data_provider')

from tushare_data_provider import TushareDataProvider, DataRequest
from datetime import datetime, timedelta


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试Tushare数据提供器基本功能 ===")
    
    try:
        # 初始化数据提供器
        provider = TushareDataProvider()
        print("✅ 数据提供器初始化成功")
        
        # 测试股票代码
        test_codes = ['000001.SZ']  # 平安银行
        
        # 测试获取最近几天的数据
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=5)).strftime("%Y%m%d")
        
        print(f"\n📊 测试获取数据: {test_codes[0]} ({start_date} - {end_date})")
        
        request = DataRequest(
            ts_codes=test_codes,
            start_date=start_date,
            end_date=end_date,
            include_basic=True,
            include_technical=True,
            include_macro=False,  # 暂时关闭宏观数据以加快测试
            include_news=False,   # 暂时关闭新闻数据
            include_fund_flow=False  # 暂时关闭资金流向
        )
        
        response = provider.get_comprehensive_data(request)
        
        if response.success:
            print(f"✅ 成功获取 {response.data_count} 条数据")
            
            if response.data:
                # 显示第一条数据的详细信息
                first_data = response.data[0]
                print(f"\n📈 数据详情:")
                print(f"  股票代码: {first_data.ts_code}")
                print(f"  交易日期: {first_data.trade_date}")
                print(f"  开盘价: {first_data.market_data.open}")
                print(f"  收盘价: {first_data.market_data.close}")
                print(f"  涨跌幅: {first_data.market_data.pct_chg}%")
                print(f"  成交量: {first_data.market_data.vol}")
                print(f"  成交额: {first_data.market_data.amount}")
                
                # 显示技术指标
                tech = first_data.technical_indicators
                print(f"\n📊 技术指标:")
                print(f"  MA5: {tech.ma5}")
                print(f"  MA20: {tech.ma20}")
                print(f"  RSI: {tech.rsi}")
                print(f"  MACD DIF: {tech.macd_dif}")
                print(f"  KDJ K: {tech.kdj_k}")
                print(f"  布林带上轨: {tech.boll_upper}")
                
                # 测试特征向量转换
                feature_vector = first_data.to_feature_vector()
                print(f"\n🔢 特征向量:")
                print(f"  维度: {len(feature_vector)}")
                print(f"  前10个特征: {feature_vector[:10]}")
                print(f"  非零特征数量: {(feature_vector != 0).sum()}")
                print(f"  缺失值数量: {sum(1 for x in feature_vector if str(x) == 'nan')}")
                
                # 转换为DataFrame测试
                df = response.to_dataframe()
                if df is not None:
                    print(f"\n📋 DataFrame信息:")
                    print(f"  形状: {df.shape}")
                    print(f"  列数: {len(df.columns)}")
                else:
                    print("❌ DataFrame转换失败")
                
            else:
                print("❌ 响应中没有数据")
        else:
            print(f"❌ 获取数据失败: {response.error_message}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def test_technical_indicators():
    """测试技术指标计算"""
    print("\n=== 测试技术指标计算 ===")
    
    try:
        from tushare_data_provider.technical_calculator import TechnicalCalculator
        import pandas as pd
        import numpy as np
        
        # 创建模拟数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 生成模拟价格数据
        base_price = 10.0
        price_changes = np.random.normal(0, 0.02, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))  # 确保价格为正
        
        # 创建DataFrame
        df = pd.DataFrame({
            'trade_date': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'vol': np.random.randint(1000, 10000, 100),
            'amount': np.random.randint(10000, 100000, 100)
        })
        
        print(f"📊 模拟数据创建完成: {len(df)} 条记录")
        
        # 计算技术指标
        df_with_indicators = TechnicalCalculator.calculate_all_indicators(df)
        
        print(f"✅ 技术指标计算完成")
        print(f"  原始列数: {len(df.columns)}")
        print(f"  计算后列数: {len(df_with_indicators.columns)}")
        print(f"  新增指标数: {len(df_with_indicators.columns) - len(df.columns)}")
        
        # 检查关键指标
        key_indicators = ['ma5', 'ma20', 'rsi', 'macd_dif', 'kdj_k', 'boll_upper', 'atr']
        print(f"\n📈 关键指标检查:")
        
        for indicator in key_indicators:
            if indicator in df_with_indicators.columns:
                values = df_with_indicators[indicator].dropna()
                if len(values) > 0:
                    print(f"  {indicator}: ✅ (有效值: {len(values)}, 范围: {values.min():.4f} - {values.max():.4f})")
                else:
                    print(f"  {indicator}: ❌ (无有效值)")
            else:
                print(f"  {indicator}: ❌ (列不存在)")
                
    except Exception as e:
        print(f"❌ 技术指标测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_data_structures():
    """测试数据结构"""
    print("\n=== 测试数据结构 ===")
    
    try:
        from tushare_data_provider.data_structures import (
            MarketData, TechnicalIndicators, MacroData, FundFlowData,
            ComprehensiveData, DataRequest, DataResponse
        )
        
        # 测试MarketData
        market_data = MarketData(
            ts_code='000001.SZ',
            trade_date='20240125',
            open=10.0,
            high=10.5,
            low=9.8,
            close=10.2,
            pre_close=10.0,
            change=0.2,
            pct_chg=2.0,
            vol=1000000,
            amount=10200000
        )
        print(f"✅ MarketData创建成功: {market_data.ts_code}")
        
        # 测试TechnicalIndicators
        tech_indicators = TechnicalIndicators(
            ts_code='000001.SZ',
            trade_date='20240125',
            ma5=10.1,
            ma20=9.9,
            rsi=65.5,
            macd_dif=0.05
        )
        print(f"✅ TechnicalIndicators创建成功")
        
        # 测试MacroData
        macro_data = MacroData(
            trade_date='20240125',
            shibor_on=2.5,
            shibor_1y=3.2,
            cpi=102.1
        )
        print(f"✅ MacroData创建成功")
        
        # 测试FundFlowData
        fund_flow = FundFlowData(
            ts_code='000001.SZ',
            trade_date='20240125',
            main_net_inflow=1000000,
            main_net_inflow_rate=5.2
        )
        print(f"✅ FundFlowData创建成功")
        
        # 测试ComprehensiveData
        comp_data = ComprehensiveData(
            ts_code='000001.SZ',
            trade_date='20240125',
            market_data=market_data,
            technical_indicators=tech_indicators,
            macro_data=macro_data,
            fund_flow=fund_flow,
            news_sentiment=0.3,
            news_count=5
        )
        print(f"✅ ComprehensiveData创建成功")
        
        # 测试特征向量转换
        feature_vector = comp_data.to_feature_vector()
        print(f"✅ 特征向量转换成功: 维度 {len(feature_vector)}")
        
        # 测试字典转换
        data_dict = comp_data.to_dict()
        print(f"✅ 字典转换成功: {len(data_dict)} 个字段")
        
        # 测试DataRequest
        request = DataRequest(
            ts_codes=['000001.SZ', '600000.SH'],
            start_date='20240101',
            end_date='20240125'
        )
        print(f"✅ DataRequest创建成功: {len(request.ts_codes)} 只股票")
        
        # 测试DataResponse
        response = DataResponse(
            success=True,
            data=[comp_data],
            data_count=1,
            request_time=datetime.now()
        )
        print(f"✅ DataResponse创建成功: {response.data_count} 条数据")
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 开始测试Tushare数据提供器")
    print("=" * 60)
    
    # 运行所有测试
    test_data_structures()
    test_technical_indicators()
    test_basic_functionality()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")