import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
import os
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入所有模块
from .data_preprocessor import DataPreprocessor
from .feature_engineer import FeatureEngineer
from .timeseries_processor import TimeSeriesProcessor
from .network_architecture import NetworkArchitecture
from .model_trainer import ModelTrainer
from .backtest_engine import BacktestEngine
from .risk_manager import RiskManager


class AdvancedBacktestSystem:
    """
    高级回测系统主控制器
    协调所有模块完成完整的回测流程
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化高级回测系统
        
        Args:
            config: 系统配置字典
        """
        # 默认配置
        self.default_config = {
            'data_preprocessing': {
                'missing_strategy': 'interpolate',
                'outlier_threshold': 3.0
            },
            'feature_engineering': {
                'correlation_threshold': 0.8,
                'variance_threshold': 0.01,
                'selection_method': 'random_forest',
                'n_features': 20
            },
            'timeseries_processing': {
                'sequence_length': 30,
                'test_size': 0.2,
                'validation_size': 0.1,
                'scaler_type': 'standard'
            },
            'network_architecture': {
                'model_type': 'lstm',
                'lstm_units': [64, 32],
                'dropout_rate': 0.2,
                'bidirectional': False
            },
            'model_training': {
                'epochs': 100,
                'batch_size': 32,
                'early_stopping_patience': 10,
                'learning_rate': 0.001
            },
            'backtest_engine': {
                'initial_capital': 100000,
                'transaction_cost': 0.001,
                'slippage': 0.0005,
                'signal_method': 'threshold',
                'signal_threshold': 0.6
            },
            'risk_management': {
                'max_position_size': 0.1,
                'stop_loss_pct': 0.05,
                'take_profit_pct': 0.15,
                'max_drawdown_limit': 0.2,
                'max_daily_loss': 0.02
            }
        }
        
        # 合并用户配置
        self.config = self.default_config.copy()
        if config:
            self._update_config(self.config, config)
        
        # 初始化所有模块
        self._initialize_modules()
        
        # 系统状态
        self.system_log = []
        self.pipeline_results = {}
        self.models = {}
        self.is_trained = False
        
    def _update_config(self, base_config: Dict, user_config: Dict):
        """递归更新配置"""
        for key, value in user_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def _initialize_modules(self):
        """初始化所有模块"""
        # 数据预处理模块
        self.data_preprocessor = DataPreprocessor(
            missing_strategy=self.config['data_preprocessing']['missing_strategy'],
            outlier_threshold=self.config['data_preprocessing']['outlier_threshold']
        )
        
        # 特征工程模块
        self.feature_engineer = FeatureEngineer(
            correlation_threshold=self.config['feature_engineering']['correlation_threshold'],
            variance_threshold=self.config['feature_engineering']['variance_threshold']
        )
        
        # 时间序列处理模块
        self.timeseries_processor = TimeSeriesProcessor(
            sequence_length=self.config['timeseries_processing']['sequence_length'],
            test_size=self.config['timeseries_processing']['test_size'],
            validation_size=self.config['timeseries_processing']['validation_size'],
            scaler_type=self.config['timeseries_processing']['scaler_type']
        )
        
        # 回测引擎
        self.backtest_engine = BacktestEngine(
            initial_capital=self.config['backtest_engine']['initial_capital'],
            transaction_cost=self.config['backtest_engine']['transaction_cost'],
            slippage=self.config['backtest_engine']['slippage']
        )
        
        # 风险管理模块
        self.risk_manager = RiskManager(
            max_position_size=self.config['risk_management']['max_position_size'],
            stop_loss_pct=self.config['risk_management']['stop_loss_pct'],
            take_profit_pct=self.config['risk_management']['take_profit_pct'],
            max_drawdown_limit=self.config['risk_management']['max_drawdown_limit'],
            max_daily_loss=self.config['risk_management']['max_daily_loss']
        )
        
        self.system_log.append(f"[{datetime.now()}] All modules initialized successfully")
    
    def load_data(self, data_source: Union[pd.DataFrame, str], target_col: str = 'target',
                  time_col: str = 'dt', price_col: str = 'close') -> pd.DataFrame:
        """
        加载和验证数据
        
        Args:
            data_source: 数据源（DataFrame或文件路径）
            target_col: 目标列名
            time_col: 时间列名
            price_col: 价格列名
            
        Returns:
            加载的数据DataFrame
        """
        self.system_log.append(f"[{datetime.now()}] Loading data...")
        
        if isinstance(data_source, str):
            # 从文件加载
            if data_source.endswith('.csv'):
                df = pd.read_csv(data_source)
            elif data_source.endswith('.parquet'):
                df = pd.read_parquet(data_source)
            else:
                raise ValueError(f"Unsupported file format: {data_source}")
        else:
            df = data_source.copy()
        
        # 验证必要列存在
        required_cols = [time_col, price_col]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # 如果没有目标列，创建一个基于价格变化的目标
        if target_col not in df.columns:
            df[target_col] = df[price_col].pct_change().shift(-1)  # 下一期收益率
            self.system_log.append(f"Created target column '{target_col}' based on price changes")
        
        self.system_log.append(f"Data loaded successfully: {len(df)} rows, {len(df.columns)} columns")
        return df
    
    def run_complete_pipeline(self, df: pd.DataFrame, target_col: str = 'target',
                            time_col: str = 'dt', price_col: str = 'close',
                            save_results: bool = True, results_dir: str = 'backtest_results') -> Dict:
        """
        运行完整的回测流水线
        
        Args:
            df: 输入数据
            target_col: 目标列名
            time_col: 时间列名
            price_col: 价格列名
            save_results: 是否保存结果
            results_dir: 结果保存目录
            
        Returns:
            完整的回测结果
        """
        self.system_log.append(f"[{datetime.now()}] Starting complete backtest pipeline")
        
        try:
            # 1. 数据预处理
            self.system_log.append("Step 1: Data preprocessing...")
            df_processed, quality_report = self.data_preprocessor.preprocess_pipeline(
                df, time_col=time_col)
            self.pipeline_results['data_quality'] = quality_report
            
            # 2. 特征工程
            self.system_log.append("Step 2: Feature engineering...")
            df_featured, feature_report = self.feature_engineer.feature_engineering_pipeline(
                df_processed, 
                target_col=target_col,
                price_col=price_col,
                selection_method=self.config['feature_engineering']['selection_method'],
                n_features=self.config['feature_engineering']['n_features']
            )
            self.pipeline_results['feature_engineering'] = feature_report
            
            # 3. 时间序列处理
            self.system_log.append("Step 3: Time series processing...")
            processed_data, ts_report = self.timeseries_processor.timeseries_processing_pipeline(
                df_featured, target_col=target_col, time_col=time_col)
            self.pipeline_results['timeseries_processing'] = ts_report
            
            # 4. 模型训练
            self.system_log.append("Step 4: Model training...")
            training_results = self._train_models(processed_data)
            self.pipeline_results['model_training'] = training_results
            
            # 5. 生成预测
            self.system_log.append("Step 5: Generating predictions...")
            predictions = self._generate_predictions(processed_data)
            
            # 6. 回测执行
            self.system_log.append("Step 6: Running backtest...")
            backtest_results = self._run_backtest(
                predictions, df_featured, processed_data, price_col, time_col)
            self.pipeline_results['backtest'] = backtest_results
            
            # 7. 风险分析
            self.system_log.append("Step 7: Risk analysis...")
            risk_analysis = self._analyze_risks(backtest_results)
            self.pipeline_results['risk_analysis'] = risk_analysis
            
            # 8. 整合结果
            complete_results = self._compile_results()
            
            # 9. 保存结果
            if save_results:
                self._save_results(complete_results, results_dir)
            
            self.system_log.append(f"[{datetime.now()}] Pipeline completed successfully")
            return complete_results
            
        except Exception as e:
            error_msg = f"Pipeline failed at step: {str(e)}"
            self.system_log.append(f"[{datetime.now()}] ERROR: {error_msg}")
            raise Exception(error_msg)
    
    def _train_models(self, processed_data: Dict) -> Dict:
        """训练模型"""
        X_train = processed_data['X_train']
        y_train = processed_data['y_train']
        X_val = processed_data['X_val']
        y_val = processed_data['y_val']
        
        # 确定输入形状
        input_shape = (X_train.shape[1], X_train.shape[2])
        
        # 初始化网络架构模块
        self.network_arch = NetworkArchitecture(input_shape=input_shape, output_dim=1)
        
        # 初始化模型训练器
        self.model_trainer = ModelTrainer(
            early_stopping_patience=self.config['model_training']['early_stopping_patience']
        )
        
        # 训练不同类型的模型
        model_configs = self.config['network_architecture']
        model_type = model_configs['model_type']
        
        training_results = {}
        
        if model_type == 'lstm':
            model = self.network_arch.build_lstm_model(
                lstm_units=model_configs['lstm_units'],
                dropout_rate=model_configs['dropout_rate'],
                bidirectional=model_configs['bidirectional']
            )
        elif model_type == 'gru':
            model = self.network_arch.build_gru_model(
                gru_units=model_configs.get('gru_units', [64, 32]),
                dropout_rate=model_configs['dropout_rate'],
                bidirectional=model_configs['bidirectional']
            )
        elif model_type == 'transformer':
            model = self.network_arch.build_transformer_model(
                d_model=model_configs.get('d_model', 64),
                num_heads=model_configs.get('num_heads', 8)
            )
        else:
            model = self.network_arch.build_lstm_model()  # 默认LSTM
        
        # 编译模型
        model = self.network_arch.compile_model(
            model, learning_rate=self.config['model_training']['learning_rate'])
        
        # 获取回调函数
        callbacks = self.network_arch.get_callbacks(
            patience=self.config['model_training']['early_stopping_patience'])
        
        # 训练模型
        history = self.model_trainer.train_with_validation(
            model, X_train, y_train, X_val, y_val,
            epochs=self.config['model_training']['epochs'],
            batch_size=self.config['model_training']['batch_size'],
            callbacks=callbacks
        )
        
        # 保存模型
        self.models['main_model'] = model
        self.is_trained = True
        
        # 评估模型
        evaluation = self.model_trainer.evaluate_model(
            model, processed_data['X_test'], processed_data['y_test'],
            self.timeseries_processor.target_scaler
        )
        
        training_results['main_model'] = {
            'history': history.history,
            'evaluation': evaluation,
            'model_config': model_configs
        }
        
        return training_results
    
    def _generate_predictions(self, processed_data: Dict) -> np.ndarray:
        """生成预测"""
        if not self.is_trained:
            raise Exception("Models must be trained before generating predictions")
        
        model = self.models['main_model']
        X_test = processed_data['X_test']
        
        # 生成预测
        predictions = model.predict(X_test, verbose=0)
        
        # 反变换预测结果
        if self.timeseries_processor.target_scaler:
            predictions = self.timeseries_processor.inverse_transform_predictions(predictions)
        
        return predictions.flatten()
    
    def _run_backtest(self, predictions: np.ndarray, df_featured: pd.DataFrame,
                     processed_data: Dict, price_col: str, time_col: str) -> Dict:
        """运行回测"""
        # 获取测试期间的价格和时间数据
        test_start_idx = len(df_featured) - len(predictions)
        test_prices = df_featured[price_col].iloc[test_start_idx:]
        test_timestamps = pd.to_datetime(df_featured[time_col].iloc[test_start_idx:])
        
        # 执行回测
        backtest_df = self.backtest_engine.run_backtest(
            predictions, test_prices, test_timestamps,
            signal_method=self.config['backtest_engine']['signal_method'],
            threshold=self.config['backtest_engine']['signal_threshold']
        )
        
        # 计算性能指标
        metrics = self.backtest_engine.calculate_metrics(backtest_df)
        
        # 获取交易分析
        trade_analysis = self.backtest_engine.get_trade_analysis()
        
        # 获取回测总结
        backtest_summary = self.backtest_engine.get_backtest_summary()
        
        return {
            'backtest_dataframe': backtest_df,
            'metrics': metrics,
            'trade_analysis': trade_analysis,
            'summary': backtest_summary
        }
    
    def _analyze_risks(self, backtest_results: Dict) -> Dict:
        """分析风险"""
        backtest_df = backtest_results['backtest_dataframe']
        portfolio_values = backtest_df['portfolio_value']
        
        # 获取当前持仓（简化版）
        current_positions = {}
        if len(backtest_df) > 0:
            last_position_value = backtest_df['position_value'].iloc[-1]
            if last_position_value > 0:
                current_positions['main_asset'] = last_position_value
        
        # 进行风险评估
        risk_assessment = self.risk_manager.assess_portfolio_risk(
            portfolio_values, current_positions)
        
        # 获取风险建议
        recommendations = self.risk_manager.get_risk_recommendations(risk_assessment)
        
        # 获取风险总结
        risk_summary = self.risk_manager.get_risk_summary()
        
        return {
            'risk_assessment': risk_assessment,
            'recommendations': recommendations,
            'risk_summary': risk_summary
        }
    
    def _compile_results(self) -> Dict:
        """整合所有结果"""
        complete_results = {
            'system_config': self.config,
            'pipeline_results': self.pipeline_results,
            'system_log': self.system_log.copy(),
            'execution_timestamp': datetime.now(),
            'model_summary': {
                'models_trained': list(self.models.keys()),
                'is_trained': self.is_trained
            }
        }
        
        return complete_results
    
    def _save_results(self, results: Dict, results_dir: str):
        """保存结果"""
        # 创建结果目录
        os.makedirs(results_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存配置
        config_file = os.path.join(results_dir, f"config_{timestamp}.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存回测结果
        if 'backtest' in self.pipeline_results:
            backtest_df = self.pipeline_results['backtest']['backtest_dataframe']
            backtest_file = os.path.join(results_dir, f"backtest_results_{timestamp}.csv")
            backtest_df.to_csv(backtest_file)
        
        # 保存完整结果（排除大的DataFrame）
        results_copy = results.copy()
        if 'pipeline_results' in results_copy and 'backtest' in results_copy['pipeline_results']:
            results_copy['pipeline_results']['backtest'] = {
                k: v for k, v in results_copy['pipeline_results']['backtest'].items()
                if k != 'backtest_dataframe'
            }
        
        results_file = os.path.join(results_dir, f"complete_results_{timestamp}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_copy, f, indent=2, ensure_ascii=False, default=str)
        
        self.system_log.append(f"Results saved to {results_dir}")
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'is_initialized': True,
            'is_trained': self.is_trained,
            'models_available': list(self.models.keys()),
            'config': self.config,
            'system_log_entries': len(self.system_log),
            'pipeline_steps_completed': list(self.pipeline_results.keys())
        }
        
        return status
    
    def update_config(self, new_config: Dict):
        """更新系统配置"""
        self._update_config(self.config, new_config)
        self._initialize_modules()  # 重新初始化模块
        self.system_log.append(f"[{datetime.now()}] Configuration updated and modules reinitialized")
    
    def reset_system(self):
        """重置系统状态"""
        self.pipeline_results = {}
        self.models = {}
        self.is_trained = False
        self.system_log = []
        self._initialize_modules()
        self.system_log.append(f"[{datetime.now()}] System reset completed") 