import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
from sklearn.feature_selection import SelectKBest, f_regression, RFE, VarianceThreshold
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV
from scipy.stats import pearsonr, spearmanr
import talib
import warnings
warnings.filterwarnings('ignore')


class FeatureEngineer:
    """
    特征工程模块
    负责特征选择、特征创建、相关性分析、特征重要性评估
    """
    
    def __init__(self, correlation_threshold: float = 0.8, variance_threshold: float = 0.01):
        """
        初始化特征工程器
        
        Args:
            correlation_threshold: 特征相关性阈值，超过此值将被视为高相关
            variance_threshold: 方差阈值，低于此值的特征将被移除
        """
        self.correlation_threshold = correlation_threshold
        self.variance_threshold = variance_threshold
        self.selected_features = []
        self.feature_importance_scores = {}
        self.correlation_matrix = None
        self.engineering_log = []
        
    def create_technical_features(self, df: pd.DataFrame, price_col: str = 'close', 
                                volume_col: str = 'volume') -> pd.DataFrame:
        """
        创建技术指标特征
        
        Args:
            df: 输入数据框
            price_col: 价格列名
            volume_col: 成交量列名
            
        Returns:
            包含技术指标的数据框
        """
        df_features = df.copy()
        
        if price_col not in df.columns:
            self.engineering_log.append(f"Warning: Price column '{price_col}' not found")
            return df_features
        
        prices = df[price_col].values
        
        try:
            # 移动平均线
            df_features['sma_5'] = talib.SMA(prices, timeperiod=5)
            df_features['sma_10'] = talib.SMA(prices, timeperiod=10)
            df_features['sma_20'] = talib.SMA(prices, timeperiod=20)
            df_features['sma_50'] = talib.SMA(prices, timeperiod=50)
            
            # 指数移动平均线
            df_features['ema_12'] = talib.EMA(prices, timeperiod=12)
            df_features['ema_26'] = talib.EMA(prices, timeperiod=26)
            
            # 布林带
            df_features['bb_upper'], df_features['bb_middle'], df_features['bb_lower'] = talib.BBANDS(prices)
            df_features['bb_width'] = df_features['bb_upper'] - df_features['bb_lower']
            df_features['bb_position'] = (prices - df_features['bb_lower']) / df_features['bb_width']
            
            # RSI
            df_features['rsi_14'] = talib.RSI(prices, timeperiod=14)
            df_features['rsi_21'] = talib.RSI(prices, timeperiod=21)
            
            # MACD
            df_features['macd'], df_features['macd_signal'], df_features['macd_hist'] = talib.MACD(prices)
            
            # 威廉指标
            if 'high' in df.columns and 'low' in df.columns:
                df_features['williams_r'] = talib.WILLR(df['high'].values, df['low'].values, prices)
            
            # 随机指标
            if 'high' in df.columns and 'low' in df.columns:
                df_features['stoch_k'], df_features['stoch_d'] = talib.STOCH(
                    df['high'].values, df['low'].values, prices)
            
            # 平均真实波幅
            if 'high' in df.columns and 'low' in df.columns:
                df_features['atr_14'] = talib.ATR(df['high'].values, df['low'].values, prices, timeperiod=14)
            
            # 价格变化率
            df_features['roc_1'] = talib.ROC(prices, timeperiod=1)
            df_features['roc_5'] = talib.ROC(prices, timeperiod=5)
            df_features['roc_10'] = talib.ROC(prices, timeperiod=10)
            
            # 成交量相关指标
            if volume_col in df.columns:
                volumes = df[volume_col].values
                df_features['volume_sma_5'] = talib.SMA(volumes, timeperiod=5)
                df_features['volume_ratio'] = volumes / df_features['volume_sma_5']
                
                # OBV (On Balance Volume)
                df_features['obv'] = talib.OBV(prices, volumes)
                
                # 成交量加权平均价格近似
                df_features['vwap_approx'] = (prices * volumes).rolling(window=20).sum() / volumes.rolling(window=20).sum()
            
            # 价格动量特征
            df_features['momentum_5'] = prices / np.roll(prices, 5) - 1
            df_features['momentum_10'] = prices / np.roll(prices, 10) - 1
            
            # 波动率特征
            df_features['volatility_5'] = df[price_col].rolling(window=5).std()
            df_features['volatility_20'] = df[price_col].rolling(window=20).std()
            
            self.engineering_log.append(f"Created {len([col for col in df_features.columns if col not in df.columns])} technical features")
            
        except Exception as e:
            self.engineering_log.append(f"Error creating technical features: {str(e)}")
        
        return df_features
    
    def create_statistical_features(self, df: pd.DataFrame, price_col: str = 'close') -> pd.DataFrame:
        """
        创建统计特征
        
        Args:
            df: 输入数据框
            price_col: 价格列名
            
        Returns:
            包含统计特征的数据框
        """
        df_features = df.copy()
        
        if price_col not in df.columns:
            return df_features
        
        # 滚动统计特征
        for window in [5, 10, 20]:
            # 均值回归特征
            df_features[f'mean_reversion_{window}'] = (df[price_col] - df[price_col].rolling(window).mean()) / df[price_col].rolling(window).std()
            
            # 偏度和峰度
            df_features[f'skewness_{window}'] = df[price_col].rolling(window).skew()
            df_features[f'kurtosis_{window}'] = df[price_col].rolling(window).kurt()
            
            # 分位数特征
            df_features[f'quantile_25_{window}'] = df[price_col].rolling(window).quantile(0.25)
            df_features[f'quantile_75_{window}'] = df[price_col].rolling(window).quantile(0.75)
            
        # 价格位置特征
        df_features['price_position_20'] = (df[price_col] - df[price_col].rolling(20).min()) / (df[price_col].rolling(20).max() - df[price_col].rolling(20).min())
        
        self.engineering_log.append("Created statistical features")
        
        return df_features
    
    def analyze_correlation(self, df: pd.DataFrame, target_col: Optional[str] = None) -> pd.DataFrame:
        """
        分析特征相关性
        
        Args:
            df: 输入数据框
            target_col: 目标列名，如果提供则计算与目标的相关性
            
        Returns:
            相关性矩阵
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if target_col and target_col in numeric_cols:
            # 计算与目标变量的相关性
            correlations = []
            for col in numeric_cols:
                if col != target_col:
                    corr, _ = pearsonr(df[col].dropna(), df[target_col].dropna())
                    correlations.append({'feature': col, 'correlation': corr, 'abs_correlation': abs(corr)})
            
            correlation_df = pd.DataFrame(correlations).sort_values('abs_correlation', ascending=False)
            self.correlation_matrix = correlation_df
            
        else:
            # 计算特征间相关性矩阵
            self.correlation_matrix = df[numeric_cols].corr()
        
        return self.correlation_matrix
    
    def remove_highly_correlated_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        移除高相关性特征
        
        Args:
            df: 输入数据框
            
        Returns:
            移除高相关性特征后的数据框
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        corr_matrix = df[numeric_cols].corr().abs()
        
        # 找到高相关性特征对
        upper_triangle = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
        high_corr_features = [column for column in upper_triangle.columns if any(upper_triangle[column] > self.correlation_threshold)]
        
        # 移除高相关性特征
        df_reduced = df.drop(columns=high_corr_features)
        
        self.engineering_log.append(f"Removed {len(high_corr_features)} highly correlated features: {high_corr_features}")
        
        return df_reduced
    
    def select_features_by_variance(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        基于方差阈值选择特征
        
        Args:
            df: 输入数据框
            
        Returns:
            特征选择后的数据框
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        # 使用方差阈值选择器
        selector = VarianceThreshold(threshold=self.variance_threshold)
        selected_data = selector.fit_transform(df[numeric_cols])
        
        # 获取选择的特征名
        selected_features = [numeric_cols[i] for i in range(len(numeric_cols)) if selector.get_support()[i]]
        
        # 保留非数值列和选择的数值列
        non_numeric_cols = df.select_dtypes(exclude=[np.number]).columns.tolist()
        df_selected = df[non_numeric_cols + selected_features].copy()
        
        removed_count = len(numeric_cols) - len(selected_features)
        self.engineering_log.append(f"Removed {removed_count} low variance features")
        
        return df_selected
    
    def select_features_by_importance(self, X: pd.DataFrame, y: pd.Series, method: str = 'random_forest', k: int = 20) -> List[str]:
        """
        基于特征重要性选择特征
        
        Args:
            X: 特征数据框
            y: 目标变量
            method: 特征选择方法 ('random_forest', 'lasso', 'f_test', 'rfe')
            k: 选择的特征数量
            
        Returns:
            选择的特征名列表
        """
        X_clean = X.select_dtypes(include=[np.number]).dropna()
        y_clean = y.loc[X_clean.index]
        
        if method == 'random_forest':
            # 随机森林特征重要性
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X_clean, y_clean)
            feature_importance = pd.DataFrame({
                'feature': X_clean.columns,
                'importance': rf.feature_importances_
            }).sort_values('importance', ascending=False)
            
            selected_features = feature_importance.head(k)['feature'].tolist()
            self.feature_importance_scores['random_forest'] = feature_importance
            
        elif method == 'lasso':
            # Lasso回归特征选择
            lasso = LassoCV(cv=5, random_state=42)
            lasso.fit(X_clean, y_clean)
            feature_importance = pd.DataFrame({
                'feature': X_clean.columns,
                'coefficient': np.abs(lasso.coef_)
            }).sort_values('coefficient', ascending=False)
            
            selected_features = feature_importance[feature_importance['coefficient'] > 0].head(k)['feature'].tolist()
            self.feature_importance_scores['lasso'] = feature_importance
            
        elif method == 'f_test':
            # F统计量特征选择
            selector = SelectKBest(score_func=f_regression, k=k)
            selector.fit(X_clean, y_clean)
            selected_features = X_clean.columns[selector.get_support()].tolist()
            
            feature_scores = pd.DataFrame({
                'feature': X_clean.columns,
                'f_score': selector.scores_
            }).sort_values('f_score', ascending=False)
            self.feature_importance_scores['f_test'] = feature_scores
            
        elif method == 'rfe':
            # 递归特征消除
            rf = RandomForestRegressor(n_estimators=50, random_state=42)
            selector = RFE(rf, n_features_to_select=k)
            selector.fit(X_clean, y_clean)
            selected_features = X_clean.columns[selector.get_support()].tolist()
            
            feature_ranking = pd.DataFrame({
                'feature': X_clean.columns,
                'ranking': selector.ranking_
            }).sort_values('ranking')
            self.feature_importance_scores['rfe'] = feature_ranking
        
        self.selected_features = selected_features
        self.engineering_log.append(f"Selected {len(selected_features)} features using {method}")
        
        return selected_features
    
    def create_interaction_features(self, df: pd.DataFrame, feature_pairs: List[Tuple[str, str]]) -> pd.DataFrame:
        """
        创建交互特征
        
        Args:
            df: 输入数据框
            feature_pairs: 特征对列表
            
        Returns:
            包含交互特征的数据框
        """
        df_interaction = df.copy()
        
        for feature1, feature2 in feature_pairs:
            if feature1 in df.columns and feature2 in df.columns:
                # 乘积交互
                df_interaction[f'{feature1}_x_{feature2}'] = df[feature1] * df[feature2]
                
                # 比值交互
                df_interaction[f'{feature1}_div_{feature2}'] = df[feature1] / (df[feature2] + 1e-8)
                
                # 差值交互
                df_interaction[f'{feature1}_minus_{feature2}'] = df[feature1] - df[feature2]
        
        interaction_count = len(feature_pairs) * 3
        self.engineering_log.append(f"Created {interaction_count} interaction features")
        
        return df_interaction
    
    def feature_engineering_pipeline(self, df: pd.DataFrame, target_col: str, 
                                   price_col: str = 'close', volume_col: str = 'volume',
                                   selection_method: str = 'random_forest', n_features: int = 20) -> Tuple[pd.DataFrame, Dict]:
        """
        完整的特征工程流水线
        
        Args:
            df: 输入数据框
            target_col: 目标列名
            price_col: 价格列名
            volume_col: 成交量列名
            selection_method: 特征选择方法
            n_features: 选择的特征数量
            
        Returns:
            (特征工程后的数据框, 工程报告)
        """
        self.engineering_log.clear()
        self.engineering_log.append("Starting feature engineering pipeline")
        
        # 1. 创建技术指标特征
        df_featured = self.create_technical_features(df, price_col, volume_col)
        
        # 2. 创建统计特征
        df_featured = self.create_statistical_features(df_featured, price_col)
        
        # 3. 移除低方差特征
        df_featured = self.select_features_by_variance(df_featured)
        
        # 4. 移除高相关性特征
        df_featured = self.remove_highly_correlated_features(df_featured)
        
        # 5. 基于重要性选择特征
        if target_col in df_featured.columns:
            X = df_featured.drop(columns=[target_col])
            y = df_featured[target_col]
            
            selected_features = self.select_features_by_importance(X, y, selection_method, n_features)
            df_final = df_featured[selected_features + [target_col]].copy()
        else:
            df_final = df_featured.copy()
        
        # 6. 生成特征工程报告
        engineering_report = {
            'original_features': len(df.columns),
            'final_features': len(df_final.columns),
            'selected_features': self.selected_features,
            'feature_importance_scores': self.feature_importance_scores,
            'correlation_matrix': self.correlation_matrix,
            'engineering_log': self.engineering_log.copy()
        }
        
        self.engineering_log.append("Feature engineering pipeline completed")
        
        return df_final, engineering_report 