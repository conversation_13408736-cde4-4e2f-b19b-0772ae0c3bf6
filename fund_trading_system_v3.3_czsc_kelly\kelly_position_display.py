"""
凯利仓位显示工具
专门用于清晰显示凯利公式的计算过程和结果
"""

import numpy as np
from typing import Dict, List, Tuple
from dataclasses import dataclass
import pandas as pd

@dataclass
class KellyDisplayResult:
    """凯利显示结果"""
    symbol: str
    signal_type: str
    
    # 输入参数
    win_probability: float
    risk_reward_ratio: float
    confidence: float
    
    # 计算过程
    classic_kelly: float
    adjusted_kelly: float
    final_position: float
    
    # 显示信息
    position_size_text: str
    risk_level_text: str
    recommendation_text: str
    calculation_details: str

class KellyPositionDisplay:
    """凯利仓位显示器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {
            'kelly_fraction': 0.25,
            'max_position': 0.25,
            'min_position': 0.01
        }
    
    def get_simplified_kelly_result(self, 
                                  symbol: str,
                                  signal_type: str,
                                  win_probability: float,
                                  risk_reward_ratio: float,
                                  confidence: float) -> Dict:
        """
        获取简化的凯利计算结果，用于整合到LLM输出中
        """
        # 计算经典凯利
        classic_kelly = self._calculate_classic_kelly(win_probability, risk_reward_ratio)
        
        # 应用分数凯利和置信度调整
        kelly_fraction = self.config['kelly_fraction']
        adjusted_kelly = classic_kelly * kelly_fraction * confidence
        
        # 应用仓位限制
        max_pos = self.config['max_position']
        min_pos = self.config['min_position']
        final_position = np.clip(adjusted_kelly, 0, max_pos)
        
        if final_position < min_pos:
            final_position = 0.0
        
        # 风险等级评估
        risk_level = self._assess_risk_level(classic_kelly, final_position)
        
        # 生成简洁的建议
        if final_position > 0:
            if signal_type.lower() == 'buy':
                recommendation = f"建议以{final_position:.1%}仓位做多"
            elif signal_type.lower() == 'sell':
                recommendation = f"建议以{final_position:.1%}仓位做空"
            else:
                recommendation = f"建议保持{final_position:.1%}仓位观望"
        else:
            recommendation = "建议观望，暂不建仓"
        
        return {
            'position': final_position,
            'risk_level': risk_level.replace('🟢 ', '').replace('🟡 ', '').replace('🟠 ', '').replace('🔴 ', ''),
            'recommendation': recommendation,
            'win_probability': win_probability,
            'risk_reward_ratio': risk_reward_ratio,
            'classic_kelly': classic_kelly,
            'confidence': confidence
        }
    
    def display_kelly_calculation(self, 
                                symbol: str,
                                signal_type: str,
                                win_probability: float,
                                risk_reward_ratio: float,
                                confidence: float,
                                entry_price: float = 0,
                                stop_loss: float = 0,
                                take_profit: float = 0) -> KellyDisplayResult:
        """
        显示完整的凯利公式计算过程
        """
        
        print(f"\n{'='*60}")
        print(f"🎯 凯利公式仓位计算 - {symbol}")
        print(f"{'='*60}")
        
        # 1. 显示输入参数
        print(f"📊 输入参数:")
        print(f"   交易信号: {signal_type.upper()}")
        print(f"   胜率: {win_probability:.1%}")
        print(f"   盈亏比: {risk_reward_ratio:.2f}")
        print(f"   置信度: {confidence:.1%}")
        
        if entry_price > 0:
            print(f"   入场价: {entry_price:.4f}")
            print(f"   止损价: {stop_loss:.4f}")
            print(f"   止盈价: {take_profit:.4f}")
        
        # 2. 计算经典凯利
        classic_kelly = self._calculate_classic_kelly(win_probability, risk_reward_ratio)
        
        print(f"\n🧮 凯利公式计算:")
        print(f"   公式: f = (bp - q) / b")
        print(f"   其中: b={risk_reward_ratio:.2f}(盈亏比), p={win_probability:.3f}(胜率), q={1-win_probability:.3f}(败率)")
        print(f"   计算: f = ({risk_reward_ratio:.2f}×{win_probability:.3f} - {1-win_probability:.3f}) / {risk_reward_ratio:.2f}")
        print(f"   经典凯利分数: {classic_kelly:.4f} = {classic_kelly:.2%}")
        
        # 3. 应用分数凯利
        kelly_fraction = self.config['kelly_fraction']
        adjusted_kelly = classic_kelly * kelly_fraction
        
        print(f"\n⚖️ 分数凯利调整:")
        print(f"   分数系数: {kelly_fraction:.0%} (更保守的策略)")
        print(f"   调整后凯利: {classic_kelly:.4f} × {kelly_fraction:.2f} = {adjusted_kelly:.4f}")
        print(f"   调整后仓位: {adjusted_kelly:.2%}")
        
        # 4. 应用置信度调整
        confidence_adjusted = adjusted_kelly * confidence
        
        print(f"\n🎯 置信度调整:")
        print(f"   置信度系数: {confidence:.1%}")
        print(f"   置信度调整后: {adjusted_kelly:.4f} × {confidence:.2f} = {confidence_adjusted:.4f}")
        print(f"   置信度调整仓位: {confidence_adjusted:.2%}")
        
        # 5. 应用仓位限制
        max_pos = self.config['max_position']
        min_pos = self.config['min_position']
        
        final_position = np.clip(confidence_adjusted, 0, max_pos)
        
        print(f"\n🚧 仓位限制:")
        print(f"   最大仓位限制: {max_pos:.0%}")
        print(f"   最小仓位限制: {min_pos:.0%}")
        
        if final_position < min_pos:
            final_position = 0.0
            print(f"   ⚠️ 计算结果{confidence_adjusted:.2%}低于最小阈值，设为0")
        elif final_position == max_pos and confidence_adjusted > max_pos:
            print(f"   ⚠️ 计算结果{confidence_adjusted:.2%}超过最大限制，限制为{max_pos:.0%}")
        
        print(f"   最终建议仓位: {final_position:.2%}")
        
        # 6. 风险等级评估
        risk_level = self._assess_risk_level(classic_kelly, final_position)
        
        print(f"\n⚠️ 风险评估:")
        print(f"   基于经典凯利分数{classic_kelly:.2%}的风险等级: {risk_level}")
        
        # 7. 具体建议
        recommendation = self._generate_recommendation(signal_type, final_position, risk_level)
        
        print(f"\n💡 投资建议:")
        print(f"   {recommendation}")
        
        # 8. 计算详情汇总
        calculation_details = self._generate_calculation_summary(
            win_probability, risk_reward_ratio, confidence,
            classic_kelly, adjusted_kelly, confidence_adjusted, final_position
        )
        
        print(f"\n📋 计算汇总:")
        print(calculation_details)
        
        # 9. 风险提示
        self._display_risk_warnings(classic_kelly, final_position, win_probability, risk_reward_ratio)
        
        print(f"{'='*60}")
        
        return KellyDisplayResult(
            symbol=symbol,
            signal_type=signal_type,
            win_probability=win_probability,
            risk_reward_ratio=risk_reward_ratio,
            confidence=confidence,
            classic_kelly=classic_kelly,
            adjusted_kelly=confidence_adjusted,
            final_position=final_position,
            position_size_text=f"{final_position:.2%}",
            risk_level_text=risk_level,
            recommendation_text=recommendation,
            calculation_details=calculation_details
        )
    
    def _calculate_classic_kelly(self, win_prob: float, risk_reward: float) -> float:
        """计算经典凯利分数"""
        if win_prob <= 0 or win_prob >= 1 or risk_reward <= 0:
            return 0.0
        
        lose_prob = 1 - win_prob
        kelly_fraction = (win_prob * risk_reward - lose_prob) / risk_reward
        
        return max(kelly_fraction, 0.0)
    
    def _assess_risk_level(self, classic_kelly: float, final_position: float) -> str:
        """评估风险等级"""
        if final_position == 0:
            return "🟢 无风险 (不建仓)"
        elif classic_kelly <= 0.05:
            return "🟢 极低风险"
        elif classic_kelly <= 0.10:
            return "🟡 低风险"
        elif classic_kelly <= 0.20:
            return "🟡 中等风险"
        elif classic_kelly <= 0.30:
            return "🟠 较高风险"
        else:
            return "🔴 高风险"
    
    def _generate_recommendation(self, signal_type: str, position: float, risk_level: str) -> str:
        """生成投资建议"""
        if position == 0:
            return "建议观望，不建立仓位"
        
        position_desc = ""
        if position <= 0.05:
            position_desc = "轻仓试探"
        elif position <= 0.15:
            position_desc = "适度建仓"
        elif position <= 0.25:
            position_desc = "标准仓位"
        else:
            position_desc = "重仓配置"
        
        action = "做多" if signal_type == "buy" else "做空" if signal_type == "sell" else "观望"
        
        return f"建议{action}，{position_desc}({position:.1%})，{risk_level}"
    
    def _generate_calculation_summary(self, win_prob: float, risk_reward: float, confidence: float,
                                    classic_kelly: float, adjusted_kelly: float, 
                                    confidence_adjusted: float, final_position: float) -> str:
        """生成计算汇总"""
        
        summary_lines = [
            f"   胜率: {win_prob:.1%} | 盈亏比: {risk_reward:.2f} | 置信度: {confidence:.1%}",
            f"   经典凯利: {classic_kelly:.2%} → 分数调整: {adjusted_kelly:.2%} → 置信度调整: {confidence_adjusted:.2%}",
            f"   最终仓位: {final_position:.2%}"
        ]
        
        return "\n".join(summary_lines)
    
    def _display_risk_warnings(self, classic_kelly: float, final_position: float, 
                             win_prob: float, risk_reward: float):
        """显示风险警告"""
        
        warnings = []
        
        if classic_kelly > 0.3:
            warnings.append("⚠️ 经典凯利分数过高，建议降低仓位")
        
        if win_prob < 0.55:
            warnings.append("⚠️ 胜率较低，请谨慎操作")
        
        if risk_reward < 1.5:
            warnings.append("⚠️ 盈亏比较低，风险收益不够理想")
        
        if final_position > 0.2:
            warnings.append("⚠️ 建议仓位较高，请注意风险控制")
        
        if warnings:
            print(f"\n🚨 风险提示:")
            for warning in warnings:
                print(f"   {warning}")
        else:
            print(f"\n✅ 风险检查通过，参数设置合理")

def batch_display_kelly_positions(positions_data: List[Dict]) -> pd.DataFrame:
    """批量显示多个标的的凯利仓位"""
    
    display = KellyPositionDisplay()
    results = []
    
    print(f"\n{'='*80}")
    print(f"📊 批量凯利仓位计算")
    print(f"{'='*80}")
    
    for i, data in enumerate(positions_data, 1):
        print(f"\n[{i}/{len(positions_data)}] 正在计算 {data['symbol']}...")
        
        result = display.display_kelly_calculation(
            symbol=data['symbol'],
            signal_type=data['signal_type'],
            win_probability=data['win_probability'],
            risk_reward_ratio=data['risk_reward_ratio'],
            confidence=data['confidence'],
            entry_price=data.get('entry_price', 0),
            stop_loss=data.get('stop_loss', 0),
            take_profit=data.get('take_profit', 0)
        )
        
        results.append({
            '标的代码': result.symbol,
            '交易信号': result.signal_type.upper(),
            '胜率': f"{result.win_probability:.1%}",
            '盈亏比': f"{result.risk_reward_ratio:.2f}",
            '置信度': f"{result.confidence:.1%}",
            '经典凯利': f"{result.classic_kelly:.2%}",
            '最终仓位': result.position_size_text,
            '风险等级': result.risk_level_text,
            '建议': result.recommendation_text
        })
    
    # 创建汇总表格
    df = pd.DataFrame(results)
    
    print(f"\n{'='*80}")
    print(f"📋 凯利仓位汇总表")
    print(f"{'='*80}")
    print(df.to_string(index=False))
    
    # 计算组合统计
    total_position = sum(float(r['最终仓位'].rstrip('%'))/100 for r in results)
    active_positions = len([r for r in results if float(r['最终仓位'].rstrip('%')) > 0])
    
    print(f"\n📊 组合统计:")
    print(f"   总标的数: {len(results)}")
    print(f"   活跃仓位: {active_positions}")
    print(f"   总仓位: {total_position:.1%}")
    print(f"   平均仓位: {total_position/len(results):.1%}")
    
    return df

# 使用示例
if __name__ == "__main__":
    # 单个标的示例
    print("=== 单个标的凯利计算示例 ===")
    
    display = KellyPositionDisplay()
    
    result = display.display_kelly_calculation(
        symbol="000001.SZ",
        signal_type="buy",
        win_probability=0.62,
        risk_reward_ratio=2.1,
        confidence=0.75,
        entry_price=10.50,
        stop_loss=10.00,
        take_profit=11.55
    )
    
    # 批量计算示例
    print("\n\n=== 批量凯利计算示例 ===")
    
    batch_data = [
        {
            'symbol': '000001.SZ',
            'signal_type': 'buy',
            'win_probability': 0.62,
            'risk_reward_ratio': 2.1,
            'confidence': 0.75,
            'entry_price': 10.50,
            'stop_loss': 10.00,
            'take_profit': 11.55
        },
        {
            'symbol': '000002.SZ',
            'signal_type': 'sell',
            'win_probability': 0.58,
            'risk_reward_ratio': 1.8,
            'confidence': 0.65,
            'entry_price': 15.20,
            'stop_loss': 16.00,
            'take_profit': 13.76
        },
        {
            'symbol': '000858.SZ',
            'signal_type': 'hold',
            'win_probability': 0.52,
            'risk_reward_ratio': 1.2,
            'confidence': 0.45
        }
    ]
    
    df_results = batch_display_kelly_positions(batch_data)