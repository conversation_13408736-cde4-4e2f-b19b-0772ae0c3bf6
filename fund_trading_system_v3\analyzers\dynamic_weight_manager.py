"""
动态权重管理器
根据市场环境和数据质量动态调整各维度权重
"""

import logging
import sys
import os
from typing import Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult


class DynamicWeightManager:
    """
    @class DynamicWeightManager
    @brief 动态权重管理器
    @details 根据市场环境和数据质量动态调整各维度权重
    """
    
    def __init__(self):
        self.name = "DynamicWeightManager"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 基础权重配置
        self.base_weights = {
            "趋势": 0.25,
            "波动性": 0.15,
            "流动性": 0.20,
            "情绪": 0.15,
            "结构": 0.15,
            "转换": 0.10
        }
        
        # 市场环境权重调整策略
        self.market_condition_adjustments = {
            "bull_market": {"趋势": 1.3, "情绪": 1.2, "结构": 0.8},
            "bear_market": {"波动性": 1.2, "流动性": 1.3, "情绪": 0.8},
            "volatile_market": {"波动性": 1.4, "趋势": 0.8, "转换": 1.2},
            "stable_market": {"结构": 1.3, "趋势": 1.1, "波动性": 0.7}
        }
        
    def calculate_dynamic_weights(self, evaluations: Dict[str, DimensionEvaluationResult],
                                 market_condition: str = "neutral") -> Dict[str, float]:
        """
        @brief 计算动态权重
        @param evaluations: 各维度评估结果
        @param market_condition: 市场环境
        @return: 动态权重字典
        """
        try:
            # 1. 从基础权重开始
            dynamic_weights = self.base_weights.copy()
            
            # 2. 根据市场环境调整
            if market_condition in self.market_condition_adjustments:
                adjustments = self.market_condition_adjustments[market_condition]
                for dim_name, factor in adjustments.items():
                    if dim_name in dynamic_weights:
                        dynamic_weights[dim_name] *= factor
            
            # 3. 根据数据质量调整
            for dim_name, eval_result in evaluations.items():
                if dim_name in dynamic_weights:
                    quality_factor = self._calculate_quality_factor(eval_result)
                    dynamic_weights[dim_name] *= quality_factor
            
            # 4. 根据置信度调整
            for dim_name, eval_result in evaluations.items():
                if dim_name in dynamic_weights:
                    confidence_factor = self._calculate_confidence_factor(eval_result.confidence)
                    dynamic_weights[dim_name] *= confidence_factor
            
            # 5. 标准化权重
            dynamic_weights = self._normalize_weights(dynamic_weights)
            
            return dynamic_weights
            
        except Exception as e:
            self.logger.error(f"Dynamic weight calculation failed: {str(e)}")
            return self.base_weights.copy()
    
    def _calculate_quality_factor(self, eval_result: DimensionEvaluationResult) -> float:
        """计算数据质量因子"""
        quality_mapping = {
            "good": 1.0,
            "fair": 0.8,
            "poor": 0.6,
            "error": 0.3
        }
        return quality_mapping.get(eval_result.data_quality, 0.5)
    
    def _calculate_confidence_factor(self, confidence: float) -> float:
        """计算置信度因子"""
        # 置信度越高，权重调整越大
        if confidence >= 0.8:
            return 1.2
        elif confidence >= 0.6:
            return 1.0
        elif confidence >= 0.4:
            return 0.8
        else:
            return 0.6
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """标准化权重"""
        total_weight = sum(weights.values())
        if total_weight > 0:
            return {dim: weight / total_weight for dim, weight in weights.items()}
        else:
            return self.base_weights.copy()
    
    def detect_market_condition(self, evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """
        @brief 检测市场环境
        @param evaluations: 各维度评估结果
        @return: 市场环境类型
        """
        try:
            # 获取各维度评分
            trend_score = evaluations.get("趋势", DimensionEvaluationResult("趋势", "SIDEWAYS", 0.0, 0.0, [], "low")).score
            volatility_score = evaluations.get("波动性", DimensionEvaluationResult("波动性", "NORMAL", 0.5, 0.0, [], "low")).score
            sentiment_score = evaluations.get("情绪", DimensionEvaluationResult("情绪", "NEUTRAL", 0.0, 0.0, [], "low")).score
            
            # 市场环境判断逻辑
            if trend_score >= 0.5 and sentiment_score >= 0.3:
                return "bull_market"
            elif trend_score <= -0.5 and sentiment_score <= -0.3:
                return "bear_market"
            elif volatility_score >= 0.7:
                return "volatile_market"
            elif abs(trend_score) <= 0.2 and volatility_score <= 0.3:
                return "stable_market"
            else:
                return "neutral"
                
        except Exception as e:
            self.logger.error(f"Market condition detection failed: {str(e)}")
            return "neutral"
