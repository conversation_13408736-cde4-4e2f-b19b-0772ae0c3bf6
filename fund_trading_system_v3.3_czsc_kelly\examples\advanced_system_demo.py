"""
高级系统功能演示
展示市场无效性检测、快速执行引擎和盈利优化器的集成使用
"""

import sys
import os
import logging
import time
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List

# 添加项目路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, current_dir)

from analyzers.market_inefficiency_detector import MarketInefficiencyDetector, InefficiencySignal
from execution.fast_execution_engine import FastExecutionEngine, ExecutionSignal, FastOrder
from optimization.profit_optimizer import ProfitOptimizer, ProfitMetrics
from core.data_structures import DimensionEvaluationResult
from core.enums import TrendState, SignalStrength


class AdvancedSystemDemo:
    """高级系统演示类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化各个组件
        self.inefficiency_detector = MarketInefficiencyDetector()
        self.execution_engine = FastExecutionEngine()
        self.profit_optimizer = ProfitOptimizer()
        
        # 演示数据
        self.market_data = self._generate_market_data()
        self.czsc_signals = self._generate_czsc_signals()
        
        # 设置回调
        self.execution_engine.set_order_callback(self._on_order_filled)
        self.execution_engine.set_execution_callback(self._on_execution_complete)
        
        self.logger.info("高级系统演示初始化完成")
    
    def _generate_market_data(self) -> pd.DataFrame:
        """生成模拟市场数据"""
        np.random.seed(42)
        
        # 生成252天的日线数据
        dates = pd.date_range(start='2023-01-01', periods=252, freq='D')
        
        # 生成价格数据（带趋势和噪声）
        trend = np.linspace(0, 0.2, 252)  # 20%的年化趋势
        noise = np.random.normal(0, 0.02, 252)  # 2%的日波动
        returns = trend/252 + noise
        
        prices = 100 * np.cumprod(1 + returns)
        
        # 生成OHLC数据
        opens = prices * (1 + np.random.normal(0, 0.005, 252))
        highs = prices * (1 + np.abs(np.random.normal(0, 0.01, 252)))
        lows = prices * (1 - np.abs(np.random.normal(0, 0.01, 252)))
        volumes = np.random.lognormal(10, 0.5, 252)
        
        # 确保OHLC逻辑正确
        highs = np.maximum(highs, prices)
        lows = np.minimum(lows, prices)
        
        df = pd.DataFrame({
            'datetime': dates,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': prices,
            'volume': volumes
        })
        
        return df
    
    def _generate_czsc_signals(self) -> List[DimensionEvaluationResult]:
        """生成模拟CZSC信号"""
        signals = []
        
        # 生成不同类型的信号
        signal_types = ['趋势', '波动性', '流动性', '情绪', '结构', '基本面']
        
        for i, signal_type in enumerate(signal_types):
            signal = DimensionEvaluationResult(
                dimension_name=signal_type,
                state=TrendState.STRONG_UPTREND if i % 2 == 0 else TrendState.WEAK_DOWNTREND,
                score=np.random.uniform(0.4, 0.9),
                confidence=np.random.uniform(0.6, 0.95),
                signals=[f"{signal_type}信号"],
                data_quality="good",
                details={
                    'signal_strength': np.random.uniform(0.5, 1.0),
                    'market_condition': 'normal',
                    'timestamp': datetime.now()
                },
                indicators={
                    'ma5': np.random.uniform(95, 105),
                    'ma20': np.random.uniform(90, 110),
                    'rsi': np.random.uniform(30, 70)
                }
            )
            signals.append(signal)
        
        return signals
    
    def _on_order_filled(self, order: FastOrder):
        """订单成交回调"""
        self.logger.info(f"订单成交: {order.symbol} {order.side} {order.filled_quantity} @ {order.avg_fill_price}")
    
    def _on_execution_complete(self, signal: ExecutionSignal, success: bool, execution_time: float):
        """执行完成回调"""
        status = "成功" if success else "失败"
        self.logger.info(f"信号执行{status}: {signal.signal_id}, 耗时 {execution_time:.1f}ms")
    
    def demo_market_inefficiency_detection(self):
        """演示市场无效性检测"""
        print("\n" + "="*60)
        print("🔍 市场无效性检测演示")
        print("="*60)
        
        try:
            # 检测市场无效性
            inefficiency_signals = self.inefficiency_detector.detect_inefficiencies(self.market_data)
            
            print(f"检测到 {len(inefficiency_signals)} 个无效性信号")
            
            # 显示前5个信号
            for i, signal in enumerate(inefficiency_signals[:5], 1):
                print(f"\n信号 {i}: {signal.signal_type}")
                print(f"  强度: {signal.strength:.3f}")
                print(f"  置信度: {signal.confidence:.3f}")
                print(f"  预期收益: {signal.expected_return:.2%}")
                print(f"  预期持续时间: {signal.expected_duration} 分钟")
                print(f"  风险等级: {signal.risk_level}")
                print(f"  入场价格: {signal.entry_price:.2f}")
                print(f"  出场价格: {signal.exit_price:.2f}")
                print(f"  止损价格: {signal.stop_loss:.2f}")
                print(f"  检测方法: {signal.metadata.get('detection_method', 'N/A')}")
            
            # 显示检测统计
            stats = self.inefficiency_detector.get_detection_statistics()
            print(f"\n检测统计:")
            print(f"  总信号数: {stats['total_signals']}")
            print(f"  平均预期收益: {stats['avg_return']:.2%}")
            print(f"  检测准确率: {stats['detection_accuracy']:.2%}")
            
            return inefficiency_signals
            
        except Exception as e:
            print(f"市场无效性检测失败: {e}")
            return []
    
    def demo_fast_execution_engine(self, inefficiency_signals: List[InefficiencySignal]):
        """演示快速执行引擎"""
        print("\n" + "="*60)
        print("⚡ 快速执行引擎演示")
        print("="*60)
        
        try:
            # 启动执行引擎
            self.execution_engine.start()
            print("执行引擎已启动")
            
            # 将无效性信号转换为执行信号
            execution_signals = []
            for i, ineff_signal in enumerate(inefficiency_signals[:3]):  # 只处理前3个
                exec_signal = ExecutionSignal(
                    signal_id=f"ineff_{i+1}",
                    symbol="TEST_SYMBOL",
                    action="buy" if ineff_signal.expected_return > 0 else "sell",
                    urgency=min(int(ineff_signal.strength * 10), 10),
                    quantity=100,
                    price_limit=ineff_signal.entry_price,
                    stop_loss=ineff_signal.stop_loss,
                    take_profit=ineff_signal.exit_price,
                    max_latency_ms=500,
                    metadata={
                        'source': 'inefficiency_detector',
                        'expected_return': ineff_signal.expected_return,
                        'risk_level': ineff_signal.risk_level
                    }
                )
                execution_signals.append(exec_signal)
            
            # 提交执行信号
            print(f"\n提交 {len(execution_signals)} 个执行信号:")
            for signal in execution_signals:
                success = self.execution_engine.submit_signal(signal)
                status = "✓" if success else "✗"
                print(f"  {status} {signal.signal_id}: {signal.action} {signal.quantity} @ {signal.price_limit}")
            
            # 等待执行完成
            print("\n等待执行完成...")
            time.sleep(2)
            
            # 显示执行统计
            stats = self.execution_engine.get_execution_statistics()
            print(f"\n执行统计:")
            print(f"  总信号数: {stats['total_signals']}")
            print(f"  已执行订单: {stats['executed_orders']}")
            print(f"  平均执行时间: {stats['avg_execution_time_ms']:.1f}ms")
            print(f"  成功率: {stats['success_rate']:.2%}")
            print(f"  活跃订单数: {stats['active_orders_count']}")
            print(f"  每日交易数: {stats['daily_trades']}")
            print(f"  队列大小: {stats['queue_size']}")
            
            # 显示活跃订单
            active_orders = self.execution_engine.get_active_orders()
            if active_orders:
                print(f"\n活跃订单 ({len(active_orders)}):")
                for order in active_orders:
                    print(f"  {order.order_id}: {order.symbol} {order.side} {order.quantity} @ {order.price} [{order.status.value}]")
            
            return execution_signals
            
        except Exception as e:
            print(f"快速执行引擎演示失败: {e}")
            return []
        finally:
            # 停止执行引擎
            self.execution_engine.stop()
            print("执行引擎已停止")
    
    def demo_profit_optimization(self):
        """演示盈利优化器"""
        print("\n" + "="*60)
        print("📈 盈利优化器演示")
        print("="*60)
        
        try:
            # 当前参数
            current_params = {
                'signal_threshold': 0.6,
                'confidence_threshold': 0.7,
                'stop_loss_pct': 0.05,
                'take_profit_pct': 0.1,
                'position_size_pct': 0.05,
                'commission_rate': 0.001,
                'slippage_rate': 0.0005
            }
            
            print("当前参数:")
            for key, value in current_params.items():
                print(f"  {key}: {value}")
            
            # 执行优化
            print("\n开始参数优化...")
            optimization_result = self.profit_optimizer.optimize_strategy(
                self.market_data, 
                self.czsc_signals, 
                current_params
            )
            
            # 显示优化结果
            print(f"\n优化完成!")
            print(f"优化方法: {optimization_result.optimization_method}")
            print(f"改进程度: {optimization_result.improvement_pct:.2%}")
            print(f"置信度: {optimization_result.confidence_score:.2f}")
            print(f"回测期间: {optimization_result.backtest_period}")
            print(f"交易次数: {optimization_result.trade_count}")
            
            # 对比原始和优化后的指标
            print(f"\n📊 性能对比:")
            print(f"{'指标':<20} {'原始':<12} {'优化后':<12} {'改进':<10}")
            print("-" * 60)
            
            orig = optimization_result.original_metrics
            opt = optimization_result.optimized_metrics
            
            metrics_comparison = [
                ('总收益率', orig.total_return, opt.total_return),
                ('年化收益率', orig.annualized_return, opt.annualized_return),
                ('夏普比率', orig.sharpe_ratio, opt.sharpe_ratio),
                ('最大回撤', orig.max_drawdown, opt.max_drawdown),
                ('胜率', orig.win_rate, opt.win_rate),
                ('盈利因子', orig.profit_factor, opt.profit_factor),
                ('平均盈利', orig.avg_win, opt.avg_win),
                ('平均亏损', orig.avg_loss, opt.avg_loss),
                ('总交易数', orig.total_trades, opt.total_trades),
                ('盈利交易数', orig.profitable_trades, opt.profitable_trades)
            ]
            
            for name, orig_val, opt_val in metrics_comparison:
                if isinstance(orig_val, float):
                    if name in ['总收益率', '年化收益率', '胜率', '平均盈利', '平均亏损']:
                        orig_str = f"{orig_val:.2%}"
                        opt_str = f"{opt_val:.2%}"
                    else:
                        orig_str = f"{orig_val:.3f}"
                        opt_str = f"{opt_val:.3f}"
                    
                    if orig_val != 0:
                        improvement = (opt_val - orig_val) / abs(orig_val)
                        imp_str = f"{improvement:+.1%}"
                    else:
                        imp_str = "N/A"
                else:
                    orig_str = str(orig_val)
                    opt_str = str(opt_val)
                    imp_str = f"{opt_val - orig_val:+d}" if orig_val != 0 else "N/A"
                
                print(f"{name:<20} {orig_str:<12} {opt_str:<12} {imp_str:<10}")
            
            # 显示优化后的参数
            print(f"\n🎯 优化后参数:")
            for key, value in optimization_result.optimized_parameters.items():
                old_value = current_params.get(key, 'N/A')
                if isinstance(value, float):
                    print(f"  {key}: {old_value} → {value:.4f}")
                else:
                    print(f"  {key}: {old_value} → {value}")
            
            return optimization_result
            
        except Exception as e:
            print(f"盈利优化器演示失败: {e}")
            return None
    
    def demo_integrated_workflow(self):
        """演示集成工作流"""
        print("\n" + "="*60)
        print("🔄 集成工作流演示")
        print("="*60)
        
        try:
            print("步骤1: 检测市场无效性...")
            inefficiency_signals = self.inefficiency_detector.detect_inefficiencies(self.market_data)
            print(f"  ✓ 检测到 {len(inefficiency_signals)} 个无效性机会")
            
            print("\n步骤2: 快速执行交易...")
            self.execution_engine.start()
            
            # 转换为执行信号并提交
            executed_count = 0
            for i, signal in enumerate(inefficiency_signals[:5]):
                exec_signal = ExecutionSignal(
                    signal_id=f"integrated_{i+1}",
                    symbol="INTEGRATED_TEST",
                    action="buy" if signal.expected_return > 0 else "sell",
                    urgency=min(int(signal.strength * 10), 10),
                    quantity=100,
                    price_limit=signal.entry_price,
                    max_latency_ms=200  # 更严格的延迟要求
                )
                
                if self.execution_engine.submit_signal(exec_signal):
                    executed_count += 1
            
            time.sleep(1)  # 等待执行
            self.execution_engine.stop()
            print(f"  ✓ 成功执行 {executed_count} 个交易信号")
            
            print("\n步骤3: 优化策略参数...")
            current_params = {
                'signal_threshold': 0.65,
                'stop_loss_pct': 0.04,
                'take_profit_pct': 0.08,
                'position_size_pct': 0.06
            }
            
            opt_result = self.profit_optimizer.optimize_strategy(
                self.market_data, self.czsc_signals, current_params
            )
            print(f"  ✓ 策略优化完成，改进 {opt_result.improvement_pct:.2%}")
            
            print("\n步骤4: 生成综合报告...")
            
            # 综合统计
            ineff_stats = self.inefficiency_detector.get_detection_statistics()
            exec_stats = self.execution_engine.get_execution_statistics()
            
            print(f"\n📋 综合系统报告:")
            print(f"  无效性检测:")
            print(f"    - 检测信号数: {ineff_stats['total_signals']}")
            print(f"    - 平均预期收益: {ineff_stats['avg_return']:.2%}")
            
            print(f"  执行引擎:")
            print(f"    - 处理信号数: {exec_stats['total_signals']}")
            print(f"    - 执行成功率: {exec_stats['success_rate']:.2%}")
            print(f"    - 平均执行时间: {exec_stats['avg_execution_time_ms']:.1f}ms")
            
            print(f"  策略优化:")
            print(f"    - 优化方法: {opt_result.optimization_method}")
            print(f"    - 性能改进: {opt_result.improvement_pct:.2%}")
            print(f"    - 优化置信度: {opt_result.confidence_score:.2f}")
            
            # 计算综合效率得分
            efficiency_score = (
                min(ineff_stats['total_signals'] / 10, 1.0) * 0.3 +  # 检测能力
                exec_stats['success_rate'] * 0.3 +  # 执行能力
                min(abs(opt_result.improvement_pct), 0.5) * 2 * 0.4  # 优化能力
            )
            
            print(f"\n🏆 系统综合效率得分: {efficiency_score:.2f}/1.0")
            
            if efficiency_score >= 0.8:
                print("  评级: 优秀 ⭐⭐⭐⭐⭐")
            elif efficiency_score >= 0.6:
                print("  评级: 良好 ⭐⭐⭐⭐")
            elif efficiency_score >= 0.4:
                print("  评级: 一般 ⭐⭐⭐")
            else:
                print("  评级: 需要改进 ⭐⭐")
            
            return {
                'inefficiency_signals': len(inefficiency_signals),
                'executed_signals': executed_count,
                'optimization_improvement': opt_result.improvement_pct,
                'efficiency_score': efficiency_score
            }
            
        except Exception as e:
            print(f"集成工作流演示失败: {e}")
            return None
    
    def run_full_demo(self):
        """运行完整演示"""
        print("🚀 高级系统功能完整演示")
        print("="*80)
        
        start_time = time.time()
        
        try:
            # 1. 市场无效性检测演示
            inefficiency_signals = self.demo_market_inefficiency_detection()
            
            # 2. 快速执行引擎演示
            execution_signals = self.demo_fast_execution_engine(inefficiency_signals)
            
            # 3. 盈利优化器演示
            optimization_result = self.demo_profit_optimization()
            
            # 4. 集成工作流演示
            workflow_result = self.demo_integrated_workflow()
            
            # 总结
            elapsed_time = time.time() - start_time
            
            print("\n" + "="*80)
            print("🎉 演示完成总结")
            print("="*80)
            
            print(f"总耗时: {elapsed_time:.2f} 秒")
            
            if workflow_result:
                print(f"系统性能:")
                print(f"  - 检测到无效性信号: {workflow_result['inefficiency_signals']} 个")
                print(f"  - 成功执行信号: {workflow_result['executed_signals']} 个")
                print(f"  - 策略优化改进: {workflow_result['optimization_improvement']:.2%}")
                print(f"  - 综合效率得分: {workflow_result['efficiency_score']:.2f}")
            
            print(f"\n💡 系统特点:")
            print(f"  ✓ 市场无效性实时检测")
            print(f"  ✓ 毫秒级快速执行")
            print(f"  ✓ 盈利导向参数优化")
            print(f"  ✓ 完整的风险控制")
            print(f"  ✓ 模块化可扩展架构")
            
            print(f"\n🎯 建议的下一步:")
            print(f"  1. 接入真实市场数据源")
            print(f"  2. 配置实际交易接口")
            print(f"  3. 部署到生产环境")
            print(f"  4. 建立监控和报警系统")
            print(f"  5. 持续优化和改进")
            
        except Exception as e:
            print(f"演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行演示
    demo = AdvancedSystemDemo()
    demo.run_full_demo()


if __name__ == "__main__":
    main()