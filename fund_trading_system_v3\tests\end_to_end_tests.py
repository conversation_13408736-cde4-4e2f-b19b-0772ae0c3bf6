"""
端到端测试套件 - 测试RL-LLM协作系统的完整工作流程
模拟真实交易场景，验证系统的整体表现和可靠性
"""

import unittest
import sys
import os
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import logging
import json
import tempfile
import shutil

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from rl_llm_collaboration import (
    HybridDecisionSystem,
    RealTimeCollaboration,
    LLMEnhancedRLAgent
)
from rl_infrastructure import (
    EnhancedTradingEnv,
    FeatureEngineer,
    TrainingManager
)


class EndToEndTestSuite(unittest.TestCase):
    """
    @class EndToEndTestSuite
    @brief 端到端测试套件
    @details 测试完整的系统工作流程和真实场景
    """
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.logger = logging.getLogger(cls.__name__)
        logging.basicConfig(level=logging.INFO)
        
        # 创建临时目录用于测试
        cls.temp_dir = tempfile.mkdtemp()
        
        # 创建测试数据
        cls.historical_data = cls._generate_historical_market_data()
        cls.test_scenarios = cls._create_test_scenarios()
        
        # 创建系统配置
        cls.system_config = cls._create_system_config()
        
        cls.logger.info("端到端测试套件初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        if hasattr(cls, 'temp_dir') and os.path.exists(cls.temp_dir):
            shutil.rmtree(cls.temp_dir)
        cls.logger.info("端到端测试套件清理完成")
    
    def setUp(self):
        """每个测试用例的初始化"""
        self.start_time = time.time()
        self.test_results = {}
    
    def tearDown(self):
        """每个测试用例的清理"""
        execution_time = time.time() - self.start_time
        self.logger.info(f"测试执行时间: {execution_time:.3f}秒")
    
    # ==================== 完整交易流程测试 ====================
    
    def test_complete_trading_workflow(self):
        """测试完整的交易工作流程"""
        self.logger.info("测试完整交易工作流程...")
        
        try:
            # 1. 系统初始化
            hybrid_system = HybridDecisionSystem(self.system_config)
            trading_env = EnhancedTradingEnv(self.system_config)
            
            # 2. 模拟连续交易日
            trading_results = []
            portfolio_history = []
            initial_portfolio_value = 100000.0
            current_portfolio_value = initial_portfolio_value
            
            for day in range(5):  # 模拟5个交易日
                daily_results = self._simulate_trading_day(
                    hybrid_system, trading_env, day, current_portfolio_value
                )
                trading_results.append(daily_results)
                
                # 更新组合价值
                current_portfolio_value = daily_results['end_portfolio_value']
                portfolio_history.append(current_portfolio_value)
            
            # 3. 验证交易结果
            self._validate_trading_workflow_results(
                trading_results, portfolio_history, initial_portfolio_value
            )
            
            self.logger.info("✅ 完整交易工作流程测试通过")
            
        except Exception as e:
            self.fail(f"完整交易工作流程测试失败: {str(e)}")
    
    def test_multi_fund_trading_scenario(self):
        """测试多基金交易场景"""
        self.logger.info("测试多基金交易场景...")
        
        try:
            # 创建多个基金的交易系统
            fund_codes = ['FUND001', 'FUND002', 'FUND003']
            fund_systems = {}
            fund_results = {}
            
            for fund_code in fund_codes:
                fund_config = self.system_config.copy()
                fund_config['fund_specific_params'] = self._get_fund_specific_params(fund_code)
                fund_systems[fund_code] = HybridDecisionSystem(fund_config)
                fund_results[fund_code] = []
            
            # 模拟同时为多个基金做决策
            for time_step in range(10):
                market_data = self._generate_real_time_market_data(time_step)
                
                for fund_code in fund_codes:
                    decision = fund_systems[fund_code].make_decision(
                        market_data, fund_code=fund_code
                    )
                    fund_results[fund_code].append(decision)
            
            # 验证多基金结果
            self._validate_multi_fund_results(fund_results)
            
            self.logger.info("✅ 多基金交易场景测试通过")
            
        except Exception as e:
            self.fail(f"多基金交易场景测试失败: {str(e)}")
    
    def test_real_time_collaboration_workflow(self):
        """测试实时协作工作流程"""
        self.logger.info("测试实时协作工作流程...")
        
        try:
            # 创建实时协作系统
            collaboration = RealTimeCollaboration(self.system_config)
            
            # 模拟实时协作流程
            workflow_results = []
            
            # 1. 战略层决策 (每日)
            macro_data = self._generate_macro_data()
            market_data = self.historical_data[-1]  # 最新市场数据
            
            strategy_result = collaboration.strategy_layer.generate_investment_strategy(
                macro_data=macro_data,
                market_data=market_data,
                fund_code='TEST001'
            )
            
            # 2. 战术层执行 (每小时)
            for hour in range(3):  # 模拟3小时
                hourly_data = self._generate_hourly_market_data(hour)
                
                tactical_result = collaboration.tactical_layer.execute_strategy(
                    strategy_guidance=strategy_result,
                    real_time_data=hourly_data,
                    fund_code='TEST001'
                )
                
                workflow_results.append({
                    'hour': hour,
                    'tactical_result': tactical_result
                })
            
            # 验证协作工作流程结果
            self._validate_collaboration_workflow(strategy_result, workflow_results)
            
            self.logger.info("✅ 实时协作工作流程测试通过")
            
        except Exception as e:
            self.fail(f"实时协作工作流程测试失败: {str(e)}")
    
    # ==================== 系统可靠性测试 ====================
    
    def test_system_reliability_under_stress(self):
        """测试系统在压力下的可靠性"""
        self.logger.info("测试系统压力下的可靠性...")
        
        try:
            hybrid_system = HybridDecisionSystem(self.system_config)
            
            # 压力测试参数
            stress_duration = 30  # 30秒压力测试
            request_interval = 0.1  # 每100ms一个请求
            
            start_time = time.time()
            successful_requests = 0
            failed_requests = 0
            response_times = []
            
            while time.time() - start_time < stress_duration:
                request_start = time.time()
                
                try:
                    # 生成随机市场数据
                    market_data = self._generate_random_market_data()
                    
                    # 执行决策
                    decision = hybrid_system.make_decision(market_data)
                    
                    # 验证决策结果
                    if self._is_valid_decision(decision):
                        successful_requests += 1
                    else:
                        failed_requests += 1
                    
                    response_time = time.time() - request_start
                    response_times.append(response_time)
                    
                except Exception as e:
                    failed_requests += 1
                    self.logger.warning(f"压力测试请求失败: {str(e)}")
                
                # 控制请求频率
                time.sleep(request_interval)
            
            # 验证可靠性指标
            total_requests = successful_requests + failed_requests
            success_rate = successful_requests / total_requests if total_requests > 0 else 0
            avg_response_time = np.mean(response_times) if response_times else 0
            
            # 可靠性要求
            self.assertGreater(success_rate, 0.95, "成功率应该大于95%")
            self.assertLess(avg_response_time, 0.5, "平均响应时间应该小于500ms")
            
            self.logger.info(f"✅ 系统可靠性测试通过 (成功率: {success_rate:.1%}, 平均响应时间: {avg_response_time:.3f}s)")
            
        except Exception as e:
            self.fail(f"系统可靠性测试失败: {str(e)}")
    
    def test_data_consistency_across_components(self):
        """测试组件间数据一致性"""
        self.logger.info("测试组件间数据一致性...")
        
        try:
            # 创建系统组件
            hybrid_system = HybridDecisionSystem(self.system_config)
            feature_engineer = FeatureEngineer(self.system_config)
            trading_env = EnhancedTradingEnv(self.system_config)
            
            # 使用相同的市场数据
            market_data = self.historical_data[-1]
            
            # 1. 特征工程器处理
            state_vector = feature_engineer.create_basic_state_vector(market_data)
            
            # 2. 混合决策系统处理
            decision = hybrid_system.make_decision(market_data)
            
            # 3. 交易环境处理
            trading_env.reset()
            trading_env._update_market_data(market_data)
            env_state = trading_env._get_observation()
            
            # 验证数据一致性
            self.assertEqual(len(state_vector), len(env_state), "状态向量维度应该一致")
            
            # 验证关键数据点一致性
            price_from_engineer = state_vector[0]  # 假设价格是第一个特征
            price_from_env = env_state[0]
            self.assertAlmostEqual(price_from_engineer, price_from_env, places=4, 
                                   msg="价格数据应该在组件间保持一致")
            
            self.logger.info("✅ 数据一致性测试通过")
            
        except Exception as e:
            self.fail(f"数据一致性测试失败: {str(e)}")
    
    def test_long_running_system_stability(self):
        """测试长时间运行的系统稳定性"""
        self.logger.info("测试长时间运行系统稳定性...")
        
        try:
            hybrid_system = HybridDecisionSystem(self.system_config)
            
            # 长时间运行测试（模拟）
            runtime_minutes = 2  # 2分钟测试，实际部署中应该更长
            check_interval = 10  # 每10秒检查一次
            
            start_time = time.time()
            stability_checks = []
            memory_usage = []
            
            while time.time() - start_time < runtime_minutes * 60:
                check_start = time.time()
                
                try:
                    # 执行决策
                    market_data = self._generate_random_market_data()
                    decision = hybrid_system.make_decision(market_data)
                    
                    # 记录稳定性指标
                    check_duration = time.time() - check_start
                    stability_checks.append({
                        'timestamp': datetime.now(),
                        'response_time': check_duration,
                        'decision_valid': self._is_valid_decision(decision)
                    })
                    
                    # 模拟内存使用情况
                    memory_usage.append(len(str(decision)))  # 简化的内存使用指标
                    
                except Exception as e:
                    stability_checks.append({
                        'timestamp': datetime.now(),
                        'error': str(e),
                        'decision_valid': False
                    })
                
                time.sleep(check_interval)
            
            # 分析稳定性结果
            valid_decisions = sum(1 for check in stability_checks if check.get('decision_valid', False))
            stability_rate = valid_decisions / len(stability_checks) if stability_checks else 0
            
            # 检查内存泄漏（内存使用应该保持稳定）
            if len(memory_usage) > 1:
                memory_trend = np.polyfit(range(len(memory_usage)), memory_usage, 1)[0]
                self.assertLess(abs(memory_trend), 100, "内存使用应该保持稳定")
            
            self.assertGreater(stability_rate, 0.9, "长时间运行稳定性应该大于90%")
            
            self.logger.info(f"✅ 长时间运行稳定性测试通过 (稳定率: {stability_rate:.1%})")
            
        except Exception as e:
            self.fail(f"长时间运行稳定性测试失败: {str(e)}")
    
    # ==================== 业务场景测试 ====================
    
    def test_market_volatility_scenarios(self):
        """测试市场波动场景"""
        self.logger.info("测试市场波动场景...")
        
        try:
            hybrid_system = HybridDecisionSystem(self.system_config)
            
            # 测试不同市场波动场景
            scenarios = [
                {'name': '牛市', 'trend': 'bull', 'volatility': 'low'},
                {'name': '熊市', 'trend': 'bear', 'volatility': 'high'},
                {'name': '震荡市', 'trend': 'sideways', 'volatility': 'medium'},
                {'name': '极端波动', 'trend': 'volatile', 'volatility': 'extreme'}
            ]
            
            scenario_results = {}
            
            for scenario in scenarios:
                scenario_name = scenario['name']
                market_data = self._generate_scenario_market_data(scenario)
                
                # 执行多次决策以获得稳定结果
                decisions = []
                for _ in range(5):
                    decision = hybrid_system.make_decision(market_data)
                    decisions.append(decision)
                
                # 分析场景结果
                scenario_analysis = self._analyze_scenario_decisions(decisions, scenario)
                scenario_results[scenario_name] = scenario_analysis
            
            # 验证场景适应性
            self._validate_market_scenario_adaptability(scenario_results)
            
            self.logger.info("✅ 市场波动场景测试通过")
            
        except Exception as e:
            self.fail(f"市场波动场景测试失败: {str(e)}")
    
    def test_emergency_response_scenarios(self):
        """测试紧急响应场景"""
        self.logger.info("测试紧急响应场景...")
        
        try:
            collaboration = RealTimeCollaboration(self.system_config)
            
            # 模拟紧急事件
            emergency_scenarios = [
                {'type': 'market_crash', 'severity': 'high', 'price_change': -15.0},
                {'type': 'liquidity_crisis', 'severity': 'medium', 'volume_change': -80.0},
                {'type': 'regulatory_change', 'severity': 'low', 'sentiment_impact': -0.6}
            ]
            
            emergency_responses = {}
            
            for scenario in emergency_scenarios:
                # 生成紧急情况下的市场数据
                emergency_data = self._generate_emergency_market_data(scenario)
                
                # 测试紧急响应
                response = collaboration._handle_emergency_scenario(emergency_data, scenario)
                
                # 验证响应措施
                self.assertIn('emergency_action', response)
                self.assertIn('risk_mitigation', response)
                self.assertIn('response_time', response)
                
                # 响应时间应该很快
                self.assertLess(response['response_time'], 0.1, "紧急响应时间应该小于100ms")
                
                emergency_responses[scenario['type']] = response
            
            self.logger.info("✅ 紧急响应场景测试通过")
            
        except Exception as e:
            self.fail(f"紧急响应场景测试失败: {str(e)}")
    
    # ==================== 性能基准测试 ====================
    
    def test_end_to_end_performance_benchmark(self):
        """测试端到端性能基准"""
        self.logger.info("测试端到端性能基准...")
        
        try:
            # 性能基准目标
            performance_targets = {
                'decision_latency': 0.2,      # 决策延迟 <200ms
                'throughput_qps': 50,         # 吞吐量 >50 QPS
                'memory_efficiency': 500,     # 内存效率 <500MB
                'cpu_efficiency': 80          # CPU效率 <80%
            }
            
            hybrid_system = HybridDecisionSystem(self.system_config)
            
            # 1. 决策延迟测试
            latencies = []
            for _ in range(100):
                market_data = self._generate_random_market_data()
                start_time = time.time()
                decision = hybrid_system.make_decision(market_data)
                latency = time.time() - start_time
                latencies.append(latency)
            
            avg_latency = np.mean(latencies)
            p95_latency = np.percentile(latencies, 95)
            
            # 2. 吞吐量测试
            throughput_start = time.time()
            throughput_requests = 0
            
            while time.time() - throughput_start < 10:  # 10秒测试
                market_data = self._generate_random_market_data()
                decision = hybrid_system.make_decision(market_data)
                throughput_requests += 1
            
            throughput_qps = throughput_requests / 10
            
            # 验证性能指标
            self.assertLess(avg_latency, performance_targets['decision_latency'],
                            f"平均决策延迟应该小于{performance_targets['decision_latency']}s")
            self.assertGreater(throughput_qps, performance_targets['throughput_qps'],
                               f"吞吐量应该大于{performance_targets['throughput_qps']} QPS")
            
            self.logger.info(f"✅ 端到端性能基准测试通过")
            self.logger.info(f"   平均延迟: {avg_latency:.3f}s, P95延迟: {p95_latency:.3f}s")
            self.logger.info(f"   吞吐量: {throughput_qps:.1f} QPS")
            
        except Exception as e:
            self.fail(f"端到端性能基准测试失败: {str(e)}")
    
    # ==================== 辅助方法 ====================
    
    def _simulate_trading_day(self, hybrid_system, trading_env, day: int, 
                              portfolio_value: float) -> Dict[str, Any]:
        """模拟一个交易日"""
        daily_decisions = []
        start_value = portfolio_value
        
        # 模拟一天内多次决策
        for hour in range(6):  # 6小时交易时间
            market_data = self._generate_hourly_market_data(hour, day)
            decision = hybrid_system.make_decision(market_data)
            daily_decisions.append(decision)
        
        # 计算当日收益
        end_value = start_value * (1 + np.random.normal(0.001, 0.02))  # 模拟收益
        
        return {
            'day': day,
            'decisions': daily_decisions,
            'start_portfolio_value': start_value,
            'end_portfolio_value': end_value,
            'daily_return': (end_value - start_value) / start_value
        }
    
    def _validate_trading_workflow_results(self, trading_results: List[Dict], 
                                           portfolio_history: List[float], 
                                           initial_value: float) -> None:
        """验证交易工作流程结果"""
        # 验证结果完整性
        self.assertEqual(len(trading_results), 5, "应该有5个交易日的结果")
        self.assertEqual(len(portfolio_history), 5, "应该有5个组合价值记录")
        
        # 验证收益合理性
        total_return = (portfolio_history[-1] - initial_value) / initial_value
        self.assertGreater(total_return, -0.5, "总收益不应该低于-50%")
        self.assertLess(total_return, 2.0, "总收益不应该高于200%")
        
        # 验证决策质量
        for day_result in trading_results:
            self.assertIn('decisions', day_result)
            self.assertGreater(len(day_result['decisions']), 0, "每天应该有决策记录")
    
    def _validate_multi_fund_results(self, fund_results: Dict[str, List]) -> None:
        """验证多基金结果"""
        for fund_code, results in fund_results.items():
            self.assertEqual(len(results), 10, f"基金{fund_code}应该有10个决策结果")
            
            for decision in results:
                self.assertIn('final_decision', decision)
                self.assertIn('confidence', decision)
    
    def _validate_collaboration_workflow(self, strategy_result: Dict, 
                                         workflow_results: List[Dict]) -> None:
        """验证协作工作流程"""
        # 验证战略层结果
        self.assertIn('strategy_framework', strategy_result)
        
        # 验证战术层结果
        self.assertEqual(len(workflow_results), 3, "应该有3小时的战术执行结果")
        
        for result in workflow_results:
            self.assertIn('tactical_result', result)
            self.assertIn('executed', result['tactical_result'])
    
    def _is_valid_decision(self, decision: Dict[str, Any]) -> bool:
        """验证决策是否有效"""
        if not isinstance(decision, dict):
            return False
        
        required_fields = ['final_decision', 'confidence']
        for field in required_fields:
            if field not in decision:
                return False
        
        # 验证决策值
        if decision['final_decision'] not in ['buy', 'hold', 'sell']:
            return False
        
        # 验证置信度
        confidence = decision.get('confidence', 0)
        if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
            return False
        
        return True
    
    def _analyze_scenario_decisions(self, decisions: List[Dict], 
                                    scenario: Dict) -> Dict[str, Any]:
        """分析场景决策"""
        actions = [d['final_decision'] for d in decisions]
        confidences = [d['confidence'] for d in decisions]
        
        return {
            'scenario': scenario['name'],
            'dominant_action': max(set(actions), key=actions.count),
            'avg_confidence': np.mean(confidences),
            'action_distribution': {
                'buy': actions.count('buy') / len(actions),
                'hold': actions.count('hold') / len(actions),
                'sell': actions.count('sell') / len(actions)
            }
        }
    
    def _validate_market_scenario_adaptability(self, scenario_results: Dict) -> None:
        """验证市场场景适应性"""
        # 牛市应该倾向于买入
        bull_result = scenario_results.get('牛市', {})
        if bull_result:
            self.assertGreater(bull_result['action_distribution']['buy'], 0.3,
                               "牛市场景下买入比例应该较高")
        
        # 熊市应该倾向于卖出或持有
        bear_result = scenario_results.get('熊市', {})
        if bear_result:
            sell_hold_ratio = (bear_result['action_distribution']['sell'] + 
                               bear_result['action_distribution']['hold'])
            self.assertGreater(sell_hold_ratio, 0.5,
                               "熊市场景下卖出和持有比例应该较高")
    
    # ==================== 数据生成方法 ====================
    
    @classmethod
    def _generate_historical_market_data(cls) -> List[Dict[str, Any]]:
        """生成历史市场数据"""
        data = []
        base_price = 1.0
        
        for i in range(30):  # 30天历史数据
            price_change = np.random.normal(0, 0.02)
            base_price *= (1 + price_change)
            
            data.append({
                'timestamp': (datetime.now() - timedelta(days=30-i)).isoformat(),
                'price_data': {
                    'current_price': base_price,
                    'change_pct': price_change * 100,
                    'volume': np.random.randint(1000000, 5000000)
                },
                'technical_analysis': {
                    'indicators': {
                        'rsi': np.random.uniform(30, 70),
                        'macd': np.random.normal(0, 0.01),
                        'ma5': base_price * np.random.uniform(0.98, 1.02),
                        'ma20': base_price * np.random.uniform(0.95, 1.05)
                    }
                },
                'evaluations': {}
            })
        
        return data
    
    def _generate_real_time_market_data(self, time_step: int) -> Dict[str, Any]:
        """生成实时市场数据"""
        return {
            'timestamp': datetime.now().isoformat(),
            'price_data': {
                'current_price': 1.0 + np.random.normal(0, 0.02),
                'change_pct': np.random.normal(0, 2),
                'volume': np.random.randint(1000000, 3000000)
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': np.random.uniform(20, 80),
                    'macd': np.random.normal(0, 0.02)
                }
            }
        }
    
    def _generate_random_market_data(self) -> Dict[str, Any]:
        """生成随机市场数据"""
        return {
            'timestamp': datetime.now().isoformat(),
            'price_data': {
                'current_price': np.random.uniform(0.8, 1.5),
                'change_pct': np.random.normal(0, 3),
                'volume': np.random.randint(500000, 5000000)
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': np.random.uniform(10, 90),
                    'macd': np.random.normal(0, 0.05),
                    'ma5': np.random.uniform(0.8, 1.5),
                    'ma20': np.random.uniform(0.8, 1.5)
                }
            },
            'evaluations': {}
        }
    
    def _generate_hourly_market_data(self, hour: int, day: int = 0) -> Dict[str, Any]:
        """生成小时级市场数据"""
        base_time = datetime.now() - timedelta(days=day) + timedelta(hours=hour)
        
        return {
            'timestamp': base_time.isoformat(),
            'price_data': {
                'current_price': 1.0 + np.random.normal(0, 0.01),
                'change_pct': np.random.normal(0, 1),
                'volume': np.random.randint(800000, 2000000)
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': np.random.uniform(40, 60),
                    'macd': np.random.normal(0, 0.01)
                }
            }
        }
    
    def _generate_scenario_market_data(self, scenario: Dict) -> Dict[str, Any]:
        """根据场景生成市场数据"""
        trend = scenario['trend']
        volatility = scenario['volatility']
        
        # 根据趋势调整价格变化
        if trend == 'bull':
            price_change = np.random.normal(0.02, 0.01)
        elif trend == 'bear':
            price_change = np.random.normal(-0.02, 0.01)
        elif trend == 'sideways':
            price_change = np.random.normal(0, 0.005)
        else:  # volatile
            price_change = np.random.normal(0, 0.05)
        
        # 根据波动性调整RSI
        if volatility == 'low':
            rsi = np.random.uniform(45, 55)
        elif volatility == 'medium':
            rsi = np.random.uniform(30, 70)
        elif volatility == 'high':
            rsi = np.random.uniform(20, 80)
        else:  # extreme
            rsi = np.random.uniform(10, 90)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'price_data': {
                'current_price': 1.0 * (1 + price_change),
                'change_pct': price_change * 100,
                'volume': np.random.randint(1000000, 3000000)
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': rsi,
                    'macd': np.random.normal(0, 0.02)
                }
            },
            'scenario_info': scenario
        }
    
    def _generate_emergency_market_data(self, scenario: Dict) -> Dict[str, Any]:
        """生成紧急情况市场数据"""
        return {
            'timestamp': datetime.now().isoformat(),
            'price_data': {
                'current_price': 1.0 * (1 + scenario.get('price_change', 0) / 100),
                'change_pct': scenario.get('price_change', 0),
                'volume': 2000000 * (1 + scenario.get('volume_change', 0) / 100)
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': 20 if scenario.get('price_change', 0) < -10 else 80,
                    'macd': scenario.get('price_change', 0) / 1000
                }
            },
            'emergency_flags': {
                'type': scenario['type'],
                'severity': scenario['severity'],
                'detected_at': datetime.now().isoformat()
            }
        }
    
    def _generate_macro_data(self) -> Dict[str, Any]:
        """生成宏观数据"""
        return {
            'interest_rate': np.random.uniform(2.0, 5.0),
            'inflation_rate': np.random.uniform(1.0, 4.0),
            'gdp_growth': np.random.uniform(3.0, 8.0),
            'policy_sentiment': np.random.choice(['positive', 'neutral', 'negative'])
        }
    
    @classmethod
    def _create_test_scenarios(cls) -> List[Dict[str, Any]]:
        """创建测试场景"""
        return [
            {'name': '正常交易', 'type': 'normal', 'duration': '1D'},
            {'name': '高频交易', 'type': 'high_frequency', 'duration': '1H'},
            {'name': '长期投资', 'type': 'long_term', 'duration': '30D'},
            {'name': '风险控制', 'type': 'risk_control', 'duration': '1D'}
        ]
    
    @classmethod
    def _create_system_config(cls) -> Dict[str, Any]:
        """创建系统配置"""
        return {
            'collaboration_mode': 'adaptive',
            'decision_threshold': 0.6,
            'llm_weight': 0.6,
            'rl_weight': 0.4,
            'state_dim': 50,
            'action_dim': 2,
            'hidden_dim': 128,
            'max_episodes': 50,
            'enable_llm_enhancement': True,
            'risk_tolerance': 'medium',
            'update_frequency': '1H',
            'model_save_path': cls.temp_dir
        }
    
    def _get_fund_specific_params(self, fund_code: str) -> Dict[str, Any]:
        """获取基金特定参数"""
        params_map = {
            'FUND001': {'risk_level': 'low', 'investment_style': 'conservative'},
            'FUND002': {'risk_level': 'medium', 'investment_style': 'balanced'},
            'FUND003': {'risk_level': 'high', 'investment_style': 'aggressive'}
        }
        return params_map.get(fund_code, {'risk_level': 'medium', 'investment_style': 'balanced'})


class EndToEndTestRunner:
    """端到端测试运行器"""
    
    @staticmethod
    def run_end_to_end_tests():
        """运行端到端测试"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 创建测试套件
        suite = unittest.TestLoader().loadTestsFromTestCase(EndToEndTestSuite)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        # 返回测试结果
        return {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun if result.testsRun > 0 else 0,
            'details': {
                'failures': result.failures,
                'errors': result.errors
            }
        }


if __name__ == '__main__':
    # 直接运行测试
    runner = EndToEndTestRunner()
    result = runner.run_end_to_end_tests()
    
    print(f"\n{'='*50}")
    print(f"端到端测试结果:")
    print(f"运行测试: {result['tests_run']}")
    print(f"失败: {result['failures']}")
    print(f"错误: {result['errors']}")
    print(f"成功率: {result['success_rate']:.1%}")
    print(f"{'='*50}") 