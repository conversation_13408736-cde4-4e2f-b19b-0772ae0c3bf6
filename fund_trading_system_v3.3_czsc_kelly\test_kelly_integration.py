"""
测试凯利公式集成效果
验证凯利仓位计算是否正确集成到主系统中
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from coordinators.kelly_position_coordinator import KellyPositionCoordinator
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3


def test_kelly_coordinator_standalone():
    """测试凯利协调器独立功能"""
    print("=" * 60)
    print("🧪 测试凯利协调器独立功能")
    print("=" * 60)
    
    # 模拟分析结果
    mock_analysis_result = {
        'fund_code': '518880',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.66,
            'weighted_score': 0.274
        },
        'final_decision': 'buy',
        'final_confidence': 0.66,
        'technical_data': {
            'current_price': 7.525,
            '综合评分': 0.6,
            'support_resistance': {
                'support': 7.520,
                'resistance': 7.580
            }
        },
        'gua_data': {
            '综合评分': 0.55
        },
        'flow_data': {
            '综合评分': 0.62
        },
        'llm_analysis': {
            'confidence': 0.65,
            'market_sentiment': '谨慎',
            'strategy_suggestion': '采用CZSC缠论区间交易策略'
        }
    }
    
    try:
        # 初始化凯利协调器
        kelly_coordinator = KellyPositionCoordinator({
            'enable_display': True,
            'enable_detailed_logging': True,
            'kelly_method': 'fractional',
            'kelly_fraction': 0.25,
            'max_position': 0.25,
            'min_position': 0.01
        })
        
        # 计算凯利仓位
        kelly_result = kelly_coordinator.calculate_kelly_position(mock_analysis_result)
        
        print("\n✅ 凯利协调器测试成功")
        print(f"基金代码: {kelly_result['fund_code']}")
        print(f"建议仓位: {kelly_result['kelly_calculation']['optimal_position']:.2%}")
        print(f"风险等级: {kelly_result['kelly_calculation']['risk_level']}")
        print(f"推理说明: {kelly_result['position_reasoning']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 凯利协调器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_agent_coordinator_integration():
    """测试多智能体协调器集成效果"""
    print("\n" + "=" * 60)
    print("🧪 测试多智能体协调器集成")
    print("=" * 60)
    
    try:
        # 初始化多智能体协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 检查凯利协调器是否正确初始化
        if hasattr(coordinator, 'kelly_coordinator'):
            print("✅ 凯利协调器已成功集成到多智能体协调器")
            
            # 测试基金分析（使用一个简单的基金代码）
            test_fund_code = '518880'
            print(f"\n🔍 测试基金分析: {test_fund_code}")
            
            # 执行协调分析
            analysis_result = coordinator.coordinate_analysis(test_fund_code)
            
            # 检查是否包含凯利分析结果
            if 'kelly_analysis' in analysis_result:
                kelly_analysis = analysis_result['kelly_analysis']
                kelly_calc = kelly_analysis.get('kelly_calculation', {})
                
                print("✅ 凯利分析结果已成功集成")
                print(f"   基金代码: {kelly_analysis.get('fund_code', 'N/A')}")
                print(f"   建议仓位: {kelly_calc.get('optimal_position', 0):.2%}")
                print(f"   胜率: {kelly_calc.get('win_probability', 0):.1%}")
                print(f"   盈亏比: {kelly_calc.get('risk_reward_ratio', 0):.2f}")
                print(f"   风险等级: {kelly_calc.get('risk_level', 'N/A')}")
                
                return True
            else:
                print("❌ 凯利分析结果未找到")
                return False
                
        else:
            print("❌ 凯利协调器未正确集成")
            return False
            
    except Exception as e:
        print(f"❌ 多智能体协调器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_display_integration():
    """测试显示集成效果"""
    print("\n" + "=" * 60)
    print("🧪 测试显示集成效果")
    print("=" * 60)
    
    try:
        # 模拟完整的分析结果（包含凯利分析）
        mock_full_result = {
            'fund_code': '518880',
            'enhanced_decision': {
                'decision': 'buy',
                'confidence': 0.66,
                'weighted_score': 0.274,
                'market_classification': {
                    'primary_classification': '震荡整理'
                }
            },
            'final_decision': 'buy',
            'final_confidence': 0.66,
            'llm_analysis': {
                'market_sentiment': '谨慎',
                'confidence_level': 0.65,
                'strategy_suggestion': '采用CZSC缠论区间交易策略：1) 7.5250上方轻仓试多，止损设于7.5200(底分型下方)；2)若放量突破7.5480可加仓，目标位7.5800(线段3的0.382回撤位)；3)若出现顶分型跌破7.5250则反手做空，止损7.5300。仓位控制在30%以内，严格遵循笔破坏原则',
                'market_drivers': [
                    'CZSC缠论技术指标显示MA5(7.4536)已上穿MA20(7.4024)形成短期多头排列',
                    'MACD柱状图正值扩大(0.0165)确认动能增强',
                    '成交量比率1.1473高于均值配合分型结构'
                ]
            },
            'kelly_analysis': {
                'fund_code': '518880',
                'kelly_calculation': {
                    'win_probability': 0.58,
                    'risk_reward_ratio': 1.6,
                    'confidence': 0.66,
                    'kelly_fraction': 0.1125,
                    'optimal_position': 0.0743,  # 7.43%
                    'adjusted_position': 0.0743,
                    'risk_level': '中等风险',
                    'recommendation': '建议做多，适度建仓(7.4%)，中等风险'
                },
                'position_reasoning': '交易决策：BUY | 胜率评估：58.0% | 风险收益比：1.60 | 综合置信度：66.0% | 凯利分数：11.25% | 建议仓位：7.43% | 风险等级：中等风险 | 建议以7.4%仓位做多',
                'timestamp': '2024-01-01T10:00:00'
            }
        }
        
        # 导入主系统的显示方法
        from system.enhanced_trading_system import EnhancedFundTradingSystemV3
        
        # 创建系统实例
        trading_system = EnhancedFundTradingSystemV3()
        
        print("🖥️ 模拟系统显示输出:")
        print("-" * 40)
        
        # 调用显示方法
        trading_system.display_detailed_analysis_result(mock_full_result)
        
        print("-" * 40)
        print("✅ 显示集成测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 显示集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    # 设置日志
    setup_logging()
    
    print("🚀 凯利公式集成测试开始")
    print("=" * 80)
    
    test_results = []
    
    # 测试1：凯利协调器独立功能
    result1 = test_kelly_coordinator_standalone()
    test_results.append(("凯利协调器独立功能", result1))
    
    # 测试2：多智能体协调器集成
    result2 = test_multi_agent_coordinator_integration()
    test_results.append(("多智能体协调器集成", result2))
    
    # 测试3：显示集成效果
    result3 = test_display_integration()
    test_results.append(("显示集成效果", result3))
    
    # 汇总测试结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 80)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！凯利公式已成功集成到系统中")
        print("\n💡 现在运行 python main.py 应该能看到凯利仓位计算结果")
    else:
        print("⚠️ 部分测试失败，请检查集成问题")
    
    print("=" * 80)


if __name__ == "__main__":
    main()