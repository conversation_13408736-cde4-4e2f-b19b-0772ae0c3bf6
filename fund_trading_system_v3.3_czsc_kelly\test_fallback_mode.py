"""
测试系统兜底模式
验证在SSL不可用时，系统是否能正常运行基本功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_llm_analyzer_fallback():
    """测试LLM分析器的兜底模式"""
    print("🧪 测试LLM分析器兜底模式")
    print("="*50)
    
    try:
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        
        # 初始化分析器
        analyzer = LLMMarketAnalyzer()
        print(f"✅ LLM分析器初始化成功")
        print(f"📊 客户端状态: {'可用' if analyzer.client else '兜底模式'}")
        
        # 准备测试数据
        test_market_data = {
            'price': 1.2345,
            'change_pct': 2.15,
            'volume': 1500000,
            'ma5': 1.2345,
            'ma20': 1.2100,
            'rsi': 65.5,
            'macd': 0.0123,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 测试市场分析
        print("\n🔍 测试市场分析功能...")
        analysis_result = analyzer.analyze_market_narrative(
            market_data=test_market_data,
            fund_code="000001"
        )
        
        print("✅ 市场分析完成")
        print(f"📈 分析结果类型: {type(analysis_result)}")
        print(f"📊 结果字段: {list(analysis_result.keys()) if isinstance(analysis_result, dict) else 'N/A'}")
        
        # 显示关键结果
        if isinstance(analysis_result, dict):
            print(f"\n📋 分析摘要:")
            print(f"   市场情绪: {analysis_result.get('market_sentiment', 'N/A')}")
            print(f"   置信度: {analysis_result.get('confidence_level', 'N/A')}")
            print(f"   策略建议: {analysis_result.get('strategy_suggestion', 'N/A')}")
            
            if 'market_drivers' in analysis_result:
                print(f"   市场驱动因素: {len(analysis_result['market_drivers'])}个")
            if 'risk_points' in analysis_result:
                print(f"   风险点: {len(analysis_result['risk_points'])}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n🔧 测试系统集成")
    print("="*50)
    
    try:
        # 测试配置加载
        try:
            from config.ai_config import get_ai_config
            config = get_ai_config()
            print("✅ AI配置加载成功")
            print(f"📋 配置提供商数量: {len(config.providers) if hasattr(config, 'providers') else 'N/A'}")
        except Exception as e:
            print(f"⚠️ AI配置加载失败: {e}")
        
        # 测试数据结构
        try:
            from core.data_structures import DimensionEvaluationResult
            print("✅ 核心数据结构导入成功")
        except Exception as e:
            print(f"❌ 数据结构导入失败: {e}")
        
        # 测试日志系统
        import logging
        logger = logging.getLogger("TestLogger")
        logger.info("测试日志消息")
        print("✅ 日志系统正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 启动系统兜底模式测试")
    print("="*60)
    
    # 测试结果
    results = []
    
    # 测试LLM分析器
    llm_ok = test_llm_analyzer_fallback()
    results.append(("LLM分析器", llm_ok))
    
    # 测试系统集成
    integration_ok = test_system_integration()
    results.append(("系统集成", integration_ok))
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("-" * 30)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("-" * 30)
    if all_passed:
        print("🎉 所有测试通过！系统兜底模式正常工作")
        print("💡 建议: 系统可以在当前环境下正常运行")
    else:
        print("⚠️ 部分测试失败")
        print("💡 建议: 检查失败的组件")
    
    print("\n🔍 环境信息:")
    print(f"   Python版本: {sys.version}")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   项目目录: {current_dir}")

if __name__ == "__main__":
    main()