import pandas as pd
import simplejson as json
from functools import partial
import requests


class DataApiNew:

    __token = ''
    # __http_url = 'http://db.datakkk.com/tushare'
    __http_url = 'https://apix.mega.tech/gate/ts'

    def __init__(self, token='luo_I5FczI', timeout=15):    # 'http://106.55.199.120/tushare'
        """
        Parameters
        ----------
        token: str
            API接口TOKEN，用于用户认证
        """
        self.__token = token
        self.__timeout = timeout

    def query(self, api_name, fields='', **kwargs):
        req_params = {
            'api_name': api_name,
            'token': self.__token,
            'params': kwargs,
            'fields': fields
        }

        res = requests.post(self.__http_url, json=req_params, timeout=self.__timeout)
        result = json.loads(res.text)
        if result['code'] != 0:
            raise Exception(result['msg'])
        data = result['data']
        columns = data['fields']
        items = data['items']

        return pd.DataFrame(items, columns=columns)

    def __getattr__(self, name):
        return partial(self.query, name)


def ts_pro():
    import tushare as ts
    pro = ts.pro_api()
    pro._DataApi__http_url = 'https://apix.mega.tech/gate/ts'
    pro._DataApi__token = 'luo_I5FczI'
    return pro

# def ts_pro_bar(ts_code, start_date, end_date, freq):
#     import tushare as ts
#     ts.pro.data_pro.client.DataApi._DataApi__http_url = 'http://db.datakkk.com/tushare'
#     ts.pro.data_pro.client.DataApi._DataApi__token = 'luo_I5FczI'
#     df = ts.pro_bar(ts_code=ts_code, start_date=start_date, end_date=end_date, freq=freq)
#     return df




if __name__ == '__main__':

    pro = DataApiNew()
    # pro = ts_pro()
    print(pro.stock_basic())
    print(pro.stk_mins(ts_code='600000.SH', start_date='20230901', end_date='20231026', freq='60min'))
    print(pro.fina_indicator_vip(period='20230930'))


    """
    def get_tick(code):
        df = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction_realtime(code)
        df['new_order'] = df['order'].groupby(df['datetime']).rank()
        df.index = df.index + timedelta(seconds=3)*((df['new_order']-1)%20)
        return df
    
    # 重组15秒的K线
    def get_czsc_tick(code):
        df = QA.QAFetch.QATdx.QA_fetch_get_stock_transaction_realtime(code)
        df['new_order'] = df['order'].groupby(df['datetime']).rank()
        df.index = df.index + timedelta(seconds=3)*((df['new_order']-1)%20)
        df_new = df['price'].resample('15s').ohlc()
        df_new['vol'] = df['vol'].resample('15s').sum()
        df_new = df_new.dropna()
        df_new['symbol'] = code
        df_new['dt'] = df_new.index
        df_new.reset_index(drop=True, inplace=True)
        return df_new
    """
    # import QAStrategy