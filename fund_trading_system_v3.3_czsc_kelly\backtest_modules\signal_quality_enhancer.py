"""
信号质量增强器
结合机器学习和统计分析提升CZSC信号质量
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score
import logging

logger = logging.getLogger(__name__)

class SignalQualityEnhancer:
    """信号质量增强器，结合机器学习提升CZSC信号质量"""
    
    def __init__(self, backtest_system):
        self.backtest_system = backtest_system
        self.feature_engineer = backtest_system.feature_engineer
        self.model_trainer = backtest_system.model_trainer
        self.quality_model = None
        self.signal_history = []
        self.enhancement_log = []
        
    def enhance_czsc_signals(self, df: pd.DataFrame, czsc_results: List) -> List:
        """增强CZSC信号质量"""
        try:
            logger.info("开始增强CZSC信号质量")
            
            # 1. 特征工程增强
            df_featured = self._create_enhanced_features(df)
            
            # 2. 信号质量评估
            signal_quality_scores = self._evaluate_signal_quality(czsc_results, df_featured)
            
            # 3. 机器学习过滤
            enhanced_results = self._ml_signal_filter(czsc_results, signal_quality_scores, df_featured)
            
            # 4. 记录增强日志
            self.enhancement_log.append({
                'timestamp': pd.Timestamp.now(),
                'original_signals': len(czsc_results),
                'enhanced_signals': len(enhanced_results),
                'improvement_ratio': len(enhanced_results) / len(czsc_results) if czsc_results else 0
            })
            
            logger.info(f"信号增强完成: {len(czsc_results)} -> {len(enhanced_results)}")
            return enhanced_results
            
        except Exception as e:
            logger.error(f"信号增强失败: {str(e)}")
            return czsc_results  # 返回原始信号
    
    def _create_enhanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建增强特征"""
        df_featured = df.copy()
        
        # 技术指标特征
        try:
            if hasattr(self.feature_engineer, 'create_technical_features'):
                df_featured = self.feature_engineer.create_technical_features(df_featured)
            else:
                # 使用简化的技术指标创建
                df_featured = self._create_basic_technical_features(df_featured)
        except Exception as e:
            logger.warning(f"技术指标特征创建失败: {e}")
            df_featured = self._create_basic_technical_features(df_featured)
        
        # 统计特征
        try:
            if hasattr(self.feature_engineer, 'create_statistical_features'):
                df_featured = self.feature_engineer.create_statistical_features(df_featured)
            else:
                df_featured = self._create_basic_statistical_features(df_featured)
        except Exception as e:
            logger.warning(f"统计特征创建失败: {e}")
            df_featured = self._create_basic_statistical_features(df_featured)
        
        # 自定义CZSC相关特征
        df_featured = self._create_czsc_specific_features(df_featured)
        
        return df_featured
    
    def _create_basic_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建基础技术指标特征"""
        try:
            if 'close' in df.columns:
                # RSI
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                df['rsi'] = 100 - (100 / (1 + rs))
                
                # 移动平均
                df['ma5'] = df['close'].rolling(5).mean()
                df['ma20'] = df['close'].rolling(20).mean()
                
                # MACD
                ema12 = df['close'].ewm(span=12).mean()
                ema26 = df['close'].ewm(span=26).mean()
                df['macd'] = ema12 - ema26
                df['macd_signal'] = df['macd'].ewm(span=9).mean()
            
            if 'volume' in df.columns:
                df['volume_ma5'] = df['volume'].rolling(5).mean()
                df['volume_ratio'] = df['volume'] / df['volume_ma5']
                
        except Exception as e:
            logger.error(f"基础技术指标创建失败: {e}")
        
        return df
    
    def _create_basic_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建基础统计特征"""
        try:
            if 'close' in df.columns:
                # 价格变化率
                df['price_change'] = df['close'].pct_change()
                
                # 波动率
                df['volatility_5d'] = df['price_change'].rolling(5).std()
                df['volatility_20d'] = df['price_change'].rolling(20).std()
                
                # 价格位置
                df['price_position_20d'] = (df['close'] - df['close'].rolling(20).min()) / (df['close'].rolling(20).max() - df['close'].rolling(20).min())
                
        except Exception as e:
            logger.error(f"基础统计特征创建失败: {e}")
        
        return df
    
    def _create_czsc_specific_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建CZSC特定特征"""
        # 笔的强度特征
        df['bi_strength'] = self._calculate_bi_strength(df)
        
        # 中枢特征
        df['zhongshu_strength'] = self._calculate_zhongshu_strength(df)
        
        # 背驰特征
        df['beichi_signal'] = self._calculate_beichi_signal(df)
        
        # 趋势一致性特征
        df['trend_consistency'] = self._calculate_trend_consistency(df)
        
        return df
    
    def _calculate_bi_strength(self, df: pd.DataFrame) -> pd.Series:
        """计算笔的强度"""
        # 简化的笔强度计算
        price_change = df['close'].pct_change()
        volume_change = df['volume'].pct_change()
        
        # 价量配合度
        strength = abs(price_change) * (1 + abs(volume_change))
        return strength.rolling(window=5).mean()
    
    def _calculate_zhongshu_strength(self, df: pd.DataFrame) -> pd.Series:
        """计算中枢强度"""
        # 简化的中枢强度计算
        high_ma = df['high'].rolling(window=9).mean()
        low_ma = df['low'].rolling(window=9).mean()
        
        # 中枢区间大小
        zhongshu_range = (high_ma - low_ma) / df['close']
        return zhongshu_range
    
    def _calculate_beichi_signal(self, df: pd.DataFrame) -> pd.Series:
        """计算背驰信号"""
        # 简化的背驰计算
        price_momentum = df['close'].rolling(window=5).apply(lambda x: (x[-1] - x[0]) / x[0])
        volume_momentum = df['volume'].rolling(window=5).apply(lambda x: (x[-1] - x[0]) / x[0])
        
        # 价量背驰
        beichi = price_momentum - volume_momentum
        return beichi
    
    def _calculate_trend_consistency(self, df: pd.DataFrame) -> pd.Series:
        """计算趋势一致性"""
        # 多周期趋势一致性
        ma5_trend = df['close'].rolling(5).mean().pct_change()
        ma20_trend = df['close'].rolling(20).mean().pct_change()
        ma60_trend = df['close'].rolling(60).mean().pct_change()
        
        # 趋势方向一致性
        consistency = np.sign(ma5_trend) * np.sign(ma20_trend) * np.sign(ma60_trend)
        return consistency
    
    def _evaluate_signal_quality(self, czsc_results: List, df_featured: pd.DataFrame) -> List[float]:
        """评估信号质量"""
        quality_scores = []
        
        for result in czsc_results:
            try:
                # 技术指标一致性
                technical_consistency = self._check_technical_consistency(result, df_featured)
                
                # 历史成功率
                historical_success = self._calculate_historical_success_rate(result)
                
                # 市场环境适应性
                market_fit = self._assess_market_environment_fit(result, df_featured)
                
                # 信号强度
                signal_strength = getattr(result, 'score', 0.5)
                
                # 综合质量评分
                quality_score = (
                    technical_consistency * 0.3 +
                    historical_success * 0.3 +
                    market_fit * 0.2 +
                    signal_strength * 0.2
                )
                
                quality_scores.append(max(0.0, min(1.0, quality_score)))
                
            except Exception as e:
                logger.warning(f"评估信号质量失败: {str(e)}")
                quality_scores.append(0.5)  # 默认中等质量
        
        return quality_scores
    
    def _check_technical_consistency(self, signal, df_featured: pd.DataFrame) -> float:
        """检查技术指标一致性"""
        try:
            # 获取信号对应的时间点数据
            latest_data = df_featured.iloc[-1]
            
            consistency_score = 0.0
            checks = 0
            
            # RSI一致性检查
            if 'rsi' in latest_data:
                rsi = latest_data['rsi']
                if hasattr(signal.state, 'name'):
                    if 'UPTREND' in signal.state.name and rsi > 50:
                        consistency_score += 1
                    elif 'DOWNTREND' in signal.state.name and rsi < 50:
                        consistency_score += 1
                checks += 1
            
            # MACD一致性检查
            if 'macd' in latest_data and 'macd_signal' in latest_data:
                macd_diff = latest_data['macd'] - latest_data['macd_signal']
                if hasattr(signal.state, 'name'):
                    if 'UPTREND' in signal.state.name and macd_diff > 0:
                        consistency_score += 1
                    elif 'DOWNTREND' in signal.state.name and macd_diff < 0:
                        consistency_score += 1
                checks += 1
            
            # 移动平均线一致性检查
            if 'ma5' in latest_data and 'ma20' in latest_data:
                ma_trend = latest_data['ma5'] > latest_data['ma20']
                if hasattr(signal.state, 'name'):
                    if 'UPTREND' in signal.state.name and ma_trend:
                        consistency_score += 1
                    elif 'DOWNTREND' in signal.state.name and not ma_trend:
                        consistency_score += 1
                checks += 1
            
            return consistency_score / checks if checks > 0 else 0.5
            
        except Exception as e:
            logger.warning(f"技术指标一致性检查失败: {str(e)}")
            return 0.5
    
    def _calculate_historical_success_rate(self, signal) -> float:
        """计算历史成功率"""
        try:
            # 从信号历史中查找相似信号的成功率
            similar_signals = [
                s for s in self.signal_history 
                if s.get('dimension_name') == getattr(signal, 'dimension_name', '') and
                   s.get('state') == getattr(signal, 'state', None)
            ]
            
            if not similar_signals:
                return 0.6  # 默认成功率
            
            success_count = sum(1 for s in similar_signals if s.get('success', False))
            return success_count / len(similar_signals)
            
        except Exception as e:
            logger.warning(f"历史成功率计算失败: {str(e)}")
            return 0.6
    
    def _assess_market_environment_fit(self, signal, df_featured: pd.DataFrame) -> float:
        """评估市场环境适应性"""
        try:
            latest_data = df_featured.iloc[-1]
            
            # 波动率适应性
            volatility = df_featured['close'].pct_change().rolling(20).std().iloc[-1]
            
            # 趋势强度适应性
            trend_strength = abs(latest_data.get('trend_consistency', 0))
            
            # 成交量适应性
            volume_ratio = latest_data.get('volume_ratio', 1.0)
            
            # 综合适应性评分
            fit_score = (
                min(1.0, 1 / (1 + volatility * 10)) * 0.4 +  # 波动率适应
                trend_strength * 0.4 +  # 趋势强度适应
                min(1.0, volume_ratio / 2) * 0.2  # 成交量适应
            )
            
            return max(0.0, min(1.0, fit_score))
            
        except Exception as e:
            logger.warning(f"市场环境适应性评估失败: {str(e)}")
            return 0.5
    
    def _ml_signal_filter(self, czsc_results: List, quality_scores: List[float], 
                         df_featured: pd.DataFrame) -> List:
        """使用机器学习过滤信号"""
        try:
            # 如果质量模型未训练，先训练
            if self.quality_model is None:
                self._train_quality_model(df_featured)
            
            enhanced_results = []
            
            for i, (result, quality_score) in enumerate(zip(czsc_results, quality_scores)):
                # 基础质量过滤
                if quality_score < 0.4:
                    continue
                
                # 机器学习预测
                if self.quality_model is not None:
                    features = self._extract_signal_features(result, df_featured)
                    if features is not None and len(features) > 0:
                        try:
                            ml_prediction = self.quality_model.predict_proba([features])
                            if len(ml_prediction) > 0 and len(ml_prediction[0]) > 1:
                                ml_score = ml_prediction[0][1]
                            else:
                                ml_score = 0.5  # 默认值
                        except Exception as e:
                            logger.warning(f"ML预测失败: {e}")
                            ml_score = 0.5
                        
                        # 综合评分
                        final_score = quality_score * 0.6 + ml_score * 0.4
                        
                        if final_score > 0.5:
                            # 更新结果的置信度
                            if hasattr(result, 'confidence'):
                                result.confidence = final_score
                            enhanced_results.append(result)
                    else:
                        # 如果特征提取失败，使用质量评分
                        if quality_score > 0.5:
                            enhanced_results.append(result)
                else:
                    # 如果没有ML模型，使用质量评分过滤
                    if quality_score > 0.5:
                        enhanced_results.append(result)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"机器学习信号过滤失败: {str(e)}")
            # 返回基于质量评分的过滤结果
            return [result for result, score in zip(czsc_results, quality_scores) if score > 0.5]
    
    def _train_quality_model(self, df_featured: pd.DataFrame):
        """训练信号质量模型"""
        try:
            # 这里应该使用历史数据训练模型
            # 简化实现：创建一个基础的随机森林模型
            logger.info("训练信号质量模型")
            
            # 模拟训练数据（实际应用中应使用真实历史数据）
            n_samples = min(1000, len(df_featured))
            if n_samples < 100:
                logger.warning("训练数据不足，跳过模型训练")
                return
            
            # 创建特征和标签
            features = []
            labels = []
            
            for i in range(n_samples):
                idx = np.random.randint(0, len(df_featured))
                feature_vector = self._create_feature_vector(df_featured.iloc[idx])
                if feature_vector is not None:
                    features.append(feature_vector)
                    # 模拟标签（实际应用中应使用真实的信号成功/失败标签）
                    labels.append(np.random.choice([0, 1], p=[0.4, 0.6]))
            
            if len(features) > 50:
                self.quality_model = RandomForestClassifier(n_estimators=100, random_state=42)
                self.quality_model.fit(features, labels)
                logger.info("信号质量模型训练完成")
            else:
                logger.warning("特征数据不足，无法训练模型")
                
        except Exception as e:
            logger.error(f"模型训练失败: {str(e)}")
    
    def _extract_signal_features(self, signal, df_featured: pd.DataFrame) -> List[float]:
        """提取信号特征用于ML预测"""
        try:
            latest_data = df_featured.iloc[-1]
            return self._create_feature_vector(latest_data)
        except Exception as e:
            logger.warning(f"特征提取失败: {str(e)}")
            return None
    
    def _create_feature_vector(self, data_row) -> List[float]:
        """创建特征向量"""
        try:
            features = []
            
            # 技术指标特征
            feature_names = ['rsi', 'macd', 'ma5', 'ma20', 'volume_ratio', 
                           'bi_strength', 'zhongshu_strength', 'beichi_signal', 
                           'trend_consistency']
            
            for name in feature_names:
                value = data_row.get(name, 0)
                if pd.isna(value):
                    value = 0
                features.append(float(value))
            
            return features if len(features) == len(feature_names) else None
            
        except Exception as e:
            logger.warning(f"特征向量创建失败: {str(e)}")
            return None
    
    def update_signal_history(self, signal, success: bool):
        """更新信号历史记录"""
        self.signal_history.append({
            'timestamp': pd.Timestamp.now(),
            'dimension_name': getattr(signal, 'dimension_name', ''),
            'state': getattr(signal, 'state', None),
            'score': getattr(signal, 'score', 0),
            'success': success
        })
        
        # 保持历史记录在合理范围内
        if len(self.signal_history) > 10000:
            self.signal_history = self.signal_history[-5000:]
    
    def get_enhancement_statistics(self) -> Dict[str, Any]:
        """获取增强统计信息"""
        if not self.enhancement_log:
            return {}
        
        recent_logs = self.enhancement_log[-100:]  # 最近100次记录
        
        return {
            'total_enhancements': len(self.enhancement_log),
            'average_improvement_ratio': np.mean([log['improvement_ratio'] for log in recent_logs]),
            'signal_history_size': len(self.signal_history),
            'model_trained': self.quality_model is not None,
            'last_enhancement': self.enhancement_log[-1]['timestamp'] if self.enhancement_log else None
        }