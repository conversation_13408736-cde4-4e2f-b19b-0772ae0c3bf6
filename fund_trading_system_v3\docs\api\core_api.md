# 核心模块 API 文档

## 枚举类型 (core.enums)

### TrendState
趋势状态枚举，定义市场趋势的不同状态。

```python
from core.enums import TrendState

# 可用状态
TrendState.STRONG_UPTREND     # 强上涨趋势
TrendState.WEAK_UPTREND       # 弱上涨趋势  
TrendState.SIDEWAYS           # 横盘整理
TrendState.WEAK_DOWNTREND     # 弱下跌趋势
TrendState.STRONG_DOWNTREND   # 强下跌趋势
```

### VolatilityState
波动性状态枚举，定义市场波动性的不同级别。

```python
from core.enums import VolatilityState

# 可用状态
VolatilityState.EXTREMELY_LOW   # 极低波动
VolatilityState.LOW            # 低波动
VolatilityState.NORMAL         # 正常波动
VolatilityState.HIGH           # 高波动
VolatilityState.EXTREMELY_HIGH # 极高波动
```

### SignalStrength
信号强度枚举，定义交易信号的强度级别。

```python
from core.enums import SignalStrength

# 可用强度
SignalStrength.VERY_WEAK   # 0.1 - 非常弱
SignalStrength.WEAK        # 0.3 - 弱
SignalStrength.MEDIUM      # 0.5 - 中等
SignalStrength.STRONG      # 0.7 - 强
SignalStrength.VERY_STRONG # 0.9 - 非常强
```

## 数据结构 (core.data_structures)

### DimensionEvaluationResult
维度评估结果数据结构，用于存储单个维度的评估结果。

```python
from core.data_structures import DimensionEvaluationResult
from core.enums import TrendState

# 创建评估结果
result = DimensionEvaluationResult(
    dimension_name="趋势",                    # 维度名称
    state=TrendState.STRONG_UPTREND,         # 状态
    score=0.8,                               # 评分 (0-1)
    confidence=0.9,                          # 置信度 (0-1)
    signals=["上涨信号", "强势信号"],          # 信号列表
    data_quality="good",                     # 数据质量
    details={"ma_trend": "上涨"},             # 详细信息
    indicators={"ma5": 100, "ma20": 95}      # 技术指标
)

# 访问属性
print(result.dimension_name)  # "趋势"
print(result.score)           # 0.8
print(result.confidence)      # 0.9
```

### 属性说明

| 属性 | 类型 | 说明 |
|------|------|------|
| dimension_name | str | 维度名称 |
| state | Enum | 维度状态 |
| score | float | 评分 (0-1) |
| confidence | float | 置信度 (0-1) |
| signals | List[str] | 信号列表 |
| data_quality | str | 数据质量 |
| details | Dict | 详细信息 |
| indicators | Dict | 技术指标 |

## 工具函数 (core.utils)

### 可用性检查

```python
from core.utils import CZSC_FUNC_AVAILABLE, PUPPET_AVAILABLE, CZSC_AVAILABLE

# 检查功能可用性
if CZSC_FUNC_AVAILABLE:
    print("CZSC函数可用")
    
if PUPPET_AVAILABLE:
    print("Puppet交易库可用")
    
if CZSC_AVAILABLE:
    print("CZSC库可用")
```

### 常用工具函数

```python
from core.utils import *

# 获取实时报价 (需要CZSC函数可用)
if CZSC_FUNC_AVAILABLE:
    quote = get_realtime_quote('513500')
    
# 获取K线数据 (需要CZSC函数可用)
if CZSC_FUNC_AVAILABLE:
    kline = get_kline('513500', 'D')
```

## 配置 (core.config)

### 日志配置

```python
from core.config import setup_logging

# 设置日志
setup_logging()
```

### 配置参数

系统支持以下配置参数:
- 日志级别
- 输出格式
- 文件路径
- 缓存设置

## 使用示例

### 基本使用

```python
from core.enums import TrendState, SignalStrength
from core.data_structures import DimensionEvaluationResult

# 创建趋势评估结果
trend_result = DimensionEvaluationResult(
    dimension_name="趋势",
    state=TrendState.STRONG_UPTREND,
    score=0.85,
    confidence=0.92,
    signals=["MA5上穿MA20", "MACD金叉"],
    data_quality="excellent",
    details={
        "ma_trend": "强势上涨",
        "macd_signal": "金叉确认"
    },
    indicators={
        "ma5": 102.5,
        "ma20": 98.3,
        "macd": 0.45
    }
)

print(f"维度: {trend_result.dimension_name}")
print(f"状态: {trend_result.state.value}")
print(f"评分: {trend_result.score}")
print(f"置信度: {trend_result.confidence}")
```

### 错误处理

```python
try:
    # 执行分析
    result = some_analysis_function()
except Exception as e:
    logger.error(f"分析失败: {str(e)}")
    # 返回默认结果
    result = DimensionEvaluationResult(
        dimension_name="未知",
        state=TrendState.SIDEWAYS,
        score=0.0,
        confidence=0.0,
        signals=[],
        data_quality="poor",
        details={"error": str(e)},
        indicators={}
    )
```
