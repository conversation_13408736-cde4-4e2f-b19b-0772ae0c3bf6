"""
凯利公式仓位管理模块
实现多种凯利公式变体和风险控制策略
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import math

@dataclass
class KellyResult:
    """凯利公式计算结果"""
    optimal_position: float  # 最优仓位
    kelly_fraction: float   # 原始凯利分数
    adjusted_position: float  # 风险调整后仓位
    confidence_level: float  # 置信水平
    risk_level: str         # 风险等级
    recommendation: str     # 仓位建议说明

class KellyFormulaCalculator:
    """凯利公式计算器"""
    
    def __init__(self, 
                 max_position: float = 0.25,
                 min_position: float = 0.01,
                 kelly_fraction: float = 0.25):
        """
        初始化凯利公式计算器
        
        Args:
            max_position: 最大仓位限制
            min_position: 最小仓位限制
            kelly_fraction: 凯利分数调整系数（分数凯利）
        """
        self.max_position = max_position
        self.min_position = min_position
        self.kelly_fraction = kelly_fraction
        
    def calculate_optimal_position(self, 
                                 win_probability: float,
                                 win_loss_ratio: float,
                                 confidence: float = 1.0,
                                 method: str = "classic") -> KellyResult:
        """
        计算最优仓位
        
        Args:
            win_probability: 胜率 (0-1)
            win_loss_ratio: 盈亏比 (平均盈利/平均亏损)
            confidence: 置信度调整因子 (0-1)
            method: 计算方法 ("classic", "fractional", "adaptive")
        
        Returns:
            KellyResult: 计算结果
        """
        
        if method == "classic":
            return self._classic_kelly(win_probability, win_loss_ratio, confidence)
        elif method == "fractional":
            return self._fractional_kelly(win_probability, win_loss_ratio, confidence)
        elif method == "adaptive":
            return self._adaptive_kelly(win_probability, win_loss_ratio, confidence)
        else:
            raise ValueError(f"不支持的计算方法: {method}")
    
    def _classic_kelly(self, win_prob: float, win_loss_ratio: float, confidence: float) -> KellyResult:
        """经典凯利公式"""
        
        # 输入验证
        if win_prob <= 0 or win_prob >= 1:
            return self._create_zero_position_result("胜率超出有效范围")
        
        if win_loss_ratio <= 0:
            return self._create_zero_position_result("盈亏比必须大于0")
        
        # 经典凯利公式: f = (bp - q) / b
        # 其中: b = 盈亏比, p = 胜率, q = 败率
        lose_prob = 1 - win_prob
        kelly_fraction = (win_prob * win_loss_ratio - lose_prob) / win_loss_ratio
        
        # 置信度调整
        adjusted_kelly = kelly_fraction * confidence
        
        # 应用仓位限制
        final_position = np.clip(adjusted_kelly, 0, self.max_position)
        
        # 应用最小仓位限制 - 如果计算结果大于0但小于最小仓位，则设为最小仓位
        if final_position > 0 and final_position < self.min_position:
            final_position = self.min_position
        
        # 风险等级评估
        risk_level = self._assess_risk_level(kelly_fraction, win_prob, win_loss_ratio)
        
        return KellyResult(
            optimal_position=max(final_position, 0),
            kelly_fraction=kelly_fraction,
            adjusted_position=final_position,
            confidence_level=confidence,
            risk_level=risk_level,
            recommendation=self._generate_recommendation(final_position, risk_level)
        )
    
    def _fractional_kelly(self, win_prob: float, win_loss_ratio: float, confidence: float) -> KellyResult:
        """分数凯利公式（更保守）"""
        
        # 先计算经典凯利
        classic_result = self._classic_kelly(win_prob, win_loss_ratio, confidence)
        
        # 应用分数调整
        fractional_position = classic_result.kelly_fraction * self.kelly_fraction
        final_position = np.clip(fractional_position, 0, self.max_position)
        
        # 应用最小仓位限制 - 如果计算结果大于0但小于最小仓位，则设为最小仓位
        if final_position > 0 and final_position < self.min_position:
            final_position = self.min_position
        
        return KellyResult(
            optimal_position=max(final_position, 0),
            kelly_fraction=classic_result.kelly_fraction,
            adjusted_position=final_position,
            confidence_level=confidence,
            risk_level=self._assess_risk_level(fractional_position, win_prob, win_loss_ratio),
            recommendation=f"分数凯利策略({self.kelly_fraction:.0%}): " + 
                          self._generate_recommendation(final_position, classic_result.risk_level)
        )
    
    def _adaptive_kelly(self, win_prob: float, win_loss_ratio: float, confidence: float) -> KellyResult:
        """自适应凯利公式"""
        
        # 基于市场条件动态调整凯利分数
        market_volatility = self._estimate_market_volatility(win_prob, win_loss_ratio)
        adaptive_fraction = self._calculate_adaptive_fraction(market_volatility, confidence)
        
        # 计算经典凯利
        classic_result = self._classic_kelly(win_prob, win_loss_ratio, confidence)
        
        # 应用自适应调整
        adaptive_position = classic_result.kelly_fraction * adaptive_fraction
        final_position = np.clip(adaptive_position, 0, self.max_position)
        
        return KellyResult(
            optimal_position=max(final_position, 0),
            kelly_fraction=classic_result.kelly_fraction,
            adjusted_position=final_position,
            confidence_level=confidence,
            risk_level=self._assess_risk_level(adaptive_position, win_prob, win_loss_ratio),
            recommendation=f"自适应凯利策略(调整系数{adaptive_fraction:.2f}): " + 
                          self._generate_recommendation(final_position, classic_result.risk_level)
        )
    
    def _estimate_market_volatility(self, win_prob: float, win_loss_ratio: float) -> float:
        """估算市场波动率"""
        # 基于胜率和盈亏比估算波动率
        # 胜率接近0.5且盈亏比接近1时，波动率较高
        prob_uncertainty = abs(win_prob - 0.5) * 2  # 0-1之间
        ratio_stability = min(win_loss_ratio, 1/win_loss_ratio) if win_loss_ratio > 0 else 0
        
        volatility = 1 - (prob_uncertainty * ratio_stability)
        return np.clip(volatility, 0.1, 1.0)
    
    def _calculate_adaptive_fraction(self, volatility: float, confidence: float) -> float:
        """计算自适应调整系数"""
        # 波动率越高，调整系数越小（更保守）
        # 置信度越低，调整系数越小
        base_fraction = 0.5  # 基础调整系数
        volatility_adjustment = 1 - volatility * 0.5  # 波动率调整
        confidence_adjustment = confidence  # 置信度调整
        
        adaptive_fraction = base_fraction * volatility_adjustment * confidence_adjustment
        return np.clip(adaptive_fraction, 0.1, 1.0)
    
    def _assess_risk_level(self, kelly_fraction: float, win_prob: float, win_loss_ratio: float) -> str:
        """评估风险等级"""
        
        if kelly_fraction <= 0:
            return "无风险"
        elif kelly_fraction <= 0.1:
            return "低风险"
        elif kelly_fraction <= 0.2:
            return "中等风险"
        elif kelly_fraction <= 0.3:
            return "较高风险"
        else:
            return "高风险"
    
    def _generate_recommendation(self, position: float, risk_level: str) -> str:
        """生成仓位建议"""
        
        if position <= 0:
            return "建议观望，不建立仓位"
        elif position <= 0.05:
            return f"建议轻仓试探({position:.1%})，{risk_level}"
        elif position <= 0.15:
            return f"建议适度建仓({position:.1%})，{risk_level}"
        elif position <= 0.25:
            return f"建议标准仓位({position:.1%})，{risk_level}"
        else:
            return f"建议重仓配置({position:.1%})，{risk_level}，请谨慎操作"
    
    def _create_zero_position_result(self, reason: str) -> KellyResult:
        """创建零仓位结果"""
        return KellyResult(
            optimal_position=0.0,
            kelly_fraction=0.0,
            adjusted_position=0.0,
            confidence_level=0.0,
            risk_level="无风险",
            recommendation=f"不建议建仓: {reason}"
        )

class AdvancedKellyCalculator(KellyFormulaCalculator):
    """高级凯利公式计算器，支持多种风险调整"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.risk_adjustments = {
            'drawdown_protection': True,
            'volatility_scaling': True,
            'correlation_adjustment': True,
            'time_decay': True
        }
    
    def calculate_with_risk_adjustments(self, 
                                      win_probability: float,
                                      win_loss_ratio: float,
                                      confidence: float = 1.0,
                                      market_conditions: Dict = None) -> KellyResult:
        """
        带风险调整的凯利公式计算
        
        Args:
            win_probability: 胜率
            win_loss_ratio: 盈亏比
            confidence: 置信度
            market_conditions: 市场条件字典
        """
        
        # 基础凯利计算
        base_result = self._classic_kelly(win_probability, win_loss_ratio, confidence)
        
        if market_conditions is None:
            return base_result
        
        # 应用各种风险调整
        adjusted_position = base_result.optimal_position
        
        # 回撤保护调整
        if self.risk_adjustments['drawdown_protection']:
            adjusted_position = self._apply_drawdown_protection(
                adjusted_position, market_conditions.get('current_drawdown', 0)
            )
        
        # 波动率调整
        if self.risk_adjustments['volatility_scaling']:
            adjusted_position = self._apply_volatility_scaling(
                adjusted_position, market_conditions.get('volatility', 0.2)
            )
        
        # 相关性调整
        if self.risk_adjustments['correlation_adjustment']:
            adjusted_position = self._apply_correlation_adjustment(
                adjusted_position, market_conditions.get('portfolio_correlation', 0.5)
            )
        
        # 时间衰减调整
        if self.risk_adjustments['time_decay']:
            adjusted_position = self._apply_time_decay(
                adjusted_position, market_conditions.get('signal_age', 0)
            )
        
        # 最终仓位限制
        final_position = np.clip(adjusted_position, 0, self.max_position)
        
        return KellyResult(
            optimal_position=final_position,
            kelly_fraction=base_result.kelly_fraction,
            adjusted_position=final_position,
            confidence_level=confidence,
            risk_level=self._assess_risk_level(final_position, win_probability, win_loss_ratio),
            recommendation=f"风险调整后建议: {self._generate_recommendation(final_position, base_result.risk_level)}"
        )
    
    def _apply_drawdown_protection(self, position: float, current_drawdown: float) -> float:
        """应用回撤保护"""
        if current_drawdown <= 0:
            return position
        
        # 回撤越大，仓位越小
        protection_factor = max(0.1, 1 - current_drawdown * 2)
        return position * protection_factor
    
    def _apply_volatility_scaling(self, position: float, volatility: float) -> float:
        """应用波动率调整"""
        # 标准化波动率（假设正常波动率为20%）
        normalized_vol = volatility / 0.2
        
        # 波动率越高，仓位越小
        vol_factor = 1 / max(1, normalized_vol)
        return position * vol_factor
    
    def _apply_correlation_adjustment(self, position: float, correlation: float) -> float:
        """应用相关性调整"""
        # 与组合相关性越高，仓位越小
        correlation_factor = 1 - abs(correlation) * 0.5
        return position * correlation_factor
    
    def _apply_time_decay(self, position: float, signal_age: int) -> float:
        """应用时间衰减调整"""
        if signal_age <= 0:
            return position
        
        # 信号越老，仓位越小
        decay_factor = math.exp(-signal_age * 0.1)  # 指数衰减
        return position * decay_factor

class PortfolioKellyManager:
    """组合级别的凯利仓位管理器"""
    
    def __init__(self, max_total_position: float = 0.8):
        self.max_total_position = max_total_position
        self.current_positions = {}
        self.kelly_calculator = AdvancedKellyCalculator()
    
    def calculate_portfolio_positions(self, signals: List[Dict]) -> Dict[str, KellyResult]:
        """
        计算组合级别的仓位分配
        
        Args:
            signals: 交易信号列表，每个信号包含symbol, win_prob, win_loss_ratio等
        
        Returns:
            各标的的仓位计算结果
        """
        
        results = {}
        total_kelly_demand = 0
        
        # 第一轮：计算各标的的凯利仓位需求
        for signal in signals:
            symbol = signal['symbol']
            kelly_result = self.kelly_calculator.calculate_optimal_position(
                win_probability=signal['win_probability'],
                win_loss_ratio=signal['win_loss_ratio'],
                confidence=signal.get('confidence', 1.0)
            )
            
            results[symbol] = kelly_result
            total_kelly_demand += kelly_result.optimal_position
        
        # 第二轮：如果总需求超过限制，按比例缩放
        if total_kelly_demand > self.max_total_position:
            scale_factor = self.max_total_position / total_kelly_demand
            
            for symbol, result in results.items():
                scaled_position = result.optimal_position * scale_factor
                
                # 更新结果
                results[symbol] = KellyResult(
                    optimal_position=scaled_position,
                    kelly_fraction=result.kelly_fraction,
                    adjusted_position=scaled_position,
                    confidence_level=result.confidence_level,
                    risk_level=result.risk_level,
                    recommendation=f"组合缩放后({scale_factor:.2f}): {result.recommendation}"
                )
        
        return results
    
    def get_position_summary(self, results: Dict[str, KellyResult]) -> Dict:
        """获取仓位分配摘要"""
        
        total_position = sum(result.optimal_position for result in results.values())
        
        summary = {
            'total_position': total_position,
            'position_utilization': total_position / self.max_total_position,
            'number_of_positions': len([r for r in results.values() if r.optimal_position > 0]),
            'average_position': total_position / len(results) if results else 0,
            'risk_distribution': self._analyze_risk_distribution(results)
        }
        
        return summary
    
    def _analyze_risk_distribution(self, results: Dict[str, KellyResult]) -> Dict:
        """分析风险分布"""
        
        risk_counts = {}
        for result in results.values():
            risk_level = result.risk_level
            risk_counts[risk_level] = risk_counts.get(risk_level, 0) + 1
        
        return risk_counts

# 使用示例
if __name__ == "__main__":
    # 基础凯利计算示例
    calculator = KellyFormulaCalculator()
    
    # 示例1：经典凯利
    result1 = calculator.calculate_optimal_position(
        win_probability=0.6,
        win_loss_ratio=2.0,
        confidence=0.8,
        method="classic"
    )
    
    print("=== 经典凯利公式结果 ===")
    print(f"最优仓位: {result1.optimal_position:.2%}")
    print(f"凯利分数: {result1.kelly_fraction:.2%}")
    print(f"风险等级: {result1.risk_level}")
    print(f"建议: {result1.recommendation}")
    print()
    
    # 示例2：分数凯利
    result2 = calculator.calculate_optimal_position(
        win_probability=0.6,
        win_loss_ratio=2.0,
        confidence=0.8,
        method="fractional"
    )
    
    print("=== 分数凯利公式结果 ===")
    print(f"最优仓位: {result2.optimal_position:.2%}")
    print(f"建议: {result2.recommendation}")
    print()
    
    # 示例3：组合级别管理
    portfolio_manager = PortfolioKellyManager()
    
    signals = [
        {'symbol': 'A', 'win_probability': 0.65, 'win_loss_ratio': 2.0, 'confidence': 0.8},
        {'symbol': 'B', 'win_probability': 0.55, 'win_loss_ratio': 1.5, 'confidence': 0.7},
        {'symbol': 'C', 'win_probability': 0.70, 'win_loss_ratio': 2.5, 'confidence': 0.9}
    ]
    
    portfolio_results = portfolio_manager.calculate_portfolio_positions(signals)
    summary = portfolio_manager.get_position_summary(portfolio_results)
    
    print("=== 组合仓位分配结果 ===")
    for symbol, result in portfolio_results.items():
        print(f"{symbol}: {result.optimal_position:.2%} ({result.risk_level})")
    
    print(f"\n总仓位: {summary['total_position']:.2%}")
    print(f"仓位利用率: {summary['position_utilization']:.1%}")
    print(f"持仓数量: {summary['number_of_positions']}")
    print(f"风险分布: {summary['risk_distribution']}")