"""
增强版基金交易多智能体系统 V3.0 主程序入口
集成六大维度评估体系的完整自动化交易系统
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from system.enhanced_trading_system import EnhancedFundTradingSystemV3


def main():
    """主程序入口"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        # 完整自动化交易系统模式
        print(f"\n💰 启动完整自动化交易系统")
        print("-" * 60)
        logger.info("🚀 启动增强版基金交易多智能体系统 V3.0")
        logger.info("💰 自动化交易模式")

        # 初始化交易系统
        trading_system = EnhancedFundTradingSystemV3(title='')

        # 运行交易系统
        trading_system.run_trading_system_v3()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断系统运行")
        logger.info("⚠️ 用户中断交易系统")
    except Exception as e:
        print(f"\n❌ 系统运行异常: {str(e)}")
        logger.error(f"❌ 交易系统异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n👋 交易系统已退出")
        logger.info("👋 交易系统已退出")


if __name__ == '__main__':
    """增强版基金交易多智能体系统 V3.0 主程序入口"""
    main()
