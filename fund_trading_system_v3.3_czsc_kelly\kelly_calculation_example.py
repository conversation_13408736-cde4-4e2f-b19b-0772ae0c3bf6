"""
凯利仓位计算示例演示
展示具体的数据输入和计算过程
"""

def demonstrate_kelly_calculation():
    """演示凯利仓位计算的完整过程"""
    
    print("🧮 凯利仓位计算详细演示")
    print("=" * 80)
    
    # 模拟输入数据
    analysis_data = {
        'fund_code': '518880',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.72,
            'coordination_score': 0.68,
            'weighted_score': 0.25
        },
        'final_decision': 'buy',
        'final_confidence': 0.74,
        'technical_data': {
            'buy_signal': True,
            'signal_strength': 'strong',
            'confidence_score': 0.88,
            'technical_indicators': {
                'ma5': 7.55,
                'ma20': 7.35,
                'rsi': 28,      # 超卖
                'macd': 0.025   # 强金叉
            }
        },
        'gua_data': {
            'is_buy_gua': True,
            'is_sell_gua': False,
            'gua_score': 0.28,
            'main_gua': '需'
        },
        'flow_data': {
            'high_liquidity': True,
            'capital_flow': '净流入',
            'price_data': {
                'price': 7.58,
                'change_rate': 3.2,  # 大涨
                'volume': 25000     # 放量
            }
        },
        'llm_analysis': {
            'confidence_level': 0.85,
            'market_sentiment': '乐观',
            'strategy_suggestion': '技术指标显示强烈买入信号，MA5大幅上穿MA20，MACD金叉扩大，RSI从超卖区域快速回升，资金大幅净流入，建议积极做多。'
        }
    }
    
    print("📊 输入数据概览:")
    print(f"   基金代码: {analysis_data['fund_code']}")
    print(f"   最终决策: {analysis_data['final_decision']}")
    print(f"   技术信号: {analysis_data['technical_data']['signal_strength']}")
    print(f"   资金流向: {analysis_data['flow_data']['capital_flow']}")
    print(f"   市场情绪: {analysis_data['llm_analysis']['market_sentiment']}")
    print(f"   当前价格: {analysis_data['flow_data']['price_data']['price']}")
    print(f"   价格变化: {analysis_data['flow_data']['price_data']['change_rate']}%")
    
    print("\n" + "=" * 80)
    print("🎯 第一步: 胜率计算")
    print("=" * 80)
    
    # 1. 胜率计算详细过程
    base_prob = 0.5
    print(f"基础胜率: {base_prob:.3f} (50.0%)")
    
    # 1.1 技术分析贡献
    print(f"\n📈 技术分析贡献计算:")
    tech_contrib = 0.0
    
    # 买入信号强度
    signal_strength = analysis_data['technical_data']['signal_strength']
    if signal_strength == 'strong':
        signal_contrib = 0.15
        tech_contrib += signal_contrib
        print(f"   强买入信号: +{signal_contrib:.3f} (+15.0%)")
    
    # 置信度评分
    confidence_score = analysis_data['technical_data']['confidence_score']
    confidence_contrib = (confidence_score - 0.5) * 0.12
    tech_contrib += confidence_contrib
    print(f"   置信度评分({confidence_score:.2f}): +{confidence_contrib:.3f} (+{confidence_contrib*100:.1f}%)")
    
    # RSI分析
    rsi = analysis_data['technical_data']['technical_indicators']['rsi']
    if rsi < 30:
        rsi_contrib = 0.08
        print(f"   RSI超卖({rsi}): +{rsi_contrib:.3f} (+8.0%)")
    else:
        rsi_contrib = 0.03
        print(f"   RSI正常({rsi}): +{rsi_contrib:.3f} (+3.0%)")
    tech_contrib += rsi_contrib
    
    # MACD分析
    macd = analysis_data['technical_data']['technical_indicators']['macd']
    if macd > 0:
        macd_contrib = 0.05
        print(f"   MACD金叉({macd:.3f}): +{macd_contrib:.3f} (+5.0%)")
    else:
        macd_contrib = 0.0
        print(f"   MACD死叉({macd:.3f}): +{macd_contrib:.3f} (+0.0%)")
    tech_contrib += macd_contrib
    
    # 均线分析
    ma5 = analysis_data['technical_data']['technical_indicators']['ma5']
    ma20 = analysis_data['technical_data']['technical_indicators']['ma20']
    if ma5 > ma20:
        ma_contrib = 0.06
        print(f"   均线多头排列(MA5:{ma5} > MA20:{ma20}): +{ma_contrib:.3f} (+6.0%)")
    else:
        ma_contrib = 0.0
        print(f"   均线空头排列(MA5:{ma5} < MA20:{ma20}): +{ma_contrib:.3f} (+0.0%)")
    tech_contrib += ma_contrib
    
    print(f"   技术分析总贡献: {tech_contrib:.3f} ({tech_contrib*100:.1f}%)")
    
    # 1.2 资金流向贡献
    print(f"\n💰 资金流向贡献计算:")
    flow_contrib = 0.0
    
    # 流动性
    high_liquidity = analysis_data['flow_data']['high_liquidity']
    if high_liquidity:
        liquidity_contrib = 0.05
        print(f"   高流动性: +{liquidity_contrib:.3f} (+5.0%)")
    else:
        liquidity_contrib = 0.0
        print(f"   低流动性: +{liquidity_contrib:.3f} (+0.0%)")
    flow_contrib += liquidity_contrib
    
    # 资金流向
    capital_flow = analysis_data['flow_data']['capital_flow']
    if capital_flow == '净流入':
        capital_contrib = 0.08
        print(f"   资金净流入: +{capital_contrib:.3f} (+8.0%)")
    elif capital_flow == '净流出':
        capital_contrib = -0.03
        print(f"   资金净流出: {capital_contrib:.3f} (-3.0%)")
    else:
        capital_contrib = 0.0
        print(f"   资金平衡: +{capital_contrib:.3f} (+0.0%)")
    flow_contrib += capital_contrib
    
    # 价格成交量配合
    change_rate = analysis_data['flow_data']['price_data']['change_rate']
    volume = analysis_data['flow_data']['price_data']['volume']
    if change_rate > 0 and volume > 5000:
        volume_contrib = 0.06
        print(f"   上涨放量({change_rate:.1f}%, {volume}): +{volume_contrib:.3f} (+6.0%)")
    elif change_rate < 0 and volume > 5000:
        volume_contrib = 0.03
        print(f"   下跌放量({change_rate:.1f}%, {volume}): +{volume_contrib:.3f} (+3.0%)")
    else:
        volume_contrib = 0.0
        print(f"   成交量一般({change_rate:.1f}%, {volume}): +{volume_contrib:.3f} (+0.0%)")
    flow_contrib += volume_contrib
    
    print(f"   资金流向总贡献: {flow_contrib:.3f} ({flow_contrib*100:.1f}%)")
    
    # 1.3 卦象分析贡献
    print(f"\n🔮 卦象分析贡献计算:")
    gua_contrib = 0.0
    
    is_buy_gua = analysis_data['gua_data']['is_buy_gua']
    if is_buy_gua:
        buy_gua_contrib = 0.06
        print(f"   买入卦象: +{buy_gua_contrib:.3f} (+6.0%)")
    else:
        buy_gua_contrib = 0.0
        print(f"   非买入卦象: +{buy_gua_contrib:.3f} (+0.0%)")
    gua_contrib += buy_gua_contrib
    
    gua_score = analysis_data['gua_data']['gua_score']
    if abs(gua_score) > 0.1:
        gua_score_contrib = abs(gua_score) * 0.08
        print(f"   卦象评分({gua_score:.2f}): +{gua_score_contrib:.3f} (+{gua_score_contrib*100:.1f}%)")
    else:
        gua_score_contrib = 0.0
        print(f"   卦象评分({gua_score:.2f}): +{gua_score_contrib:.3f} (+0.0%)")
    gua_contrib += gua_score_contrib
    
    print(f"   卦象分析总贡献: {gua_contrib:.3f} ({gua_contrib*100:.1f}%)")
    
    # 1.4 LLM分析贡献
    print(f"\n🤖 LLM分析贡献计算:")
    llm_contrib = 0.0
    
    # LLM置信度
    llm_confidence = analysis_data['llm_analysis']['confidence_level']
    llm_confidence_contrib = (llm_confidence - 0.5) * 0.1
    llm_contrib += llm_confidence_contrib
    print(f"   LLM置信度({llm_confidence:.2f}): +{llm_confidence_contrib:.3f} (+{llm_confidence_contrib*100:.1f}%)")
    
    # 市场情绪
    market_sentiment = analysis_data['llm_analysis']['market_sentiment']
    if market_sentiment in ['乐观', '积极']:
        sentiment_contrib = 0.05
        print(f"   市场情绪({market_sentiment}): +{sentiment_contrib:.3f} (+5.0%)")
    elif market_sentiment == '谨慎':
        sentiment_contrib = 0.03
        print(f"   市场情绪({market_sentiment}): +{sentiment_contrib:.3f} (+3.0%)")
    else:
        sentiment_contrib = 0.0
        print(f"   市场情绪({market_sentiment}): +{sentiment_contrib:.3f} (+0.0%)")
    llm_contrib += sentiment_contrib
    
    print(f"   LLM分析总贡献: {llm_contrib:.3f} ({llm_contrib*100:.1f}%)")
    
    # 1.5 增强决策贡献
    print(f"\n🔄 增强决策贡献计算:")
    enhanced_contrib = 0.0
    
    # 决策置信度
    decision_confidence = analysis_data['enhanced_decision']['confidence']
    decision_contrib = (decision_confidence - 0.5) * 0.08
    enhanced_contrib += decision_contrib
    print(f"   决策置信度({decision_confidence:.2f}): +{decision_contrib:.3f} (+{decision_contrib*100:.1f}%)")
    
    # 协调评分
    coordination_score = analysis_data['enhanced_decision']['coordination_score']
    coordination_contrib = (coordination_score - 0.5) * 0.06
    enhanced_contrib += coordination_contrib
    print(f"   协调评分({coordination_score:.2f}): +{coordination_contrib:.3f} (+{coordination_contrib*100:.1f}%)")
    
    # 加权评分
    weighted_score = analysis_data['enhanced_decision']['weighted_score']
    if abs(weighted_score) > 0.1:
        weighted_contrib = abs(weighted_score) * 0.05
        enhanced_contrib += weighted_contrib
        print(f"   加权评分({weighted_score:.2f}): +{weighted_contrib:.3f} (+{weighted_contrib*100:.1f}%)")
    else:
        print(f"   加权评分({weighted_score:.2f}): +0.000 (+0.0%)")
    
    print(f"   增强决策总贡献: {enhanced_contrib:.3f} ({enhanced_contrib*100:.1f}%)")
    
    # 1.6 胜率汇总和缩放
    print(f"\n📊 胜率汇总:")
    total_prob_raw = (base_prob + tech_contrib + flow_contrib + 
                     gua_contrib + llm_contrib + enhanced_contrib)
    print(f"   原始总胜率: {total_prob_raw:.3f} ({total_prob_raw*100:.1f}%)")
    
    # 智能缩放
    if total_prob_raw > 0.90:
        scaled_prob = 0.75 + (total_prob_raw - 0.90) * 0.1
        final_win_prob = min(scaled_prob, 0.85)
        print(f"   应用对数缩放: {final_win_prob:.3f} ({final_win_prob*100:.1f}%)")
        print(f"   ✅ 被上限缩放处理")
    elif total_prob_raw < 0.30:
        final_win_prob = max(total_prob_raw, 0.35)
        print(f"   应用下限保护: {final_win_prob:.3f} ({final_win_prob*100:.1f}%)")
        print(f"   ✅ 被下限保护处理")
    else:
        final_win_prob = total_prob_raw
        print(f"   保持原值: {final_win_prob:.3f} ({final_win_prob*100:.1f}%)")
        print(f"   ✅ 在正常范围内")
    
    print("\n" + "=" * 80)
    print("💰 第二步: 风险收益比计算")
    print("=" * 80)
    
    # 2. 风险收益比计算
    print("🔍 基于技术分析的风险收益比:")
    tech_risk_reward = 1.2
    print(f"   基础比率: {tech_risk_reward:.2f}")
    
    # RSI贡献
    if rsi < 30:
        rsi_rr_contrib = 0.6
        print(f"   RSI超卖奖励: +{rsi_rr_contrib:.2f}")
    elif rsi > 70:
        rsi_rr_contrib = 0.2
        print(f"   RSI超买惩罚: +{rsi_rr_contrib:.2f}")
    else:
        rsi_rr_contrib = 0.3
        print(f"   RSI正常区间: +{rsi_rr_contrib:.2f}")
    tech_risk_reward += rsi_rr_contrib
    
    # MACD贡献
    if macd > 0:
        macd_rr_contrib = 0.3
        print(f"   MACD金叉奖励: +{macd_rr_contrib:.2f}")
    else:
        macd_rr_contrib = 0.0
        print(f"   MACD死叉: +{macd_rr_contrib:.2f}")
    tech_risk_reward += macd_rr_contrib
    
    # 均线贡献
    if ma5 > ma20:
        ma_rr_contrib = 0.4
        print(f"   多头排列奖励: +{ma_rr_contrib:.2f}")
    else:
        ma_rr_contrib = 0.2
        print(f"   空头排列: +{ma_rr_contrib:.2f}")
    tech_risk_reward += ma_rr_contrib
    
    # 信号强度贡献
    if signal_strength == 'strong':
        signal_rr_contrib = 0.5
        print(f"   强信号奖励: +{signal_rr_contrib:.2f}")
    elif signal_strength == 'medium':
        signal_rr_contrib = 0.3
        print(f"   中信号奖励: +{signal_rr_contrib:.2f}")
    else:
        signal_rr_contrib = 0.1
        print(f"   弱信号奖励: +{signal_rr_contrib:.2f}")
    tech_risk_reward += signal_rr_contrib
    
    print(f"   技术分析风险收益比: {tech_risk_reward:.2f}")
    
    # 限制最大值
    final_risk_reward = min(tech_risk_reward, 2.8)
    if final_risk_reward != tech_risk_reward:
        print(f"   应用上限限制: {final_risk_reward:.2f}")
    
    print("\n" + "=" * 80)
    print("⚖️ 第三步: 凯利公式计算")
    print("=" * 80)
    
    # 3. 凯利公式计算
    b = final_risk_reward  # 风险收益比
    p = final_win_prob     # 胜率
    q = 1 - p             # 败率
    
    print(f"凯利公式参数:")
    print(f"   b (风险收益比): {b:.3f}")
    print(f"   p (胜率): {p:.3f} ({p*100:.1f}%)")
    print(f"   q (败率): {q:.3f} ({q*100:.1f}%)")
    
    # 标准凯利公式: f = (bp - q) / b
    kelly_fraction_raw = (b * p - q) / b
    print(f"\n标准凯利分数: f = (bp - q) / b")
    print(f"   f = ({b:.3f} × {p:.3f} - {q:.3f}) / {b:.3f}")
    print(f"   f = ({b*p:.3f} - {q:.3f}) / {b:.3f}")
    print(f"   f = {b*p-q:.3f} / {b:.3f}")
    print(f"   f = {kelly_fraction_raw:.3f} ({kelly_fraction_raw*100:.1f}%)")
    
    # 分数凯利调整
    kelly_fraction = max(0, kelly_fraction_raw) * 0.25
    print(f"\n分数凯利调整 (25%):")
    print(f"   调整后凯利分数: {kelly_fraction:.3f} ({kelly_fraction*100:.1f}%)")
    
    # 置信度调整
    final_confidence = analysis_data['final_confidence']
    optimal_position = kelly_fraction * final_confidence
    print(f"\n置信度调整:")
    print(f"   综合置信度: {final_confidence:.3f} ({final_confidence*100:.1f}%)")
    print(f"   最终建议仓位: {optimal_position:.3f} ({optimal_position*100:.1f}%)")
    
    # 仓位限制
    min_position = 0.01
    max_position = 0.25
    final_position = max(min_position, min(max_position, optimal_position))
    
    if final_position != optimal_position:
        print(f"   应用仓位限制: {final_position:.3f} ({final_position*100:.1f}%)")
    else:
        print(f"   仓位在合理范围内: {final_position:.3f} ({final_position*100:.1f}%)")
    
    # 风险等级评估
    if final_position == 0:
        risk_level = '无风险'
    elif final_position <= 0.05:
        risk_level = '低风险'
    elif final_position <= 0.15:
        risk_level = '中风险'
    else:
        risk_level = '高风险'
    
    print(f"   风险等级: {risk_level}")
    
    print("\n" + "=" * 80)
    print("🎯 最终结果汇总")
    print("=" * 80)
    
    print(f"基金代码: {analysis_data['fund_code']}")
    print(f"交易决策: {analysis_data['final_decision'].upper()}")
    print(f"计算胜率: {final_win_prob:.1%}")
    print(f"风险收益比: {final_risk_reward:.2f}")
    print(f"综合置信度: {final_confidence:.1%}")
    print(f"凯利分数: {kelly_fraction:.2%}")
    print(f"建议仓位: {final_position:.2%}")
    print(f"风险等级: {risk_level}")
    
    print(f"\n💡 仓位建议解读:")
    if final_position > 0.15:
        print(f"   建议重仓操作，信号强烈，风险收益比良好")
    elif final_position > 0.05:
        print(f"   建议适度建仓，信号明确，风险可控")
    elif final_position > 0:
        print(f"   建议轻仓试探，信号较弱，谨慎操作")
    else:
        print(f"   建议观望等待，当前信号不足以支持建仓")
    
    print("=" * 80)


if __name__ == "__main__":
    demonstrate_kelly_calculation()