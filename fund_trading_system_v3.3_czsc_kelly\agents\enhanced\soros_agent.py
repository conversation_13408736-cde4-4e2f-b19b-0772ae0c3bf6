"""
索罗斯反身性理论分析智能体
基于索罗斯反身性理论进行市场分析和投资决策
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.base_agent import BaseAgent
from core.utils import *


class SorosReflexivityAgent(BaseAgent):
    """
    @class SorosReflexivityAgent
    @brief 索罗斯反身性理论分析智能体
    @details 基于索罗斯反身性理论进行市场分析和投资决策
    """
    
    def __init__(self, name: str = "SorosReflexivityAgent"):
        super().__init__(name, "soros_reflexivity_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据并进行反身性分析"""
        fund_code = data.get('fund_code')
        self.logger.info(f"Processing reflexivity analysis for {fund_code}")
        
        try:
            # 简化的反身性分析
            market_data = self.fetch_market_data(fund_code)
            sentiment_data = self.fetch_sentiment_data(fund_code)
            feedback_loop = self.identify_feedback_loops(fund_code)
            judgment = self.generate_judgment(fund_code, feedback_loop)
            
            return judgment
            
        except Exception as e:
            self.logger.error(f"Error in reflexivity analysis for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'action': '观望',
                'error': str(e),
                'confidence': 0.0,
                'timestamp': datetime.now().isoformat()
            }
    
    def fetch_market_data(self, fund_code: str) -> pd.DataFrame:
        """获取市场数据 - 基于真实数据"""
        try:
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            df = get_kline(fund_code, freq='D')
            if df.empty:
                raise ValueError(f"No market data available for {fund_code}")
            return df
        except Exception as e:
            self.logger.error(f"Failed to fetch market data for {fund_code}: {e}")
            return pd.DataFrame()
    
    def fetch_sentiment_data(self, fund_code: str) -> Dict[str, Any]:
        """获取情绪数据 - 基于真实数据"""
        try:
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            sentiment_data = calculate_market_sentiment_indicators(fund_code)
            return sentiment_data
        except Exception as e:
            self.logger.error(f"Failed to fetch sentiment data for {fund_code}: {e}")
            return {}
    
    def identify_feedback_loops(self, fund_code: str) -> Dict[str, Any]:
        """识别反馈循环 - 基于真实数据"""
        try:
            # 获取市场数据
            market_df = self.fetch_market_data(fund_code)
            if market_df.empty:
                raise ValueError("No market data for feedback analysis")
            
            # 获取情绪数据
            sentiment = self.fetch_sentiment_data(fund_code)
            if not sentiment:
                raise ValueError("No sentiment data for feedback analysis")
            
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            # 获取资金流向数据
            flow_data = calculate_real_fund_flow_strength(fund_code)
            
            # 分析反馈强度
            price_momentum = sentiment.get('momentum_sentiment', 0)
            sentiment_score = sentiment.get('composite_sentiment', 0)
            flow_strength = flow_data.get('net_flow_strength', 0)
            
            # 反馈强度：价格动量与情绪/资金流的协调程度
            feedback_strength = abs(price_momentum + sentiment_score + flow_strength) / 3
            
            # 反馈方向
            if sentiment_score > 0.2 and flow_strength > 0.2:
                feedback_direction = 'positive'  # 正反馈
                market_stage = '上涨趋势'
            elif sentiment_score < -0.2 and flow_strength < -0.2:
                feedback_direction = 'negative'  # 负反馈
                market_stage = '下跌趋势'
            else:
                feedback_direction = 'neutral'
                market_stage = '均衡过渡'
            
            return {
                'feedback_strength': min(1.0, max(0.0, feedback_strength)),
                'feedback_direction': feedback_direction,
                'market_stage': market_stage,
                'price_momentum': price_momentum,
                'sentiment_score': sentiment_score,
                'flow_strength': flow_strength
            }
        except Exception as e:
            self.logger.error(f"Failed to identify feedback loops for {fund_code}: {e}")
            # 返回中性结果而非随机结果
            return {
                'feedback_strength': 0.5,
                'feedback_direction': 'neutral',
                'market_stage': '均衡过渡',
                'error': str(e)
            }
    
    def generate_judgment(self, fund_code: str, feedback_loop: Dict[str, Any]) -> Dict[str, Any]:
        """生成反身性判断 - 基于真实数据"""
        try:
            feedback_strength = feedback_loop.get('feedback_strength', 0.5)
            feedback_direction = feedback_loop.get('feedback_direction', 'neutral')
            market_stage = feedback_loop.get('market_stage', '均衡过渡')
            
            # 基于反身性理论的决策逻辑
            if feedback_direction == 'positive' and feedback_strength > 0.6:
                # 强正反馈：趋势可能持续，但要警惕过度
                if feedback_strength > 0.8:
                    action = '观望'  # 过度正反馈，警惕泡沫
                    confidence = 0.6
                    reason = "强正反馈但警惕过度投机"
                else:
                    action = '买入'
                    confidence = 0.7
                    reason = "正反馈循环支持上涨趋势"
            elif feedback_direction == 'negative' and feedback_strength > 0.6:
                # 强负反馈：下跌趋势，但可能出现反转机会
                if feedback_strength > 0.8:
                    action = '买入'  # 过度负反馈，底部机会
                    confidence = 0.6
                    reason = "过度负反馈可能接近底部"
                else:
                    action = '卖出'
                    confidence = 0.7
                    reason = "负反馈循环支持下跌趋势"
            else:
                # 弱反馈或中性：观望等待明确信号
                action = '观望'
                confidence = 0.4
                reason = "反馈信号不明确，保持观望"
            
            return {
                'fund_code': fund_code,
                'action': action,
                'confidence': confidence,
                'reason': reason,
                'feedback_analysis': feedback_loop,
                'market_stage': market_stage,
                'reflexivity_score': feedback_strength,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate judgment for {fund_code}: {e}")
            return {
                'fund_code': fund_code,
                'action': '观望',
                'confidence': 0.0,
                'reason': f"分析失败: {str(e)}",
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
