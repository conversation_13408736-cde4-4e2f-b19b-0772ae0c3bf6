"""
集成测试套件 - 测试RL-LLM协作系统各组件的集成
验证组件间的数据流、接口调用和协作机制
"""

import unittest
import sys
import os
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from rl_llm_collaboration import (
    HybridDecisionSystem,
    DecisionFusionLayer,
    RealTimeCollaboration,
    LLMEnhancedRLAgent
)
from rl_infrastructure import (
    EnhancedTradingEnv,
    FeatureEngineer,
    TrainingManager
)
from analyzers.llm_market_analyzer import LLMMarketAnalyzer


class IntegrationTestSuite(unittest.TestCase):
    """
    @class IntegrationTestSuite
    @brief 集成测试套件
    @details 测试系统各组件间的集成和协作
    """
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.logger = logging.getLogger(cls.__name__)
        logging.basicConfig(level=logging.INFO)
        
        # 创建测试用的市场数据
        cls.test_market_data = cls._create_test_market_data()
        
        # 创建测试配置
        cls.test_config = cls._create_test_config()
        
        cls.logger.info("集成测试套件初始化完成")
    
    def setUp(self):
        """每个测试用例的初始化"""
        self.start_time = time.time()
    
    def tearDown(self):
        """每个测试用例的清理"""
        execution_time = time.time() - self.start_time
        self.logger.info(f"测试执行时间: {execution_time:.3f}秒")
    
    # ==================== 组件初始化测试 ====================
    
    def test_hybrid_decision_system_initialization(self):
        """测试混合决策系统初始化"""
        self.logger.info("测试混合决策系统初始化...")
        
        try:
            hybrid_system = HybridDecisionSystem(self.test_config)
            
            # 验证组件初始化
            self.assertIsNotNone(hybrid_system.llm_analyzer)
            self.assertIsNotNone(hybrid_system.fusion_layer)
            self.assertEqual(hybrid_system.collaboration_mode, 'adaptive')
            
            self.logger.info("✅ 混合决策系统初始化测试通过")
            
        except Exception as e:
            self.fail(f"混合决策系统初始化失败: {str(e)}")
    
    def test_rl_infrastructure_initialization(self):
        """测试RL基础设施初始化"""
        self.logger.info("测试RL基础设施初始化...")
        
        try:
            # 测试增强交易环境
            env = EnhancedTradingEnv(self.test_config)
            self.assertEqual(env.state_dim, 50)
            self.assertEqual(env.action_dim, 2)
            
            # 测试特征工程器
            feature_engineer = FeatureEngineer(self.test_config)
            self.assertEqual(feature_engineer.state_dim, 50)
            
            # 测试训练管理器
            trainer = TrainingManager(self.test_config)
            self.assertIn('ppo', trainer.algorithms)
            
            self.logger.info("✅ RL基础设施初始化测试通过")
            
        except Exception as e:
            self.fail(f"RL基础设施初始化失败: {str(e)}")
    
    def test_realtime_collaboration_initialization(self):
        """测试实时协作系统初始化"""
        self.logger.info("测试实时协作系统初始化...")
        
        try:
            collaboration = RealTimeCollaboration(self.test_config)
            
            # 验证组件初始化
            self.assertIsNotNone(collaboration.strategy_layer)
            self.assertIsNotNone(collaboration.tactical_layer)
            self.assertIsNotNone(collaboration.hybrid_system)
            
            # 验证协作频率配置
            frequencies = collaboration.collaboration_frequencies
            self.assertIn('strategic_review', frequencies)
            self.assertIn('tactical_adjustment', frequencies)
            self.assertIn('execution_decision', frequencies)
            
            self.logger.info("✅ 实时协作系统初始化测试通过")
            
        except Exception as e:
            self.fail(f"实时协作系统初始化失败: {str(e)}")
    
    # ==================== 数据流集成测试 ====================
    
    def test_market_data_flow(self):
        """测试市场数据流处理"""
        self.logger.info("测试市场数据流处理...")
        
        try:
            # 创建特征工程器
            feature_engineer = FeatureEngineer(self.test_config)
            
            # 测试基础特征提取
            basic_features = feature_engineer.create_basic_state_vector(self.test_market_data)
            self.assertEqual(len(basic_features), 50)
            self.assertTrue(np.all(np.isfinite(basic_features)))
            
            # 测试LLM增强特征提取
            llm_insights = self._create_mock_llm_insights()
            enhanced_features = feature_engineer.create_enhanced_state_vector(
                self.test_market_data, llm_insights
            )
            self.assertEqual(len(enhanced_features), 50)
            self.assertTrue(np.all(np.isfinite(enhanced_features)))
            
            self.logger.info("✅ 市场数据流处理测试通过")
            
        except Exception as e:
            self.fail(f"市场数据流处理失败: {str(e)}")
    
    def test_decision_flow_integration(self):
        """测试决策流程集成"""
        self.logger.info("测试决策流程集成...")
        
        try:
            # 创建混合决策系统
            hybrid_system = HybridDecisionSystem(self.test_config)
            
            # 执行决策流程
            decision = hybrid_system.make_decision(
                self.test_market_data, 
                fund_code='TEST001'
            )
            
            # 验证决策结果
            self.assertIn('final_decision', decision)
            self.assertIn('confidence', decision)
            self.assertIn('explanation', decision)
            
            # 验证决策类型
            decision_action = decision['final_decision']
            self.assertIn(decision_action, ['buy', 'hold', 'sell'])
            
            # 验证置信度
            confidence = decision['confidence']
            self.assertGreaterEqual(confidence, 0.0)
            self.assertLessEqual(confidence, 1.0)
            
            self.logger.info("✅ 决策流程集成测试通过")
            
        except Exception as e:
            self.fail(f"决策流程集成失败: {str(e)}")
    
    def test_llm_rl_collaboration(self):
        """测试LLM-RL协作机制"""
        self.logger.info("测试LLM-RL协作机制...")
        
        try:
            # 创建LLM增强RL智能体
            llm_rl_agent = LLMEnhancedRLAgent(self.test_config)
            
            # 执行增强决策
            enhanced_decision = llm_rl_agent.make_enhanced_decision(
                self.test_market_data,
                fund_code='TEST001'
            )
            
            # 验证增强决策结果
            self.assertIn('action', enhanced_decision)
            self.assertIn('confidence', enhanced_decision)
            self.assertIn('llm_explanation', enhanced_decision)
            self.assertIn('llm_rl_consistency', enhanced_decision)
            
            # 验证一致性评分
            consistency = enhanced_decision['llm_rl_consistency']
            self.assertGreaterEqual(consistency, 0.0)
            self.assertLessEqual(consistency, 1.0)
            
            self.logger.info("✅ LLM-RL协作机制测试通过")
            
        except Exception as e:
            self.fail(f"LLM-RL协作机制测试失败: {str(e)}")
    
    # ==================== 系统协作测试 ====================
    
    def test_strategic_tactical_collaboration(self):
        """测试战略战术层协作"""
        self.logger.info("测试战略战术层协作...")
        
        try:
            collaboration = RealTimeCollaboration(self.test_config)
            
            # 测试战略层决策
            strategy_result = collaboration.strategy_layer.generate_investment_strategy(
                macro_data=self._create_mock_macro_data(),
                market_data=self.test_market_data,
                fund_code='TEST001'
            )
            
            # 验证战略层结果
            self.assertIn('strategy_framework', strategy_result)
            self.assertIn('risk_framework', strategy_result)
            self.assertIn('temporal_framework', strategy_result)
            
            # 测试战术层执行
            tactical_result = collaboration.tactical_layer.execute_strategy(
                strategy_guidance=strategy_result,
                real_time_data=self.test_market_data,
                fund_code='TEST001'
            )
            
            # 验证战术层结果
            self.assertIn('executed', tactical_result)
            self.assertIn('execution_plan', tactical_result)
            
            self.logger.info("✅ 战略战术层协作测试通过")
            
        except Exception as e:
            self.fail(f"战略战术层协作测试失败: {str(e)}")
    
    def test_decision_fusion(self):
        """测试决策融合机制"""
        self.logger.info("测试决策融合机制...")
        
        try:
            fusion_layer = DecisionFusionLayer(self.test_config)
            
            # 创建模拟的LLM和RL决策
            llm_decision = {
                'action': 'buy',
                'confidence': 0.8,
                'reasoning': 'LLM分析结果'
            }
            
            rl_decision = {
                'action': 'buy',
                'confidence': 0.7,
                'position_change': 0.3
            }
            
            # 执行决策融合
            fused_decision = fusion_layer.fuse_decisions(
                llm_decision=llm_decision,
                rl_decision=rl_decision,
                market_context=self.test_market_data
            )
            
            # 验证融合结果
            self.assertIn('final_action', fused_decision)
            self.assertIn('combined_confidence', fused_decision)
            self.assertIn('fusion_metadata', fused_decision)
            
            self.logger.info("✅ 决策融合机制测试通过")
            
        except Exception as e:
            self.fail(f"决策融合机制测试失败: {str(e)}")
    
    # ==================== 环境集成测试 ====================
    
    def test_trading_environment_integration(self):
        """测试交易环境集成"""
        self.logger.info("测试交易环境集成...")
        
        try:
            # 创建增强交易环境
            env = EnhancedTradingEnv(self.test_config)
            
            # 重置环境
            initial_state = env.reset()
            self.assertEqual(len(initial_state), 50)
            
            # 执行几步交易
            for step in range(5):
                # 生成随机动作
                action = env.action_space.sample()
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 验证返回值
                self.assertEqual(len(next_state), 50)
                self.assertIsInstance(reward, (int, float))
                self.assertIsInstance(done, bool)
                self.assertIsInstance(info, dict)
                
                if done:
                    break
            
            # 获取性能指标
            metrics = env.get_performance_metrics()
            self.assertIn('total_return', metrics)
            self.assertIn('sharpe_ratio', metrics)
            self.assertIn('max_drawdown', metrics)
            
            self.logger.info("✅ 交易环境集成测试通过")
            
        except Exception as e:
            self.fail(f"交易环境集成测试失败: {str(e)}")
    
    def test_training_integration(self):
        """测试训练集成"""
        self.logger.info("测试训练集成...")
        
        try:
            # 创建训练管理器（使用较小的配置进行快速测试）
            test_training_config = {
                'algorithms': ['ppo'],
                'max_episodes': 10,  # 减少episodes用于快速测试
                'state_dim': 50,
                'action_dim': 2,
                'hidden_dim': 64  # 减少网络大小
            }
            
            trainer = TrainingManager(test_training_config)
            
            # 执行快速训练测试
            training_result = trainer.train_single_algorithm('ppo')
            
            # 验证训练结果
            self.assertIn('algorithm', training_result)
            self.assertIn('total_episodes', training_result)
            self.assertIn('training_completed', training_result)
            
            # 如果训练成功，验证评估
            if training_result.get('training_completed', False):
                evaluation_result = trainer.evaluate_algorithm('ppo', num_episodes=3)
                self.assertIn('avg_reward', evaluation_result)
                self.assertIn('win_rate', evaluation_result)
            
            self.logger.info("✅ 训练集成测试通过")
            
        except Exception as e:
            self.logger.warning(f"训练集成测试跳过 (计算资源限制): {str(e)}")
            # 训练测试可能因为计算资源限制而跳过
            pass
    
    # ==================== 错误处理和稳定性测试 ====================
    
    def test_error_handling(self):
        """测试错误处理机制"""
        self.logger.info("测试错误处理机制...")
        
        try:
            # 测试无效数据处理
            invalid_market_data = {'invalid': 'data'}
            
            hybrid_system = HybridDecisionSystem(self.test_config)
            decision = hybrid_system.make_decision(invalid_market_data)
            
            # 系统应该能够处理无效数据而不崩溃
            self.assertIsInstance(decision, dict)
            
            # 测试异常配置处理
            invalid_config = {'invalid_param': 'invalid_value'}
            try:
                HybridDecisionSystem(invalid_config)
            except Exception:
                pass  # 预期会有异常，这是正常的
            
            self.logger.info("✅ 错误处理机制测试通过")
            
        except Exception as e:
            self.fail(f"错误处理机制测试失败: {str(e)}")
    
    def test_concurrent_access(self):
        """测试并发访问"""
        self.logger.info("测试并发访问...")
        
        try:
            import threading
            import queue
            
            hybrid_system = HybridDecisionSystem(self.test_config)
            results = queue.Queue()
            
            def worker():
                try:
                    decision = hybrid_system.make_decision(self.test_market_data)
                    results.put(('success', decision))
                except Exception as e:
                    results.put(('error', str(e)))
            
            # 创建多个并发线程
            threads = []
            for i in range(5):
                thread = threading.Thread(target=worker)
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=10)
            
            # 检查结果
            success_count = 0
            error_count = 0
            
            while not results.empty():
                status, result = results.get()
                if status == 'success':
                    success_count += 1
                else:
                    error_count += 1
            
            # 至少应该有一些成功的并发访问
            self.assertGreater(success_count, 0)
            
            self.logger.info(f"✅ 并发访问测试通过 (成功: {success_count}, 错误: {error_count})")
            
        except Exception as e:
            self.fail(f"并发访问测试失败: {str(e)}")
    
    # ==================== 辅助方法 ====================
    
    @classmethod
    def _create_test_market_data(cls) -> Dict[str, Any]:
        """创建测试用市场数据"""
        return {
            'price_data': {
                'current_price': 1.25,
                'change_pct': 2.1,
                'volume': 1500000
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': 65.5,
                    'macd': 0.025,
                    'ma5': 1.23,
                    'ma20': 1.20
                }
            },
            'evaluations': {},  # 简化版本，实际应包含六大维度评估
            'timestamp': datetime.now().isoformat()
        }
    
    @classmethod
    def _create_test_config(cls) -> Dict[str, Any]:
        """创建测试配置"""
        return {
            'collaboration_mode': 'adaptive',
            'decision_threshold': 0.6,
            'llm_weight': 0.6,
            'rl_weight': 0.4,
            'state_dim': 50,
            'action_dim': 2,
            'hidden_dim': 64,
            'max_episodes': 10,
            'enable_llm_enhancement': True
        }
    
    def _create_mock_llm_insights(self) -> Dict[str, Any]:
        """创建模拟LLM洞察"""
        return {
            'enhanced_insights': {
                'market_sentiment_score': 0.7,
                'confidence_level': 0.8,
                'risk_level': 0.4,
                'opportunity_score': 0.6
            },
            'semantic_features': {
                'sentiment_polarity': 0.7,
                'risk_appetite': 0.5,
                'time_urgency': 0.3
            }
        }
    
    def _create_mock_macro_data(self) -> Dict[str, Any]:
        """创建模拟宏观数据"""
        return {
            'interest_rate': 3.5,
            'inflation_rate': 2.1,
            'gdp_growth': 6.2,
            'policy_sentiment': 'neutral'
        }


class TestRunner:
    """测试运行器"""
    
    @staticmethod
    def run_integration_tests():
        """运行集成测试"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 创建测试套件
        suite = unittest.TestLoader().loadTestsFromTestCase(IntegrationTestSuite)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        # 返回测试结果
        return {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun if result.testsRun > 0 else 0,
            'details': {
                'failures': result.failures,
                'errors': result.errors
            }
        }


if __name__ == '__main__':
    # 直接运行测试
    runner = TestRunner()
    result = runner.run_integration_tests()
    
    print(f"\n{'='*50}")
    print(f"集成测试结果:")
    print(f"运行测试: {result['tests_run']}")
    print(f"失败: {result['failures']}")
    print(f"错误: {result['errors']}")
    print(f"成功率: {result['success_rate']:.1%}")
    print(f"{'='*50}") 