"""
特征工程器
Feature Engineer
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """特征工程器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化特征工程器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.correlation_threshold = self.config.get('correlation_threshold', 0.8)
        self.variance_threshold = self.config.get('variance_threshold', 0.01)
        self.feature_selection_method = self.config.get('feature_selection_method', 'correlation')
        
        logger.info("FeatureEngineer 初始化完成")
    
    def engineer_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        特征工程主函数
        
        Args:
            data: 输入数据
            
        Returns:
            工程化后的特征数据
        """
        logger.info(f"开始特征工程，输入数据形状: {data.shape}")
        
        try:
            features = data.copy()
            
            # 1. 基础技术指标
            features = self._add_technical_indicators(features)
            
            # 2. 价格特征
            features = self._add_price_features(features)
            
            # 3. 成交量特征
            features = self._add_volume_features(features)
            
            # 4. 时间特征
            features = self._add_time_features(features)
            
            # 5. 统计特征
            features = self._add_statistical_features(features)
            
            # 6. 交互特征
            features = self._add_interaction_features(features)
            
            # 7. 特征选择
            features = self._select_features(features)
            
            # 8. 特征标准化
            features = self._normalize_features(features)
            
            logger.info(f"特征工程完成，输出特征形状: {features.shape}")
            
            return features
            
        except Exception as e:
            logger.error(f"特征工程失败: {e}")
            return data
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加技术指标"""
        try:
            if 'close' not in data.columns:
                return data
            
            close = data['close']
            
            # 移动平均线
            for period in [5, 10, 20, 50]:
                data[f'ma_{period}'] = close.rolling(period).mean()
                data[f'ma_{period}_ratio'] = close / data[f'ma_{period}']
            
            # 指数移动平均
            for period in [12, 26]:
                data[f'ema_{period}'] = close.ewm(span=period).mean()
            
            # MACD
            data['macd'] = data['ema_12'] - data['ema_26']
            data['macd_signal'] = data['macd'].ewm(span=9).mean()
            data['macd_histogram'] = data['macd'] - data['macd_signal']
            
            # RSI
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))
            
            # 布林带
            data['bb_middle'] = close.rolling(20).mean()
            bb_std = close.rolling(20).std()
            data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
            data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
            data['bb_width'] = (data['bb_upper'] - data['bb_lower']) / data['bb_middle']
            data['bb_position'] = (close - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
            
            # KDJ
            if all(col in data.columns for col in ['high', 'low']):
                low_min = data['low'].rolling(9).min()
                high_max = data['high'].rolling(9).max()
                rsv = (close - low_min) / (high_max - low_min) * 100
                data['kdj_k'] = rsv.ewm(com=2).mean()
                data['kdj_d'] = data['kdj_k'].ewm(com=2).mean()
                data['kdj_j'] = 3 * data['kdj_k'] - 2 * data['kdj_d']
            
            logger.debug("技术指标添加完成")
            return data
            
        except Exception as e:
            logger.error(f"技术指标添加失败: {e}")
            return data
    
    def _add_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加价格特征"""
        try:
            if 'close' not in data.columns:
                return data
            
            close = data['close']
            
            # 收益率特征
            for period in [1, 3, 5, 10, 20]:
                data[f'return_{period}d'] = close.pct_change(period)
                data[f'log_return_{period}d'] = np.log(close / close.shift(period))
            
            # 价格位置特征
            for period in [20, 50, 100]:
                high_period = close.rolling(period).max()
                low_period = close.rolling(period).min()
                data[f'price_position_{period}d'] = (close - low_period) / (high_period - low_period)
            
            # 波动率特征
            for period in [5, 10, 20]:
                data[f'volatility_{period}d'] = close.pct_change().rolling(period).std()
                data[f'volatility_ratio_{period}d'] = data[f'volatility_{period}d'] / data[f'volatility_{period}d'].rolling(period).mean()
            
            # 价格趋势特征
            data['price_trend_5d'] = (close.rolling(5).mean() - close.rolling(5).mean().shift(5)) / close.rolling(5).mean().shift(5)
            data['price_trend_20d'] = (close.rolling(20).mean() - close.rolling(20).mean().shift(20)) / close.rolling(20).mean().shift(20)
            
            # 价格加速度
            data['price_acceleration'] = close.pct_change().diff()
            
            logger.debug("价格特征添加完成")
            return data
            
        except Exception as e:
            logger.error(f"价格特征添加失败: {e}")
            return data
    
    def _add_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加成交量特征"""
        try:
            if 'volume' not in data.columns:
                return data
            
            volume = data['volume']
            
            # 成交量移动平均
            for period in [5, 10, 20]:
                data[f'volume_ma_{period}'] = volume.rolling(period).mean()
                data[f'volume_ratio_{period}'] = volume / data[f'volume_ma_{period}']
            
            # 成交量变化率
            for period in [1, 5, 10]:
                data[f'volume_change_{period}d'] = volume.pct_change(period)
            
            # 量价关系
            if 'close' in data.columns:
                price_change = data['close'].pct_change()
                volume_change = volume.pct_change()
                data['price_volume_corr_5d'] = price_change.rolling(5).corr(volume_change)
                data['price_volume_corr_20d'] = price_change.rolling(20).corr(volume_change)
                
                # OBV (On Balance Volume)
                obv = np.where(price_change > 0, volume, 
                              np.where(price_change < 0, -volume, 0))
                data['obv'] = pd.Series(obv).cumsum()
                data['obv_ma_10'] = data['obv'].rolling(10).mean()
            
            # 成交量波动率
            data['volume_volatility_10d'] = volume.pct_change().rolling(10).std()
            
            logger.debug("成交量特征添加完成")
            return data
            
        except Exception as e:
            logger.error(f"成交量特征添加失败: {e}")
            return data
    
    def _add_time_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加时间特征"""
        try:
            # 寻找时间列
            time_col = None
            for col in ['dt', 'date', 'time', 'datetime']:
                if col in data.columns:
                    time_col = col
                    break
            
            if time_col is None or not pd.api.types.is_datetime64_any_dtype(data[time_col]):
                return data
            
            dt = pd.to_datetime(data[time_col])
            
            # 基础时间特征
            data['year'] = dt.dt.year
            data['month'] = dt.dt.month
            data['day'] = dt.dt.day
            data['weekday'] = dt.dt.weekday
            data['quarter'] = dt.dt.quarter
            
            # 周期性特征
            data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
            data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)
            data['weekday_sin'] = np.sin(2 * np.pi * data['weekday'] / 7)
            data['weekday_cos'] = np.cos(2 * np.pi * data['weekday'] / 7)
            
            # 是否为月初/月末
            data['is_month_start'] = dt.dt.is_month_start.astype(int)
            data['is_month_end'] = dt.dt.is_month_end.astype(int)
            data['is_quarter_start'] = dt.dt.is_quarter_start.astype(int)
            data['is_quarter_end'] = dt.dt.is_quarter_end.astype(int)
            
            # 距离特殊日期的天数
            data['days_from_year_start'] = (dt - dt.dt.to_period('Y').dt.start_time).dt.days
            data['days_to_year_end'] = (dt.dt.to_period('Y').dt.end_time - dt).dt.days
            
            logger.debug("时间特征添加完成")
            return data
            
        except Exception as e:
            logger.error(f"时间特征添加失败: {e}")
            return data
    
    def _add_statistical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加统计特征"""
        try:
            if 'close' not in data.columns:
                return data
            
            close = data['close']
            
            # 滚动统计特征
            for period in [10, 20, 50]:
                # 偏度和峰度
                data[f'skewness_{period}d'] = close.pct_change().rolling(period).skew()
                data[f'kurtosis_{period}d'] = close.pct_change().rolling(period).kurt()
                
                # 分位数特征
                data[f'quantile_25_{period}d'] = close.rolling(period).quantile(0.25)
                data[f'quantile_75_{period}d'] = close.rolling(period).quantile(0.75)
                data[f'iqr_{period}d'] = data[f'quantile_75_{period}d'] - data[f'quantile_25_{period}d']
                
                # Z-score
                rolling_mean = close.rolling(period).mean()
                rolling_std = close.rolling(period).std()
                data[f'zscore_{period}d'] = (close - rolling_mean) / rolling_std
            
            # 趋势强度
            for period in [10, 20]:
                data[f'trend_strength_{period}d'] = abs(close.rolling(period).corr(pd.Series(range(period))))
            
            logger.debug("统计特征添加完成")
            return data
            
        except Exception as e:
            logger.error(f"统计特征添加失败: {e}")
            return data
    
    def _add_interaction_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加交互特征"""
        try:
            # 价格和成交量的交互
            if 'close' in data.columns and 'volume' in data.columns:
                data['price_volume_product'] = data['close'] * data['volume']
                data['price_volume_ratio'] = data['close'] / (data['volume'] + 1e-8)
            
            # 技术指标的交互
            if 'rsi' in data.columns and 'macd' in data.columns:
                data['rsi_macd_product'] = data['rsi'] * data['macd']
                data['rsi_macd_ratio'] = data['rsi'] / (abs(data['macd']) + 1e-8)
            
            # 移动平均的交互
            if 'ma_5' in data.columns and 'ma_20' in data.columns:
                data['ma_5_20_ratio'] = data['ma_5'] / data['ma_20']
                data['ma_5_20_diff'] = data['ma_5'] - data['ma_20']
            
            # 波动率的交互
            if 'volatility_5d' in data.columns and 'volatility_20d' in data.columns:
                data['volatility_ratio_5_20'] = data['volatility_5d'] / (data['volatility_20d'] + 1e-8)
            
            logger.debug("交互特征添加完成")
            return data
            
        except Exception as e:
            logger.error(f"交互特征添加失败: {e}")
            return data
    
    def _select_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """特征选择"""
        try:
            # 获取数值特征
            numeric_features = data.select_dtypes(include=[np.number]).columns.tolist()
            
            if len(numeric_features) == 0:
                return data
            
            # 移除方差过小的特征
            low_variance_features = []
            for feature in numeric_features:
                if data[feature].var() < self.variance_threshold:
                    low_variance_features.append(feature)
            
            if low_variance_features:
                logger.info(f"移除低方差特征: {len(low_variance_features)} 个")
                data = data.drop(columns=low_variance_features)
                numeric_features = [f for f in numeric_features if f not in low_variance_features]
            
            # 移除高相关性特征
            if self.feature_selection_method == 'correlation' and len(numeric_features) > 1:
                corr_matrix = data[numeric_features].corr().abs()
                
                # 找到高相关性的特征对
                high_corr_pairs = []
                for i in range(len(corr_matrix.columns)):
                    for j in range(i+1, len(corr_matrix.columns)):
                        if corr_matrix.iloc[i, j] > self.correlation_threshold:
                            high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j]))
                
                # 移除高相关性特征中的一个
                features_to_remove = set()
                for feat1, feat2 in high_corr_pairs:
                    if feat1 not in features_to_remove and feat2 not in features_to_remove:
                        # 保留方差更大的特征
                        if data[feat1].var() > data[feat2].var():
                            features_to_remove.add(feat2)
                        else:
                            features_to_remove.add(feat1)
                
                if features_to_remove:
                    logger.info(f"移除高相关性特征: {len(features_to_remove)} 个")
                    data = data.drop(columns=list(features_to_remove))
            
            return data
            
        except Exception as e:
            logger.error(f"特征选择失败: {e}")
            return data
    
    def _normalize_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """特征标准化"""
        try:
            numeric_features = data.select_dtypes(include=[np.number]).columns.tolist()
            
            # 排除一些不需要标准化的特征
            exclude_features = ['year', 'month', 'day', 'weekday', 'quarter', 
                              'is_month_start', 'is_month_end', 'is_quarter_start', 'is_quarter_end']
            features_to_normalize = [f for f in numeric_features if f not in exclude_features]
            
            if not features_to_normalize:
                return data
            
            # Z-score标准化
            for feature in features_to_normalize:
                if data[feature].std() > 0:
                    data[f'{feature}_normalized'] = (data[feature] - data[feature].mean()) / data[feature].std()
                else:
                    data[f'{feature}_normalized'] = 0
            
            logger.debug(f"标准化了 {len(features_to_normalize)} 个特征")
            return data
            
        except Exception as e:
            logger.error(f"特征标准化失败: {e}")
            return data
    
    def get_feature_importance(self, features: pd.DataFrame, target: pd.Series) -> Dict[str, float]:
        """计算特征重要性"""
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.feature_selection import mutual_info_regression
            
            # 对齐数据
            common_index = features.index.intersection(target.index)
            X = features.loc[common_index].select_dtypes(include=[np.number])
            y = target.loc[common_index]
            
            # 处理缺失值
            X = X.fillna(X.mean())
            y = y.fillna(y.mean())
            
            if len(X) == 0 or len(X.columns) == 0:
                return {}
            
            # 随机森林特征重要性
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            rf_importance = dict(zip(X.columns, rf.feature_importances_))
            
            # 互信息特征重要性
            mi_scores = mutual_info_regression(X, y, random_state=42)
            mi_importance = dict(zip(X.columns, mi_scores))
            
            # 综合重要性评分
            combined_importance = {}
            for feature in X.columns:
                combined_importance[feature] = (rf_importance[feature] + mi_importance[feature]) / 2
            
            # 按重要性排序
            sorted_importance = dict(sorted(combined_importance.items(), key=lambda x: x[1], reverse=True))
            
            return sorted_importance
            
        except Exception as e:
            logger.error(f"特征重要性计算失败: {e}")
            return {}