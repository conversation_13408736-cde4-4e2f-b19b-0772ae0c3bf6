"""
系统配置示例文件
包含各种使用场景的配置模板
"""

# 保守型配置 - 适合风险厌恶型投资者
CONSERVATIVE_CONFIG = {
    'llm_model': 'local',
    'max_position': 0.15,           # 单个标的最大仓位15%
    'kelly_fraction': 0.20,         # 使用1/5凯利策略
    'max_total_position': 0.60,     # 总仓位上限60%
    'confidence_threshold': 0.70,   # 高置信度阈值70%
    'min_kelly_position': 0.03,     # 最小仓位3%
    'risk_free_rate': 0.03,
    'kelly_method': 'fractional',   # 使用分数凯利
    'min_k_num': 10,               # 需要更多K线数据
    'max_k_num': 30,
    'bi_min_len': 7                # 更严格的笔长度要求
}

# 平衡型配置 - 适合一般投资者
BALANCED_CONFIG = {
    'llm_model': 'local',
    'max_position': 0.20,           # 单个标的最大仓位20%
    'kelly_fraction': 0.25,         # 使用1/4凯利策略
    'max_total_position': 0.75,     # 总仓位上限75%
    'confidence_threshold': 0.50,   # 中等置信度阈值50%
    'min_kelly_position': 0.02,     # 最小仓位2%
    'risk_free_rate': 0.03,
    'kelly_method': 'fractional',
    'min_k_num': 7,
    'max_k_num': 21,
    'bi_min_len': 5
}

# 激进型配置 - 适合风险偏好型投资者
AGGRESSIVE_CONFIG = {
    'llm_model': 'local',
    'max_position': 0.30,           # 单个标的最大仓位30%
    'kelly_fraction': 0.35,         # 使用更高的凯利分数
    'max_total_position': 0.90,     # 总仓位上限90%
    'confidence_threshold': 0.40,   # 较低置信度阈值40%
    'min_kelly_position': 0.01,     # 最小仓位1%
    'risk_free_rate': 0.03,
    'kelly_method': 'adaptive',     # 使用自适应凯利
    'min_k_num': 5,                # 更少的K线数据要求
    'max_k_num': 15,
    'bi_min_len': 3                # 更宽松的笔长度要求
}

# 研究型配置 - 适合策略研究和回测
RESEARCH_CONFIG = {
    'llm_model': 'local',
    'max_position': 0.25,
    'kelly_fraction': 0.25,
    'max_total_position': 0.80,
    'confidence_threshold': 0.30,   # 低阈值以获得更多信号
    'min_kelly_position': 0.005,    # 很小的最小仓位
    'risk_free_rate': 0.03,
    'kelly_method': 'classic',      # 使用经典凯利公式
    'min_k_num': 7,
    'max_k_num': 21,
    'bi_min_len': 5,
    'enable_detailed_logging': True, # 启用详细日志
    'save_analysis_results': True   # 保存分析结果
}

# 实盘交易配置 - 适合实际交易使用
LIVE_TRADING_CONFIG = {
    'llm_model': 'openai',          # 使用真实的LLM服务
    'max_position': 0.20,
    'kelly_fraction': 0.25,
    'max_total_position': 0.70,     # 实盘时更保守的总仓位
    'confidence_threshold': 0.60,   # 实盘时更高的置信度要求
    'min_kelly_position': 0.02,
    'risk_free_rate': 0.03,
    'kelly_method': 'fractional',
    'min_k_num': 10,               # 实盘时需要更多数据
    'max_k_num': 30,
    'bi_min_len': 7,
    'enable_risk_alerts': True,     # 启用风险警报
    'max_drawdown_limit': 0.15,     # 最大回撤限制15%
    'position_check_interval': 300, # 仓位检查间隔(秒)
    'emergency_stop_loss': 0.20     # 紧急止损20%
}

# 高频交易配置 - 适合短期交易
HIGH_FREQ_CONFIG = {
    'llm_model': 'local',
    'max_position': 0.15,           # 高频交易单仓位较小
    'kelly_fraction': 0.20,
    'max_total_position': 0.60,
    'confidence_threshold': 0.40,
    'min_kelly_position': 0.01,
    'risk_free_rate': 0.03,
    'kelly_method': 'adaptive',
    'min_k_num': 5,                # 更少的数据要求
    'max_k_num': 10,
    'bi_min_len': 3,
    'fast_execution': True,         # 快速执行模式
    'reduce_analysis_depth': True,  # 减少分析深度以提高速度
    'max_holding_period': 5         # 最大持仓周期(天)
}

# 组合管理配置 - 适合管理多个投资组合
PORTFOLIO_CONFIG = {
    'llm_model': 'local',
    'max_position': 0.10,           # 组合管理时单仓位更小
    'kelly_fraction': 0.25,
    'max_total_position': 0.80,
    'confidence_threshold': 0.50,
    'min_kelly_position': 0.01,
    'risk_free_rate': 0.03,
    'kelly_method': 'fractional',
    'min_k_num': 7,
    'max_k_num': 21,
    'bi_min_len': 5,
    'max_symbols_per_portfolio': 20, # 每个组合最大标的数
    'sector_concentration_limit': 0.30, # 行业集中度限制
    'correlation_threshold': 0.70,   # 相关性阈值
    'rebalance_frequency': 'weekly'  # 再平衡频率
}

# 风险控制增强配置
RISK_ENHANCED_CONFIG = {
    'llm_model': 'local',
    'max_position': 0.18,
    'kelly_fraction': 0.22,
    'max_total_position': 0.70,
    'confidence_threshold': 0.55,
    'min_kelly_position': 0.02,
    'risk_free_rate': 0.03,
    'kelly_method': 'fractional',
    'min_k_num': 8,
    'max_k_num': 25,
    'bi_min_len': 6,
    # 增强风险控制参数
    'enable_drawdown_protection': True,
    'enable_volatility_scaling': True,
    'enable_correlation_adjustment': True,
    'enable_time_decay': True,
    'max_sector_exposure': 0.25,    # 单行业最大敞口
    'var_limit': 0.05,              # VaR限制
    'stress_test_scenarios': 5       # 压力测试场景数
}

def get_config(config_type: str = 'balanced'):
    """
    获取指定类型的配置
    
    Args:
        config_type: 配置类型
            - 'conservative': 保守型
            - 'balanced': 平衡型  
            - 'aggressive': 激进型
            - 'research': 研究型
            - 'live': 实盘交易型
            - 'high_freq': 高频交易型
            - 'portfolio': 组合管理型
            - 'risk_enhanced': 风险控制增强型
    
    Returns:
        配置字典
    """
    
    config_map = {
        'conservative': CONSERVATIVE_CONFIG,
        'balanced': BALANCED_CONFIG,
        'aggressive': AGGRESSIVE_CONFIG,
        'research': RESEARCH_CONFIG,
        'live': LIVE_TRADING_CONFIG,
        'high_freq': HIGH_FREQ_CONFIG,
        'portfolio': PORTFOLIO_CONFIG,
        'risk_enhanced': RISK_ENHANCED_CONFIG
    }
    
    if config_type not in config_map:
        print(f"警告：未知配置类型 '{config_type}'，使用默认平衡型配置")
        return BALANCED_CONFIG.copy()
    
    return config_map[config_type].copy()

def create_custom_config(**kwargs):
    """
    创建自定义配置
    
    Args:
        **kwargs: 自定义参数
    
    Returns:
        自定义配置字典
    """
    
    # 从平衡型配置开始
    config = BALANCED_CONFIG.copy()
    
    # 更新自定义参数
    config.update(kwargs)
    
    return config

def validate_config(config: dict) -> bool:
    """
    验证配置参数的合理性
    
    Args:
        config: 配置字典
    
    Returns:
        是否通过验证
    """
    
    errors = []
    
    # 检查必要参数
    required_params = [
        'max_position', 'kelly_fraction', 'max_total_position',
        'confidence_threshold', 'min_kelly_position'
    ]
    
    for param in required_params:
        if param not in config:
            errors.append(f"缺少必要参数: {param}")
    
    # 检查参数范围
    if config.get('max_position', 0) > 1 or config.get('max_position', 0) <= 0:
        errors.append("max_position 应该在 (0, 1] 范围内")
    
    if config.get('max_total_position', 0) > 1 or config.get('max_total_position', 0) <= 0:
        errors.append("max_total_position 应该在 (0, 1] 范围内")
    
    if config.get('confidence_threshold', 0) > 1 or config.get('confidence_threshold', 0) < 0:
        errors.append("confidence_threshold 应该在 [0, 1] 范围内")
    
    if config.get('kelly_fraction', 0) > 1 or config.get('kelly_fraction', 0) <= 0:
        errors.append("kelly_fraction 应该在 (0, 1] 范围内")
    
    # 检查逻辑一致性
    if config.get('max_position', 0) > config.get('max_total_position', 1):
        errors.append("max_position 不应该大于 max_total_position")
    
    if config.get('min_kelly_position', 0) > config.get('max_position', 1):
        errors.append("min_kelly_position 不应该大于 max_position")
    
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("配置验证通过")
    return True

# 使用示例
if __name__ == "__main__":
    # 获取不同类型的配置
    print("=== 配置示例 ===")
    
    configs = ['conservative', 'balanced', 'aggressive', 'research']
    
    for config_type in configs:
        config = get_config(config_type)
        print(f"\n{config_type.upper()} 配置:")
        print(f"  最大单仓位: {config['max_position']:.0%}")
        print(f"  凯利分数: {config['kelly_fraction']:.0%}")
        print(f"  总仓位上限: {config['max_total_position']:.0%}")
        print(f"  置信度阈值: {config['confidence_threshold']:.0%}")
        
        # 验证配置
        is_valid = validate_config(config)
        print(f"  配置有效性: {'✅' if is_valid else '❌'}")
    
    # 创建自定义配置示例
    print(f"\n=== 自定义配置示例 ===")
    custom_config = create_custom_config(
        max_position=0.22,
        confidence_threshold=0.65,
        kelly_method='adaptive'
    )
    
    print("自定义配置:")
    for key, value in custom_config.items():
        print(f"  {key}: {value}")
    
    validate_config(custom_config)