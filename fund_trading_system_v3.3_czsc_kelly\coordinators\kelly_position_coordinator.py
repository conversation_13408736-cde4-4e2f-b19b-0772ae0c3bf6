"""
凯利公式仓位协调器
集成到现有的多智能体系统中，提供精确的仓位计算
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from kelly_position_manager import KellyFormulaCalculator, KellyResult
from kelly_position_display import KellyPositionDisplay


class KellyPositionCoordinator:
    """
    凯利公式仓位协调器
    负责在现有系统中集成凯利公式仓位计算
    """
    
    def __init__(self, config: Dict = None):
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 默认配置
        self.config = config or {
            'kelly_method': 'fractional',
            'kelly_fraction': 0.25,
            'max_position': 0.25,
            'min_position': 0.01,
            'confidence_threshold': 0.5,
            'enable_display': False,  # 禁用详细显示，改为整合显示
            'enable_detailed_logging': True
        }
        
        # 初始化凯利计算器
        self.kelly_calculator = KellyFormulaCalculator(
            max_position=self.config['max_position'],
            min_position=self.config['min_position'],
            kelly_fraction=self.config['kelly_fraction']
        )
        
        # 初始化显示器
        if self.config['enable_display']:
            self.kelly_display = KellyPositionDisplay(self.config)
        else:
            self.kelly_display = None
    
    def calculate_kelly_position(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于分析结果计算凯利仓位
        
        Args:
            analysis_result: 包含所有智能体分析结果的字典
            
        Returns:
            包含凯利仓位计算结果的字典
        """
        
        fund_code = analysis_result.get('fund_code', 'unknown')
        
        try:
            # 1. 提取决策信息
            decision_info = self._extract_decision_info(analysis_result)
            
            # 2. 计算胜率和盈亏比
            win_probability = self._calculate_win_probability(analysis_result)
            risk_reward_ratio = self._calculate_risk_reward_ratio(analysis_result)
            confidence = self._calculate_overall_confidence(analysis_result)
            
            # 3. 执行凯利公式计算
            kelly_result = self.kelly_calculator.calculate_optimal_position(
                win_probability=win_probability,
                win_loss_ratio=risk_reward_ratio,
                confidence=confidence,
                method=self.config['kelly_method']
            )
            
            # 4. 生成详细的凯利分析
            kelly_analysis = {
                'fund_code': fund_code,
                'kelly_calculation': {
                    'win_probability': win_probability,
                    'risk_reward_ratio': risk_reward_ratio,
                    'confidence': confidence,
                    'kelly_fraction': kelly_result.kelly_fraction,
                    'optimal_position': kelly_result.optimal_position,
                    'adjusted_position': kelly_result.adjusted_position,
                    'risk_level': kelly_result.risk_level,
                    'recommendation': kelly_result.recommendation
                },
                'position_reasoning': self._generate_position_reasoning(
                    decision_info, win_probability, risk_reward_ratio, confidence, kelly_result
                ),
                'timestamp': datetime.now().isoformat()
            }
            
            # 5. 显示详细计算过程（如果启用）
            if self.kelly_display and self.config['enable_display']:
                self._display_kelly_calculation(
                    fund_code, decision_info, win_probability, 
                    risk_reward_ratio, confidence, kelly_result
                )
            
            # 6. 记录日志
            if self.config['enable_detailed_logging']:
                self._log_kelly_calculation(kelly_analysis)
            
            return kelly_analysis
            
        except Exception as e:
            self.logger.error(f"凯利仓位计算失败 {fund_code}: {e}")
            return self._create_fallback_kelly_result(fund_code)
    
    def _extract_decision_info(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """从分析结果中提取决策信息"""
        
        # 获取增强决策结果
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        
        # 获取最终决策（风控后）
        final_decision = analysis_result.get('final_decision', enhanced_decision.get('decision', 'hold'))
        final_confidence = analysis_result.get('final_confidence', enhanced_decision.get('confidence', 0.0))
        
        # 获取价格信息 - 修复数据结构匹配
        flow_data = analysis_result.get('flow_data', {})
        price_data = flow_data.get('price_data', {})
        current_price = price_data.get('price', 1.0)
        
        # 如果flow_data中没有价格，尝试从technical_data获取
        if current_price <= 1.0:
            technical_data = analysis_result.get('technical_data', {})
            current_price = technical_data.get('current_price', 1.0)
        
        # 获取支撑阻力位 - 基于价格变化率估算
        change_rate = price_data.get('change_rate', 0)
        volatility_factor = max(0.02, abs(change_rate) / 100)  # 至少2%的波动
        
        support_level = current_price * (1 - volatility_factor)
        resistance_level = current_price * (1 + volatility_factor)
        
        return {
            'decision': final_decision,
            'confidence': final_confidence,
            'current_price': current_price,
            'support_level': support_level,
            'resistance_level': resistance_level,
            'entry_price': current_price,
            'stop_loss': support_level if final_decision == 'buy' else resistance_level,
            'take_profit': resistance_level if final_decision == 'buy' else support_level
        }
    
    def _calculate_win_probability(self, analysis_result: Dict[str, Any]) -> float:
        """计算胜率"""
        
        # 从各个智能体获取信号强度
        technical_data = analysis_result.get('technical_data', {})
        gua_data = analysis_result.get('gua_data', {})
        flow_data = analysis_result.get('flow_data', {})
        llm_analysis = analysis_result.get('llm_analysis', {})
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        
        # 检查是否所有智能体都失败了（CZSC不可用的情况）
        all_agents_failed = (
            technical_data.get('error') and 
            gua_data.get('error') and 
            flow_data.get('error')
        )
        
        if all_agents_failed:
            # CZSC不可用时，基于增强决策和LLM分析进行智能估算
            return self._estimate_win_probability_fallback(analysis_result)
        
        # 正常情况下的胜率计算
        base_win_prob = 0.5
        
        # 技术分析贡献 - 修复数据结构匹配
        technical_contribution = 0.0
        if technical_data.get('buy_signal', False):
            signal_strength = technical_data.get('signal_strength', 'none')
            if signal_strength == 'strong':
                technical_contribution = 0.15
            elif signal_strength == 'medium':
                technical_contribution = 0.10
            elif signal_strength == 'weak':
                technical_contribution = 0.05
        
        confidence_score = technical_data.get('confidence_score', 0.5)
        technical_contribution += (confidence_score - 0.5) * 0.1
        
        # 卦象分析贡献 - 修复数据结构匹配
        gua_contribution = 0.0
        if gua_data.get('is_buy_gua', False):
            gua_contribution += 0.08
        elif gua_data.get('is_sell_gua', False):
            gua_contribution -= 0.08
        
        gua_score = gua_data.get('gua_score', 0.0)
        if abs(gua_score) > 0.1:
            gua_contribution += gua_score * 0.05
        
        # 资金流向贡献 - 修复数据结构匹配
        flow_contribution = 0.0
        if flow_data.get('high_liquidity', False):
            flow_contribution += 0.05
        
        capital_flow = flow_data.get('capital_flow', '平衡')
        if capital_flow == '净流入':
            flow_contribution += 0.08
        elif capital_flow == '净流出':
            flow_contribution -= 0.08
        
        # LLM分析贡献
        llm_confidence = llm_analysis.get('confidence', 0.5)
        llm_contribution = (llm_confidence - 0.5) * 0.1
        
        # 综合胜率
        estimated_win_prob = (base_win_prob + 
                             technical_contribution + 
                             gua_contribution + 
                             flow_contribution + 
                             llm_contribution)
        
        # 限制在合理范围内
        return np.clip(estimated_win_prob, 0.35, 0.85)
    
    def _estimate_win_probability_fallback(self, analysis_result: Dict[str, Any]) -> float:
        """CZSC不可用时的胜率估算方法"""
        
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        llm_analysis = analysis_result.get('llm_analysis', {})
        final_decision = analysis_result.get('final_decision', 'hold')
        
        # 基础胜率
        base_win_prob = 0.5
        
        # 基于最终决策调整 - 即使是hold也要分析原始信号
        original_decision = enhanced_decision.get('decision', final_decision)
        if original_decision == 'buy':
            decision_adjustment = 0.08  # 原始买入信号增加胜率
        elif original_decision == 'sell':
            decision_adjustment = 0.06  # 原始卖出信号适度增加胜率
        else:
            decision_adjustment = -0.02  # 观望轻微降低胜率
        
        # 基于增强决策的置信度调整
        enhanced_confidence = enhanced_decision.get('confidence', 0.5)
        confidence_adjustment = (enhanced_confidence - 0.5) * 0.15
        
        # 基于LLM分析调整 - 重点分析策略建议内容
        llm_adjustment = 0.0
        if llm_analysis and 'error' not in llm_analysis:
            llm_confidence = llm_analysis.get('confidence_level', 0.5)
            market_sentiment = llm_analysis.get('market_sentiment', '中性')
            strategy_suggestion = llm_analysis.get('strategy_suggestion', '')
            
            # LLM置信度贡献
            llm_adjustment += (llm_confidence - 0.5) * 0.1
            
            # 市场情绪贡献
            if market_sentiment in ['乐观', '积极']:
                llm_adjustment += 0.05
            elif market_sentiment in ['悲观', '消极']:
                llm_adjustment -= 0.03
            elif market_sentiment == '谨慎':
                llm_adjustment += 0.02  # 谨慎通常意味着有机会但需小心
            
            # 策略建议内容分析
            if strategy_suggestion:
                # 检查技术信号关键词
                positive_signals = [
                    'MA5', 'MA20', '金叉', 'MACD', '正值扩大', '多头', 
                    '突破', '上穿', '放量', '动能增强'
                ]
                negative_signals = [
                    '顶分型', '抛压', '破位', '止损', '风险'
                ]
                
                positive_count = sum(1 for signal in positive_signals if signal in strategy_suggestion)
                negative_count = sum(1 for signal in negative_signals if signal in strategy_suggestion)
                
                # 技术信号贡献
                signal_adjustment = (positive_count - negative_count) * 0.02
                llm_adjustment += signal_adjustment
        
        # 基于协调评分调整（如果有的话）
        coordination_adjustment = 0.0
        if enhanced_decision:
            coordination_score = enhanced_decision.get('coordination_score', 0.5)
            weighted_score = enhanced_decision.get('weighted_score', 0.0)
            
            # 协调评分贡献
            coordination_adjustment += (coordination_score - 0.5) * 0.08
            
            # 加权评分贡献 - 即使是负分也可能有交易价值
            if abs(weighted_score) > 0.2:  # 有明确信号
                coordination_adjustment += 0.04
        
        # 综合估算胜率
        estimated_win_prob = (base_win_prob + 
                             decision_adjustment + 
                             confidence_adjustment + 
                             llm_adjustment + 
                             coordination_adjustment)
        
        # 限制在合理范围内
        return np.clip(estimated_win_prob, 0.45, 0.75)
    
    def _calculate_risk_reward_ratio(self, analysis_result: Dict[str, Any]) -> float:
        """计算风险收益比"""
        
        # 检查是否所有智能体都失败了（CZSC不可用的情况）
        technical_data = analysis_result.get('technical_data', {})
        gua_data = analysis_result.get('gua_data', {})
        flow_data = analysis_result.get('flow_data', {})
        
        all_agents_failed = (
            technical_data.get('error') and 
            gua_data.get('error') and 
            flow_data.get('error')
        )
        
        if all_agents_failed:
            # CZSC不可用时，基于决策信息进行智能估算
            return self._estimate_risk_reward_fallback(analysis_result)
        
        # 正常情况下的风险收益比计算
        decision_info = self._extract_decision_info(analysis_result)
        
        entry_price = decision_info['entry_price']
        stop_loss = decision_info['stop_loss']
        take_profit = decision_info['take_profit']
        decision = decision_info['decision']
        
        # 如果是hold决策但有价格数据，仍然可以计算风险收益比
        if stop_loss == 0 or entry_price == 0:
            # 如果没有价格数据，使用fallback方法
            return self._estimate_risk_reward_fallback(analysis_result)
        
        try:
            if decision == 'buy':
                risk = abs(entry_price - stop_loss)
                reward = abs(take_profit - entry_price)
            else:  # sell
                risk = abs(stop_loss - entry_price)
                reward = abs(entry_price - take_profit)
            
            risk_reward_ratio = reward / risk if risk > 0 else 1.0
            
            # 确保最小风险收益比
            return max(risk_reward_ratio, 1.0)
            
        except Exception as e:
            self.logger.warning(f"风险收益比计算失败: {e}")
            return 1.5  # 默认值
    
    def _estimate_risk_reward_fallback(self, analysis_result: Dict[str, Any]) -> float:
        """CZSC不可用时的风险收益比估算方法"""
        
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        llm_analysis = analysis_result.get('llm_analysis', {})
        final_decision = analysis_result.get('final_decision', 'hold')
        
        # 基础风险收益比
        base_ratio = 1.2
        
        # 基于原始决策类型调整 - 即使最终是hold也要考虑原始信号
        original_decision = enhanced_decision.get('decision', final_decision)
        if original_decision == 'buy':
            decision_adjustment = 0.3  # 原始买入信号通常有更好的风险收益比
        elif original_decision == 'sell':
            decision_adjustment = 0.2  # 原始卖出信号相对保守
        else:
            decision_adjustment = 0.0  # 观望时基础调整
        
        # 基于置信度调整
        confidence_adjustment = 0.0
        if enhanced_decision:
            confidence = enhanced_decision.get('confidence', 0.5)
            weighted_score = enhanced_decision.get('weighted_score', 0.0)
            
            # 高置信度通常意味着更好的风险收益比
            confidence_adjustment += (confidence - 0.5) * 0.4
            
            # 强信号通常有更好的风险收益比
            if abs(weighted_score) > 0.2:  # 降低阈值，让更多信号被考虑
                confidence_adjustment += 0.15
        
        # 基于LLM分析调整 - 分析策略建议内容
        llm_adjustment = 0.0
        if llm_analysis and 'error' not in llm_analysis:
            llm_confidence = llm_analysis.get('confidence_level', 0.5)
            market_sentiment = llm_analysis.get('market_sentiment', '中性')
            strategy_suggestion = llm_analysis.get('strategy_suggestion', '')
            
            # LLM置信度贡献
            llm_adjustment += (llm_confidence - 0.5) * 0.3
            
            # 市场情绪贡献
            if market_sentiment in ['乐观', '积极']:
                llm_adjustment += 0.2
            elif market_sentiment in ['悲观', '消极']:
                llm_adjustment += 0.1  # 悲观时风险收益比可能更保守但仍有价值
            elif market_sentiment == '谨慎':
                llm_adjustment += 0.15  # 谨慎意味着有机会但需要更好的风险收益比
            
            # 策略建议内容分析 - 寻找风险收益相关信息
            if strategy_suggestion:
                # 检查有利于风险收益比的因素
                positive_factors = [
                    '突破', '金叉', '放量', '动能增强', '多头排列',
                    '支撑', '目标位', '上涨空间'
                ]
                risk_factors = [
                    '止损', '风险', '抛压', '阻力', '破位'
                ]
                
                positive_count = sum(1 for factor in positive_factors if factor in strategy_suggestion)
                risk_count = sum(1 for factor in risk_factors if factor in strategy_suggestion)
                
                # 风险收益因子贡献
                factor_adjustment = (positive_count - risk_count * 0.5) * 0.1
                llm_adjustment += factor_adjustment
        
        # 综合估算风险收益比
        estimated_ratio = base_ratio + decision_adjustment + confidence_adjustment + llm_adjustment
        
        # 限制在合理范围内，但允许更高的风险收益比
        return np.clip(estimated_ratio, 1.0, 2.5)
    
    def _calculate_overall_confidence(self, analysis_result: Dict[str, Any]) -> float:
        """计算综合置信度"""
        
        # 获取各智能体的置信度
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        base_confidence = enhanced_decision.get('confidence', 0.5)
        
        # 获取最终置信度（风控后）
        final_confidence = analysis_result.get('final_confidence', base_confidence)
        
        # 考虑信号一致性
        consistency_score = self._calculate_signal_consistency(analysis_result)
        
        # 综合置信度
        overall_confidence = final_confidence * 0.7 + consistency_score * 0.3
        
        return np.clip(overall_confidence, 0.1, 0.95)
    
    def _calculate_signal_consistency(self, analysis_result: Dict[str, Any]) -> float:
        """计算信号一致性"""
        
        # 获取各智能体的决策
        technical_data = analysis_result.get('technical_data', {})
        gua_data = analysis_result.get('gua_data', {})
        flow_data = analysis_result.get('flow_data', {})
        
        # 提取决策信号 - 修复数据结构匹配
        signals = []
        
        # 技术分析信号 - 基于实际数据结构
        if technical_data.get('buy_signal', False):
            signals.append('buy')
        else:
            signals.append('hold')
        
        # 卦象分析信号 - 基于实际数据结构
        if gua_data.get('is_buy_gua', False):
            signals.append('buy')
        elif gua_data.get('is_sell_gua', False):
            signals.append('sell')
        else:
            signals.append('hold')
        
        # 资金流向信号 - 基于实际数据结构
        capital_flow = flow_data.get('capital_flow', '平衡')
        if capital_flow == '净流入':
            signals.append('buy')
        elif capital_flow == '净流出':
            signals.append('sell')
        else:
            signals.append('hold')
        
        # 计算一致性
        if not signals:
            return 0.5
        
        # 统计各信号出现次数
        signal_counts = {'buy': 0, 'sell': 0, 'hold': 0}
        for signal in signals:
            signal_counts[signal] += 1
        
        # 计算最大一致性比例
        max_count = max(signal_counts.values())
        consistency = max_count / len(signals)
        
        return consistency
    
    def _generate_position_reasoning(self, decision_info: Dict, win_prob: float, 
                                   risk_reward: float, confidence: float, 
                                   kelly_result: KellyResult) -> str:
        """生成仓位推理说明"""
        
        reasoning_parts = []
        
        # 决策信息
        decision = decision_info['decision']
        reasoning_parts.append(f"交易决策：{decision.upper()}")
        
        # 概率分析
        reasoning_parts.append(f"胜率评估：{win_prob:.1%}")
        reasoning_parts.append(f"风险收益比：{risk_reward:.2f}")
        reasoning_parts.append(f"综合置信度：{confidence:.1%}")
        
        # 凯利计算
        reasoning_parts.append(f"凯利分数：{kelly_result.kelly_fraction:.2%}")
        reasoning_parts.append(f"建议仓位：{kelly_result.optimal_position:.2%}")
        reasoning_parts.append(f"风险等级：{kelly_result.risk_level}")
        
        # 具体建议
        if kelly_result.optimal_position > 0:
            if decision == 'buy':
                reasoning_parts.append(f"建议以{kelly_result.optimal_position:.1%}仓位做多")
            elif decision == 'sell':
                reasoning_parts.append(f"建议以{kelly_result.optimal_position:.1%}仓位做空")
        else:
            reasoning_parts.append("建议暂不建仓，保持观望")
        
        return " | ".join(reasoning_parts)
    
    def _display_kelly_calculation(self, fund_code: str, decision_info: Dict,
                                 win_prob: float, risk_reward: float, 
                                 confidence: float, kelly_result: KellyResult):
        """显示简化的凯利计算结果"""
        
        if not self.kelly_display:
            return
        
        try:
            # 使用简化的凯利结果显示，而不是详细的计算过程
            kelly_simplified = self.kelly_display.get_simplified_kelly_result(
                symbol=fund_code,
                signal_type=decision_info['decision'],
                win_probability=win_prob,
                risk_reward_ratio=risk_reward,
                confidence=confidence
            )
            
            # 只在日志中记录简化信息，不在控制台显示详细计算
            self.logger.info(f"💰 {fund_code} 凯利仓位: {kelly_simplified['position']:.1%} | {kelly_simplified['risk_level']}")
            
        except Exception as e:
            self.logger.warning(f"凯利计算显示失败: {e}")
    
    def _log_kelly_calculation(self, kelly_analysis: Dict[str, Any]):
        """记录凯利计算日志"""
        
        fund_code = kelly_analysis['fund_code']
        calc = kelly_analysis['kelly_calculation']
        
        self.logger.info(f"💰 凯利仓位计算 - {fund_code}")
        self.logger.info(f"   胜率: {calc['win_probability']:.1%}")
        self.logger.info(f"   盈亏比: {calc['risk_reward_ratio']:.2f}")
        self.logger.info(f"   置信度: {calc['confidence']:.1%}")
        self.logger.info(f"   凯利分数: {calc['kelly_fraction']:.2%}")
        self.logger.info(f"   建议仓位: {calc['optimal_position']:.2%}")
        self.logger.info(f"   风险等级: {calc['risk_level']}")
    
    def _create_fallback_kelly_result(self, fund_code: str) -> Dict[str, Any]:
        """创建备用的凯利结果"""
        
        return {
            'fund_code': fund_code,
            'kelly_calculation': {
                'win_probability': 0.5,
                'risk_reward_ratio': 1.0,
                'confidence': 0.3,
                'kelly_fraction': 0.0,
                'optimal_position': 0.0,
                'adjusted_position': 0.0,
                'risk_level': '无风险',
                'recommendation': '计算失败，建议观望'
            },
            'position_reasoning': '凯利公式计算失败，建议保持观望',
            'timestamp': datetime.now().isoformat()
        }

# 使用示例
if __name__ == "__main__":
    # 模拟分析结果
    mock_analysis_result = {
        'fund_code': '518880',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.65
        },
        'final_decision': 'buy',
        'final_confidence': 0.66,
        'technical_data': {
            'current_price': 7.525,
            '综合评分': 0.6,
            'support_resistance': {
                'support': 7.520,
                'resistance': 7.580
            }
        },
        'gua_data': {
            '综合评分': 0.55
        },
        'flow_data': {
            '综合评分': 0.62
        },
        'llm_analysis': {
            'confidence': 0.65
        }
    }
    
    # 初始化协调器
    coordinator = KellyPositionCoordinator({
        'enable_display': True,
        'enable_detailed_logging': True
    })
    
    # 计算凯利仓位
    kelly_result = coordinator.calculate_kelly_position(mock_analysis_result)
    
    print("\n=== 凯利仓位协调器测试结果 ===")
    print(f"基金代码: {kelly_result['fund_code']}")
    print(f"建议仓位: {kelly_result['kelly_calculation']['optimal_position']:.2%}")
    print(f"风险等级: {kelly_result['kelly_calculation']['risk_level']}")
    print(f"推理说明: {kelly_result['position_reasoning']}")