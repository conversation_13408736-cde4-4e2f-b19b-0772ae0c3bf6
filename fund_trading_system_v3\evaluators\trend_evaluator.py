"""
趋势维度评估器
负责评估市场趋势的强度、方向和可持续性
"""

import logging
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult
from core.enums import TrendState


class TrendEvaluator:
    """
    @class TrendEvaluator
    @brief 趋势维度评估器
    @details 负责评估市场趋势的强度、方向和可持续性
    """
    
    def __init__(self):
        self.name = "TrendEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估趋势维度
        @param data: 市场数据
        @return: 趋势评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            technical_data = data.get('technical_data', {})
            
            # 获取技术指标数据
            ma5 = technical_data.get('ma5', 0)
            ma20 = technical_data.get('ma20', 0)
            rsi = technical_data.get('rsi', 50)
            macd = technical_data.get('macd', 0)
            
            current_price = price_data.get('price', 0)
            change_rate = price_data.get('change_rate', 0)
            
            # 趋势强度计算
            trend_signals = []
            trend_score = 0.0
            
            # 1. 移动平均线趋势判断
            if ma5 > ma20:
                ma_trend = "上涨"
                ma_strength = min(1.0, (ma5 - ma20) / ma20 * 10)
                trend_score += ma_strength * 0.3
                trend_signals.append(f"MA5>MA20(+{ma_strength:.2f})")
            elif ma5 < ma20:
                ma_trend = "下跌"
                ma_strength = min(1.0, (ma20 - ma5) / ma20 * 10)
                trend_score -= ma_strength * 0.3
                trend_signals.append(f"MA5<MA20(-{ma_strength:.2f})")
            else:
                ma_trend = "横盘"
                trend_signals.append("MA5=MA20(0)")
            
            # 2. RSI趋势确认
            if rsi > 70:
                rsi_signal = "超买"
                rsi_strength = min(1.0, (rsi - 70) / 30)
                trend_score += rsi_strength * 0.2
                trend_signals.append(f"RSI超买({rsi:.1f})")
            elif rsi < 30:
                rsi_signal = "超卖"
                rsi_strength = min(1.0, (30 - rsi) / 30)
                trend_score -= rsi_strength * 0.2
                trend_signals.append(f"RSI超卖({rsi:.1f})")
            else:
                rsi_signal = "正常"
                trend_signals.append(f"RSI正常({rsi:.1f})")
            
            # 3. MACD趋势确认
            if macd > 0:
                macd_signal = "向上"
                macd_strength = min(1.0, abs(macd) * 100)
                trend_score += macd_strength * 0.25
                trend_signals.append(f"MACD向上(+{macd:.3f})")
            elif macd < 0:
                macd_signal = "向下"
                macd_strength = min(1.0, abs(macd) * 100)
                trend_score -= macd_strength * 0.25
                trend_signals.append(f"MACD向下({macd:.3f})")
            else:
                macd_signal = "中性"
                trend_signals.append("MACD中性")
            
            # 4. 价格变化趋势
            if abs(change_rate) > 2:
                price_signal = "强势" if change_rate > 0 else "弱势"
                price_strength = min(1.0, abs(change_rate) / 5)
                if change_rate > 0:
                    trend_score += price_strength * 0.25
                else:
                    trend_score -= price_strength * 0.25
                trend_signals.append(f"价格{price_signal}({change_rate:+.2f}%)")
            else:
                price_signal = "稳定"
                trend_signals.append(f"价格稳定({change_rate:+.2f}%)")
            
            # 趋势状态判断
            if trend_score >= 0.7:
                trend_state = TrendState.STRONG_UPTREND
            elif trend_score >= 0.3:
                trend_state = TrendState.WEAK_UPTREND
            elif trend_score >= -0.3:
                trend_state = TrendState.SIDEWAYS
            elif trend_score >= -0.7:
                trend_state = TrendState.WEAK_DOWNTREND
            else:
                trend_state = TrendState.STRONG_DOWNTREND
            
            # 置信度计算
            signal_consistency = len([s for s in [ma_trend, rsi_signal, macd_signal, price_signal] 
                                    if any(word in s for word in ['上涨', '向上', '超买', '强势'])])
            confidence = min(0.95, max(0.1, signal_consistency / 4.0 + abs(trend_score) * 0.3))
            
            # 数据质量评估
            data_quality = "good" if all([ma5, ma20, current_price]) else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="趋势",
                state=trend_state,
                score=trend_score,
                confidence=confidence,
                signals=trend_signals,
                data_quality=data_quality,
                details={
                    'ma_trend': ma_trend,
                    'rsi_signal': rsi_signal,
                    'macd_signal': macd_signal,
                    'price_signal': price_signal
                },
                indicators={
                    'ma5': ma5,
                    'ma20': ma20,
                    'rsi': rsi,
                    'macd': macd,
                    'change_rate': change_rate
                }
            )
            
        except Exception as e:
            self.logger.error(f"Trend evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="趋势",
                state=TrendState.SIDEWAYS,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )
