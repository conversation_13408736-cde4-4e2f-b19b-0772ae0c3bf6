"""
测试Kimi API连接
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.ai_config import get_ai_config

def test_kimi_api():
    """测试Kimi API连接"""
    print("🧪 测试Kimi API连接")
    print("="*50)
    
    # 获取配置
    ai_config = get_ai_config()
    
    # 检查配置
    print("1. 检查配置...")
    moonshot_config = ai_config.get_llm_config("moonshot")
    print(f"   配置状态: {'✓' if moonshot_config else '✗'}")
    print(f"   API密钥: {'✓' if moonshot_config.get('api_key') else '✗'}")
    print(f"   启用状态: {'✓' if moonshot_config.get('enabled') else '✗'}")
    
    if not moonshot_config.get('api_key'):
        print("❌ API密钥未配置")
        return False
    
    # 测试OpenAI库导入
    print("\n2. 测试OpenAI库...")
    try:
        from openai import OpenAI
        print("   OpenAI库: ✓")
    except ImportError as e:
        print(f"   OpenAI库: ✗ ({e})")
        return False
    
    # 测试API连接
    print("\n3. 测试API连接...")
    try:
        client = OpenAI(
            api_key=moonshot_config['api_key'],
            base_url=moonshot_config['base_url']
        )
        
        # 发送测试请求
        response = client.chat.completions.create(
            model="kimi-k2-0711-preview",
            messages=[
                {"role": "user", "content": "你好，请简单回复一句话确认连接正常。"}
            ],
            max_tokens=50,
            temperature=0.3
        )
        
        reply = response.choices[0].message.content
        print(f"   API响应: ✓")
        print(f"   回复内容: {reply}")
        
        return True
        
    except Exception as e:
        print(f"   API连接: ✗ ({e})")
        return False

def test_llm_analyzer():
    """测试LLM市场分析器"""
    print("\n🔍 测试LLM市场分析器")
    print("="*50)
    
    try:
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        
        # 创建分析器
        analyzer = LLMMarketAnalyzer()
        
        # 检查客户端状态
        if analyzer.client:
            print("   分析器初始化: ✓")
            print("   LLM客户端: ✓")
            
            # 测试市场分析
            print("\n   测试市场分析...")
            test_data = {
                'evaluations': {},
                'price_data': {
                    'current_price': 1.2345,
                    'change_pct': 2.15
                }
            }
            
            result = analyzer.analyze_market_narrative(test_data, "TEST001")
            
            if result.get('analysis_type') == 'llm_market_analysis':
                print("   市场分析: ✓")
                print(f"   分析结果: {result.get('market_sentiment', 'N/A')}")
            else:
                print("   市场分析: ✗ (使用兜底模式)")
                
        else:
            print("   分析器初始化: ✗")
            print("   LLM客户端: ✗")
            
    except Exception as e:
        print(f"   测试失败: {e}")

if __name__ == "__main__":
    # 测试API连接
    api_ok = test_kimi_api()
    
    # 测试分析器
    test_llm_analyzer()
    
    # 总结
    print("\n" + "="*50)
    if api_ok:
        print("🎉 Kimi API连接正常！")
        print("💡 建议: 系统已准备好使用LLM功能")
    else:
        print("❌ Kimi API连接失败")
        print("💡 建议: 检查API密钥配置或网络连接")