"""
测试集成的动态基金分类功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from system.enhanced_trading_system import EnhancedFundTradingSystemV3


def test_integrated_fund_categories():
    """测试集成的基金分类功能"""
    print("🔗 测试集成的动态基金分类功能")
    print("=" * 60)
    
    # 创建交易系统（会自动初始化基金分析器）
    trading_system = EnhancedFundTradingSystemV3("测试系统")
    
    print("1. 测试系统初始化后的基金分类:")
    for fund_code in trading_system.buy_fund_list:
        category_info = trading_system.get_fund_category_info(fund_code)
        print(f"   {fund_code}: {category_info['category']} (关联性: {category_info['correlation_score']})")
    
    print("\n2. 测试基金分类统计:")
    stats = trading_system.fund_analyzer.get_category_stats()
    for category, count in stats.items():
        print(f"   {category}: {count}只基金")
    
    print("\n3. 测试新基金的动态分类:")
    # 模拟添加新基金到列表
    new_funds = ['513100', '600036', '000001']
    trading_system.buy_fund_list.extend(new_funds)
    
    # 重新同步分类
    trading_system._sync_fund_categories()
    
    print("   新添加的基金分类:")
    for fund_code in new_funds:
        category_info = trading_system.get_fund_category_info(fund_code)
        print(f"   {fund_code}: {category_info['category']} (关联性: {category_info['correlation_score']})")
    
    print("\n4. 测试类别调整系数:")
    test_funds = ['518880', '601398', '513500']
    for fund_code in test_funds:
        category_info = trading_system.get_fund_category_info(fund_code)
        adjustment = category_info['category_adjustment']
        print(f"   {fund_code} ({category_info['category']}):")
        print(f"     敏感度调整: {adjustment['sensitivity']}")
        print(f"     平滑度调整: {adjustment['smoothness']}")
    
    print("\n5. 测试基金特性分析集成:")
    # 测试基金特性分析是否能正确使用分类信息
    try:
        import pandas as pd
        import numpy as np
        
        # 创建模拟数据
        mock_df = pd.DataFrame({
            'close': np.random.randn(100).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        fund_code = '518880'  # 黄金ETF
        characteristics = trading_system.fund_analyzer.analyze_fund_characteristics(
            fund_code, mock_df, 1000000000
        )
        
        print(f"   {fund_code} 特性分析:")
        print(f"     类别: {characteristics['category']}")
        print(f"     流动性评分: {characteristics['liquidity_score']:.3f}")
        print(f"     关联性评分: {characteristics['correlation_score']:.3f}")
        print(f"     综合评分: {characteristics['composite_score']:.3f}")
        
    except Exception as e:
        print(f"   基金特性分析测试跳过: {e}")
    
    print("\n✅ 集成的动态基金分类功能测试完成!")
    print("=" * 60)
    
    return trading_system


def test_category_persistence():
    """测试分类持久化功能"""
    print("\n💾 测试分类持久化功能")
    print("-" * 40)
    
    # 创建第一个系统实例
    system1 = EnhancedFundTradingSystemV3("系统1")
    
    # 添加自定义分类
    system1.fund_analyzer.fund_categories['999888'] = 'test_category'
    
    # 保存配置
    success = system1.fund_analyzer.save_categories_to_config()
    print(f"保存分类配置: {'成功' if success else '失败'}")
    
    # 创建第二个系统实例
    system2 = EnhancedFundTradingSystemV3("系统2")
    
    # 检查是否加载了之前的分类
    category = system2.fund_analyzer.get_fund_category('999888')
    print(f"加载自定义分类: 999888 -> {category}")
    
    # 清理测试数据
    if '999888' in system2.fund_analyzer.fund_categories:
        del system2.fund_analyzer.fund_categories['999888']
        system2.fund_analyzer.save_categories_to_config()
        print("清理测试数据: 成功")
    
    print("✅ 分类持久化功能测试完成!")


if __name__ == "__main__":
    try:
        trading_system = test_integrated_fund_categories()
        test_category_persistence()
        
        print("\n🎉 所有集成测试完成!")
        print("✅ 动态基金分类系统已完全集成到交易系统中")
        print("🔄 系统现在可以自动识别和分类新的基金代码")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
