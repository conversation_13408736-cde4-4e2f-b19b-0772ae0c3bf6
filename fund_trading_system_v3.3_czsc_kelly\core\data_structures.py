"""
核心数据结构定义
包含系统中使用的所有数据类
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Union, Optional, Tuple, Any
from .enums import MarketRegime


@dataclass
class RawBar:
    """原始K线数据结构"""
    symbol: str
    dt: datetime
    id: int
    freq: str
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float


@dataclass
class DimensionEvaluationResult:
    """维度评估结果"""
    dimension_name: str
    state: Any  # 对应的枚举状态
    score: float
    confidence: float
    signals: List[str]
    data_quality: str
    details: Optional[Dict[str, Any]] = None
    indicators: Optional[Dict[str, float]] = None


@dataclass
class MultiDimensionalMarketState:
    """多维度市场状态"""
    timestamp: datetime
    trend: DimensionEvaluationResult
    volatility: DimensionEvaluationResult
    liquidity: DimensionEvaluationResult
    sentiment: DimensionEvaluationResult
    structural: DimensionEvaluationResult
    transition: DimensionEvaluationResult
    composite_score: float
    overall_confidence: float
    market_regime: MarketRegime
    recommended_action: str
    risk_level: str


@dataclass
class SignalConflictResult:
    """信号冲突处理结果"""
    final_signal: int  # -1, 0, 1
    final_strength: float
    final_confidence: float
    conflict_reason: str
    resolution_method: str
    contributing_signals: List[Dict[str, Any]]


@dataclass
class DynamicWeights:
    """动态权重结果"""
    trend_weight: float
    volatility_weight: float
    liquidity_weight: float
    sentiment_weight: float
    structural_weight: float
    transition_weight: float
    adjustment_reason: str
    market_conditions: Dict[str, float]


@dataclass
class EnhancedAnalysisResult:
    """增强分析结果"""
    symbol: str
    timestamp: datetime
    market_state: MultiDimensionalMarketState
    signal_conflict_result: SignalConflictResult
    dynamic_weights: DynamicWeights
    composite_score: float
    recommendation: str
    confidence: float
    risk_assessment: Dict[str, Any]
    execution_details: Dict[str, Any]


@dataclass
class TimeframeCycleDivergence:
    """时间周期背驰分析结果"""
    timeframe: str  # 时间框架
    divergence_type: str  # 背驰类型 
    divergence_strength: float  # 背驰强度 (0-1)
    macd_area_ratio: float  # MACD面积比率
    dif_peak_comparison: Dict[str, float]  # DIF峰值比较
    confirmation_bars: int  # 确认K线数量
    cycle_position: str  # 周期位置: early/middle/late


@dataclass  
class MultiTimeframeResonance:
    """多时间框架共振分析结果"""
    short_term: TimeframeCycleDivergence
    medium_term: TimeframeCycleDivergence  
    long_term: TimeframeCycleDivergence
    resonance_score: float  # 共振评分 (0-1)
    consensus_direction: str  # 共识方向: bullish/bearish/neutral
    confidence_level: float  # 置信水平 (0-1)
    transition_signal: str  # 变盘信号强度: strong/medium/weak/none


@dataclass
class CzscBiStructure:
    """缠论笔结构数据"""
    bi_direction: str
    bi_start_price: float
    bi_end_price: float
    bi_amplitude: float
    bi_duration: int
    macd_area: float
    dif_peak: float


@dataclass
class RiskControlResult:
    """风控验证结果"""
    fund_code: str
    validation_time: datetime
    passed: bool
    risk_level: str  # 'low', 'medium', 'high', 'critical'
    rejection_reasons: List[str]
    technical_violations: Dict[str, Any]
    market_environment_score: float
    portfolio_risk_score: float
    recommended_action: str  # 'proceed', 'reduce_size', 'delay', 'reject'
    confidence: float
    details: Dict[str, Any]


@dataclass
class TechnicalIndicatorResult:
    """技术指标验证结果"""
    indicator_name: str
    value: float
    threshold: float
    condition_met: bool
    score: float
    violation_reason: Optional[str]
    analysis: str
    weight: float


@dataclass
class PositionInfo:
    """持仓信息"""
    fund_code: str
    shares: float
    average_cost: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    max_profit: float  # 历史最高盈利
    max_profit_pct: float  # 历史最高盈利百分比
    current_drawdown: float  # 当前回撤
    current_drawdown_pct: float  # 当前回撤百分比
    last_update: datetime
    entry_date: datetime
    holding_days: int


@dataclass
class DrawdownCheckResult:
    """回撤检查结果"""
    fund_code: str
    current_profit_pct: float
    max_profit_pct: float
    drawdown_from_peak: float
    drawdown_threshold: float
    should_sell: bool
    sell_reason: str
    recommended_sell_ratio: float  # 建议卖出比例 0.0-1.0
    urgency_level: str  # 'low', 'medium', 'high', 'critical'
    check_time: datetime


@dataclass
class SellTriggerResult:
    """卖出触发结果"""
    triggered: bool
    trigger_type: str  # 'profit_drawdown', 'stop_loss', 'risk_limit', 'technical_signal'
    current_position: PositionInfo
    recommended_action: str  # 'partial_sell', 'full_sell', 'hold'
    sell_ratio: float
    urgency_level: str  # 'low', 'medium', 'high', 'critical'
    explanation: str
    trigger_time: datetime


@dataclass
class Transaction:
    """交易记录"""
    transaction_id: str
    fund_code: str
    transaction_type: str  # 'buy', 'sell'
    shares: float
    price: float
    amount: float
    commission: float
    timestamp: datetime
    reason: str
    related_signal: Optional[str]


@dataclass
class PerformanceMetrics:
    """绩效指标"""
    fund_code: str
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    profitable_trades: int
    average_holding_days: float
    calculation_period: int  # 计算周期（天）


@dataclass
class RiskAlert:
    """风险预警"""
    alert_id: str
    fund_code: str
    alert_type: str  # 'drawdown_warning', 'position_limit', 'correlation_risk'
    severity: str  # 'low', 'medium', 'high', 'critical'
    message: str
    current_value: float
    threshold_value: float
    recommended_action: str
    alert_time: datetime
    acknowledged: bool


@dataclass
class MarketEnvironmentAssessment:
    """市场环境评估"""
    assessment_time: datetime
    market_regime: str  # 'bull', 'bear', 'sideways'
    volatility_level: str  # 'low', 'medium', 'high'
    liquidity_condition: str  # 'good', 'fair', 'poor'
    sentiment_score: float
    trend_strength: float
    overall_risk_level: str
    recommended_strategy: str


@dataclass
class FundRiskProfile:
    """基金风险档案"""
    fund_code: str
    risk_rating: str  # 'conservative', 'moderate', 'aggressive'
    volatility_percentile: float
    correlation_with_market: float
    liquidity_score: float
    sector_concentration: float
    historical_max_drawdown: float
    beta: float
    tracking_error: float
    last_updated: datetime
