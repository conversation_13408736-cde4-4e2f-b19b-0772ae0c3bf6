"""
增强版交易环境 - 支持RL训练和LLM状态增强
实现完整的基金交易环境，集成六大维度评估和LLM分析结果
"""

import gym
from gym import spaces
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, List
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from analyzers.llm_market_analyzer import LLMMarketAnalyzer
from .feature_engineer import FeatureEngineer


class EnhancedTradingEnv(gym.Env):
    """
    @class EnhancedTradingEnv
    @brief 增强版交易环境
    @details 支持LLM增强的状态空间和基金交易的完整环境模拟
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super(EnhancedTradingEnv, self).__init__()
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 环境参数
        self.state_dim = self.config.get('state_dim', 50)
        self.action_dim = self.config.get('action_dim', 2)
        self.initial_balance = self.config.get('initial_balance', 100000.0)
        self.transaction_cost = self.config.get('transaction_cost', 0.001)
        self.max_position_size = self.config.get('max_position_size', 1.0)
        
        # 定义动作空间和观察空间
        self.action_space = spaces.Box(
            low=np.array([-1.0, 0.0]),  # [position_change, confidence]
            high=np.array([1.0, 1.0]),
            dtype=np.float32
        )
        
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.state_dim,),
            dtype=np.float32
        )
        
        # 环境状态
        self.current_step = 0
        self.max_steps = self.config.get('max_steps', 1000)
        self.data = None
        self.data_index = 0
        
        # 交易状态
        self.balance = self.initial_balance
        self.position = 0.0  # 当前持仓比例 [-1, 1]
        self.portfolio_value = self.initial_balance
        self.transaction_history = []
        
        # 性能跟踪
        self.episode_returns = []
        self.episode_sharpe_ratios = []
        self.episode_max_drawdowns = []
        
        # LLM增强组件
        self.llm_analyzer = LLMMarketAnalyzer()
        self.feature_engineer = FeatureEngineer()
        self.enable_llm_enhancement = self.config.get('enable_llm_enhancement', True)
        
        # 奖励函数参数
        self.reward_config = self.config.get('reward_config', {})
        self.alpha = self.reward_config.get('alpha', 1.0)  # 收益权重
        self.beta = self.reward_config.get('beta', 0.5)    # 风险权重
        self.gamma = self.reward_config.get('gamma', 0.3)  # 回撤权重
        self.delta = self.reward_config.get('delta', 0.1)  # LLM一致性权重
        
        self.logger.info("增强版交易环境初始化完成")
    
    def reset(self, seed: Optional[int] = None) -> np.ndarray:
        """
        重置环境到初始状态
        """
        super().reset(seed=seed)
        
        # 重置环境状态
        self.current_step = 0
        self.data_index = 0
        
        # 重置交易状态
        self.balance = self.initial_balance
        self.position = 0.0
        self.portfolio_value = self.initial_balance
        self.transaction_history = []
        
        # 生成或加载数据
        if self.data is None:
            self.data = self._generate_market_data()
        
        # 获取初始观察
        initial_observation = self._get_observation()
        
        self.logger.info("环境已重置")
        return initial_observation
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """
        执行一步动作
        """
        try:
            # 解析动作
            position_change, confidence = self._parse_action(action)
            
            # 执行交易
            execution_result = self._execute_trade(position_change, confidence)
            
            # 更新环境状态
            self.current_step += 1
            self.data_index = min(self.data_index + 1, len(self.data) - 1)
            
            # 计算奖励
            reward = self._calculate_reward(execution_result)
            
            # 检查是否结束
            done = self._check_done()
            
            # 获取新观察
            next_observation = self._get_observation()
            
            # 构建信息字典
            info = self._build_info_dict(execution_result, reward)
            
            return next_observation, reward, done, info
            
        except Exception as e:
            self.logger.error(f"环境步进失败: {str(e)}")
            # 返回安全的默认值
            return self._get_observation(), -1.0, True, {'error': str(e)}
    
    def _parse_action(self, action: np.ndarray) -> Tuple[float, float]:
        """解析动作"""
        try:
            if len(action) >= 2:
                position_change = np.clip(action[0], -1.0, 1.0)
                confidence = np.clip(action[1], 0.0, 1.0)
            else:
                position_change = np.clip(action[0], -1.0, 1.0)
                confidence = 0.5
            
            return float(position_change), float(confidence)
            
        except Exception as e:
            self.logger.error(f"解析动作失败: {str(e)}")
            return 0.0, 0.5
    
    def _execute_trade(self, position_change: float, confidence: float) -> Dict[str, Any]:
        """执行交易"""
        try:
            # 获取当前市场数据
            current_data = self.data.iloc[self.data_index]
            current_price = current_data['price']
            
            # 计算目标持仓
            new_position = np.clip(self.position + position_change, -self.max_position_size, self.max_position_size)
            actual_position_change = new_position - self.position
            
            # 计算交易成本
            trade_amount = abs(actual_position_change) * self.portfolio_value
            transaction_cost = trade_amount * self.transaction_cost
            
            # 更新持仓和余额
            old_position = self.position
            old_portfolio_value = self.portfolio_value
            
            self.position = new_position
            self.balance -= transaction_cost
            
            # 计算新的组合价值
            price_return = (current_price - current_data.get('prev_price', current_price)) / current_data.get('prev_price', current_price)
            position_pnl = self.position * self.portfolio_value * price_return
            self.portfolio_value = self.balance + abs(self.position) * self.portfolio_value
            
            # 记录交易
            trade_record = {
                'step': self.current_step,
                'timestamp': current_data.get('timestamp', datetime.now().isoformat()),
                'price': current_price,
                'position_change': actual_position_change,
                'new_position': self.position,
                'confidence': confidence,
                'transaction_cost': transaction_cost,
                'portfolio_value': self.portfolio_value,
                'pnl': position_pnl
            }
            
            self.transaction_history.append(trade_record)
            
            return {
                'executed': True,
                'position_change': actual_position_change,
                'transaction_cost': transaction_cost,
                'portfolio_value': self.portfolio_value,
                'pnl': position_pnl,
                'trade_record': trade_record
            }
            
        except Exception as e:
            self.logger.error(f"执行交易失败: {str(e)}")
            return {
                'executed': False,
                'error': str(e),
                'position_change': 0.0,
                'transaction_cost': 0.0,
                'portfolio_value': self.portfolio_value,
                'pnl': 0.0
            }
    
    def _calculate_reward(self, execution_result: Dict[str, Any]) -> float:
        """计算奖励"""
        try:
            if not execution_result.get('executed', False):
                return -0.1  # 执行失败的惩罚
            
            # 基础收益奖励
            pnl = execution_result.get('pnl', 0.0)
            return_reward = self.alpha * (pnl / self.initial_balance)
            
            # 风险惩罚
            portfolio_volatility = self._calculate_portfolio_volatility()
            risk_penalty = self.beta * portfolio_volatility
            
            # 回撤惩罚
            max_drawdown = self._calculate_current_drawdown()
            drawdown_penalty = self.gamma * max_drawdown
            
            # LLM一致性奖励
            llm_alignment_bonus = 0.0
            if self.enable_llm_enhancement:
                llm_alignment_bonus = self._calculate_llm_alignment_bonus(execution_result)
            
            # 综合奖励
            total_reward = return_reward - risk_penalty - drawdown_penalty + self.delta * llm_alignment_bonus
            
            return float(total_reward)
            
        except Exception as e:
            self.logger.error(f"计算奖励失败: {str(e)}")
            return -0.1
    
    def _get_observation(self) -> np.ndarray:
        """获取当前观察"""
        try:
            if self.data_index >= len(self.data):
                # 返回最后一个有效观察
                self.data_index = len(self.data) - 1
            
            current_data = self.data.iloc[self.data_index]
            
            # 使用特征工程器生成状态
            if self.enable_llm_enhancement:
                # 获取LLM增强特征
                market_data = self._prepare_market_data_for_llm(current_data)
                enhanced_state = self.feature_engineer.create_enhanced_state_vector(
                    market_data, llm_insights=None
                )
                return enhanced_state
            else:
                # 基础技术特征
                basic_state = self.feature_engineer.create_basic_state_vector(current_data, self.position, self.portfolio_value)
                return basic_state
                
        except Exception as e:
            self.logger.error(f"获取观察失败: {str(e)}")
            return np.zeros(self.state_dim, dtype=np.float32)
    
    def _prepare_market_data_for_llm(self, current_data: pd.Series) -> Dict[str, Any]:
        """为LLM准备市场数据"""
        return {
            'price_data': {
                'current_price': current_data.get('price', 1.0),
                'change_pct': current_data.get('price_change_pct', 0.0),
                'volume': current_data.get('volume', 1000000)
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': current_data.get('rsi', 50),
                    'macd': current_data.get('macd', 0.0),
                    'ma5': current_data.get('ma5', current_data.get('price', 1.0)),
                    'ma20': current_data.get('ma20', current_data.get('price', 1.0))
                }
            },
            'timestamp': current_data.get('timestamp', datetime.now().isoformat())
        }
    
    def _check_done(self) -> bool:
        """检查是否结束"""
        # 达到最大步数
        if self.current_step >= self.max_steps:
            return True
        
        # 数据用完
        if self.data_index >= len(self.data) - 1:
            return True
        
        # 组合价值过低
        if self.portfolio_value < self.initial_balance * 0.1:
            return True
        
        return False
    
    def _build_info_dict(self, execution_result: Dict[str, Any], reward: float) -> Dict[str, Any]:
        """构建信息字典"""
        return {
            'step': self.current_step,
            'portfolio_value': self.portfolio_value,
            'position': self.position,
            'balance': self.balance,
            'reward': reward,
            'execution_result': execution_result,
            'total_return': (self.portfolio_value - self.initial_balance) / self.initial_balance,
            'transaction_count': len(self.transaction_history)
        }
    
    def _generate_market_data(self) -> pd.DataFrame:
        """生成模拟市场数据"""
        try:
            np.random.seed(self.config.get('data_seed', 42))
            
            # 生成价格序列
            num_periods = self.max_steps + 100  # 额外的缓冲
            initial_price = 1.0
            
            # 使用几何布朗运动生成价格
            dt = 1.0 / 252  # 日频数据
            mu = 0.1  # 年化收益率
            sigma = 0.2  # 年化波动率
            
            price_changes = np.random.normal(
                (mu - 0.5 * sigma**2) * dt,
                sigma * np.sqrt(dt),
                num_periods
            )
            
            prices = [initial_price]
            for change in price_changes:
                prices.append(prices[-1] * np.exp(change))
            
            # 生成技术指标
            df = pd.DataFrame({
                'timestamp': pd.date_range(start='2023-01-01', periods=num_periods, freq='D'),
                'price': prices[1:],
                'prev_price': prices[:-1]
            })
            
            # 计算价格变化百分比
            df['price_change_pct'] = (df['price'] - df['prev_price']) / df['prev_price'] * 100
            
            # 生成技术指标
            df['volume'] = np.random.randint(500000, 2000000, num_periods)
            df['rsi'] = self._calculate_rsi(df['price'])
            df['macd'] = self._calculate_macd(df['price'])
            df['ma5'] = df['price'].rolling(window=5).mean().fillna(df['price'])
            df['ma20'] = df['price'].rolling(window=20).mean().fillna(df['price'])
            
            self.logger.info(f"生成了 {len(df)} 条市场数据")
            return df
            
        except Exception as e:
            self.logger.error(f"生成市场数据失败: {str(e)}")
            # 返回最小数据集
            return pd.DataFrame({
                'timestamp': [datetime.now()],
                'price': [1.0],
                'prev_price': [1.0],
                'price_change_pct': [0.0],
                'volume': [1000000],
                'rsi': [50.0],
                'macd': [0.0],
                'ma5': [1.0],
                'ma20': [1.0]
            })
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.fillna(50)
        except:
            return pd.Series([50] * len(prices), index=prices.index)
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26) -> pd.Series:
        """计算MACD指标"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            return macd.fillna(0)
        except:
            return pd.Series([0] * len(prices), index=prices.index)
    
    def _calculate_portfolio_volatility(self, window: int = 20) -> float:
        """计算组合波动率"""
        try:
            if len(self.transaction_history) < 2:
                return 0.0
            
            recent_returns = []
            for i in range(max(0, len(self.transaction_history) - window), len(self.transaction_history)):
                pnl = self.transaction_history[i].get('pnl', 0.0)
                portfolio_value = self.transaction_history[i].get('portfolio_value', self.initial_balance)
                return_rate = pnl / portfolio_value if portfolio_value > 0 else 0.0
                recent_returns.append(return_rate)
            
            if len(recent_returns) > 1:
                return float(np.std(recent_returns))
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"计算组合波动率失败: {str(e)}")
            return 0.0
    
    def _calculate_current_drawdown(self) -> float:
        """计算当前回撤"""
        try:
            if len(self.transaction_history) == 0:
                return 0.0
            
            portfolio_values = [record['portfolio_value'] for record in self.transaction_history]
            portfolio_values.append(self.portfolio_value)
            
            peak = max(portfolio_values)
            current_value = portfolio_values[-1]
            
            if peak > 0:
                drawdown = (peak - current_value) / peak
                return max(0.0, drawdown)
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"计算回撤失败: {str(e)}")
            return 0.0
    
    def _calculate_llm_alignment_bonus(self, execution_result: Dict[str, Any]) -> float:
        """计算LLM一致性奖励"""
        try:
            # 简化实现：如果有交易执行且置信度高，给予奖励
            if execution_result.get('executed', False):
                trade_record = execution_result.get('trade_record', {})
                confidence = trade_record.get('confidence', 0.5)
                
                # 置信度高的交易给予更多奖励
                if confidence > 0.7:
                    return 1.0
                elif confidence > 0.5:
                    return 0.5
                else:
                    return 0.0
            else:
                return -0.1  # 执行失败的惩罚
                
        except Exception as e:
            self.logger.error(f"计算LLM一致性奖励失败: {str(e)}")
            return 0.0
    
    def set_data(self, data: pd.DataFrame) -> None:
        """设置外部数据"""
        self.data = data
        self.logger.info(f"设置外部数据，共 {len(data)} 条记录")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            if len(self.transaction_history) == 0:
                return {
                    'total_return': 0.0,
                    'sharpe_ratio': 0.0,
                    'max_drawdown': 0.0,
                    'win_rate': 0.0,
                    'transaction_count': 0
                }
            
            # 计算总收益率
            total_return = (self.portfolio_value - self.initial_balance) / self.initial_balance
            
            # 计算夏普比率
            returns = [record.get('pnl', 0.0) / self.initial_balance for record in self.transaction_history]
            if len(returns) > 1:
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0.0
            else:
                sharpe_ratio = 0.0
            
            # 计算最大回撤
            portfolio_values = [record['portfolio_value'] for record in self.transaction_history]
            peak = np.maximum.accumulate(portfolio_values)
            drawdowns = (peak - portfolio_values) / peak
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0.0
            
            # 计算胜率
            profitable_trades = sum(1 for record in self.transaction_history if record.get('pnl', 0) > 0)
            win_rate = profitable_trades / len(self.transaction_history) if len(self.transaction_history) > 0 else 0.0
            
            return {
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'transaction_count': len(self.transaction_history),
                'current_position': self.position,
                'portfolio_value': self.portfolio_value
            }
            
        except Exception as e:
            self.logger.error(f"计算性能指标失败: {str(e)}")
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'transaction_count': 0,
                'error': str(e)
            }
    
    def render(self, mode: str = 'human') -> None:
        """渲染环境状态"""
        if mode == 'human':
            metrics = self.get_performance_metrics()
            print(f"Step: {self.current_step}")
            print(f"Portfolio Value: {self.portfolio_value:.2f}")
            print(f"Position: {self.position:.3f}")
            print(f"Total Return: {metrics['total_return']:.3f}")
            print(f"Transaction Count: {metrics['transaction_count']}")
            print("-" * 40)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'state_dim': 50,
            'action_dim': 2,
            'initial_balance': 100000.0,
            'transaction_cost': 0.001,
            'max_position_size': 1.0,
            'max_steps': 1000,
            'enable_llm_enhancement': True,
            'data_seed': 42,
            'reward_config': {
                'alpha': 1.0,  # 收益权重
                'beta': 0.5,   # 风险权重
                'gamma': 0.3,  # 回撤权重
                'delta': 0.1   # LLM一致性权重
            }
        } 