"""
数据预处理器
Data Preprocessor
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据预处理器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.missing_strategy = self.config.get('missing_strategy', 'interpolate')
        self.outlier_threshold = self.config.get('outlier_threshold', 3.0)
        self.min_data_points = self.config.get('min_data_points', 20)
        
        logger.info("DataPreprocessor 初始化完成")
    
    def preprocess(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        预处理数据
        
        Args:
            data: 原始数据
            
        Returns:
            预处理后的数据
        """
        logger.info(f"开始数据预处理，原始数据形状: {data.shape}")
        
        try:
            processed_data = data.copy()
            
            # 1. 数据类型检查和转换
            processed_data = self._convert_data_types(processed_data)
            
            # 2. 处理缺失值
            processed_data = self._handle_missing_values(processed_data)
            
            # 3. 处理异常值
            processed_data = self._handle_outliers(processed_data)
            
            # 4. 数据验证
            processed_data = self._validate_data(processed_data)
            
            # 5. 数据清理
            processed_data = self._clean_data(processed_data)
            
            logger.info(f"数据预处理完成，处理后数据形状: {processed_data.shape}")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            return data  # 返回原始数据
    
    def _convert_data_types(self, data: pd.DataFrame) -> pd.DataFrame:
        """转换数据类型"""
        try:
            # 转换时间列
            time_columns = ['dt', 'date', 'time', 'datetime']
            for col in time_columns:
                if col in data.columns:
                    if not pd.api.types.is_datetime64_any_dtype(data[col]):
                        data[col] = pd.to_datetime(data[col], errors='coerce')
            
            # 转换数值列
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'price']
            for col in data.columns:
                if any(keyword in col.lower() for keyword in numeric_columns):
                    if not pd.api.types.is_numeric_dtype(data[col]):
                        data[col] = pd.to_numeric(data[col], errors='coerce')
            
            logger.debug("数据类型转换完成")
            return data
            
        except Exception as e:
            logger.error(f"数据类型转换失败: {e}")
            return data
    
    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        try:
            missing_count = data.isnull().sum().sum()
            if missing_count == 0:
                return data
            
            logger.info(f"发现 {missing_count} 个缺失值，使用策略: {self.missing_strategy}")
            
            if self.missing_strategy == 'drop':
                # 删除包含缺失值的行
                data = data.dropna()
            
            elif self.missing_strategy == 'forward_fill':
                # 前向填充
                data = data.fillna(method='ffill')
                # 处理开头的缺失值
                data = data.fillna(method='bfill')
            
            elif self.missing_strategy == 'interpolate':
                # 插值填充
                numeric_columns = data.select_dtypes(include=[np.number]).columns
                for col in numeric_columns:
                    data[col] = data[col].interpolate(method='linear')
                
                # 非数值列使用前向填充
                non_numeric_columns = data.select_dtypes(exclude=[np.number]).columns
                for col in non_numeric_columns:
                    data[col] = data[col].fillna(method='ffill').fillna(method='bfill')
            
            elif self.missing_strategy == 'mean':
                # 均值填充
                numeric_columns = data.select_dtypes(include=[np.number]).columns
                for col in numeric_columns:
                    data[col] = data[col].fillna(data[col].mean())
                
                # 非数值列使用众数填充
                non_numeric_columns = data.select_dtypes(exclude=[np.number]).columns
                for col in non_numeric_columns:
                    mode_value = data[col].mode()
                    if len(mode_value) > 0:
                        data[col] = data[col].fillna(mode_value[0])
            
            # 最后检查是否还有缺失值
            remaining_missing = data.isnull().sum().sum()
            if remaining_missing > 0:
                logger.warning(f"仍有 {remaining_missing} 个缺失值未处理")
                data = data.dropna()  # 删除剩余的缺失值
            
            return data
            
        except Exception as e:
            logger.error(f"缺失值处理失败: {e}")
            return data
    
    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理异常值"""
        try:
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            outlier_count = 0
            
            for col in numeric_columns:
                if col in data.columns:
                    # 使用IQR方法检测异常值
                    Q1 = data[col].quantile(0.25)
                    Q3 = data[col].quantile(0.75)
                    IQR = Q3 - Q1
                    
                    # 定义异常值边界
                    lower_bound = Q1 - self.outlier_threshold * IQR
                    upper_bound = Q3 + self.outlier_threshold * IQR
                    
                    # 统计异常值
                    outliers = (data[col] < lower_bound) | (data[col] > upper_bound)
                    outlier_count += outliers.sum()
                    
                    # 处理异常值（使用边界值替换）
                    data.loc[data[col] < lower_bound, col] = lower_bound
                    data.loc[data[col] > upper_bound, col] = upper_bound
            
            if outlier_count > 0:
                logger.info(f"处理了 {outlier_count} 个异常值")
            
            return data
            
        except Exception as e:
            logger.error(f"异常值处理失败: {e}")
            return data
    
    def _validate_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """验证数据质量"""
        try:
            # 检查数据量是否足够
            if len(data) < self.min_data_points:
                logger.warning(f"数据量不足: {len(data)} < {self.min_data_points}")
            
            # 检查价格数据的合理性
            price_columns = ['open', 'high', 'low', 'close']
            available_price_cols = [col for col in price_columns if col in data.columns]
            
            if len(available_price_cols) >= 4:
                # 检查OHLC关系
                invalid_ohlc = (
                    (data['high'] < data['low']) |
                    (data['high'] < data['open']) |
                    (data['high'] < data['close']) |
                    (data['low'] > data['open']) |
                    (data['low'] > data['close'])
                )
                
                if invalid_ohlc.any():
                    invalid_count = invalid_ohlc.sum()
                    logger.warning(f"发现 {invalid_count} 行OHLC数据不合理")
                    
                    # 修正不合理的数据
                    for idx in data[invalid_ohlc].index:
                        prices = [data.loc[idx, 'open'], data.loc[idx, 'close']]
                        data.loc[idx, 'high'] = max(data.loc[idx, 'high'], max(prices))
                        data.loc[idx, 'low'] = min(data.loc[idx, 'low'], min(prices))
            
            # 检查成交量
            if 'volume' in data.columns:
                negative_volume = data['volume'] < 0
                if negative_volume.any():
                    logger.warning(f"发现 {negative_volume.sum()} 个负成交量")
                    data.loc[negative_volume, 'volume'] = 0
            
            return data
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return data
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清理数据"""
        try:
            original_length = len(data)
            
            # 删除重复行
            data = data.drop_duplicates()
            
            # 如果有时间列，按时间排序
            time_columns = ['dt', 'date', 'time', 'datetime']
            time_col = None
            for col in time_columns:
                if col in data.columns:
                    time_col = col
                    break
            
            if time_col:
                data = data.sort_values(time_col).reset_index(drop=True)
            
            # 删除全为NaN的行
            data = data.dropna(how='all')
            
            # 删除全为0的数值行（可能是无效数据）
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            if len(numeric_columns) > 0:
                all_zero_mask = (data[numeric_columns] == 0).all(axis=1)
                if all_zero_mask.any():
                    logger.info(f"删除 {all_zero_mask.sum()} 行全零数据")
                    data = data[~all_zero_mask]
            
            cleaned_length = len(data)
            if cleaned_length != original_length:
                logger.info(f"数据清理完成: {original_length} -> {cleaned_length}")
            
            return data
            
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
            return data
    
    def get_data_quality_report(self, data: pd.DataFrame) -> Dict[str, Any]:
        """生成数据质量报告"""
        try:
            report = {
                'total_rows': len(data),
                'total_columns': len(data.columns),
                'missing_values': {},
                'data_types': {},
                'numeric_summary': {},
                'date_range': {},
                'quality_score': 0.0
            }
            
            # 缺失值统计
            missing_counts = data.isnull().sum()
            report['missing_values'] = {
                col: int(count) for col, count in missing_counts.items() if count > 0
            }
            
            # 数据类型统计
            report['data_types'] = {
                col: str(dtype) for col, dtype in data.dtypes.items()
            }
            
            # 数值列统计
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                report['numeric_summary'][col] = {
                    'mean': float(data[col].mean()),
                    'std': float(data[col].std()),
                    'min': float(data[col].min()),
                    'max': float(data[col].max()),
                    'zeros': int((data[col] == 0).sum())
                }
            
            # 时间范围
            time_columns = ['dt', 'date', 'time', 'datetime']
            for col in time_columns:
                if col in data.columns and pd.api.types.is_datetime64_any_dtype(data[col]):
                    report['date_range'] = {
                        'start': str(data[col].min()),
                        'end': str(data[col].max()),
                        'days': (data[col].max() - data[col].min()).days
                    }
                    break
            
            # 质量评分
            quality_score = 1.0
            
            # 缺失值扣分
            missing_ratio = sum(missing_counts) / (len(data) * len(data.columns))
            quality_score -= missing_ratio * 0.5
            
            # 数据量扣分
            if len(data) < self.min_data_points:
                quality_score -= 0.3
            
            # 异常值扣分（简单检查）
            for col in numeric_columns:
                if col in data.columns:
                    Q1 = data[col].quantile(0.25)
                    Q3 = data[col].quantile(0.75)
                    IQR = Q3 - Q1
                    outliers = ((data[col] < Q1 - 3*IQR) | (data[col] > Q3 + 3*IQR)).sum()
                    outlier_ratio = outliers / len(data)
                    quality_score -= outlier_ratio * 0.2
            
            report['quality_score'] = max(0.0, min(1.0, quality_score))
            
            return report
            
        except Exception as e:
            logger.error(f"数据质量报告生成失败: {e}")
            return {
                'error': str(e),
                'total_rows': len(data) if data is not None else 0,
                'total_columns': len(data.columns) if data is not None else 0,
                'quality_score': 0.0
            }