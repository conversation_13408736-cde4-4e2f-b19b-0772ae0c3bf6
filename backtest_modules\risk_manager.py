import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class RiskManager:
    """
    风险管理模块
    负责止损、仓位控制、风险指标监控
    """
    
    def __init__(self, max_position_size: float = 0.1, stop_loss_pct: float = 0.05,
                 take_profit_pct: float = 0.15, max_drawdown_limit: float = 0.2,
                 max_daily_loss: float = 0.02, position_concentration_limit: float = 0.3):
        """
        初始化风险管理器
        
        Args:
            max_position_size: 单个仓位最大比例
            stop_loss_pct: 止损百分比
            take_profit_pct: 止盈百分比  
            max_drawdown_limit: 最大回撤限制
            max_daily_loss: 最大日损失限制
            position_concentration_limit: 仓位集中度限制
        """
        self.max_position_size = max_position_size
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.max_drawdown_limit = max_drawdown_limit
        self.max_daily_loss = max_daily_loss
        self.position_concentration_limit = position_concentration_limit
        
        # 风险监控状态
        self.current_drawdown = 0.0
        self.daily_pnl = 0.0
        self.risk_alerts = []
        self.position_limits_breached = 0
        self.stop_loss_triggered = 0
        self.take_profit_triggered = 0
        self.risk_log = []
        
        # 持仓跟踪
        self.positions = {}  # {symbol: position_info}
        self.entry_prices = {}
        self.position_start_times = {}
        
    def validate_position_size(self, symbol: str, position_value: float, 
                             total_portfolio_value: float) -> Tuple[bool, float]:
        """
        验证仓位大小是否符合风险限制
        
        Args:
            symbol: 交易标的
            position_value: 仓位价值
            total_portfolio_value: 总组合价值
            
        Returns:
            (是否通过验证, 调整后的仓位价值)
        """
        # 计算仓位比例
        position_ratio = abs(position_value) / total_portfolio_value
        
        # 检查单个仓位限制
        if position_ratio > self.max_position_size:
            adjusted_value = total_portfolio_value * self.max_position_size
            if position_value < 0:
                adjusted_value = -adjusted_value
                
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'POSITION_SIZE_LIMIT',
                'symbol': symbol,
                'original_ratio': position_ratio,
                'max_allowed': self.max_position_size,
                'adjusted_value': adjusted_value
            })
            
            self.position_limits_breached += 1
            self.risk_log.append(f"Position size limit breached for {symbol}: {position_ratio:.3f} > {self.max_position_size:.3f}")
            
            return False, adjusted_value
        
        return True, position_value
    
    def check_concentration_risk(self, all_positions: Dict[str, float], 
                               total_portfolio_value: float) -> Dict:
        """
        检查仓位集中度风险
        
        Args:
            all_positions: 所有仓位 {symbol: position_value}
            total_portfolio_value: 总组合价值
            
        Returns:
            集中度风险报告
        """
        position_ratios = {}
        total_position_value = 0
        
        for symbol, position_value in all_positions.items():
            ratio = abs(position_value) / total_portfolio_value
            position_ratios[symbol] = ratio
            total_position_value += abs(position_value)
        
        # 计算总仓位比例
        total_position_ratio = total_position_value / total_portfolio_value
        
        # 检查是否过度集中
        concentration_issues = []
        for symbol, ratio in position_ratios.items():
            if ratio > self.position_concentration_limit:
                concentration_issues.append({
                    'symbol': symbol,
                    'ratio': ratio,
                    'excess': ratio - self.position_concentration_limit
                })
        
        concentration_report = {
            'total_position_ratio': total_position_ratio,
            'position_ratios': position_ratios,
            'concentration_issues': concentration_issues,
            'max_concentration': max(position_ratios.values()) if position_ratios else 0,
            'is_over_concentrated': len(concentration_issues) > 0
        }
        
        if concentration_issues:
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'CONCENTRATION_RISK',
                'issues': concentration_issues
            })
            self.risk_log.append(f"Concentration risk detected: {len(concentration_issues)} positions over limit")
        
        return concentration_report
    
    def apply_stop_loss(self, symbol: str, current_price: float, 
                       entry_price: float, position_type: str = 'long') -> bool:
        """
        应用止损逻辑
        
        Args:
            symbol: 交易标的
            current_price: 当前价格
            entry_price: 入场价格
            position_type: 仓位类型 ('long', 'short')
            
        Returns:
            是否触发止损
        """
        if position_type == 'long':
            # 多头止损：当前价格低于入场价格的(1-止损百分比)
            stop_loss_price = entry_price * (1 - self.stop_loss_pct)
            stop_loss_triggered = current_price <= stop_loss_price
            
        else:  # short
            # 空头止损：当前价格高于入场价格的(1+止损百分比)
            stop_loss_price = entry_price * (1 + self.stop_loss_pct)
            stop_loss_triggered = current_price >= stop_loss_price
        
        if stop_loss_triggered:
            loss_pct = abs(current_price - entry_price) / entry_price
            
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'STOP_LOSS_TRIGGERED',
                'symbol': symbol,
                'entry_price': entry_price,
                'current_price': current_price,
                'loss_pct': loss_pct,
                'position_type': position_type
            })
            
            self.stop_loss_triggered += 1
            self.risk_log.append(f"Stop loss triggered for {symbol}: {loss_pct:.3f} loss")
        
        return stop_loss_triggered
    
    def apply_take_profit(self, symbol: str, current_price: float,
                         entry_price: float, position_type: str = 'long') -> bool:
        """
        应用止盈逻辑
        
        Args:
            symbol: 交易标的
            current_price: 当前价格
            entry_price: 入场价格
            position_type: 仓位类型 ('long', 'short')
            
        Returns:
            是否触发止盈
        """
        if position_type == 'long':
            # 多头止盈：当前价格高于入场价格的(1+止盈百分比)
            take_profit_price = entry_price * (1 + self.take_profit_pct)
            take_profit_triggered = current_price >= take_profit_price
            
        else:  # short
            # 空头止盈：当前价格低于入场价格的(1-止盈百分比)
            take_profit_price = entry_price * (1 - self.take_profit_pct)
            take_profit_triggered = current_price <= take_profit_price
        
        if take_profit_triggered:
            profit_pct = abs(current_price - entry_price) / entry_price
            
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'TAKE_PROFIT_TRIGGERED',
                'symbol': symbol,
                'entry_price': entry_price,
                'current_price': current_price,
                'profit_pct': profit_pct,
                'position_type': position_type
            })
            
            self.take_profit_triggered += 1
            self.risk_log.append(f"Take profit triggered for {symbol}: {profit_pct:.3f} profit")
        
        return take_profit_triggered
    
    def monitor_drawdown(self, portfolio_values: pd.Series) -> Dict:
        """
        监控和报告回撤情况
        
        Args:
            portfolio_values: 组合价值序列
            
        Returns:
            回撤监控报告
        """
        # 计算累计最高点
        cumulative_max = portfolio_values.expanding().max()
        
        # 计算回撤
        drawdowns = (portfolio_values - cumulative_max) / cumulative_max
        
        # 当前回撤
        self.current_drawdown = drawdowns.iloc[-1]
        
        # 最大回撤
        max_drawdown = drawdowns.min()
        max_drawdown_date = drawdowns.idxmin()
        
        # 回撤持续时间
        current_drawdown_start = None
        drawdown_duration = 0
        
        if self.current_drawdown < -0.001:  # 当前处于回撤中
            # 找到当前回撤开始时间
            for i in range(len(drawdowns)-1, -1, -1):
                if drawdowns.iloc[i] >= -0.001:  # 找到非回撤点
                    current_drawdown_start = drawdowns.index[i+1] if i+1 < len(drawdowns) else drawdowns.index[0]
                    break
            
            if current_drawdown_start:
                drawdown_duration = (drawdowns.index[-1] - current_drawdown_start).days
        
        # 检查是否超过回撤限制
        drawdown_limit_breached = abs(self.current_drawdown) > self.max_drawdown_limit
        
        if drawdown_limit_breached:
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'DRAWDOWN_LIMIT_BREACHED',
                'current_drawdown': self.current_drawdown,
                'limit': self.max_drawdown_limit,
                'duration_days': drawdown_duration
            })
            self.risk_log.append(f"Drawdown limit breached: {self.current_drawdown:.3f} > {self.max_drawdown_limit:.3f}")
        
        drawdown_report = {
            'current_drawdown': self.current_drawdown,
            'max_drawdown': max_drawdown,
            'max_drawdown_date': max_drawdown_date,
            'drawdown_duration_days': drawdown_duration,
            'limit_breached': drawdown_limit_breached,
            'drawdown_series': drawdowns
        }
        
        return drawdown_report
    
    def monitor_daily_pnl(self, daily_returns: pd.Series) -> Dict:
        """
        监控日度盈亏
        
        Args:
            daily_returns: 日度收益率序列
            
        Returns:
            日度盈亏监控报告
        """
        # 最近一日的盈亏
        if len(daily_returns) > 0:
            self.daily_pnl = daily_returns.iloc[-1]
        
        # 检查是否超过日损失限制
        daily_loss_limit_breached = self.daily_pnl < -self.max_daily_loss
        
        if daily_loss_limit_breached:
            self.risk_alerts.append({
                'timestamp': datetime.now(),
                'type': 'DAILY_LOSS_LIMIT_BREACHED',
                'daily_pnl': self.daily_pnl,
                'limit': self.max_daily_loss
            })
            self.risk_log.append(f"Daily loss limit breached: {self.daily_pnl:.3f} < -{self.max_daily_loss:.3f}")
        
        # 统计最近一周的表现
        recent_week = daily_returns.tail(7) if len(daily_returns) >= 7 else daily_returns
        weekly_return = recent_week.sum()
        weekly_volatility = recent_week.std()
        positive_days = (recent_week > 0).sum()
        negative_days = (recent_week < 0).sum()
        
        pnl_report = {
            'current_daily_pnl': self.daily_pnl,
            'daily_loss_limit_breached': daily_loss_limit_breached,
            'weekly_return': weekly_return,
            'weekly_volatility': weekly_volatility,
            'positive_days_last_week': positive_days,
            'negative_days_last_week': negative_days,
            'win_rate_last_week': positive_days / len(recent_week) if len(recent_week) > 0 else 0
        }
        
        return pnl_report
    
    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.05) -> float:
        """
        计算风险价值(VaR)
        
        Args:
            returns: 收益率序列
            confidence_level: 置信水平 (0.05 = 95%置信度)
            
        Returns:
            VaR值
        """
        if len(returns) == 0:
            return 0.0
        
        var = np.percentile(returns, confidence_level * 100)
        return var
    
    def calculate_expected_shortfall(self, returns: pd.Series, confidence_level: float = 0.05) -> float:
        """
        计算期望损失(Expected Shortfall/CVaR)
        
        Args:
            returns: 收益率序列
            confidence_level: 置信水平
            
        Returns:
            期望损失值
        """
        if len(returns) == 0:
            return 0.0
        
        var = self.calculate_var(returns, confidence_level)
        expected_shortfall = returns[returns <= var].mean()
        
        return expected_shortfall if not pd.isna(expected_shortfall) else 0.0
    
    def assess_portfolio_risk(self, portfolio_values: pd.Series, 
                            positions: Dict[str, float]) -> Dict:
        """
        综合评估组合风险
        
        Args:
            portfolio_values: 组合价值序列
            positions: 当前持仓情况
            
        Returns:
            风险评估报告
        """
        # 计算收益率
        returns = portfolio_values.pct_change().dropna()
        
        # 基本风险指标
        portfolio_volatility = returns.std() * np.sqrt(252)  # 年化波动率
        var_95 = self.calculate_var(returns, 0.05)
        var_99 = self.calculate_var(returns, 0.01)
        expected_shortfall = self.calculate_expected_shortfall(returns, 0.05)
        
        # 回撤分析
        drawdown_report = self.monitor_drawdown(portfolio_values)
        
        # 日度盈亏分析
        pnl_report = self.monitor_daily_pnl(returns)
        
        # 仓位集中度分析
        concentration_report = self.check_concentration_risk(positions, portfolio_values.iloc[-1])
        
        # 风险评分 (0-100, 100为最高风险)
        risk_score = 0
        
        # 波动率风险 (0-30分)
        if portfolio_volatility > 0.3:
            risk_score += 30
        elif portfolio_volatility > 0.2:
            risk_score += 20
        elif portfolio_volatility > 0.1:
            risk_score += 10
        
        # 回撤风险 (0-30分)
        if abs(self.current_drawdown) > 0.15:
            risk_score += 30
        elif abs(self.current_drawdown) > 0.1:
            risk_score += 20
        elif abs(self.current_drawdown) > 0.05:
            risk_score += 10
        
        # 集中度风险 (0-25分)
        if concentration_report['is_over_concentrated']:
            risk_score += 25
        elif concentration_report['max_concentration'] > 0.2:
            risk_score += 15
        elif concentration_report['max_concentration'] > 0.15:
            risk_score += 10
        
        # VaR风险 (0-15分)
        if var_95 < -0.05:
            risk_score += 15
        elif var_95 < -0.03:
            risk_score += 10
        elif var_95 < -0.02:
            risk_score += 5
        
        # 风险等级
        if risk_score >= 80:
            risk_level = 'CRITICAL'
        elif risk_score >= 60:
            risk_level = 'HIGH'
        elif risk_score >= 40:
            risk_level = 'MEDIUM'
        elif risk_score >= 20:
            risk_level = 'LOW'
        else:
            risk_level = 'MINIMAL'
        
        risk_assessment = {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'portfolio_volatility': portfolio_volatility,
            'var_95': var_95,
            'var_99': var_99,
            'expected_shortfall': expected_shortfall,
            'drawdown_report': drawdown_report,
            'pnl_report': pnl_report,
            'concentration_report': concentration_report,
            'total_alerts': len(self.risk_alerts),
            'recent_alerts': [alert for alert in self.risk_alerts 
                            if (datetime.now() - alert['timestamp']).days <= 7]
        }
        
        return risk_assessment
    
    def get_risk_recommendations(self, risk_assessment: Dict) -> List[str]:
        """
        基于风险评估提供建议
        
        Args:
            risk_assessment: 风险评估报告
            
        Returns:
            风险管理建议列表
        """
        recommendations = []
        
        risk_level = risk_assessment['risk_level']
        risk_score = risk_assessment['risk_score']
        
        if risk_level in ['CRITICAL', 'HIGH']:
            recommendations.append("立即减少仓位规模，将总仓位降至50%以下")
            recommendations.append("暂停新的交易信号，专注于风险控制")
            
        if risk_assessment['portfolio_volatility'] > 0.25:
            recommendations.append("组合波动率过高，考虑分散投资或降低杠杆")
            
        if abs(risk_assessment['drawdown_report']['current_drawdown']) > 0.1:
            recommendations.append("当前回撤较大，建议暂停交易并重新评估策略")
            
        if risk_assessment['concentration_report']['is_over_concentrated']:
            recommendations.append("存在仓位集中度风险，建议分散持仓")
            
        if risk_assessment['var_95'] < -0.04:
            recommendations.append("VaR值过高，建议降低风险敞口")
            
        if len(risk_assessment['recent_alerts']) > 5:
            recommendations.append("近期风险警报频繁，建议全面检查交易策略")
            
        if not recommendations:
            recommendations.append("当前风险水平可控，继续执行策略并保持监控")
            
        return recommendations
    
    def get_risk_summary(self) -> Dict:
        """
        获取风险管理总结
        
        Returns:
            风险管理总结
        """
        summary = {
            'risk_parameters': {
                'max_position_size': self.max_position_size,
                'stop_loss_pct': self.stop_loss_pct,
                'take_profit_pct': self.take_profit_pct,
                'max_drawdown_limit': self.max_drawdown_limit,
                'max_daily_loss': self.max_daily_loss,
                'position_concentration_limit': self.position_concentration_limit
            },
            'risk_events': {
                'position_limits_breached': self.position_limits_breached,
                'stop_loss_triggered': self.stop_loss_triggered,
                'take_profit_triggered': self.take_profit_triggered,
                'total_alerts': len(self.risk_alerts)
            },
            'current_status': {
                'current_drawdown': self.current_drawdown,
                'daily_pnl': self.daily_pnl,
                'active_positions': len(self.positions)
            },
            'risk_alerts': self.risk_alerts,
            'risk_log': self.risk_log.copy()
        }
        
        return summary 