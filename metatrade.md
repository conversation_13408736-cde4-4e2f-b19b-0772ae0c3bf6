# Your Offline Policy is Not Trustworthy: Bilevel Reinforcement Learning for Sequential Portfolio Optimization

Haochen Yuan $^{1\dagger}$ , Minting Pan $^{1\dagger}$ , <PERSON><PERSON> Wang $^{1*}$ , Siyu Gao $^{1}$ , <PERSON> $^{2}$ , <PERSON><PERSON><PERSON> $^{1}$

$^{1}$  MoE Key Lab of Artificial Intelligence, AI Institute, Shanghai Jiao Tong University, China   $^{2}$  Department of Computer Science, University of Illinois Chicago, USA   $^{\dagger}$ These authors contributed equally to this work.  *Corresponding author. Email: <EMAIL>.

# Abstract

Reinforcement learning (RL) has shown significant promise for sequential portfolio optimization tasks, such as stock trading, where the objective is to maximize cumulative returns while minimizing risks using historical data. However, traditional RL approaches often produce policies that merely "memorize" the optimal yet impractical buying and selling behaviors within the fixed dataset. These offline policies are less generalizable as they fail to account for the non- stationary nature of the market. Our approach, MetaTrader, frames portfolio optimization as a new type of partial- offline RL problem and makes two technical contributions. First, MetaTrader employs a bilevel learning framework that explicitly trains the RL agent to improve both in- domain profits on the original dataset and out- of- domain performance across diverse transformations of the raw financial data. Second, our approach incorporates a new temporal difference (TD) method that approximates worst- case TD estimates from a batch of transformed TD targets, addressing the value overestimation issue that is particularly challenging in scenarios with limited offline data. Our empirical results on two public stock datasets show that MetaTrader outperforms existing methods, including both RL- based approaches and traditional stock prediction models.

# 1 Introduction

Portfolio optimization refers to the process of selecting the best mix of assets (such as stocks, bonds, or other investment vehicles) to achieve a specific financial goal, often maximizing return while minimizing risk. The objective is to create a portfolio that offers the most efficient balance between risk and return, based on the investor's preferences, constraints, and market conditions. In traditional methods, portfolio optimization often uses historical data (such as past returns and covariances between assets) to determine the optimal asset allocation. However, modern reinforcement learning (RL) approaches aim to optimize portfolios dynamically, accounting for changing market conditions over time [Deng et al., 2016, Ye et al., 2020, Briola et al., 2021, Liu et al., 2021, Kumar, 2023, Gao et al., 2023a].

Recent advances in RL- based trading, such as StockFormer [Gao et al., 2023a], have demonstrated superior performance compared to simpler strategies that combine stock prediction models [Li et al., 2018, Xu and Cohen, 2018, Wang et al., 2021, Zheng et al., 2023] with fixed trading policies - such as buying stocks with the highest predicted future gains and holding them for a predefined period. These RL approaches commonly employ advanced deep learning models to extract meaningful features from the noisy market data, e.g., stock prices, trading volumes, and financial news. These extracted features are then used as inputs for RL algorithms, which are typically designed to maximize the expected total payoff within the constraints of the offline training data.

Generalization- optimality dilemma. However, most existing RL- for- finance methods apply standard RL algorithms to find an optimal policy from a previously collected static dataset, which bears algorithmic difficulties due to function approximation errors from out- of- distribution (OOD) data points. Since the RL agent cannot actively explore the rapidly evolving financial market, it tends to overfit the historical data and simply memorize the "optimal" offline policy—transactions that yield the highest profits within the dataset—even though such a policy may not be generalizable outside the dataset's

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/9afe08b39b7f88f4d6ecca69997fe6e9bc1a032c90341897d9e14ffc8fb2a531.jpg)  
Figure 1: A comparison of MetaTrader and existing RL-based trading methods. a, Existing RL-for-finance methods typically adopt an offline training setup rather than online RL, causing them to struggle with the generalization-optimality dilemma, a common challenge in the inherently non-stationary financial market. b, MetaTrader tackles this paradox through: (1) specialized data transformations to simulate OOD financial data, (2) a bilevel RL framework that explicitly optimizes both in- and out-of-domain performance across diverse transformations, and (3) a novel TD learning method that conservatively estimates state-action values by approximating the minimum TD targets generated from a batch of data transformations.

scope. This raises a crucial yet under- explored question: How can we learn more robust trading policies that can jointly handle the in- domain optimality<sup>1</sup> and out- of- domain generalizability?<sup>2</sup>

The generalization- optimality dilemma can also be a fitting description for the exploration- exploitation dilemma in our offline RL formulation of the portfolio optimization problem. Generalization refers to an agent's ability to apply knowledge gained from a fixed dataset to unseen market conditions. This capability is crucial for tasks where the training dataset is incomplete or significantly diverges from the test data distribution. However, excessive generalization risks the agent straying into OOD regions where the model's estimations of state- action values become unreliable, potentially resulting in poor performance or unsafe behaviors. Optimality, on the other hand, focuses on maximizing rewards by closely following the optimal actions within the training dataset, ensuring reliable performance in familiar scenarios. Yet, an overemphasis on in- domain optimality can hinder the agent's ability to explore potentially superior policies beyond the dataset's boundaries. This limitation may result in suboptimal behavior in highly non- stationary, evolving environments where broader exploration could reveal more effective solutions.

Overview of MetaTrader. In this paper, we introduce MetaTrader, an early study on bilevel optimization of actor- critic methods in stock trading, formulated as a partial- offline RL problem with decoupled state branches. The core idea of MetaTrader goes beyond maximizing expected total rewards on current trajectories, learning policies that also perform effectively on OOD financial data. As shown in Figure 1b, we enhance existing RL- based trading methods in two key areas: a bilevel RL framework and a novel temporal difference (TD) learning approach.

A primary contribution of our work is to enhance the generalization capability of the policy from both the data transformation and algorithmic perspectives, which are closely interconnected. From a data perspective, we introduce specific data transformation methods designed to simulate OOD samples. These transformations focus on different factorized components of the time series data, including short- term randomness, long- term trends, and multi- scale correlations.

From an algorithmic perspective, we propose a novel actor- critic method based on bilevel optimization. Incorporating bilevel gradient updates helps prevent the agent from overfitting to the historical distribution by explicitly evaluating the hypothetical model parameters on the transformed OOD market data. This training strategy ensures the model does not simply memorize the optimal policy based on specific patterns in the training data.

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/3b5cf18110b15d104162e08fad1c29659de7bff32acc1700f980e1ea1d61cf09.jpg)  
Figure 2: The MDP in the partial-offline RL setup for sequential portfolio optimization. The MDP consists of decoupled pairs of action-free market states and action-dependent balance states, with market states restricted to the offline training set. Unlike standard offline RL, where no new rewards are accessible during policy optimization, the partial-offline setup allows the agent to interact with the fixed training set, exploring different policies and collecting new reward feedback.

Another contribution of our work is a novel TD learning method that conservatively estimates state- action values by approximating the minimum TD targets generated from a batch of data transformations. This approach seeks to enhance the generalizability of policies learned from offline data to mitigate the value overestimation issue, which is particularly severe when there are significant discrepancies between the training and test distributions of non- stationary market data. We empirically demonstrate that the pronounced distributional shift makes the existing conservative offline RL methods, such as CQL [Kumar et al., 2020] and IQL [Kostrikov et al., 2021], inadequate for tackling RL- for- finance tasks effectively. Specifically, we modify TD learning by constructing an ensemble of TD targets, separately computing the next- step Q- values for both the original data and its transformations. We then use the minimum Q value among them as the TD target to train the current- step value estimate. Unlike previous ensemble- based Q- learning methods, which use the multiple target Q- networks to compute ensemble value regularization, our method relies on a single target Q- network and derives the worst- case Q- value through a diverse set of transformed data.

Our approach significantly outperforms existing RL- for- finance methods on the CSI- 300 and the NASDAQ- 100 stock datasets, achieving superior cumulative returns and Sharpe ratios. This highlights its ability to effectively balance trading profits and risks. By addressing critical challenges such as policy overfitting and value overestimation, our method offers a more reliable and adaptive solution for financial trading in real- world, non- stationary environments. Additionally, the techniques we propose provide a generalizable framework that can be extended to a wide range of non- stationary decision- making scenarios, such as autonomous driving and power systems management, where adaptability to dynamic environments is crucial for success.

# 2 Problem Formulation: Partial-Offline Reinforcement Learning

We introduce a novel formulation of sequential portfolio optimization as a "partial- offline" RL problem, with the key distinction from the standard offline RL setup explained later.

As shown in Figure 2, the Markov decision process (MDP) in the context of stock trading can be described as an 8- tuple  $(\mathcal{O}, \mathcal{A}, \mathcal{H}, \mathcal{Z}, P_h, P_z, R, \gamma)$ :

Observation space  $(O)$  .The raw data includes: (1)  $o_t^{\mathrm{price}}\in \mathbb{R}^{T\times |S|\times 5}$  : Daily open, close, high, low stock prices, and trading volumes for the previous  $T$  days.  $|S|$  is the total number of stocks. (2)  $o_t^{\mathrm{stat}}\in \mathbb{R}^{|S|\times I}$  : I technical indicators that reflect the temporal trends of stock prices. (3) A covariance matrix  $o_t^{\mathrm{cov}}$  that measures the correlations between historical daily closing prices of all stocks. In our partial- offline RL setup, only a finite set of observation data is accessible.

Action space  $(\mathcal{A})$ . We use a continuous action space  $a_t \in \mathbb{R}^{|S|}$ , where each component represents the number of shares to buy, hold, or sell for each asset. To simulate real- world trading, we discretize  $a_t$  into several intervals, such as 100, 200, ... shares when deploying the agent for testing.

Decoupled state space  $(\mathcal{H},\mathcal{Z})$  and state transitions  $(P_h,P_z)$  .We decouple the state space into two components:  $S = (\mathcal{H},\mathcal{Z})$  . Here,  $\mathcal{H}$  is the market state space represented by the embeddings from the observed financial data, while  $\mathcal{Z}$  is the balance state space that models the balance sheet. The market state  $h_t$  is composed of three types of latent states  $(h_t^{\mathrm{relat}},h_t^{\mathrm{long}},h_t^{\mathrm{short}})$  generated from  $o_t^{\mathrm{price}}$ $o_t^{\mathrm{stat}}$  and  $o_t^{\mathrm{cov}}$  using encoding networks. Please refer to Eq. (1) for details. The balance state  $z_{t}\in \mathbb{R}^{|S| + 1}$  represents the total account balance and holding amount of each trading asset.

Since individual buying and selling actions typically have minimal impact on market dynamics, market state transitions are largely action- free whereas balance state transitions are action- dependent. Therefore, we define the state transition probabilities as  $P_{h}(h_{t + 1}|h_{t})$  for market states and  $P_z(z_{t + 1}|z_t,a_t)$  for balance states. In partial- offline RL, market states are limited to the offline training set, while the agent can explore various actions that lead to new balance states.

Reward function  $(R)$  and discount factor  $(\gamma)$  .The immediate reward is defined as the daily portfolio return ratios:  $r_t = R(h_{t:t + 1},z_{t:t + 1})$  , where  $z_{t + 1}$  is dependent on  $a_{t}$  .  $\gamma$  is the reward discount factor that determines how much the RL agents care about rewards in the distant future.

Distinctions from standard offline RL setups. In standard offline learning setups, we commonly face value overestimation issues because the agents can only be trained on a limited set of observable states, historical actions, and historical rewards, without the ability to effectively assess the rewards of OOD actions and corresponding future states. As a result, inaccurate value estimates for future states can lead to ineffective TD learning, typically causing overly optimistic value estimates due to bootstrapping. In our formulation of partial- offline RL, the agent can explore different actions within the fixed training set, evaluating new policies with online updated reward feedback.

More specifically, in conventional offline RL formulation, we cannot directly obtain  $r_t = R(s_t,s_{t + 1})$  from the environment, where  $s_{t + 1}\sim P(s_{t + 1}\mid s_t,a_t)$  and  $a_{t}$  is an out- of- domain action generated by the agent. This is because, despite knowing the exact definition of the reward function  $R(\cdot)$  accurately estimating  $s_{t + 1}$  is highly challenging due to the paradox of limited training data and the non- stationary nature of market dynamics. In partial- offline RL with decoupled state spaces, we instead have  $r_t = R(h_t,h_{t + 1},z_t,z_{t + 1})$  , where  $h_{t + 1},P_t(h_{t + 1}\mid h_t)$  and  $z_{t + 1}\sim P_{\cdot}(z_{t + 1}\mid z_{t},a_{t})$  . Here, we still use a pre- defined reward function  $R(\cdot)$  and can directly compute the next- step balance state  $z_{t + 1}$  given an out- of- domain  $a_{t}$  . Although accurately estimating the distribution of the next- step market state  $h_{t + 1}$  remains intractable, it is independent of  $a_{t}$  MetaTrader leverages this property to approximate the worst- case reward  $r_t$  in TD learning, using Monte Carlo sampling over carefully designed transformations applied to  $h_{t + 1}$  . For further details, refer to the proposed transformation- based TD learning method, which offers a novel solution to mitigating the value overestimation issue. We will demonstrate in later sections that, due to a better use of the partial- offline properties, the proposed TD method outperforms existing conservative offline RL methods, such as CQL [Kumar et al., 2020] and IQL [Kostrikov et al., 2021], remarkably in tackling portfolio optimization tasks.

# 3 MetaTrader

In this section, we first review state- of- the- art RL- based methods for portfolio optimization, followed by a detailed presentation of the three key contributions of MetaTrader: (1) the specialized data transformation methods to simulate OOD financial data, (2) a bilevel RL framework that explicitly optimizes both in- and out- of- domain trading performance, and (3) a novel TD learning method that approximates worst- case TD estimates from a batch of transformed TD targets.

Revisiting RL- based trading methods. We use StockFormer [Gao et al., 2023a] as an example. Despite its state- of- the- art performance, a potential drawback lies in the straightforward use of conventional RL methods for offline data. StockFormer has three network branches  $f_{\psi_{t,2,3}}(\cdot)$  to extract the cross- stock relational features  $h_t^{\mathrm{relat}}\in \mathbb{R}^{|S|\times D}$  , the long- term predictive features  $h_t^{\mathrm{long}}\in \mathbb{R}^{|S|\times D}$  , and the short- term predictive features  $h_t^{\mathrm{short}}\in \mathbb{R}^{|S|\times D}$  from the stock data  $o_t = [o_{t - T + 1:t}^{\mathrm{price}},o_{t - T + 1:t}^{\mathrm{sat}},o_{t - T + 1:t}^{\mathrm{cov}}]$  in the past  $T$  days.  $D$  represents the dimension of the hidden features per stock. The feature extraction module is frozen during policy optimization. These features are used as the input states of the Soft

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/32f7a48294c70cce075b692f94ddeb11bd454fd31e508b3ec7990a1625a6a13c.jpg)  
Figure 3: The bilevel learning scheme of MetaTrader based on transformed market data. In the inner optimization loop (blue arrows), we optimize the model parameters on a batch of data subsets. In the outer optimization loop (red arrows), we perform bilevel gradient updates by explicitly evaluating the inner-loop parameters against another batch of data subsets. This process leads to a more generalizable agent and prevents overfitting to the in-domain optimal policy.

Actor- Critic (SAC) algorithm [Haarnoja et al., 2018]:

$$
\begin{array}{rl} & {\mathrm{Market~state~encoding:}\quad h_t^{\mathrm{relat}} = f_{\psi_1}(o_t),\quad h_t^{\mathrm{long}} = f_{\psi_2}(o_t),\quad h_t^{\mathrm{short}} = f_{\psi_3}(o_t),}\\ & {\mathrm{Actor:}\quad a_t\sim \pi_\theta (h_t^{\mathrm{relat}},h_t^{\mathrm{long}},h_t^{\mathrm{short}},z_t),\quad \mathrm{Critic:}q_t\sim Q_\phi (h_t^{\mathrm{relat}},h_t^{\mathrm{long}},h_t^{\mathrm{short}},z_t,a_t),} \end{array} \tag{1}
$$

where  $z_{t} \in \mathbb{R}^{|S| \times 1}$  represents the holding amount of all trading assets at a certain time step. Our approach follows the basic network architectures of StockFormer, including the feature extraction module  $f_{\psi_{1,2,3}}$ , the actor module  $\pi_{\theta}$ , and the critic module  $Q_{\phi}$ .

Most existing RL- based portfolio optimization methods train the agent in a manner similar to StockFormer, in which the trading policies are exclusively optimized within a specific offline dataset. By maximizing cumulative rewards, this approach carries the risk of overfitting to optimal behaviors in a fixed dataset, potentially leading to ineffective policies when faced with the unobserved dynamics of a non- stationary market in the future.

Training stages and data transformations. As shown in Figure 3, we first partition the entire offline training set into multiple subsets, denoted as  $\{\mathcal{D}_m\}_{m = 1}^M$ . These subsets are then separated into sequences of  $T$  time steps $^2$ . Given the non- stationary nature of market data, we employ a two- stage training process: a policy learning stage on the first  $M - M'$  subsets, followed by a finetuning stage on the more recent  $M'$  subsets. This is grounded in the fundamental assumption in online temporal data applications: training data closer to the test set may better capture trend patterns that align with those in the test set.

To improve the generalizability of the learned policy to market conditions with notable distribution shifts from the training set, we expand the training subsets by generating a diverse range of OOD market data, using them exclusively in the initial policy learning stage. In contrast, we use only the

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/574c3f40dcb92727656db901232b8978a3fc119408ff3e9bd97f22029e9bb7b0.jpg)  
Figure 4: An example of market data transformations.  $F_{1}$  selects the top  $\alpha \%$  of assets with the highest price gains and inverses their original growth rate to declines to simulate unexpected short-term disruptions.  $F_{2}$  reverses the temporal order of a  $T$ -length sequence to simulate the long-term impact of certain events.  $F_{3}$  downsamples the original data by  $\Delta$  time steps to simulate squeezed global dynamics.

original training data during the finetuning stage to guide the policy toward scenarios more aligned with the test data. Accordingly, we name the two training stages in MetaTrader as (1) OOD policy learning and (2) in- domain finetuning.

To design effective data transformation methods, we treat market data as multivariate time series, whose dynamic patterns can typically be viewed as a combination of three components: short- term randomness, long- term trends, and multi- scale dynamics. Accordingly, we introduce three data transformation methods  $F_{1,3}$  to simulate OOD yet plausible market changes that have not been included in the training set, with each method focusing on one of the three dynamic components:

-  $F_{1}$ : At each time step, we select the top  $\alpha \%$  assets with the highest price gains and invert their growth rates to simulate unexpected short-term disruptions.-  $F_{2}$ : We reverse the overall trends in each training subset to simulate the long-term impact of sudden market events, aiming to assess the policy's robustness in such scenarios.-  $F_{3}$ : We downsample the training data by  $\Delta$  time steps, squeezing the original temporal dynamics. This enables the model to capture multi-scale temporal correlations with greater flexibility.

More technical details are provided in Supplementary Section S1. By applying the above transformation methods, we expand the subset collections to  $\{\mathcal{D}_{m,n}\}_{m = 1,n = 0}^{M,N}$  during OOD policy learning, where  $n = 0$  denotes the original data split.  $N$  is scalable in MetaTrader by adjusting hyperparameters (e.g.,  $\alpha$ ,  $T$ , and  $\Delta$ ) in the data transformation functions. As  $N$  increases, we can achieve (1) a broader expansion of the data distribution for policy learning, increasing the likelihood of covering OOD market dynamics, and (2) more accurate Monte Carlo estimations for the expected worst- case future payoffs during TD learning, which we will discuss later. For simplicity and without loss of generality, we use  $N = 3$  with  $\alpha = 10$ ,  $T = 64$ , and  $\Delta = 4$  in our stock trading experiments.

We organize various partitions of the original data along with a diverse range of transformed data into a unified replay buffer as training subsets. We then apply the two- stage bilevel RL scheme across different subsets to enable more generalizable policy optimization.

Bilevel learning across transformed data. As shown in Algorithm 1, we first sample training subsets randomly,  $\{\mathcal{D}^{(i)}\}_{i = 1}^{K} \sim \{\mathcal{D}_{m,n}\}_{m = 1,n = 0}^{M,N}$ . We then perform an inner- loop optimization step to derive  $K$  sets of hypothetical model parameters for the actor and critics, denoted by  $\theta^{(i)}$  and  $\phi_{k}^{(i)}$  for each individual data subset. Here, we employ double Q- networks, parameterized by  $\phi_{1,2}$ , with  $k$

# Algorithm 1: OOD Policy Learning

1 Input: Expanded datasets  $\{\mathcal{D}_{m,n}\}_{m = 1,n = 0}^{M,N}$  2 Parameters:  $\alpha_{1}$ $\alpha_{2}$ $\eta_{1}$ $\eta_{2}$  3 Randomly initialize  $\theta$ $\phi_1$ $\phi_2$  4 for  $T_{1}$  steps do 5 Sample  $\{\mathcal{D}^{(i)}\}_{i = 1}^{K}\sim \{\mathcal{D}_{m,n}\}_{m = 1,n = 0}^{M,N}$  6 for each  $\mathcal{D}^{(i)}\in \{\mathcal{D}^{(i)}\}_{i = 1}^{K}$  do 7 Sample a batch of data  $\mathcal{B}^{(i)}\sim \mathcal{D}^{(i)}$  8  $\phi_1^{(i)}\gets \phi_1 - \eta_1\nabla_{\phi_1}\mathcal{L}_Q\left(\mathcal{B}^{(i)};\phi_1\right)$  9  $\phi_{2}^{(i)}\gets \phi_{2} - \eta_{1}\nabla_{\phi_{2}}\mathcal{L}_{Q}\left(\mathcal{B}^{(i)};\phi_{2}\right)$  10  $\theta^{(i)}\gets \theta - \alpha_{1}\nabla_{\theta}\mathcal{L}_{\pi}\left(\mathcal{B}^{(i)};\theta ,\phi_{1}^{(i)}\right)$  11 end 12  $\begin{array}{r}\phi_{1}\leftarrow \phi_{1} - \eta_{2}\sum_{i}\sum_{j}\nabla_{\phi_{1}}\mathcal{L}_{Q}^{\mathrm{ens}}\left(\mathcal{B}^{(i)};\phi_{1}^{(j)}\right) \end{array}$  13  $\begin{array}{r}\phi_{2}\leftarrow \phi_{2} - \eta_{2}\sum_{i}\sum_{j}\nabla_{\phi_{2}}\mathcal{L}_{Q}^{\mathrm{ens}}\left(\mathcal{B}^{(i)};\phi_{2}^{(j)}\right) \end{array}$  14  $\begin{array}{r}\theta \leftarrow \theta - \alpha_{2}\sum_{i}\sum_{j}\nabla_{\theta}\mathcal{L}_{\pi}^{*}\left(\mathcal{B}^{(i)};\theta^{(j)},\phi_{1}^{(j)}\right) \end{array}$  15 end

# Algorithm 2: In-Domain Finetuning

1 Input: Real market data  $\{\mathcal{D}_{m,n = 0}\}_{m = M - M^{\prime} + 1}^{M}$  2 Parameters:  $\alpha_{1}$ $\alpha_{2}$ $\eta_{1}$ $\eta_{2}$  3 Obtain the learned  $\theta$ $\phi_1$ $\phi_2$  from Algorithm 1 4 for  $T_{2}$  steps do 5 Sample  $\{\mathcal{D}^{(i)}\}_{i = 1}^{K}\sim \{\mathcal{D}_{m,n = 0}\}_{m = M - M^{\prime} + 1}^{M}$  6 for each  $\mathcal{D}^{(i)}\in \{\mathcal{D}^{(i)}\}_{i = 1}^{K}$  do 7 Sample  $\mathcal{B}_{\mathrm{tr}}^{(i)},\mathcal{B}_{\mathrm{ts}}^{(i)}\sim \mathcal{D}^{(i)}$  8  $\phi_1^{(i)}\gets \phi_1 - \eta_1\nabla_{\phi_1}\mathcal{L}_Q(\mathcal{B}_{\mathrm{tr}}^{(i)};\phi_1)$  9  $\phi_{2}^{(i)}\gets \phi_{2} - \eta_{1}\nabla_{\phi_{2}}\mathcal{L}_{Q}(\mathcal{B}_{\mathrm{tr}}^{(i)};\phi_{2})$  10  $\theta^{(i)}\gets \theta - \alpha_{1}\nabla_{\theta}\mathcal{L}_{\pi}\left(\mathcal{B}_{\mathrm{ts}}^{(i)};\theta ,\phi_{1}^{(i)}\right)$  11 end 12  $\begin{array}{r}\phi_{1}\leftarrow \phi_{1} - \eta_{2}\sum_{i}\nabla_{\phi_{1}}\mathcal{L}_{Q}\left(\mathcal{B}_{\mathrm{ts}}^{(i)};\phi_{1}^{(i)}\right) \end{array}$  13  $\begin{array}{r}\phi_{2}\leftarrow \phi_{2} - \eta_{2}\sum_{i}\nabla_{\phi_{2}}\mathcal{L}_{Q}\left(\mathcal{B}_{\mathrm{ts}}^{(i)};\phi_{2}^{(i)}\right) \end{array}$  14  $\begin{array}{r}\theta \leftarrow \theta - \alpha_{2}\sum_{i}\nabla_{\theta}\mathcal{L}_{\pi}\left(\mathcal{B}_{\mathrm{ts}}^{(i)};\theta^{(i)},\phi_{1}^{(i)}\right) \end{array}$  15 end

representing their index. The objective of inner- loop optimization is to maximize in- domain rewards for each subset.

We proceed with outer- loop optimization, as illustrated in Figure 3, to compute second- order derivatives by evaluating the inner- loop parameters  $\theta^{(j)}$  and  $\phi_k^{(j)}$ , learned from subset  $j$ , on the data split  $\mathcal{B}^{(i)}$  from distinct subsets. Notably, this approach distinguishes itself from most existing meta- RL methods by conducting bilevel gradient updates across distinct pairs of subsets. The aim is to update model parameters to improve the policy's robustness to OOD trajectories.

We formulate the actor's objective function  $\mathcal{L}_{\pi}$  as follows, where  $s_t = [h_t^{\mathrm{relat}}, h_t^{\mathrm{long}}, h_t^{\mathrm{short}}, z_t]$  and  $Z_{\phi_1}$  is a normalization factor:

$$
\min_{\theta}\mathbb{E}_{s_t}\big[D_{\mathrm{KL}}(\pi_\theta (a_t\mid s_t)\| \exp (Q_{\phi_1}(s_t,a_t)) / Z_{\phi_1}(s_t))\big]. \tag{2}
$$

In subsequent sections, we will elaborate on the inner- loop critic loss  $\mathcal{L}_Q$ , which minimizes the original TD errors, and the outer- loop critic loss  $\mathcal{L}_Q^{\mathrm{ens}}$ , which optimizes the modified ensemble- based TD errors.

Bilevel finetuning across in- domain data. Due to the non- stationary nature of the time- evolving market, finetuning MetaTrader on recent training data close to the test set can enhance its final performance. In Algorithm 2, we employ the bilevel optimization scheme within each training subset. We first draw subsets from the buffer of raw data, such that  $\{\mathcal{D}^{(i)}\}_{i = 1}^{K}\sim \{\mathcal{D}_{m,n = 0}\}_{m = M - M^{\prime} + 1}^{M}$ . Importantly, we exclusively use the original data to eliminate the unexpected noise introduced by the transformed data. It is essential to note that during the finetuning phase, we perform the inner- loop and outer- loop gradient steps on separate data batches,  $\mathcal{B}_{\mathrm{tr}}^{(i)}$  and  $\mathcal{B}_{\mathrm{ts}}^{(i)}$ , sampled from the same subset  $\mathcal{D}^{(i)}$ . This approach aims to facilitate model adaptation to recent market dynamics.

Transformation- based TD learning with worst- case bootstrapping. We propose a novel TD method for training the critic model during the OOD policy learning phase. In Algorithm 1, the training objectives of  $Q_{\phi_{1,2}}$ , including the inner- loop  $\mathcal{L}_Q$  and the outer- loop  $\mathcal{L}_Q^{\mathrm{ens}}$ , can be formulated as

$$
\min_{\phi_k}\mathbb{E}_{(s_t,a_t)}\big[Q_{\phi_k}(s_t,a_t) - \mathrm{sg}\big(\widehat{Q} (s_t,a_t)\big)\big]^2, \tag{3}
$$

where  $Q_{\phi_k}(\cdot)$  represents the TD estimate of the critic  $k$  at timestamp  $t$ ,  $\widehat{Q} (\cdot)$  represents the corresponding TD target, and  $\mathrm{sg}(\cdot)$  denotes stopping the gradient backpropagation. We here denote  $s_t = [h_t,z_t]$  and  $h_t = [h_t^{\mathrm{relat}}, h_t^{\mathrm{long}}, h_t^{\mathrm{short}}]$ . In the inner- loop optimization step, we formulate the TD target  $\widehat{Q} (\cdot)$  as

$$
\widehat{Q} (s_t,a_t) = R(s_t,s_{t + 1}) + \gamma \big[-\lambda \log \pi_\theta (a_{t + 1}\mid s_{t + 1}) + \min_{k = 1,2}Q_{\bar{\phi}_k}(s_{t + 1},a_{t + 1})\big], \tag{4}
$$

where  $R(\cdot)$  is the pre- defined reward function and  $a_{t + 1}$  is generated by the policy  $\pi_{\theta}(\cdot \mid s_{t + 1})$ . We incorporate double target Q- networks  $Q_{\bar{\phi}_{1,2}}$ , which are updated using the moving- average parameters from corresponding Q- networks  $Q_{\phi_{1,2}}$ .  $Q_{\bar{\phi}_k}$  is the next- step Q- value from each target Q- network.

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/3548bb3401feaf39cccb7ad1245e573a05e5325388c4b077d9df5faa2ab729cd.jpg)  
Figure 5: Transformation-based TD learning with worst-case bootstrapping. The left part represents the TD estimate, and the right part corresponds to the TD target. By approximating worst-case future payoffs through a Monte Carlo method over a batch of data transformations, our approach aims to improve the generalizability of policies learned from offline data. This also helps mitigate the value overestimation issue, which is especially problematic when there are substantial discrepancies between the training and test distributions of non-stationary market data.

In the outer- loop gradient update step, as shown in Figure 5, we incorporate a new form of TD target derived from a batch of transformed data in Eq. (3). For clarity, we denote  $\hat{Q} (t)$  as

$$
\hat{Q}^{\mathrm{ens}}(s_t,a_t) = \min_{n = 0:N}\Big[R(s_t,s_{t + 1}^{(n)}) + \gamma \big(-\lambda \log \pi_\theta (a_{t + 1}^{(n)}\mid s_{t + 1}^{(n)}) + \min_{k = 1,2}Q_{\bar{\phi}_k}\big(s_{t + 1}^{(n)},a_{t + 1}^{(n)}\big)\Big], \tag{5}
$$

where  $\{s_{t + 1}^{(n)}\}_{n = 1}^{N}$  represent simulated next- step market states transformed by  $F_{1:3}$ , and  $a_{t + 1}^{(n)}$  is generated by the policy  $\pi_{\theta}(\cdot \mid s_{t + 1}^{(n)})$ . Notably,  $s_{t + 1}$  is specifically referred to as  $s_{t + 1}^{(0)}$ , denoting the next- step market state encoded from the original data.

Eq. (5) is feasible in our partial- offline RL formulation, where  $h_{t + 1}\sim P_h(h_{t + 1}\mid h_t)$  and  $z_{t + 1}\sim P_z(z_{t + 1}\mid z_t,a_t)$ . This decoupled state transition approach allows us to directly evaluate the value of  $a_{t}$  given  $h_t$  and  $z_{t}$ , since in  $R(h_{t},h_{t + 1},z_{t},z_{t + 1})$ , the only missing component is the next market state  $h_{t + 1}$ , which is independent of the action. As previously described, while accurately estimating the distribution of  $h_{t + 1}$  is intractable, we perform Monte Carlo sampling over diverse transformations of the original data at  $t + 1$  and approximate the worst- case TD target using  $\{h_{t + 1}^{(n)}\}_{n = 1}^{N}$ .

Furthermore, it is important to note that existing ensemble- based TD methods [An et al., 2021, Lee et al., 2022, Wu et al., 2022] typically train multiple target Q- networks with separate model parameters and compute ensemble value regularization by exploiting the implicit diversity among these Q- networks. In contrast, our approach uses a single pair of target Q- networks and derives the worst- case TD target by leveraging the explicit diversity introduced by transformed data.

# 4 Results

We evaluate MetaTrader using the CSI- 300 and NASDAQ- 100 datasets, both adopted from Stock- Former [Gao et al., 2023a]. The CSI dataset is sourced from the CSI- 300 Composite Index, which includes 88 stocks. It spans from 01/17/2011 to 04/01/2022 and is split into training and test sets containing 1,936 and 785 trading days, respectively. The NASDAQ dataset contains 86 NASDAQ stocks, collected from Yahoo Finance. It covers the period from 01/17/2011 to 04/01/2022, with a training set of 2,002 trading days and a test set of 819 trading days. We provide details on data preprocessing, normalization, and the technical indicators used in our method in Supplementary Section S2.

Table 1: Offline evaluation results on CSI and NASDAQ datasets. We use cumulative return, annualized return, Sharpe ratio, and maximum drawdown as the metrics. Given the inherent instability of RL algorithms, we present the results of RL-based models from 10 random training seeds.  

<table><tr><td rowspan="2">Method</td><td colspan="4">CSI-300</td><td colspan="4">NASDAQ-100</td></tr><tr><td>CR↑</td><td>AR↑</td><td>SR↑</td><td>MDD↓</td><td>CR↑</td><td>AR↑</td><td>SR↑</td><td>MDD↓</td></tr><tr><td>Market benchmark</td><td>0.08</td><td>0.02</td><td>0.23</td><td>0.31</td><td>0.99</td><td>0.26</td><td>0.98</td><td>0.28</td></tr><tr><td>HATR</td><td>-0.05</td><td>-0.02</td><td>0.06</td><td>0.51</td><td>0.10</td><td>0.03</td><td>0.25</td><td>0.35</td></tr><tr><td>Relational Ranking</td><td>-0.13</td><td>-0.05</td><td>-0.05</td><td>0.37</td><td>0.79</td><td>0.22</td><td>0.75</td><td>0.37</td></tr><tr><td>AutoFormer</td><td>-0.08</td><td>-0.03</td><td>0.02</td><td>0.58</td><td>-0.28</td><td>-0.10</td><td>-0.27</td><td>0.41</td></tr><tr><td>FactorVAE</td><td>0.96</td><td>0.25</td><td>1.25</td><td>0.17</td><td>0.90</td><td>0.24</td><td>0.77</td><td>0.26</td></tr><tr><td>FinRL-SAC</td><td>0.83±0.05</td><td>0.22±0.01</td><td>0.92±0.04</td><td>0.30±0.01</td><td>0.37±0.05</td><td>0.11±0.01</td><td>0.54±0.04</td><td>0.32±0.01</td></tr><tr><td>FinRL-DDPG</td><td>0.58±0.15</td><td>0.16±0.04</td><td>0.73±0.12</td><td>0.34±0.03</td><td>0.91±0.11</td><td>0.24±0.02</td><td>0.75±0.05</td><td>0.41±0.01</td></tr><tr><td>CQL</td><td>0.64±0.07</td><td>0.18±0.02</td><td>0.75±0.05</td><td>0.33±0.02</td><td>0.77±0.12</td><td>0.21±0.02</td><td>0.76±0.06</td><td>0.35±0.02</td></tr><tr><td>IQL</td><td>1.02±0.10</td><td>0.26±0.02</td><td>0.94±0.06</td><td>0.32±0.02</td><td>0.92±0.09</td><td>0.24±0.02</td><td>0.87±0.04</td><td>0.36±0.01</td></tr><tr><td>SARL</td><td>1.06±0.14</td><td>0.27±0.03</td><td>0.98±0.08</td><td>0.36±0.02</td><td>1.03±0.20</td><td>0.27±0.04</td><td>0.80±0.09</td><td>0.40±0.01</td></tr><tr><td>StockFormer</td><td>1.24±0.10</td><td>0.31±0.02</td><td>1.20±0.06</td><td>0.31±0.02</td><td>0.98±0.07</td><td>0.26±0.02</td><td>0.93±0.04</td><td>0.32±0.02</td></tr><tr><td>MetaTrader</td><td>1.44±0.07</td><td>0.35±0.02</td><td>1.35±0.08</td><td>0.28±0.02</td><td>1.30±0.08</td><td>0.32±0.02</td><td>1.11±0.04</td><td>0.31±0.00</td></tr></table>

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/1b95b7ba46ec799bbc90cd356e27b592770ae4e067e3664ebba9b91865415b33.jpg)  
Figure 6: The cumulative returns under the online adaptation setup. We divide the entire test set into three equal-length splits and progressively finetune the models over the streaming data. All results are obtained from models trained with 10 random seeds.

We compare MetaTrader with the following models: (A) Market benchmarks, including the CSI- 300 Index and the NASDAQ- 100 Index. (B) RL trading methods, including FinRL [Liu et al., 2021], SARL [Ye et al., 2020], and StockFormer [Gao et al., 2023a]. (C) Offline RL methods, including CQL [Kumar et al., 2020] and IQL [Kostrikov et al., 2021]. (D) Stock prediction or general time series forecasting methods, including HATR [Wang et al., 2021], Relational Ranking [Feng et al., 2019], AutoFormer [Wu et al., 2021], and FactorVAE [Duane et al., 2022]. For the stock prediction methods, we apply the buy- and- hold strategy, i.e., buying the stock with the highest predicted return over the next 5 days and selling it 5 days later.

All models are tested with market transaction costs. Unless otherwise specified, the results for the RL- based methods are averaged across three random training seeds. For the details on the training hyperparameters, please refer to Supplementary Section S3.

Standard offline evaluation. For both datasets, we perform OOD policy learning using training data from 01/17/2011 to 12/31/2018. Next, we conduct in- domain finetuning on the last- year training data, specifically from 01/04/2018 to 12/31/2018. To ensure no overlap between the test and training sets, we set the input data of the test sequences to start from January 2019, covering the trading days from 04/01/2019 to 04/01/2022 over three years.

Table 1 presents the quantitative results of MetaTrader in terms of cumulative return (CR), annualized return (AR), Sharpe ratio (SR), and maximum drawdown (MDD). Please refer to Supplementary Section S4 for detailed definitions of the evaluation metrics. It is worth noting that our approach generally outperforms all stock prediction methods by substantial margins. In particular, compared to FactorVAE, MetaTrader outperforms by 50% in cumulative return (1.44 vs. 0.96) on the CSI dataset

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/22295840e9cabd67e764b82eaa359a01ecf691b925d2aa9976be68a680a7270d.jpg)  
Figure 7: Full comparisons in all metrics under the online adaptation setup. The online adaptation setup more effectively demonstrates the advantages of bilevel policy learning and finetuning for efficient domain adaptation, enabling MetaTrader to outperform StockFormer by significant margins.

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/260c75bb02b7414ba1d83db612a66be43e63c5e2e6b6b22e2432d374ad60c659.jpg)  
Figure 8: Analyses of data transformation techniques for OOD policy learning (Algorithm 1). We report the mean results on the CSI dataset over 3 seeds. DT: Data Transformation.

and by  $44.4\%$  on the NASDAQ dataset (1.30 vs. 0.90). In finance, the Sharpe ratio (also known as the reward- to- variability ratio) measures the additional amount of return that an investor receives per unit of increase in risk. It is defined as the difference between the returns of the investment and the risk- free return, divided by the standard deviation of the investment returns. MetaTrader outperforms FactorVAE by  $44.1\%$  in Sharpe ratio (1.11 vs. 0.77) on the NASDAQ dataset, demonstrating a strong balance between portfolio returns and risk control.

When compared to other RL- based trading methods, MetaTrader delivers the best performance across all evaluation metrics. It improves upon the state- of- the- art StockFormer method in terms of cumulative return by  $16.1\%$  on the CSI dataset and by  $32.7\%$  on NASDAQ. Furthermore, we implement baseline models using the same neural network architecture as MetaTrader, but trained with other conservative offline RL techniques, including CQL and IQL. As observed, existing offline RL approaches struggle with RL- for- finance tasks due to fluctuations in data distributions, leading to significant shifts between the training and testing domains. In contrast, our approach achieves notable performance gains through bilevel policy learning, which effectively prevents the policy from overfitting to the offline data.

Online adaptation on streaming data. We employ another experimental setup that closely aligns with the dynamic financial decision- making applications, where we finetune the model on- the- fly over the streaming test data. The entire test set is divided into three equal- length periods: 04/01/2019—04/01/2020, 04/02/2020—04/01/2021, and 04/02/2021—04/01/2022. Each period is followed by an

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/2734ee6ac6f13ade88c2dc71a3ea7f8f9d197ab404a8d48bb4b1af29f52e325c.jpg)  
Figure 9: Ablation studies of transformation-based TD ensembles. We compare our approach with the following: (1) the use of the original TD method in bilevel learning, and (2-3) baseline models that compute TD targets based on real future market data using an ensemble of target Q-networks [An et al., 2021, Lee et al., 2022]. In contrast, during the OOD policy learning stage, MetaTrader computes the TD target using transformed data and a single pair of target Q-networks. The experiments are conducted under the online adaptation setup on the CSI dataset.

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/d92bb52cb805a2e50da52cb96225e4b3135a073c890267d157d422abd26006fc.jpg)  
Figure 10: The disparities between the predicted values by the critic and the true discounted future rewards. A larger disparity signifies a more pronounced value overestimation issue in offline RL. The results are obtained under the offline evaluation setup on the CSI dataset.

in- domain finetuning phase before testing. For instance, for the test period of 04/02/2020—04/01/2021, we perform in- domain finetuning using data in 04/01/2019—04/01/2020 prior to testing.

For the online adaptation setup, our main comparison is between MetaTrader, FactorVAE- Finetune, and StockFormer- Finetune, all of which are continuously finetuned using the streaming test data. The results are shown in Figure 6 and Figure 7. As we can see, MetaTrader presents a remarkable advantage against other approaches, including the state- of- the- art stock prediction model (i.e., FactorVAE) and RL- based stock trading method (i.e., StockFormer). On the CSI dataset, it improves StockFormer- Finetune by  $26\%$  in cumulative return (1.84 vs. 1.46) and by around  $18\%$  in Sharpe ratio (1.61 vs. 1.37). On the NASDAQ dataset, MetaTrader improves StockFormer- Finetune by over  $25\%$  in cumulative return (1.58 vs. 1.26) and by around  $43\%$  in Sharpe ratio (1.47 vs. 1.03).

The effectiveness of data transformation. To assess the true impact of different data transformation techniques, we experiment with baseline models that (1) do not incorporate transformed data at any stage of training, and (2) incorporate only some of the data transformation techniques. We have two observations from Figure 8. First, leveraging any of the data transformation methods during the OOD policy learning phase consistently enhances the model's final performance, resulting in substantial improvements across all three evaluation metrics. Second, combining multiple transformation techniques leads to further significant gains. Notably, we observe a  $10.8\%$  increase in cumulative return  $(1.66 \rightarrow 1.84)$  for online adaptation on the CSI dataset.

Impact of the transformation- based conservative TD ensembles. To assess the effectiveness of the transformation- based TD method used in the proposed bilevel RL framework, we implement a baseline model that adopts the original TD method from SAC. Figure 9 demonstrates the improvements achieved by our proposed TD method, with a significant increase of  $9.5\%$  in cumulative return on the CSI dataset. Furthermore, we experiment with other TD ensemble approaches [An et al., 2021,

Table 2: Ablation studies of the operations in the in-domain finetuning stage (Algorithm 2). We evaluate alternative configurations of the bilevel gradient update and transformed stock data. The experiments are conducted within the online adaptation setup.  

<table><tr><td rowspan="2">Bilevel gradient</td><td rowspan="2">Transfer data</td><td colspan="4">CSI</td><td colspan="4">NASDAQ</td></tr><tr><td>CR↑</td><td>AR↑</td><td>SR↑</td><td>MDD↓</td><td>CR↑</td><td>AR↑</td><td>SR↑</td><td>MDD↓</td></tr><tr><td>×</td><td>×</td><td>1.78±0.03</td><td>0.41±0.01</td><td>1.57±0.03</td><td>0.23±0.01</td><td>1.41±0.04</td><td>0.34±0.01</td><td>1.34±0.04</td><td>0.31±0.01</td></tr><tr><td>✓</td><td>×</td><td>1.84±0.03</td><td>0.42±0.01</td><td>1.61±0.03</td><td>0.20±0.01</td><td>1.58±0.03</td><td>0.37±0.01</td><td>1.47±0.04</td><td>0.30±0.01</td></tr><tr><td>✓</td><td>✓</td><td>0.84±0.04</td><td>0.23±0.01</td><td>0.94±0.05</td><td>0.33±0.02</td><td>1.24±0.03</td><td>0.31±0.01</td><td>1.03±0.05</td><td>0.33±0.01</td></tr></table>

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-04/2fd2323e-be28-42ab-b742-e4949f89a65f/101da6b41d0eadf705fdbe92ab506aff5dbaf9d5b532a1a2d56a0d88b971dff1.jpg)  
Figure 11: Experiments on the expanded dataset with 587 stocks. We follow the same offline evaluation and online adaptation setups and demonstrate that MetaTrader achieves more significant performance gains over existing RL-based trading methods than those on small sets.

Lee et al., 2022], i.e., such as using the minimum and mean values from 5 parallel Q- networks as the Bellman target. As shown in Figure 9, our approach, which computes TD targets using transformed data and a single pair of target Q- networks, demonstrates a significant advantage over other ensemble- based TD learning alternatives. These results highlight the effectiveness of using diverse future data transformations to approximate worst- case future returns, guiding the agent towards safer and more reliable trading behaviors.

Additionally, in Figure 10, we compare the value estimation accuracy with vs. without the transformation- based TD ensembles. Specifically, we report the discrepancies between the values predicted by the critic models and true values, determined by the discounted sum of rewards throughout the same data trajectories. As observed, StockFormer and "MetaTrader w/ original TD" tend to overestimate the true value function. In contrast, the values estimated by the final "MetaTrader w/ ensemble- based TD" are notably more accurate and more akin to the true values.

Technical designs of the in- domain finetuning stage. We perform model finetuning on real data from the most recent year, using bilevel gradient updates (see Algorithm 2). In Table 2, we investigate the necessity of bilevel optimization and explain why the transformed data is excluded during the finetuning phase. When compared to directly using the inner- loop gradients to update the model, bilevel optimization results in a  $3.4\%$  improvement in the cumulative return on the CSI dataset (1.84 vs. 1.78) and a  $12.1\%$  improvement on NASDAQ (1.58 vs. 1.41). Furthermore, incorporating data transformations during the finetuning phase leads to a noticeable performance drop. This is expected, as the transformed data may not align with the recent dynamic patterns close to the test set.

Handling the challenges for larger market data. We conduct experiments on a larger dataset by expanding the range of CSI stocks and selecting a dataset containing 587 stocks. Existing RL- based stock trading methods, such as FinRL, StockFormer, and SARL, primarily conduct experiments on relatively small- scale datasets. We attribute this limitation to two main factors. From a data perspective, trading suspensions frequently occur in real- world stock data. Previous studies often select stocks based on the

Table 3: A comparison of computational costs. MetaTrader achieves comparable runtime efficiency to StockFormer on the CSI dataset, but with a slight increase in training time due to the bilevel learning scheme. Both models use the same pretrained feature extractors for the market states.  

<table><tr><td>Method</td><td>Training time</td><td>Inference time per sequence</td></tr><tr><td>StockFormer</td><td>28min 02s</td><td>19.03ms</td></tr><tr><td>MetaTrader</td><td>37min 27s</td><td>19.06ms</td></tr></table>

requirement that the proportion of valid data exceeds a specific threshold (e.g.,  $98\%$  in StockFormer) to reduce noise from excessive data interpolation. From an algorithm perspective, as the stock pool size increases, the action space grows significantly, making it more challenging for RL methods to manage. If we aim to trade thousands of stocks in the market, the dimensionality of the action space can be even larger than the number of training sequences. The difficulty of high- dimensional action space is well- documented in other domains beyond stock trading [Tavakoli et al., 2018, Saito et al., 2024].

Computational costs. In Table 3, we present the total training time and the per- sequence inference time of the compared models using a single NVIDIA RTX 3090 GPU. Given that our work primarily focuses on daily- level stock trading, the increased training cost introduced by bilevel optimization is acceptable, while the inference time adequately meets the efficiency demands in this scenario.

Additional model analyses. We provide additional experimental results in Supplementary Section S5, including: (1) Comparisons of finetuning RL- based trading models, showing that the proposed bilevel RL approach improves domain adaptation by enabling effective model finetuning; (2) Evaluations of these approaches on more recent data, demonstrating the effectiveness of MetaTrader on test data with more pronounced distributional shifts from the training set; (3) Evaluations of baseline models trained with an increased number of gradient steps, showing that the benefits of the bilevel RL approach do not stem from a simple increase in optimization steps.

# 5 Related Work

Deep learning- based portfolio optimization methods. There are two primary categories of deep learning- based approaches for portfolio optimization. The first category leverages the temporal modeling capabilities of existing models to forecast asset prices [Li et al., 2018, Xu and Cohen, 2018, Feng et al., 2019, Wang et al., 2021, Duan et al., 2022, Zheng et al., 2023]. For stock trading, these methods are typically combined with relatively simple trading policies, such as buying stocks predicted to yield the highest returns and selling them at a predetermined time. The second line of work employs deep RL, framing portfolio optimization as a Markov Decision Process (MDP) to make dynamic decisions about the timing and quantity of investments [Deng et al., 2016, Briola et al., 2021, Jeong and Kim, 2019, Liu et al., 2021, Kumar, 2023, Liu et al., 2022, Gao et al., 2023a]. In this paper, we reassess the fidelity of these approaches, demonstrating that policies, constrained by offline state exploration, tend to memorize only the optimal strategy derived from the offline data. This limits the agent's ability to generalize to OOD data scenarios across varying market conditions.

Bilevel optimization. Bilevel optimization- based meta- learning has emerged as a powerful tool for addressing various machine learning problems, such as few- shot learning [Antoniou et al., 2019, Li et al., 2019, Triantafillou et al., 2020, Day et al., 2022, Cheng et al., 2023] and domain adaptation [Schmidhuber, 1987, Finn, 2018, Hospedales et al., 2021]. In the context of RL, it has been applied to learning dynamics models [Saemundsson et al., 2018, Nagabandi et al., 2019] or directly optimizing policies [Duan et al., 2017, Mishra et al., 2018, Finn et al., 2017, Nagabandi et al., 2019, Gupta et al., 2018, Humplik et al., 2019, Mitchell et al., 2021, Pong et al., 2022, Tang, 2022, Greenberg et al., 2023, Gao et al., 2023b, Ma et al., 2023, Wang et al., 2023]. These optimization- based meta- learning models have demonstrated the potential to improve the generalizability of RL policies. In contrast to prior work, we focus on the challenges posed by limited and non- stationary financial data in policy learning. To address these challenges, we introduce a novel bilevel RL approach that enhances policy generalizability while mitigating the issue of value overestimation.

Ensemble RL. Standard ensemble RL is a technique where multiple models or agents are trained together to improve performance and robustness in RL tasks. The idea is to leverage the diversity of multiple learned policies or value functions to make better decisions, reduce overfitting, and enhance exploration. Ensemble methods are particularly useful in environments with high uncertainty, or when the goal is to learn an optimal policy from a static dataset previously collected, particularly when applied to OOD data points. For instance, a prominent class of ensemble- based methods relies on model diversity, where multiple Q- networks are trained to approximate the Bellman target by taking the minimum or mean value across several parallel Q- networks [An et al., 2021, Lee et al., 2022, Wu et al., 2022, Zhao et al., 2023]. In contrast, our method leverages data diversity by performing ensemble bootstrapping on the Q- function using various data transformations. This approach captures a wider range of variability in the decision- making process while preserving model efficiency.

# 6 Conclusions, Limitations, and Broader Impacts

This paper presents MetaTrader, an RL method that formulates sequential portfolio optimization as a partial- offline RL problem with decoupled market states and balance states. MetaTrader improves the model's generalizability to non- stationary stock data by integrating carefully designed stock augmentation techniques in a bilevel policy learning framework. Additionally, we proposed a novel Q- learning method with a data transformation- based TD bootstrapping method, which aims to produce more conservative policies in highly dynamic data scenarios with limited training data points. Experiments on two public stock datasets demonstrate the effectiveness of MetaTrader compared to existing RL- for- finance approaches, showcasing its great potential in dealing with rapidly changing financial markets.

An unresolved problem in this study is the training instability. Compared to stock prediction methods like FactorVAE and HATR, we observed that RL- based methods (including SARL, StockFormer, and our approach) generally exhibit larger standard deviations in performance across multiple training runs with random seeds. To address this, we plan to investigate alternative training strategies, including more robust initialization techniques and the integration of regularization methods that can mitigate the impact of random fluctuations and improve stability across different training runs. Another limitation is that our approach is trained and validated solely on daily- level stock data, with an inference time of approximately 20 milliseconds per sequence. We plan to enhance its computational efficiency and apply it to high- frequency trading scenarios in the future.

Our MetaTrader framework, although initially designed for financial decision- making tasks, offers principles that can be effectively extended to other decision- making domains, such as autonomous driving. Similar to stock market dynamics, autonomous driving systems must continually adapt to an evolving environment (e.g., fluctuating traffic patterns). The state space an also be divided into action- free and action- dependent components: (1) The action- free traffic state represents the road conditions, including the behavior of other vehicles and external factors like weather and traffic observations that are unrelated to the agent's actions. (2) The action- dependent embodied state represents the agent's embodied states, such as the vehicle's positions and speeds. The transition of this state branch depends on the actions taken by the vehicle. Building on this decoupled MDP, we can formulate the learning problem driving agent from a previously collected traffic dataset as a partial- offline RL problem! By leveraging modern "World Models", it is possible to generate new driving scenarios by transforming action- free traffic states and simulating changes in road conditions or the behavior of other vehicles, such as altering the trajectories of other vehicles, introducing random road disruptions, or simulating unexpected weather changes. By implementing bilevel RL with an ensemble- based TD target on the transformed data, we can train a driving agent using the proposed algorithm, ensuring that autonomous systems can handle the complexities of dynamic, non- stationary traffic environments.

# Acknowledgments

This work was supported by the National Natural Science Foundation of China (Grant 62250062), the Smart Grid National Science and Technology Major Project (Grant 2024ZD0801200), the Shanghai Municipal Science and Technology Major Project (Grant 2021SHZDZX0102), and the Fundamental Research Funds for the Central Universities.

# References

[An et al., 2021] An, G., Moon, S., Kim, J.- H., and Song, H. O. (2021). Uncertainty- based offline reinforcement learning with diversified q- ensemble. In NeurIPS, volume 34, pages 7436- 7447. [Antoniou et al., 2019] Antoniou, A., Edwards, H., and Storkey, A. (2019). How to train your maml. In ICLR.[Briola et al., 2021] Briola, A., Turiel, J., Marcaccioli, R., Cauderan, A., and Aste, T. (2021). Deep reinforcement learning for active high frequency trading. arXiv preprint arXiv:2101.07107. [Cheng et al., 2023] Cheng, C., Song, L., Xue, R., Wang, H., Sun, H., Ge, Y., and Shan, Y. (2023). Meta- adapter: An online few- shot learner for vision- language model. In NeurIPS.[Day et al., 2022] Day, B. J., Torné, R. V., Simidjievski, N., and Lio, P. (2022). Attentional meta- learners for few- shot polythetic classification. In ICML.[Deng et al., 2016] Deng, Y., Bao, F., Kong, Y., Ren, Z., and Dai, Q. (2016). Deep direct reinforcement learning for financial signal representation and trading. IEEE transactions on neural networks and learning systems, 28(3):653- 664. [Duan et al., 2017] Duan, Y., Schulman, J., Chen, X., Bartlett, P. L., Sutskever, I., and Abbeel, P. (2017).  $\mathrm{Rl^2}$ : Fast reinforcement learning via slow reinforcement learning. In ICLR.[Duan et al., 2022] Duan, Y., Wang, L., Zhang, Q., and Li, J. (2022). Factorvae: A probabilistic dynamic factor model based on variational autoencoder for predicting cross- sectional stock returns. In AAAI.[Feng et al., 2019] Feng, F., He, X., Wang, X., Luo, C., Liu, Y., and Chua, T.- S. (2019). Temporal relational ranking for stock prediction. ACM Transactions on Information Systems (TOIS), 37(2):1- 30. [Finn, 2018] Finn, C. (2018). Learning to Learn with Gradients. PhD thesis, University of California, Berkeley, USA.[Finn et al., 2017] Finn, C., Abbeel, P., and Levine, S. (2017). Model- agnostic meta- learning for fast adaptation of deep networks. In ICML.[Gao et al., 2023a] Gao, S., Wang, Y., and Yang, X. (2023a). Stockformer: learning hybrid trading machines with predictive coding. In IJCAI.[Gao et al., 2023b] Gao, Y., Zhang, R., Guo, J., Wu, F., Yi, Q., Peng, S., Lan, S., Chen, R., Du, Z., Hu, X., et al. (2023b). Context shift reduction for offline meta- reinforcement learning. In NeurIPS.[Greenberg et al., 2023] Greenberg, I., Mannor, S., Chechik, G., and Meirom, E. (2023). Train hard, fight easy: Robust meta reinforcement learning. In NeurIPS.[Gupta et al., 2018] Gupta, A., Mendonca, R., Liu, Y., Abbeel, P., and Levine, S. (2018). Meta- reinforcement learning of structured exploration strategies. In NeurIPS.[Haarnoja et al., 2018] Haarnoja, T., Zhou, A., Abbeel, P., and Levine, S. (2018). Soft actor- critic: Off- policy maximum entropy deep reinforcement learning with a stochastic actor. In ICML.[Hospedales et al., 2021] Hospedales, T., Antoniou, A., Micaelli, P., and Storkey, A. (2021). Meta- learning in neural networks: A survey. IEEE transactions on pattern analysis and machine intelligence, 44(9):5149- 5169. [Humplik et al., 2019] Humplik, J., Galashov, A., Hasenclever, L., Ortega, P. A., Teh, Y. W., and Heess, N. (2019). Meta reinforcement learning as task inference. arXiv preprint arXiv:1905.06424. [Jeong and Kim, 2019] Jeong, G. and Kim, H. Y. (2019). Improving financial trading decisions using deep q- learning: Predicting the number of shares, action strategies, and transfer learning. Expert Systems with Applications, 117:125- 138.

[Kostrikov et al., 2021] Kostrikov, I., Nair, A., and Levine, S. (2021). Offline reinforcement learning with implicit q- learning. arXiv preprint arXiv:2110.06169. [Kumar et al., 2020] Kumar, A., Zhou, A., Tucker, G., and Levine, S. (2020). Conservative q- learning for offline reinforcement learning. In NeurIPS, volume 33, pages 1179- 1191. [Kumar, 2023] Kumar, P. (2023). Deep reinforcement learning for high- frequency market making. In ACML.[Lee et al., 2022] Lee, S., Seo, Y., Lee, K., Abbeel, P., and Shin, J. (2022). Offline- to- online reinforcement learning via balanced replay and pessimistic q- ensemble. In CoRL, pages 1702- 1712. PMLR.[Li et al., 2018] Li, H., Shen, Y., and Zhu, Y. (2018). Stock price prediction using attention- based multi- input lstm. In ACML.[Li et al., 2019] Li, W., Wang, L., Xu, J., Huo, J., Gao, Y., and Luo, J. (2019). Revisiting local descriptor based image- to- class measure for few- shot learning. In CVPR.[Liu et al., 2022] Liu, X.- Y., Xia, Z., Rui, J., Gao, J., Yang, H., Zhu, M., Wang, C., Wang, Z., and Guo, J. (2022). Finrl- meta: Market environments and benchmarks for data- driven financial reinforcement learning. In NeurIPS.[Liu et al., 2021] Liu, X.- Y., Yang, H., Gao, J., and Wang, C. D. (2021). Finrl: Deep reinforcement learning framework to automate trading in quantitative finance. In ICAIF.[Ma et al., 2023] Ma, Z., Guo, H., Chen, J., Li, Z., Peng, G., Gong, Y.- J., Ma, Y., and Cao, Z. (2023). Metabox: A benchmark platform for meta- black- box optimization with reinforcement learning. In NeurIPS.[Mishra et al., 2018] Mishra, N., Rohaninejad, M., Chen, X., and Abbeel, P. (2018). A simple neural attentive meta- learner. In ICLR.[Mitchell et al., 2021] Mitchell, E., Rafailov, R., Peng, X. B., Levine, S., and Finn, C. (2021). Offline meta- reinforcement learning with advantage weighting. In ICML.[Nagabandi et al., 2019] Nagabandi, A., Clavera, I., Liu, S., Fearing, R. S., Abbeel, P., Levine, S., and Finn, C. (2019). Learning to adapt in dynamic, real- world environments through meta- reinforcement learning. In ICLR.[Pong et al., 2022] Pong, V. H., Nair, A. V., Smith, L. M., Huang, C., and Levine, S. (2022). Offline meta- reinforcement learning with online self- supervision. In ICML.[Sæmundsson et al., 2018] Sæmundsson, S., Hofmann, K., and Deisenroth, M. P. (2018). Meta reinforcement learning with latent variable gaussian processes. arXiv preprint arXiv:1803.07551. [Saito et al., 2024] Saito, Y., Yao, J., and Joachims, T. (2024). Potec: Off- policy learning for large action spaces via two- stage policy decomposition. ICML.[Schmidhuber, 1987] Schmidhuber, J. (1987). Evolutionary principles in self- referential learning, or on learning how to learn: the meta- meta- ... look. PhD thesis, Technische Universität München.[Tang, 2022] Tang, Y. (2022). Biased gradient estimate with drastic variance reduction for meta reinforcement learning. In ICML.[Tavakoli et al., 2018] Tavakoli, A., Pardo, F., and Kormushev, P. (2018). Action branching architectures for deep reinforcement learning. In AAAI, volume 32. [Triantafillou et al., 2020] Triantafillou, E., Zhu, T., Dumoulin, V., Lamblin, P., Evci, U., Xu, K., Goroshin, R., Gelada, C., Swersky, K., Manzagol, P.- A., et al. (2020). Meta- dataset: A dataset of datasets for learning to learn from few examples. In ICLR.[Wang et al., 2021] Wang, H., Li, S., Wang, T., and Zheng, J. (2021). Hierarchical adaptive temporal- relational modeling for stock trend prediction. In IJCAI.

[Wang et al., 2023] Wang, J., Zhang, J., Jiang, H., Zhang, J., Wang, L., and Zhang, C. (2023). Offline meta reinforcement learning with in- distribution online adaptation. In ICML.[Wu et al., 2021] Wu, H., Xu, J., Wang, J., and Long, M. (2021). Autoformer: Decomposition transformers with auto- correlation for long- term series forecasting. In NeurIPS.[Wu et al., 2022] Wu, Y., Chen, X., Wang, C., Zhang, Y., and Ross, K. W. (2022). Aggressive q- learning with ensembles: Achieving both high sample efficiency and high asymptotic performance. In NeurIPS.[Xu and Cohen, 2018] Xu, Y. and Cohen, S. B. (2018). Stock movement prediction from tweets and historical prices. In ACL.[Ye et al., 2020] Ye, Y., Pei, H., Wang, B., Chen, P.- Y., Zhu, Y., Xiao, J., and Li, B. (2020). Reinforcement- learning based portfolio management with augmented asset movement prediction states. In A4AI.[Zhao et al., 2023] Zhao, K., Ma, Y., Liu, J., Zheng, Y., and Meng, Z. (2023). Ensemble- based offline to- online reinforcement learning: From pessimistic learning to optimistic exploration. arXiv preprint arXiv:2306.06871. [Zheng et al., 2023] Zheng, X., Liu, M., and Zhu, M. (2023). Deep hashing- based dynamic stock correlation estimation via normalizing flow. In IJCAI.

# Supplementary Information

# S1 Data Transformation

We transform the data in sequences of 64 days in length to construct the subsets. While the data transformation techniques are briefly illustrated in the main text, we present more details of the implementation here.

Consider a specific stock  $A$  : It provides an input sequence to the model, in which the daily closing prices can be denoted as  $O_{F_0}^{\mathrm{close}} = \{o_0^{\mathrm{close}},o_1^{\mathrm{close}},\ldots ,o_{63}^{\mathrm{close}}\}$  . The subsequent prices after this sequence are  $o_{64}^{\mathrm{close}},o_{65}^{\mathrm{close}},\ldots$  and so forth. Accordingly, we have the sequence of growth rate between daily closing prices for this stock:  $\Delta O_{F_0}^{\mathrm{close}} = \{0,\Delta o_1^{\mathrm{close}},\ldots ,\Delta o_{63}^{\mathrm{close}}\}$  . For example,  $o_9^{\mathrm{close}} = o_9^{\mathrm{close}}\times (1 + \Delta o_9^{\mathrm{close}})$  Without loss of generality, let us assume that its daily growth rate on the 10- th day (i.e.,  $\Delta o_9^{\mathrm{close}}$  ) is among the Top-  $10\%$  within the stock pool.

In the first transformation method, the price sequence of stock  $A$  is transformed into another sequence denoted by  $O_{F_1}^{\mathrm{close}} = \{o_0^{\mathrm{close}},o_1^{\mathrm{close}},\ldots ,o_8^{\mathrm{close}},o_9',o_{10}',o_{11}',\ldots ,o_{63}'\}$  , where  $o_9' = o_8^{\mathrm{close}}\times (1 - \Delta o_9^{\mathrm{close}})$  . We retain the daily price growth rates on other days, such that  $\Delta o_t' = \Delta o_t^{\mathrm{price}}$  for  $t\geq 10$  . In particular, on days when the number of stocks with positive price growth does not reach  $10\%$  of the total, only those stocks with positive growth will have inverted growth rates. It is noteworthy that although we manipulate the input data by measuring the daily closing prices only, we also transform the open/high/low prices along with the closing prices, while keeping the original data for the trading volumes unchanged.

In the second transformation method, the original price sequence of stock  $A$  is reversed to construct another sequence of  $O_{F_2} = \{o_{63}^{\mathrm{close}},o_{62}^{\mathrm{close}},\ldots ,o_{6}^{\mathrm{close}}\}$

In the third transformation method, the price sequence is transformed into  $\begin{array}{rl}{O_{F_3}} & = \end{array}$ $\{o_0^{\mathrm{close}},o_4^{\mathrm{close}},o_8^{\mathrm{close}},\ldots ,o_{24}^{\mathrm{close}},o_{25}^{\mathrm{close}}\}$

For transformations  $F_{2}$  and  $F_{3}$  , all input data (high/low/volume) will be shifted alongside corresponding price data. For all data transformation methods, we carefully divided the training and test sets based on dates, ensuring that all transformations were applied exclusively to the training set. This guarantees no data leakage and ensures a fair comparison among all methods.

# S2 Datasets

Data preprocessing. For the CSI- 300 stock dataset, we follow previous work [Feng et al., 2019, Gao et al., 2023a] to retain the stocks that have been traded on more than  $98\%$  training days since 01/17/2011. For the NASDAQ- 100 dataset, we also use the  $98\%$  criteria to filter stocks, which derives an investment pool of 86 stocks. If a stock is suspended from trading, we interpolate the missing training data using the daily changing rate of CSI- 300 Composite Index or the NASDAQ- 100 Index.

Data normalization. We perform normalization separately for each stock, ensuring that all normalization factors are specific to the data of the individual stock. For a given stock, all price data (open, close, high, low) share the same normalization factor. The normalized values can be formulated as

$$
N_{t_i}^{\mathrm{price}} = \frac{o_{t_i}^{\mathrm{price}} - \min_t\{o_t^{\mathrm{low}}\}}{\max_t\{o_t^{\mathrm{high}}\} - \min_t\{o_t^{\mathrm{low}}\}}, \tag{6}
$$

The normalization for volume is expressed as

$$
N_{t_i}^{\mathrm{volume}} = \frac{o_{t_i}^{\mathrm{volume}} - \min_t\{o_t^{\mathrm{volume}}\}}{\max_t\{o_t^{\mathrm{volume}}\} - \min_t\{o_t^{\mathrm{volume}}\}}, \tag{7}
$$

where  $N_{t_i}$  represents the normalized value, and the superscript "price" refers to the four price data types: open, close, high, and low.

Technical indicators. To align with StockFormer [Gao et al., 2023a], we use the Stockstats package to compute the technical indicators listed in Table 4, which are incorporated as part of the observation data in our work.

Table 4: Technical indicators and descriptions. Similar to StockFormer [Gao et al., 2023a], these technical indicators are used as parts of the input observation data in MetaTrader.  

<table><tr><td>Technical Indicator</td><td>Description</td></tr><tr><td>macd</td><td>Moving average convergence divergence</td></tr><tr><td>boll_ub</td><td>Bollinger bands (upper band)</td></tr><tr><td>boll_lb</td><td>Bollinger bands (lower band)</td></tr><tr><td>rsi_30</td><td>30 periods relative strength index</td></tr><tr><td>cci_30</td><td>Retrieves the 30 periods commodity channel index</td></tr><tr><td>dx_30</td><td>Directional index with a window length of 30</td></tr><tr><td>close_30_sma</td><td>30 periods simple moving average of the close price</td></tr><tr><td>close_60_sma</td><td>60 periods simple moving average of the close price</td></tr></table>

# S3 Hyperparameters

In Table 5, we provide the hyperparameter details in both the OOD policy learning phase and the in- domain finetuning phase. For the feature extraction module, we adopt the identical hyperparameters as those employed in StockFormer [Gao et al., 2023a].

Table 5: Training hyperparameters of MetaTrader. These hyperparameters are used in both the OOD policy learning phase and the in-domain finetuning phase.  

<table><tr><td>Notation</td><td>Hyperparameter</td><td>Description</td></tr><tr><td>η1</td><td>0.00001</td><td>learning rate of the critic (inner loop)</td></tr><tr><td>η2</td><td>0.0001</td><td>learning rate of the critic (outer loop)</td></tr><tr><td>α1</td><td>0.00001</td><td>learning rate of the actor (inner loop)</td></tr><tr><td>α2</td><td>0.0001</td><td>learning rate of the actor (outer loop)</td></tr><tr><td>d1hidden</td><td>256</td><td>number of MLP channels in the critic</td></tr><tr><td>d2hidden</td><td>256</td><td>number of MLP channels in the actor</td></tr><tr><td>B, K</td><td>32</td><td>batch size, number of sampled subsets per iteration</td></tr><tr><td>M</td><td>216</td><td>number of time period slices</td></tr><tr><td>N</td><td>3</td><td>number of stock augmentation techniques</td></tr><tr><td>T</td><td>64</td><td>length of time period slices</td></tr></table>

# S4 Evaluation Metrics

Cumulative return (CR): This is a measure of the income generated by an investment portfolio over a specific period. Specifically, it includes the entire test period.

$$
\begin{array}{c}{o_t^{\mathrm{close}}\in \mathbb{R}^{|S|},\quad z_t' = z_t^{(2:|S| + 1)}\in \mathbb{R}^{|S|}}\\ {A_t = z_t'\cdot o_t^{\mathrm{close}} = \sum_{i = 1}^{|S|}z_t^{\prime (i)},o_t^{\mathrm{close}(i)},CR_t = \frac{A_t}{A_0} -1} \end{array} \tag{8}
$$

Table 6: Results on the CSI dataset with more recent test data. The test set spans from 2022-05-01 to 2024-05-01, following the offline evaluation setup.  

<table><tr><td>Method</td><td>CR↑</td><td>AR↑</td><td>SR↑</td><td>MDD↓</td></tr><tr><td>Market benchmark</td><td>-0.08</td><td>-0.04</td><td>0.02</td><td>0.32</td></tr><tr><td>SARL</td><td>-0.13</td><td>-0.07</td><td>-0.07</td><td>0.51</td></tr><tr><td>FinRL-SAC</td><td>0.04</td><td>0.01</td><td>0.03</td><td>0.49</td></tr><tr><td>StockFormer</td><td>0.21</td><td>0.10</td><td>0.46</td><td>0.45</td></tr><tr><td>MetaTrader</td><td>0.32</td><td>0.15</td><td>0.76</td><td>0.44</td></tr></table>

where  $A_{t}$  represents the total asset value at time  $t$  and  $A_0$  denotes the initial asset value. In practice, we assume all transactions are executed at the closing price  $o_t^{\mathrm{close}}$

Annualized return (AR): This is a measure of the investment growth over one year.

$$
AR = CR_t^{\frac{d}{t}} - 1, \tag{9}
$$

where  $d$  represents the total number of trading days in one year.

Sharpe ratio (SR): This is a metric in finance to measure the performance of an investment compared to a risk- free asset.

$$
SR = \frac{CR - R_f}{\sigma_p}, \tag{10}
$$

where  $R_{f}$  is the risk- free rate of return.  $\sigma_{p}$  is the standard deviation of the portfolio's excess return. For our experiments, the risk- free rate used in the analysis is set to 0.

Maximum drawdown (MDD): This is a measure of the maximum observed loss from a peak to a trough of a portfolio's value before a new peak is achieved. It quantifies the largest decline during a specific period and is expressed as a percentage of the peak value.

$$
\mathrm{MDD} = \max_{t\in [1,H]}\left(\frac{P_{\mathrm{peak},t} - P_t}{P_{\mathrm{peak},t}}\right), \tag{11}
$$

where  $P_{\mathrm{peak},t}$  represents the maximum portfolio value observed up to time  $t$  , and  $P_{t}$  denotes the portfolio value at time  $t$  .  $H$  is the total number of time steps in the evaluation period. MDD provides insight into the portfolio's risk by showing the potential downside during periods of significant market declines. In practical applications, MDD helps assess the stability and robustness of an investment strategy.

# S5 Additional Results

Evaluation on more recent data. We used data up to 2022 to ensure a fair comparison with StockFormer [Gao et al., 2023a], which follows the same training and testing period division. Moreover, we conduct additional experiments using data beyond 2022. In this experiment, we do not extend the training set range but directly test on the CSI dataset spanning from 2022- 05- 01 to 2024- 05- 01. As shown in Table 6, during this period, the overall market is weaker than that in the original test set before 2022. Consequently, the annualized returns of all methods are reduced. Nonetheless, our method consistently outperforms all baselines, highlighting its potential for profitability even under more challenging market conditions.

Effectiveness of finetuning of RL- based trading models. In practical RL- for- finance tasks, the naive fine- tuning approach often fails to enhance model performance on test data. This is primarily due to overfitting to specific data patterns when finetuning on more recent data. This is precisely why we propose the bilevel optimization approach for the RL method. Theoretically, the bilevel optimization scheme can significantly enhance the model's generalizability to new data. Similar approaches, known

Table 7: Analyses of finetuning the models on last-year training data. The results are averaged over 3 random training seeds. Compared with previous methods, our bilevel RL approach facilitates effective model finetuning, with a  $13.39\%$  performance gain (vs.  $0.81\%$  on StockFormer).  

<table><tr><td rowspan="2">Method</td><td colspan="3">w/o Finetuning</td><td colspan="3">w/ Finetuning</td></tr><tr><td>CR↑</td><td>AR↑</td><td>SR↑</td><td>CR↑</td><td>AR↑</td><td>SR↑</td></tr><tr><td>SARL</td><td>1.03±0.13</td><td>0.27±0.03</td><td>0.89±0.08</td><td>1.06±0.14</td><td>0.27±0.03</td><td>0.98±0.08</td></tr><tr><td>CQL</td><td>0.69±0.05</td><td>0.19±0.01</td><td>0.83±0.05</td><td>0.64±0.07</td><td>0.18±0.02</td><td>0.75±0.05</td></tr><tr><td>IQL</td><td>0.96±0.10</td><td>0.25±0.02</td><td>0.89±0.04</td><td>1.02±0.10</td><td>0.26±0.02</td><td>0.94±0.06</td></tr><tr><td>FinRL-SAC</td><td>0.80±0.07</td><td>0.22±0.02</td><td>0.82±0.05</td><td>0.83±0.05</td><td>0.22±0.01</td><td>0.92±0.04</td></tr><tr><td>FinRL-DDPG</td><td>0.63±0.13</td><td>0.18±0.04</td><td>0.77±0.09</td><td>0.58±0.15</td><td>0.16±0.04</td><td>0.73±0.12</td></tr><tr><td>StockFormer</td><td>1.23±0.09</td><td>0.31±0.02</td><td>1.18±0.05</td><td>1.24±0.10</td><td>0.31±0.02</td><td>1.26±0.06</td></tr><tr><td>MetaTrader</td><td>1.27±0.08</td><td>0.31±0.02</td><td>1.21±0.05</td><td>1.44±0.07</td><td>0.35±0.02</td><td>1.35±0.08</td></tr></table>

as model- agnostic meta- learning (MAML) [Finn et al., 2017], have been widely adopted to improve finetuning results in few- shot learning scenarios. Intuitively, it aims to find well- performed parameter initialization that can be quickly adapted to a new related task using only a few data and a few gradient steps. We compare the performance of different RL methods with and without finetuning on last- year recent data, using the same configuration as offline evaluation. We present the CR, PR, SR, and MDD on the CSI dataset in Table 7. The results are averaged over 3 random training seeds. Notably in the cumulative return metric, our bilevel optimization approach significantly improves the finetuning results (by  $+13.39\%$ ), while the previous RL approaches do not support such effective model finetuning (e.g., by  $+0.81\%$  for StockFormer).

Additional gradient steps for the baselines. As our model is optimized for  $30k$  steps during OOD policy learning and for  $5k$  steps during model finetuning, we increase the training steps of other compared models to  $35k \times 2$  and  $35k \times 2 \times K$  steps respectively, where  $K$  corresponds to the number of sampled subsets in each bilevel optimization step in our method. We can see from Table 8 that after convergence, continuing training does not yield significant improvements for the baseline models.

Table 8: Experiments with a larger number of optimization steps. The results are obtained on the CSI dataset under the offline evaluation setup over 3 random seeds.  

<table><tr><td>Method</td><td>Optim. steps</td><td>CR↑</td><td>AR↑</td><td>SR↑</td><td>Optim. steps</td><td>CR↑</td><td>AR↑</td><td>SR↑</td></tr><tr><td>SARL</td><td>35k × 2</td><td>1.01</td><td>0.26</td><td>0.95</td><td>35k × 64</td><td>1.04</td><td>0.27</td><td>0.99</td></tr><tr><td>FinRL-SAC</td><td>35k × 2</td><td>0.86</td><td>0.23</td><td>0.94</td><td>35k × 64</td><td>0.89</td><td>0.24</td><td>0.93</td></tr><tr><td>FinRL-DDPG</td><td>35k × 2</td><td>0.63</td><td>0.18</td><td>0.77</td><td>35k × 64</td><td>0.65</td><td>0.18</td><td>0.79</td></tr><tr><td>StockFormer</td><td>35k × 2</td><td>1.26</td><td>0.31</td><td>1.21</td><td>35k × 64</td><td>1.28</td><td>0.32</td><td>1.24</td></tr><tr><td>MetaTrader</td><td>35k</td><td>1.44</td><td>0.35</td><td>1.35</td><td>-</td><td>-</td><td>-</td><td>-</td></tr></table>