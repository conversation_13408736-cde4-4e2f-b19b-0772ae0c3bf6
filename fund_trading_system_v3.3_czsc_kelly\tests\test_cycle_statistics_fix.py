"""
测试周期统计显示修复
"""

import sys
import os
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from system.enhanced_trading_system import EnhancedFundTradingSystemV3
from core.data_structures import DimensionEvaluationResult


def test_cycle_statistics_with_risk_control():
    """测试包含风控干预的周期统计"""
    print("测试包含风控干预的周期统计")
    print("=" * 60)
    
    # 创建交易系统
    trading_system = EnhancedFundTradingSystemV3("测试系统")
    coordinator = trading_system.coordinator
    
    # 模拟一个包含风控干预的交易周期
    fund_codes = ['601398', '513500', '159561']
    
    with patch.object(coordinator.technical_agent, 'process') as mock_tech, \
         patch.object(coordinator.gua_agent, 'process') as mock_gua, \
         patch.object(coordinator.flow_agent, 'process') as mock_flow, \
         patch.object(coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
        
        def mock_tech_side_effect(fund_code):
            # 601398 和 159561 返回不符合风控的指标，513500 符合风控
            if fund_code in ['601398', '159561']:
                return {
                    'buy_signal': True,
                    'technical_indicators': {
                        'bb_position': 0.9,  # 不符合风控
                        'rsi': 75,           # 不符合风控
                        'volume_ratio': 0.8, # 不符合风控
                        'close': 100,
                        'bb_upper': 105,
                        'bb_middle': 100,
                        'bb_lower': 95
                    },
                    'confidence': 0.8
                }
            else:  # 513500
                return {
                    'buy_signal': True,
                    'technical_indicators': {
                        'bb_position': 0.01,  # 符合风控
                        'rsi': 40,            # 符合风控
                        'volume_ratio': 1.5,  # 符合风控
                        'close': 100,
                        'bb_upper': 105,
                        'bb_middle': 100,
                        'bb_lower': 95
                    },
                    'confidence': 0.8
                }
        
        def mock_enhanced_side_effect(data):
            fund_code = data.get('fund_code', 'unknown')
            if fund_code in ['601398', '159561']:
                # 这两个基金原始决策是buy，但会被风控改为hold
                return {
                    'decision': 'buy',
                    'confidence': 0.75,
                    'weighted_score': 0.612,
                    'dimension_evaluations': {
                        '趋势': DimensionEvaluationResult('趋势', 'up', 0.39, 0.7, [], 'good'),
                        '波动性': DimensionEvaluationResult('波动性', 'medium', 0.67, 0.6, [], 'fair'),
                        '流动性': DimensionEvaluationResult('流动性', 'good', 1.00, 0.9, [], 'good')
                    }
                }
            else:  # 513500
                # 这个基金原始决策是buy，风控也会通过
                return {
                    'decision': 'buy',
                    'confidence': 0.82,
                    'weighted_score': 0.745,
                    'dimension_evaluations': {
                        '趋势': DimensionEvaluationResult('趋势', 'up', 0.75, 0.8, [], 'good'),
                        '波动性': DimensionEvaluationResult('波动性', 'low', 0.25, 0.8, [], 'good'),
                        '流动性': DimensionEvaluationResult('流动性', 'good', 0.95, 0.9, [], 'good')
                    }
                }
        
        # 设置mock函数
        mock_tech.side_effect = mock_tech_side_effect
        mock_gua.return_value = {'gua_score': 0.6}
        mock_flow.return_value = {'high_liquidity': True}
        mock_enhanced.side_effect = mock_enhanced_side_effect
        
        # 运行交易周期
        print("运行模拟交易周期...")

        # 手动模拟交易周期，因为run_trading_cycle_v3不接受参数
        results = []
        for fund_code in fund_codes:
            try:
                analysis_result = trading_system.analyze_fund_v3(fund_code)
                results.append({
                    'fund_code': fund_code,
                    'analysis_result': analysis_result,
                    'success': True
                })
            except Exception as e:
                results.append({
                    'fund_code': fund_code,
                    'error': str(e),
                    'success': False
                })

        # 手动调用统计显示
        trading_system._display_cycle_summary(results, len(fund_codes))
        
        print("\n详细分析结果:")
        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})
                fund_code = analysis_result.get('fund_code', 'unknown')
                original_decision = analysis_result.get('enhanced_decision', {}).get('decision', 'hold')
                final_decision = analysis_result.get('final_decision', original_decision)
                
                print(f"  {fund_code}: {original_decision} -> {final_decision} {'(风控干预)' if original_decision != final_decision else '(无干预)'}")


def test_cycle_statistics_no_risk_control():
    """测试无风控干预的周期统计"""
    print("\n\n测试无风控干预的周期统计")
    print("=" * 60)
    
    # 创建交易系统
    trading_system = EnhancedFundTradingSystemV3("测试系统")
    coordinator = trading_system.coordinator
    
    # 模拟一个无风控干预的交易周期
    fund_codes = ['513500', '513080']
    
    with patch.object(coordinator.technical_agent, 'process') as mock_tech, \
         patch.object(coordinator.gua_agent, 'process') as mock_gua, \
         patch.object(coordinator.flow_agent, 'process') as mock_flow, \
         patch.object(coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
        
        # 设置所有基金都符合风控条件
        mock_tech.return_value = {
            'buy_signal': True,
            'technical_indicators': {
                'bb_position': 0.01,  # 符合风控
                'rsi': 40,            # 符合风控
                'volume_ratio': 1.5,  # 符合风控
                'close': 100,
                'bb_upper': 105,
                'bb_middle': 100,
                'bb_lower': 95
            },
            'confidence': 0.8
        }
        
        mock_gua.return_value = {'gua_score': 0.7}
        mock_flow.return_value = {'high_liquidity': True}
        
        # 一个buy，一个hold
        def mock_enhanced_side_effect(data):
            fund_code = data.get('fund_code', 'unknown')
            if fund_code == '513500':
                return {
                    'decision': 'buy',
                    'confidence': 0.82,
                    'weighted_score': 0.745,
                    'dimension_evaluations': {}
                }
            else:  # 513080
                return {
                    'decision': 'hold',
                    'confidence': 0.45,
                    'weighted_score': 0.320,
                    'dimension_evaluations': {}
                }
        
        mock_enhanced.side_effect = mock_enhanced_side_effect
        
        # 运行交易周期
        print("运行模拟交易周期...")

        # 手动模拟交易周期
        results = []
        for fund_code in fund_codes:
            try:
                analysis_result = trading_system.analyze_fund_v3(fund_code)
                results.append({
                    'fund_code': fund_code,
                    'analysis_result': analysis_result,
                    'success': True
                })
            except Exception as e:
                results.append({
                    'fund_code': fund_code,
                    'error': str(e),
                    'success': False
                })

        # 手动调用统计显示
        trading_system._display_cycle_summary(results, len(fund_codes))
        
        print("\n详细分析结果:")
        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})
                fund_code = analysis_result.get('fund_code', 'unknown')
                original_decision = analysis_result.get('enhanced_decision', {}).get('decision', 'hold')
                final_decision = analysis_result.get('final_decision', original_decision)
                
                print(f"  {fund_code}: {original_decision} -> {final_decision} {'(风控干预)' if original_decision != final_decision else '(无干预)'}")


def main():
    """主测试函数"""
    print("🎨 周期统计显示修复测试")
    print("=" * 60)
    print("本测试将展示以下场景的周期统计:")
    print("1. 有风控干预的情况 - 显示风控干预次数")
    print("2. 无风控干预的情况 - 正常显示")
    print("=" * 60)
    
    try:
        test_cycle_statistics_with_risk_control()
        test_cycle_statistics_no_risk_control()
        
        print("\n" + "=" * 60)
        print("🎉 周期统计显示修复测试完成！")
        print("=" * 60)
        print("✅ 新的统计特性:")
        print("   ✅ 显示风控后的最终决策分布")
        print("   ✅ 显示风控干预次数")
        print("   ✅ 区分有/无风控干预的显示")
        print("   ✅ 准确反映实际执行的决策")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
