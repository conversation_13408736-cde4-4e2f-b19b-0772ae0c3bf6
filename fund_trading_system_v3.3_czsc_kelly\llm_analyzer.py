"""
LLM市场分析模块
集成多种LLM服务，提供市场分析和交易建议
"""

import json
import re
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
import numpy as np

@dataclass
class LLMAnalysisResult:
    """LLM分析结果"""
    direction: str  # 'buy', 'sell', 'hold'
    entry_price_range: List[float]
    stop_loss: float
    take_profit: float
    win_probability: float
    risk_reward_ratio: float
    suggested_position: float
    confidence: float
    reasoning: str
    market_sentiment: str
    key_factors: List[str]

class LLMMarketAnalyzer:
    """LLM市场分析器"""
    
    def __init__(self, model_type: str = "openai", api_key: Optional[str] = None):
        self.model_type = model_type
        self.api_key = api_key
        self.client = self._init_client()
        
    def _init_client(self):
        """初始化LLM客户端"""
        if self.model_type == "openai":
            return self._init_openai_client()
        elif self.model_type == "claude":
            return self._init_claude_client()
        elif self.model_type == "local":
            return self._init_local_client()
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def _init_openai_client(self):
        """初始化OpenAI客户端"""
        # 这里应该初始化真实的OpenAI客户端
        # 为了演示，返回None
        return None
    
    def _init_claude_client(self):
        """初始化Claude客户端"""
        # 这里应该初始化真实的Claude客户端
        # 为了演示，返回None
        return None
    
    def _init_local_client(self):
        """初始化本地模型客户端"""
        # 这里应该初始化本地模型客户端
        # 为了演示，返回None
        return None
    
    def analyze_market_narrative(self, market_data: Dict) -> LLMAnalysisResult:
        """分析市场叙事和情绪"""
        
        # 构建分析提示词
        prompt = self._build_comprehensive_prompt(market_data)
        
        # 调用LLM分析
        response = self._call_llm(prompt)
        
        # 解析响应
        return self._parse_analysis_response(response)
    
    def _build_comprehensive_prompt(self, market_data: Dict) -> str:
        """构建综合分析提示词"""
        
        # 提取关键市场数据
        current_price = market_data.get('current_price', 0)
        volume = market_data.get('volume', 0)
        volatility = market_data.get('volatility', 0)
        technical_indicators = market_data.get('technical_indicators', {})
        czsc_signals = market_data.get('czsc_signals', {})
        news_sentiment = market_data.get('news_sentiment', {})
        
        prompt = f"""
你是一位资深的量化交易分析师，具有丰富的市场分析经验。请基于以下信息进行深度分析：

## 市场数据
- 当前价格: {current_price}
- 成交量: {volume:,}
- 波动率: {volatility:.2%}

## 技术指标
{self._format_technical_indicators(technical_indicators)}

## CZSC缠论分析
{self._format_czsc_signals(czsc_signals)}

## 市场情绪
{self._format_news_sentiment(news_sentiment)}

## 分析要求
请从以下维度进行专业分析：

### 1. 市场趋势判断
- 主要趋势方向（上涨/下跌/震荡）
- 趋势强度评估
- 关键支撑阻力位

### 2. 交易机会识别
- 最佳入场时机
- 具体价位区间
- 风险控制点位

### 3. 概率评估
- 交易成功概率（基于历史模式和当前条件）
- 预期收益风险比
- 市场不确定性因素

### 4. 仓位建议
- 基于风险收益比的仓位建议
- 考虑市场波动的仓位调整
- 分批建仓策略

### 5. 风险管理
- 主要风险因素识别
- 止损策略建议
- 应急预案

请以以下JSON格式返回分析结果：
```json
{{
    "direction": "buy/sell/hold",
    "entry_price_range": [最低价, 最高价],
    "stop_loss": 止损价位,
    "take_profit": 目标价位,
    "win_probability": 胜率(0-1),
    "risk_reward_ratio": 风险收益比,
    "suggested_position": 建议仓位(0-1),
    "confidence": 分析置信度(0-1),
    "reasoning": "详细分析推理过程",
    "market_sentiment": "市场情绪描述",
    "key_factors": ["关键因素1", "关键因素2", "关键因素3"],
    "risk_factors": ["风险因素1", "风险因素2"],
    "opportunity_factors": ["机会因素1", "机会因素2"]
}}
```

注意：
1. 所有数值必须基于客观分析，避免过度乐观或悲观
2. 胜率评估要考虑历史成功率和当前市场条件
3. 仓位建议要符合风险管理原则
4. 推理过程要逻辑清晰，有理有据
"""
        
        return prompt
    
    def _format_technical_indicators(self, indicators: Dict) -> str:
        """格式化技术指标信息"""
        if not indicators:
            return "- 技术指标数据暂无"
        
        formatted = []
        for key, value in indicators.items():
            if isinstance(value, (int, float)):
                formatted.append(f"- {key}: {value:.4f}")
            else:
                formatted.append(f"- {key}: {value}")
        
        return "\n".join(formatted)
    
    def _format_czsc_signals(self, signals: Dict) -> str:
        """格式化CZSC信号信息"""
        if not signals:
            return "- CZSC信号暂无"
        
        formatted = []
        for key, value in signals.items():
            formatted.append(f"- {key}: {value}")
        
        return "\n".join(formatted)
    
    def _format_news_sentiment(self, sentiment: Dict) -> str:
        """格式化新闻情绪信息"""
        if not sentiment:
            return "- 新闻情绪数据暂无"
        
        return f"""
- 整体情绪: {sentiment.get('overall', 'neutral')}
- 正面新闻比例: {sentiment.get('positive_ratio', 0):.1%}
- 负面新闻比例: {sentiment.get('negative_ratio', 0):.1%}
- 关键新闻: {', '.join(sentiment.get('key_news', []))}
"""
    
    def _call_llm(self, prompt: str) -> str:
        """调用LLM进行分析"""
        if self.model_type == "openai":
            return self._call_openai(prompt)
        elif self.model_type == "claude":
            return self._call_claude(prompt)
        elif self.model_type == "local":
            return self._call_local_model(prompt)
        else:
            # 模拟响应用于测试
            return self._mock_llm_response()
    
    def _call_openai(self, prompt: str) -> str:
        """调用OpenAI API"""
        # 这里应该实现真实的OpenAI API调用
        # 为了演示，返回模拟响应
        return self._mock_llm_response()
    
    def _call_claude(self, prompt: str) -> str:
        """调用Claude API"""
        # 这里应该实现真实的Claude API调用
        # 为了演示，返回模拟响应
        return self._mock_llm_response()
    
    def _call_local_model(self, prompt: str) -> str:
        """调用本地模型"""
        # 这里应该实现本地模型调用
        # 为了演示，返回模拟响应
        return self._mock_llm_response()
    
    def _parse_analysis_response(self, response: str) -> LLMAnalysisResult:
        """解析LLM分析响应"""
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有找到代码块，尝试直接解析
                json_str = response
            
            data = json.loads(json_str)
            
            return LLMAnalysisResult(
                direction=data.get('direction', 'hold'),
                entry_price_range=data.get('entry_price_range', [0, 0]),
                stop_loss=data.get('stop_loss', 0),
                take_profit=data.get('take_profit', 0),
                win_probability=data.get('win_probability', 0.5),
                risk_reward_ratio=data.get('risk_reward_ratio', 1.0),
                suggested_position=data.get('suggested_position', 0.1),
                confidence=data.get('confidence', 0.5),
                reasoning=data.get('reasoning', ''),
                market_sentiment=data.get('market_sentiment', 'neutral'),
                key_factors=data.get('key_factors', [])
            )
            
        except (json.JSONDecodeError, KeyError) as e:
            print(f"解析LLM响应失败: {e}")
            return self._create_default_result()
    
    def _create_default_result(self) -> LLMAnalysisResult:
        """创建默认分析结果"""
        return LLMAnalysisResult(
            direction='hold',
            entry_price_range=[0, 0],
            stop_loss=0,
            take_profit=0,
            win_probability=0.5,
            risk_reward_ratio=1.0,
            suggested_position=0.1,
            confidence=0.3,
            reasoning='LLM分析失败，使用默认保守策略',
            market_sentiment='neutral',
            key_factors=['数据不足']
        )
    
    def _mock_llm_response(self) -> str:
        """模拟LLM响应（用于测试）"""
        return """
```json
{
    "direction": "buy",
    "entry_price_range": [10.20, 10.50],
    "stop_loss": 9.80,
    "take_profit": 11.20,
    "win_probability": 0.65,
    "risk_reward_ratio": 2.0,
    "suggested_position": 0.15,
    "confidence": 0.75,
    "reasoning": "基于技术分析和市场情绪，当前呈现上涨趋势，支撑位稳固，建议适度做多",
    "market_sentiment": "偏乐观",
    "key_factors": ["技术突破", "成交量放大", "市场情绪改善"],
    "risk_factors": ["宏观不确定性", "行业政策风险"],
    "opportunity_factors": ["技术形态良好", "基本面支撑"]
}
```
"""

class EnhancedLLMAnalyzer(LLMMarketAnalyzer):
    """增强版LLM分析器，支持多轮对话和上下文记忆"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conversation_history = []
        self.market_memory = {}
    
    def analyze_with_context(self, market_data: Dict, previous_signals: List[Dict] = None) -> LLMAnalysisResult:
        """带上下文的市场分析"""
        
        # 构建包含历史信息的提示词
        prompt = self._build_contextual_prompt(market_data, previous_signals)
        
        # 调用LLM分析
        response = self._call_llm(prompt)
        
        # 更新对话历史
        self._update_conversation_history(prompt, response)
        
        # 解析响应
        return self._parse_analysis_response(response)
    
    def _build_contextual_prompt(self, market_data: Dict, previous_signals: List[Dict]) -> str:
        """构建包含上下文的提示词"""
        
        base_prompt = self._build_comprehensive_prompt(market_data)
        
        if previous_signals:
            context_info = self._format_previous_signals(previous_signals)
            contextual_prompt = f"""
{base_prompt}

## 历史交易信号
{context_info}

请结合历史信号的表现，调整当前的分析判断。特别注意：
1. 之前信号的准确性如何？
2. 市场环境是否发生了变化？
3. 当前策略是否需要调整？
"""
            return contextual_prompt
        
        return base_prompt
    
    def _format_previous_signals(self, signals: List[Dict]) -> str:
        """格式化历史信号信息"""
        formatted = []
        for i, signal in enumerate(signals[-5:], 1):  # 只显示最近5个信号
            formatted.append(f"""
信号{i}:
- 方向: {signal.get('direction', 'N/A')}
- 价位: {signal.get('entry_price', 'N/A')}
- 实际表现: {signal.get('actual_result', '待观察')}
- 准确性: {signal.get('accuracy', 'N/A')}
""")
        
        return "\n".join(formatted)
    
    def _update_conversation_history(self, prompt: str, response: str):
        """更新对话历史"""
        from datetime import datetime
        self.conversation_history.append({
            'timestamp': datetime.now(),
            'prompt': prompt[:500] + "..." if len(prompt) > 500 else prompt,
            'response': response[:500] + "..." if len(response) > 500 else response
        })
        
        # 保持历史记录在合理范围内
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]

# 使用示例
if __name__ == "__main__":
    # 初始化分析器
    analyzer = EnhancedLLMAnalyzer(model_type="local")  # 使用模拟模式
    
    # 模拟市场数据
    market_data = {
        'current_price': 10.50,
        'volume': 1000000,
        'volatility': 0.25,
        'technical_indicators': {
            'RSI': 65.5,
            'MACD': 0.15,
            'MA20': 10.20,
            'MA50': 9.80
        },
        'czsc_signals': {
            'trend_direction': '上涨',
            'signal_strength': 7,
            'support_level': 10.00,
            'resistance_level': 11.00
        },
        'news_sentiment': {
            'overall': 'positive',
            'positive_ratio': 0.65,
            'negative_ratio': 0.20,
            'key_news': ['业绩超预期', '行业政策利好']
        }
    }
    
    # 进行分析
    result = analyzer.analyze_market_narrative(market_data)
    
    print("=== LLM市场分析结果 ===")
    print(f"交易方向: {result.direction}")
    print(f"入场价位: {result.entry_price_range}")
    print(f"止损价位: {result.stop_loss}")
    print(f"目标价位: {result.take_profit}")
    print(f"胜率评估: {result.win_probability:.1%}")
    print(f"风险收益比: {result.risk_reward_ratio:.2f}")
    print(f"建议仓位: {result.suggested_position:.1%}")
    print(f"分析置信度: {result.confidence:.1%}")
    print(f"市场情绪: {result.market_sentiment}")
    print(f"关键因素: {', '.join(result.key_factors)}")
    print(f"分析推理: {result.reasoning}")