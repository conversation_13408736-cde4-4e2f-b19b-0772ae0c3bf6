"""
LLM + CZSC + 凯利公式集成系统运行示例
演示完整的交易决策流程
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# 导入系统组件
try:
    from integrated_trading_system import IntegratedTradingSystem, MarketData, create_sample_market_data
    from llm_analyzer import LLMMarketAnalyzer
    from kelly_position_manager import KellyFormulaCalculator
    from enhanced_czsc_analyzer import EnhancedCZSCAnalyzer
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有模块文件都在同一目录下")
    sys.exit(1)

def create_realistic_market_data(symbol: str, days: int = 60) -> MarketData:
    """创建更真实的市场数据"""
    
    # 设置随机种子以获得可重复的结果
    np.random.seed(hash(symbol) % 1000)
    
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    dates = pd.date_range(start_date, end_date, freq='D')
    
    # 生成更真实的价格走势
    base_price = np.random.uniform(8, 20)
    
    # 使用几何布朗运动生成价格
    dt = 1/252  # 日频率
    mu = np.random.uniform(-0.1, 0.15)  # 年化收益率
    sigma = np.random.uniform(0.15, 0.35)  # 年化波动率
    
    prices = [base_price]
    for i in range(len(dates) - 1):
        # 添加一些趋势和周期性
        trend_factor = 1 + 0.001 * np.sin(i * 0.1)  # 轻微的周期性趋势
        
        # 几何布朗运动
        dW = np.random.randn() * np.sqrt(dt)
        price_change = mu * dt + sigma * dW
        
        new_price = prices[-1] * np.exp(price_change) * trend_factor
        prices.append(max(new_price, 0.1))  # 确保价格为正
    
    # 生成OHLC数据
    opens = []
    highs = []
    lows = []
    closes = prices.copy()
    volumes = []
    
    for i, close_price in enumerate(closes):
        # 生成开盘价（基于前一日收盘价）
        if i == 0:
            open_price = close_price * (1 + np.random.randn() * 0.01)
        else:
            gap = np.random.randn() * 0.005  # 跳空
            open_price = closes[i-1] * (1 + gap)
        
        # 生成最高价和最低价
        intraday_range = abs(np.random.randn()) * 0.03  # 日内波动
        high_price = max(open_price, close_price) * (1 + intraday_range)
        low_price = min(open_price, close_price) * (1 - intraday_range)
        
        # 生成成交量（与价格变化相关）
        price_change = abs(close_price - open_price) / open_price if open_price > 0 else 0
        base_volume = np.random.randint(50000, 500000)
        volume = int(base_volume * (1 + price_change * 5))  # 价格变化大时成交量增加
        
        opens.append(open_price)
        highs.append(high_price)
        lows.append(low_price)
        volumes.append(volume)
    
    # 创建K线DataFrame
    klines = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates[:len(closes)])
    
    # 计算技术指标
    current_price = closes[-1]
    price_changes = np.diff(closes) / np.array(closes[:-1])
    volatility = np.std(price_changes) * np.sqrt(252)  # 年化波动率
    
    # RSI计算
    def calculate_rsi(prices, period=14):
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:]) if len(gains) >= period else np.mean(gains)
        avg_loss = np.mean(losses[-period:]) if len(losses) >= period else np.mean(losses)
        
        if avg_loss == 0:
            return 50
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    rsi = calculate_rsi(closes)
    
    # MACD计算（简化版）
    ema12 = pd.Series(closes).ewm(span=12).mean().iloc[-1]
    ema26 = pd.Series(closes).ewm(span=26).mean().iloc[-1]
    macd = ema12 - ema26
    
    # 移动平均线
    ma5 = np.mean(closes[-5:]) if len(closes) >= 5 else current_price
    ma10 = np.mean(closes[-10:]) if len(closes) >= 10 else current_price
    ma20 = np.mean(closes[-20:]) if len(closes) >= 20 else current_price
    
    # 生成新闻情绪（基于价格趋势）
    recent_trend = (closes[-1] - closes[-5]) / closes[-5] if len(closes) >= 5 else 0
    
    if recent_trend > 0.05:
        sentiment = 'positive'
        pos_ratio = 0.7
        neg_ratio = 0.1
        key_news = ['业绩超预期', '行业政策利好', '机构调研增加']
    elif recent_trend < -0.05:
        sentiment = 'negative'
        pos_ratio = 0.2
        neg_ratio = 0.6
        key_news = ['业绩不及预期', '行业面临挑战', '资金流出']
    else:
        sentiment = 'neutral'
        pos_ratio = 0.4
        neg_ratio = 0.3
        key_news = ['市场观望情绪', '横盘整理']
    
    return MarketData(
        symbol=symbol,
        klines=klines,
        current_price=current_price,
        volume=volumes[-1],
        volatility=volatility,
        technical_indicators={
            'RSI': rsi,
            'MACD': macd,
            'MA5': ma5,
            'MA10': ma10,
            'MA20': ma20,
            'price_trend': recent_trend
        },
        news_sentiment={
            'overall': sentiment,
            'positive_ratio': pos_ratio,
            'negative_ratio': neg_ratio,
            'key_news': key_news
        },
        timestamp=datetime.now()
    )

def run_comprehensive_example():
    """运行综合示例"""
    
    print("=" * 80)
    print("🚀 LLM + CZSC + 凯利公式集成交易系统")
    print("=" * 80)
    
    # 1. 系统初始化
    print("\n📊 1. 系统初始化")
    print("-" * 40)
    
    config = {
        'llm_model': 'local',  # 使用模拟模式
        'max_position': 0.25,  # 单个标的最大仓位25%
        'kelly_fraction': 0.25,  # 使用1/4凯利策略
        'max_total_position': 0.80,  # 总仓位上限80%
        'confidence_threshold': 0.4,  # 置信度阈值40%
        'min_kelly_position': 0.02,  # 最小仓位2%
        'risk_free_rate': 0.03
    }
    
    trading_system = IntegratedTradingSystem(config)
    print("✅ 交易系统初始化完成")
    print(f"   - 最大单仓位: {config['max_position']:.0%}")
    print(f"   - 凯利分数: {config['kelly_fraction']:.0%}")
    print(f"   - 总仓位上限: {config['max_total_position']:.0%}")
    print(f"   - 置信度阈值: {config['confidence_threshold']:.0%}")
    
    # 2. 创建测试数据
    print(f"\n📈 2. 生成市场数据")
    print("-" * 40)
    
    test_symbols = ['000001.SZ', '000002.SZ', '000858.SZ', '600036.SH', '600519.SH']
    market_data_list = []
    
    for symbol in test_symbols:
        market_data = create_realistic_market_data(symbol, days=60)
        market_data_list.append(market_data)
        
        print(f"✅ {symbol}: 当前价格 {market_data.current_price:.2f}, "
              f"波动率 {market_data.volatility:.1%}, "
              f"情绪 {market_data.news_sentiment['overall']}")
    
    # 3. 单标的详细分析示例
    print(f"\n🔍 3. 单标的详细分析 ({test_symbols[0]})")
    print("-" * 40)
    
    sample_data = market_data_list[0]
    signal = trading_system.analyze_single_symbol(sample_data)
    
    print(f"📊 分析结果：")
    print(f"   交易信号: {signal.signal_type.upper()}")
    print(f"   目标价位: {signal.target_price:.2f}")
    print(f"   止损价位: {signal.stop_loss:.2f}")
    print(f"   止盈价位: {signal.take_profit:.2f}")
    print(f"   凯利仓位: {signal.kelly_position:.2%}")
    print(f"   置信度: {signal.confidence:.1%}")
    print(f"   预期胜率: {signal.win_probability:.1%}")
    print(f"   风险收益比: {signal.risk_reward_ratio:.2f}")
    print(f"   CZSC强度: {signal.czsc_level}/5")
    
    print(f"\n💡 分析推理:")
    reasoning_lines = signal.llm_reasoning.split('；')
    for line in reasoning_lines:
        print(f"   • {line}")
    
    # 4. 投资组合分析
    print(f"\n📋 4. 投资组合分析 ({len(test_symbols)}个标的)")
    print("-" * 40)
    
    portfolio_signals = trading_system.analyze_portfolio(market_data_list)
    
    # 统计结果
    buy_signals = [s for s in portfolio_signals.values() if s.signal_type == 'buy']
    sell_signals = [s for s in portfolio_signals.values() if s.signal_type == 'sell']
    hold_signals = [s for s in portfolio_signals.values() if s.signal_type == 'hold']
    
    total_position = sum(s.kelly_position for s in portfolio_signals.values())
    active_positions = len(buy_signals) + len(sell_signals)
    
    print(f"📈 组合概览:")
    print(f"   总标的数: {len(portfolio_signals)}")
    print(f"   买入信号: {len(buy_signals)}")
    print(f"   卖出信号: {len(sell_signals)}")
    print(f"   观望信号: {len(hold_signals)}")
    print(f"   活跃仓位: {active_positions}")
    print(f"   总仓位: {total_position:.1%}")
    print(f"   仓位利用率: {total_position/config['max_total_position']:.1%}")
    
    # 5. 详细信号表
    print(f"\n📊 5. 详细信号表")
    print("-" * 40)
    
    print(f"{'标的代码':<12} {'信号':<6} {'仓位':<8} {'置信度':<8} {'胜率':<8} {'风险收益比':<10}")
    print("-" * 60)
    
    for symbol, signal in portfolio_signals.items():
        kelly_pos_str = f"{signal.kelly_position:.1%}"
        confidence_str = f"{signal.confidence:.1%}"
        win_prob_str = f"{signal.win_probability:.1%}"
        risk_reward_str = f"{signal.risk_reward_ratio:.2f}"
        
        print(f"{symbol:<12} {signal.signal_type.upper():<6} "
              f"{kelly_pos_str:<8} {confidence_str:<8} "
              f"{win_prob_str:<8} {risk_reward_str:<10}")
    
    # 6. 风险分析
    print(f"\n⚠️  6. 风险分析")
    print("-" * 40)
    
    if active_positions > 0:
        active_signals = [s for s in portfolio_signals.values() if s.signal_type != 'hold']
        avg_confidence = np.mean([s.confidence for s in active_signals])
        avg_win_prob = np.mean([s.win_probability for s in active_signals])
        avg_risk_reward = np.mean([s.risk_reward_ratio for s in active_signals])
        
        print(f"📊 活跃仓位风险指标:")
        print(f"   平均置信度: {avg_confidence:.1%}")
        print(f"   平均胜率: {avg_win_prob:.1%}")
        print(f"   平均风险收益比: {avg_risk_reward:.2f}")
        
        # 风险等级评估
        if avg_confidence >= 0.7 and avg_win_prob >= 0.6:
            risk_level = "低风险"
            risk_color = "🟢"
        elif avg_confidence >= 0.5 and avg_win_prob >= 0.5:
            risk_level = "中等风险"
            risk_color = "🟡"
        else:
            risk_level = "高风险"
            risk_color = "🔴"
        
        print(f"   整体风险等级: {risk_color} {risk_level}")
        
        # 仓位集中度分析
        max_position = max(s.kelly_position for s in active_signals)
        position_concentration = max_position / total_position if total_position > 0 else 0
        
        print(f"   最大单仓位: {max_position:.1%}")
        print(f"   仓位集中度: {position_concentration:.1%}")
        
        if position_concentration > 0.4:
            print(f"   ⚠️  警告: 仓位过于集中，建议分散投资")
    else:
        print("📊 当前无活跃仓位，风险较低")
    
    # 7. 操作建议
    print(f"\n💼 7. 操作建议")
    print("-" * 40)
    
    if buy_signals:
        print("🔵 买入建议:")
        for signal in sorted(buy_signals, key=lambda x: x.kelly_position, reverse=True):
            print(f"   • {signal.symbol}: 建议仓位 {signal.kelly_position:.1%}, "
                  f"目标价 {signal.target_price:.2f}, 止损 {signal.stop_loss:.2f}")
    
    if sell_signals:
        print("🔴 卖出建议:")
        for signal in sorted(sell_signals, key=lambda x: x.kelly_position, reverse=True):
            print(f"   • {signal.symbol}: 建议仓位 {signal.kelly_position:.1%}, "
                  f"目标价 {signal.target_price:.2f}, 止损 {signal.stop_loss:.2f}")
    
    if hold_signals:
        print("⚪ 观望标的:")
        for signal in hold_signals:
            print(f"   • {signal.symbol}: 暂不操作 (置信度: {signal.confidence:.1%})")
    
    # 8. 系统性能评估
    print(f"\n⚡ 8. 系统性能评估")
    print("-" * 40)
    
    # 计算一些性能指标
    signal_distribution = {
        'buy': len(buy_signals),
        'sell': len(sell_signals),
        'hold': len(hold_signals)
    }
    
    decision_rate = (len(buy_signals) + len(sell_signals)) / len(portfolio_signals)
    
    print(f"📊 决策效率:")
    print(f"   信号分布: 买入{len(buy_signals)}个, 卖出{len(sell_signals)}个, 观望{len(hold_signals)}个")
    print(f"   决策率: {decision_rate:.1%} (非观望信号比例)")
    print(f"   仓位效率: {total_position/config['max_total_position']:.1%} (仓位利用率)")
    
    if total_position > 0:
        avg_position_size = total_position / active_positions
        print(f"   平均仓位: {avg_position_size:.1%}")
    
    print(f"\n🎯 系统建议:")
    if decision_rate < 0.3:
        print("   • 当前市场机会较少，建议耐心等待")
    elif decision_rate > 0.7:
        print("   • 当前市场机会较多，注意风险控制")
    else:
        print("   • 当前市场机会适中，可适度参与")
    
    if total_position < config['max_total_position'] * 0.3:
        print("   • 总仓位较低，可考虑增加配置")
    elif total_position > config['max_total_position'] * 0.8:
        print("   • 总仓位较高，注意风险管理")
    
    print("\n" + "=" * 80)
    print("✅ 分析完成！以上建议仅供参考，投资有风险，决策需谨慎。")
    print("=" * 80)

if __name__ == "__main__":
    try:
        run_comprehensive_example()
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()