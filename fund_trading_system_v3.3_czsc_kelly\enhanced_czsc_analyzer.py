"""
增强版CZSC分析模块
基于缠中说禅理论的技术分析，提供精确的买卖点判断
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import talib

class TrendDirection(Enum):
    """趋势方向枚举"""
    UP = "上涨"
    DOWN = "下跌"
    SIDEWAYS = "震荡"
    UNKNOWN = "未知"

class SignalStrength(Enum):
    """信号强度枚举"""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5

@dataclass
class CZSCSignal:
    """CZSC信号数据结构"""
    signal_type: str  # 'buy', 'sell', 'hold'
    strength: SignalStrength
    confidence: float  # 0-1
    entry_price: float
    stop_loss: float
    take_profit: float
    trend_direction: TrendDirection
    support_levels: List[float]
    resistance_levels: List[float]
    key_points: List[Dict]  # 关键转折点
    reasoning: str

class EnhancedCZSCAnalyzer:
    """增强版CZSC分析器"""
    
    def __init__(self, 
                 min_k_num: int = 7,
                 max_k_num: int = 21,
                 bi_min_len: int = 5):
        """
        初始化CZSC分析器
        
        Args:
            min_k_num: 最小K线数量
            max_k_num: 最大K线数量  
            bi_min_len: 笔的最小长度
        """
        self.min_k_num = min_k_num
        self.max_k_num = max_k_num
        self.bi_min_len = bi_min_len
        
    def analyze(self, klines: pd.DataFrame) -> CZSCSignal:
        """
        综合CZSC分析
        
        Args:
            klines: K线数据，包含open, high, low, close, volume列
            
        Returns:
            CZSCSignal: CZSC分析结果
        """
        
        if len(klines) < self.min_k_num:
            return self._create_insufficient_data_signal()
        
        # 1. 基础数据处理
        processed_data = self._preprocess_klines(klines)
        
        # 2. 识别分型
        fenxing_points = self._identify_fenxing(processed_data)
        
        # 3. 构建笔
        bi_points = self._construct_bi(fenxing_points, processed_data)
        
        # 4. 构建线段
        xd_points = self._construct_xd(bi_points, processed_data)
        
        # 5. 趋势分析
        trend_analysis = self._analyze_trend(bi_points, xd_points, processed_data)
        
        # 6. 支撑阻力分析
        support_resistance = self._analyze_support_resistance(bi_points, xd_points, processed_data)
        
        # 7. 买卖点识别
        trading_points = self._identify_trading_points(bi_points, xd_points, trend_analysis)
        
        # 8. 生成综合信号
        return self._generate_comprehensive_signal(
            processed_data, trend_analysis, support_resistance, trading_points
        )
    
    def _preprocess_klines(self, klines: pd.DataFrame) -> pd.DataFrame:
        """预处理K线数据"""
        
        df = klines.copy()
        
        # 确保数据完整性
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"缺少必要列: {col}")
        
        # 计算技术指标
        df['ma5'] = talib.SMA(df['close'], timeperiod=5)
        df['ma10'] = talib.SMA(df['close'], timeperiod=10)
        df['ma20'] = talib.SMA(df['close'], timeperiod=20)
        
        # 计算MACD
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])
        
        # 计算RSI
        df['rsi'] = talib.RSI(df['close'])
        
        # 计算布林带
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'])
        
        # 计算成交量指标
        df['volume_ma'] = talib.SMA(df['volume'], timeperiod=10)
        
        return df
    
    def _identify_fenxing(self, df: pd.DataFrame) -> List[Dict]:
        """识别分型（顶分型和底分型）"""
        
        fenxing_points = []
        
        for i in range(1, len(df) - 1):
            current = df.iloc[i]
            prev = df.iloc[i-1]
            next_k = df.iloc[i+1]
            
            # 顶分型：当前K线的高点是三根K线中最高的
            if (current['high'] > prev['high'] and 
                current['high'] > next_k['high'] and
                current['low'] > prev['low'] and 
                current['low'] > next_k['low']):
                
                fenxing_points.append({
                    'index': i,
                    'type': 'top',
                    'price': current['high'],
                    'time': current.name if hasattr(current, 'name') else i,
                    'strength': self._calculate_fenxing_strength(df, i, 'top')
                })
            
            # 底分型：当前K线的低点是三根K线中最低的
            elif (current['low'] < prev['low'] and 
                  current['low'] < next_k['low'] and
                  current['high'] < prev['high'] and 
                  current['high'] < next_k['high']):
                
                fenxing_points.append({
                    'index': i,
                    'type': 'bottom',
                    'price': current['low'],
                    'time': current.name if hasattr(current, 'name') else i,
                    'strength': self._calculate_fenxing_strength(df, i, 'bottom')
                })
        
        return fenxing_points
    
    def _calculate_fenxing_strength(self, df: pd.DataFrame, index: int, fenxing_type: str) -> float:
        """计算分型强度"""
        
        # 基于成交量、价格幅度等因素计算分型强度
        current = df.iloc[index]
        
        # 成交量因子
        volume_factor = current['volume'] / current['volume_ma'] if current['volume_ma'] > 0 else 1
        
        # 价格幅度因子
        if fenxing_type == 'top':
            price_range = current['high'] - current['low']
        else:
            price_range = current['high'] - current['low']
        
        avg_range = df['high'].rolling(10).mean() - df['low'].rolling(10).mean()
        range_factor = price_range / avg_range.iloc[index] if avg_range.iloc[index] > 0 else 1
        
        # 综合强度
        strength = (volume_factor * 0.4 + range_factor * 0.6)
        return min(strength, 5.0)  # 限制在5以内
    
    def _construct_bi(self, fenxing_points: List[Dict], df: pd.DataFrame) -> List[Dict]:
        """构建笔"""
        
        if len(fenxing_points) < 2:
            return []
        
        bi_points = []
        
        for i in range(len(fenxing_points) - 1):
            start_point = fenxing_points[i]
            end_point = fenxing_points[i + 1]
            
            # 检查笔的有效性
            if self._is_valid_bi(start_point, end_point, df):
                bi_points.append({
                    'start_index': start_point['index'],
                    'end_index': end_point['index'],
                    'start_price': start_point['price'],
                    'end_price': end_point['price'],
                    'direction': 'up' if end_point['price'] > start_point['price'] else 'down',
                    'strength': (start_point['strength'] + end_point['strength']) / 2,
                    'length': abs(end_point['price'] - start_point['price']),
                    'duration': end_point['index'] - start_point['index']
                })
        
        return bi_points
    
    def _is_valid_bi(self, start_point: Dict, end_point: Dict, df: pd.DataFrame) -> bool:
        """检查笔的有效性"""
        
        # 检查长度
        if end_point['index'] - start_point['index'] < self.bi_min_len:
            return False
        
        # 检查价格幅度
        price_change = abs(end_point['price'] - start_point['price'])
        min_change = df['close'].std() * 0.5  # 最小变化幅度
        
        if price_change < min_change:
            return False
        
        return True
    
    def _construct_xd(self, bi_points: List[Dict], df: pd.DataFrame) -> List[Dict]:
        """构建线段"""
        
        if len(bi_points) < 3:
            return []
        
        xd_points = []
        
        # 简化的线段构建逻辑
        i = 0
        while i < len(bi_points) - 2:
            # 寻找三笔构成的线段
            bi1 = bi_points[i]
            bi2 = bi_points[i + 1]
            bi3 = bi_points[i + 2]
            
            # 检查是否构成有效线段
            if self._is_valid_xd(bi1, bi2, bi3):
                xd_points.append({
                    'start_index': bi1['start_index'],
                    'end_index': bi3['end_index'],
                    'start_price': bi1['start_price'],
                    'end_price': bi3['end_price'],
                    'direction': 'up' if bi3['end_price'] > bi1['start_price'] else 'down',
                    'bi_count': 3,
                    'strength': (bi1['strength'] + bi2['strength'] + bi3['strength']) / 3
                })
                i += 3
            else:
                i += 1
        
        return xd_points
    
    def _is_valid_xd(self, bi1: Dict, bi2: Dict, bi3: Dict) -> bool:
        """检查线段的有效性"""
        
        # 简化的线段判断逻辑
        # 三笔应该有明确的方向性
        if bi1['direction'] == bi3['direction']:
            return False
        
        # 中间笔应该有足够的回调
        if bi1['direction'] == 'up':
            return bi2['end_price'] < bi1['end_price'] * 0.95
        else:
            return bi2['end_price'] > bi1['end_price'] * 1.05
    
    def _analyze_trend(self, bi_points: List[Dict], xd_points: List[Dict], df: pd.DataFrame) -> Dict:
        """分析趋势"""
        
        if not bi_points:
            return {'direction': TrendDirection.UNKNOWN, 'strength': 0}
        
        # 基于最近的笔和线段判断趋势
        recent_bi = bi_points[-3:] if len(bi_points) >= 3 else bi_points
        
        # 统计上涨和下跌笔的数量
        up_count = sum(1 for bi in recent_bi if bi['direction'] == 'up')
        down_count = sum(1 for bi in recent_bi if bi['direction'] == 'down')
        
        # 判断趋势方向
        if up_count > down_count:
            direction = TrendDirection.UP
        elif down_count > up_count:
            direction = TrendDirection.DOWN
        else:
            direction = TrendDirection.SIDEWAYS
        
        # 计算趋势强度
        if recent_bi:
            avg_strength = sum(bi['strength'] for bi in recent_bi) / len(recent_bi)
            trend_strength = min(avg_strength, 5.0)
        else:
            trend_strength = 0
        
        # 结合技术指标确认趋势
        current_price = df['close'].iloc[-1]
        ma20 = df['ma20'].iloc[-1]
        
        if current_price > ma20 and direction == TrendDirection.UP:
            trend_strength *= 1.2
        elif current_price < ma20 and direction == TrendDirection.DOWN:
            trend_strength *= 1.2
        
        return {
            'direction': direction,
            'strength': trend_strength,
            'recent_bi_count': len(recent_bi),
            'up_bi_count': up_count,
            'down_bi_count': down_count
        }
    
    def _analyze_support_resistance(self, bi_points: List[Dict], xd_points: List[Dict], df: pd.DataFrame) -> Dict:
        """分析支撑阻力位"""
        
        support_levels = []
        resistance_levels = []
        
        # 从笔的端点提取支撑阻力位
        for bi in bi_points:
            if bi['direction'] == 'up':
                support_levels.append(bi['start_price'])
                resistance_levels.append(bi['end_price'])
            else:
                resistance_levels.append(bi['start_price'])
                support_levels.append(bi['end_price'])
        
        # 去重并排序
        support_levels = sorted(list(set(support_levels)))
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
        
        # 只保留最近的几个关键位
        current_price = df['close'].iloc[-1]
        
        # 筛选有效的支撑位（低于当前价格）
        valid_supports = [s for s in support_levels if s < current_price][-3:]
        
        # 筛选有效的阻力位（高于当前价格）
        valid_resistances = [r for r in resistance_levels if r > current_price][:3]
        
        return {
            'support_levels': valid_supports,
            'resistance_levels': valid_resistances,
            'nearest_support': valid_supports[-1] if valid_supports else None,
            'nearest_resistance': valid_resistances[0] if valid_resistances else None
        }
    
    def _identify_trading_points(self, bi_points: List[Dict], xd_points: List[Dict], trend_analysis: Dict) -> Dict:
        """识别买卖点"""
        
        if not bi_points:
            return {'buy_points': [], 'sell_points': []}
        
        buy_points = []
        sell_points = []
        
        # 基于笔的结构识别买卖点
        for i, bi in enumerate(bi_points):
            if i == 0:
                continue
            
            prev_bi = bi_points[i-1]
            
            # 买点：下跌笔结束，开始上涨
            if prev_bi['direction'] == 'down' and bi['direction'] == 'up':
                buy_points.append({
                    'index': bi['start_index'],
                    'price': bi['start_price'],
                    'type': 'bi_buy',
                    'strength': bi['strength'],
                    'confidence': self._calculate_point_confidence(bi, trend_analysis, 'buy')
                })
            
            # 卖点：上涨笔结束，开始下跌
            elif prev_bi['direction'] == 'up' and bi['direction'] == 'down':
                sell_points.append({
                    'index': bi['start_index'],
                    'price': bi['start_price'],
                    'type': 'bi_sell',
                    'strength': bi['strength'],
                    'confidence': self._calculate_point_confidence(bi, trend_analysis, 'sell')
                })
        
        return {
            'buy_points': buy_points,
            'sell_points': sell_points
        }
    
    def _calculate_point_confidence(self, bi: Dict, trend_analysis: Dict, point_type: str) -> float:
        """计算买卖点置信度"""
        
        base_confidence = 0.5
        
        # 基于笔的强度调整
        strength_factor = min(bi['strength'] / 3.0, 1.0)
        
        # 基于趋势一致性调整
        trend_direction = trend_analysis['direction']
        if point_type == 'buy' and trend_direction == TrendDirection.UP:
            trend_factor = 1.2
        elif point_type == 'sell' and trend_direction == TrendDirection.DOWN:
            trend_factor = 1.2
        else:
            trend_factor = 0.8
        
        confidence = base_confidence * strength_factor * trend_factor
        return min(confidence, 1.0)
    
    def _generate_comprehensive_signal(self, df: pd.DataFrame, trend_analysis: Dict, 
                                     support_resistance: Dict, trading_points: Dict) -> CZSCSignal:
        """生成综合信号"""
        
        current_price = df['close'].iloc[-1]
        
        # 确定信号类型
        recent_buy_points = [p for p in trading_points['buy_points'] if p['index'] >= len(df) - 5]
        recent_sell_points = [p for p in trading_points['sell_points'] if p['index'] >= len(df) - 5]
        
        if recent_buy_points and not recent_sell_points:
            signal_type = 'buy'
            entry_price = current_price
            stop_loss = support_resistance.get('nearest_support', current_price * 0.95)
            take_profit = support_resistance.get('nearest_resistance', current_price * 1.1)
            confidence = max([p['confidence'] for p in recent_buy_points])
        elif recent_sell_points and not recent_buy_points:
            signal_type = 'sell'
            entry_price = current_price
            stop_loss = support_resistance.get('nearest_resistance', current_price * 1.05)
            take_profit = support_resistance.get('nearest_support', current_price * 0.9)
            confidence = max([p['confidence'] for p in recent_sell_points])
        else:
            signal_type = 'hold'
            entry_price = current_price
            stop_loss = current_price * 0.95
            take_profit = current_price * 1.05
            confidence = 0.3
        
        # 确定信号强度
        trend_strength = trend_analysis.get('strength', 0)
        if trend_strength >= 4:
            strength = SignalStrength.VERY_STRONG
        elif trend_strength >= 3:
            strength = SignalStrength.STRONG
        elif trend_strength >= 2:
            strength = SignalStrength.MODERATE
        elif trend_strength >= 1:
            strength = SignalStrength.WEAK
        else:
            strength = SignalStrength.VERY_WEAK
        
        # 生成推理说明
        reasoning = self._generate_reasoning(trend_analysis, support_resistance, trading_points, signal_type)
        
        return CZSCSignal(
            signal_type=signal_type,
            strength=strength,
            confidence=confidence,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            trend_direction=trend_analysis['direction'],
            support_levels=support_resistance['support_levels'],
            resistance_levels=support_resistance['resistance_levels'],
            key_points=trading_points['buy_points'] + trading_points['sell_points'],
            reasoning=reasoning
        )
    
    def _generate_reasoning(self, trend_analysis: Dict, support_resistance: Dict, 
                          trading_points: Dict, signal_type: str) -> str:
        """生成分析推理"""
        
        reasoning_parts = []
        
        # 趋势分析
        trend_dir = trend_analysis['direction'].value
        trend_strength = trend_analysis.get('strength', 0)
        reasoning_parts.append(f"当前趋势：{trend_dir}，强度：{trend_strength:.1f}")
        
        # 支撑阻力分析
        if support_resistance['nearest_support']:
            reasoning_parts.append(f"最近支撑位：{support_resistance['nearest_support']:.2f}")
        if support_resistance['nearest_resistance']:
            reasoning_parts.append(f"最近阻力位：{support_resistance['nearest_resistance']:.2f}")
        
        # 买卖点分析
        buy_count = len(trading_points['buy_points'])
        sell_count = len(trading_points['sell_points'])
        reasoning_parts.append(f"识别到买点{buy_count}个，卖点{sell_count}个")
        
        # 信号结论
        if signal_type == 'buy':
            reasoning_parts.append("综合判断：建议买入")
        elif signal_type == 'sell':
            reasoning_parts.append("综合判断：建议卖出")
        else:
            reasoning_parts.append("综合判断：建议观望")
        
        return "；".join(reasoning_parts)
    
    def _create_insufficient_data_signal(self) -> CZSCSignal:
        """创建数据不足时的默认信号"""
        return CZSCSignal(
            signal_type='hold',
            strength=SignalStrength.VERY_WEAK,
            confidence=0.0,
            entry_price=0.0,
            stop_loss=0.0,
            take_profit=0.0,
            trend_direction=TrendDirection.UNKNOWN,
            support_levels=[],
            resistance_levels=[],
            key_points=[],
            reasoning="数据不足，无法进行CZSC分析"
        )

# 使用示例
if __name__ == "__main__":
    # 创建模拟K线数据
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    
    # 生成模拟价格数据
    base_price = 100
    price_changes = np.random.randn(100) * 0.02
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # 构建K线数据
    klines = pd.DataFrame({
        'open': [p * (1 + np.random.randn() * 0.005) for p in prices],
        'high': [p * (1 + abs(np.random.randn()) * 0.01) for p in prices],
        'low': [p * (1 - abs(np.random.randn()) * 0.01) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)
    
    # 确保OHLC数据的逻辑正确性
    for i in range(len(klines)):
        high = max(klines.iloc[i]['open'], klines.iloc[i]['close'])
        low = min(klines.iloc[i]['open'], klines.iloc[i]['close'])
        klines.iloc[i, klines.columns.get_loc('high')] = max(klines.iloc[i]['high'], high)
        klines.iloc[i, klines.columns.get_loc('low')] = min(klines.iloc[i]['low'], low)
    
    # 进行CZSC分析
    analyzer = EnhancedCZSCAnalyzer()
    signal = analyzer.analyze(klines)
    
    print("=== CZSC分析结果 ===")
    print(f"信号类型: {signal.signal_type}")
    print(f"信号强度: {signal.strength.name}")
    print(f"置信度: {signal.confidence:.2%}")
    print(f"入场价格: {signal.entry_price:.2f}")
    print(f"止损价格: {signal.stop_loss:.2f}")
    print(f"目标价格: {signal.take_profit:.2f}")
    print(f"趋势方向: {signal.trend_direction.value}")
    print(f"支撑位: {signal.support_levels}")
    print(f"阻力位: {signal.resistance_levels}")
    print(f"关键点数量: {len(signal.key_points)}")
    print(f"分析推理: {signal.reasoning}")