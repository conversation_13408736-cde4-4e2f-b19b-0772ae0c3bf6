"""
系统稳定性测试脚本
用于测试修复后的系统是否能稳定运行而不会异常退出
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging


def test_system_components():
    """测试系统各个组件的稳定性"""
    
    print("🔧 系统稳定性测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: LLM分析器初始化
    print("📋 测试1: LLM分析器初始化...")
    try:
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        llm_analyzer = LLMMarketAnalyzer()
        print("   ✅ LLM分析器初始化成功")
        test_results.append(("LLM分析器初始化", True))
    except Exception as e:
        print(f"   ❌ LLM分析器初始化失败: {e}")
        test_results.append(("LLM分析器初始化", False))
    
    # 测试2: 多智能体协调器初始化
    print("📋 测试2: 多智能体协调器初始化...")
    try:
        from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
        coordinator = MultiAgentCoordinatorV3()
        print("   ✅ 多智能体协调器初始化成功")
        test_results.append(("多智能体协调器初始化", True))
    except Exception as e:
        print(f"   ❌ 多智能体协调器初始化失败: {e}")
        test_results.append(("多智能体协调器初始化", False))
    
    # 测试3: 增强交易系统初始化
    print("📋 测试3: 增强交易系统初始化...")
    try:
        from system.enhanced_trading_system import EnhancedFundTradingSystemV3
        trading_system = EnhancedFundTradingSystemV3(title='')
        print("   ✅ 增强交易系统初始化成功")
        test_results.append(("增强交易系统初始化", True))
    except Exception as e:
        print(f"   ❌ 增强交易系统初始化失败: {e}")
        test_results.append(("增强交易系统初始化", False))
    
    # 测试4: 单个基金分析（不会导致系统退出）
    print("📋 测试4: 单个基金分析稳定性...")
    try:
        if 'trading_system' in locals():
            # 测试分析一个基金
            result = trading_system.analyze_fund_v3('518880')
            if result and 'error' not in result:
                print("   ✅ 基金分析完成，系统稳定")
                test_results.append(("基金分析稳定性", True))
            else:
                print(f"   ⚠️ 基金分析有错误但系统稳定: {result.get('error', '未知错误')}")
                test_results.append(("基金分析稳定性", True))  # 有错误但不崩溃也算成功
        else:
            print("   ❌ 交易系统未初始化，跳过测试")
            test_results.append(("基金分析稳定性", False))
    except Exception as e:
        print(f"   ❌ 基金分析测试失败: {e}")
        test_results.append(("基金分析稳定性", False))
    
    # 测试5: 凯利协调器稳定性
    print("📋 测试5: 凯利协调器稳定性...")
    try:
        from coordinators.enhanced_kelly_coordinator import EnhancedKellyPositionCoordinator
        kelly_coordinator = EnhancedKellyPositionCoordinator({
            'enable_detailed_logging': False,
            'kelly_fraction': 0.25
        })
        
        # 测试数据
        test_data = {
            'fund_code': '518880',
            'technical_data': {'buy_signal': True, 'technical_indicators': {'rsi': 45}},
            'gua_data': {'is_buy_gua': True, 'gua_score': 0.6},
            'flow_data': {'high_liquidity': True, 'capital_flow': '净流入'},
            'enhanced_decision': {'decision': 'buy', 'confidence': 0.7},
            'final_decision': 'buy',
            'final_confidence': 0.7
        }
        
        kelly_result = kelly_coordinator.calculate_kelly_position(test_data)
        if kelly_result and 'kelly_calculation' in kelly_result:
            print("   ✅ 凯利协调器运行稳定")
            test_results.append(("凯利协调器稳定性", True))
        else:
            print("   ⚠️ 凯利协调器有问题但未崩溃")
            test_results.append(("凯利协调器稳定性", True))
    except Exception as e:
        print(f"   ❌ 凯利协调器测试失败: {e}")
        test_results.append(("凯利协调器稳定性", False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统稳定性良好")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("⚠️ 大部分测试通过，系统基本稳定")
        return True
    else:
        print("❌ 多个测试失败，系统可能不稳定")
        return False


def test_single_cycle():
    """测试单次交易周期是否会导致系统退出"""
    
    print("\n🔄 测试单次交易周期稳定性")
    print("-" * 60)
    
    try:
        from system.enhanced_trading_system import EnhancedFundTradingSystemV3
        
        # 初始化系统
        trading_system = EnhancedFundTradingSystemV3(title='')
        
        # 只测试一个基金，避免长时间运行
        trading_system.buy_fund_list = ['518880']
        
        print("🚀 开始单次交易周期测试...")
        
        # 运行一次交易周期
        results = trading_system.run_trading_cycle_v3()
        
        if results:
            print(f"✅ 交易周期完成，处理了 {len(results)} 个基金")
            
            # 检查结果
            for result in results:
                fund_code = result.get('fund_code', 'UNKNOWN')
                if 'error' in result:
                    print(f"   ⚠️ {fund_code}: 有错误但系统稳定 - {result['error']}")
                else:
                    print(f"   ✅ {fund_code}: 分析成功")
            
            print("🎉 单次交易周期测试成功，系统未异常退出")
            return True
        else:
            print("⚠️ 交易周期返回空结果，但系统未崩溃")
            return True
            
    except Exception as e:
        print(f"❌ 单次交易周期测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    
    # 设置日志
    setup_logging()
    
    print("🚀 fund_trading_system_v3.3_czsc 系统稳定性测试")
    print("🎯 目标: 验证系统修复后不会异常退出")
    print("📅 测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 测试系统组件
    components_stable = test_system_components()
    
    # 测试单次交易周期
    cycle_stable = test_single_cycle()
    
    # 最终结果
    print("\n" + "🎊" * 20)
    if components_stable and cycle_stable:
        print("🎊 系统稳定性测试全部通过！")
        print("🎊 系统已修复，不会异常退出")
        print("🎊 现在可以安全运行 main.py")
    elif components_stable or cycle_stable:
        print("⚠️ 系统部分稳定，建议进一步检查")
        print("⚠️ 但基本功能可用，不会异常退出")
    else:
        print("❌ 系统仍有稳定性问题")
        print("❌ 建议进一步调试")
    print("🎊" * 20)


if __name__ == "__main__":
    main()