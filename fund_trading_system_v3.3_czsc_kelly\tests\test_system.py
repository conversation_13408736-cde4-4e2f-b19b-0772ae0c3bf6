"""
系统集成测试
测试整个交易系统的集成功能
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from system.enhanced_trading_system import EnhancedFundTradingSystemV3
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
from coordinators.trading_executor import FundTradingExecutorV3


class TestEnhancedFundTradingSystemV3(unittest.TestCase):
    """测试增强版基金交易系统V3"""
    
    def setUp(self):
        self.system = EnhancedFundTradingSystemV3()
    
    def test_system_initialization(self):
        """测试系统初始化"""
        self.assertIsNotNone(self.system.technical_agent)
        self.assertIsNotNone(self.system.enhanced_technical_agent)
        self.assertIsNotNone(self.system.gua_agent)
        self.assertIsNotNone(self.system.fund_flow_agent)
        self.assertIsNotNone(self.system.soros_agent)
        self.assertIsNotNone(self.system.trading_agent)
        self.assertIsNotNone(self.system.coordinator)
        self.assertIsNotNone(self.system.executor)
    
    def test_fund_list(self):
        """测试基金列表"""
        self.assertIsInstance(self.system.buy_fund_list, list)
        self.assertGreater(len(self.system.buy_fund_list), 0)
        self.assertIn('513500', self.system.buy_fund_list)
    
    def test_analyze_fund_v3(self):
        """测试V3版本基金分析"""
        result = self.system.analyze_fund_v3('513500')
        
        # 验证返回结果包含必要字段
        self.assertIn('fund_code', result)
        self.assertIn('account_data', result)
    
    def test_diagnose_system_status_v3(self):
        """测试V3版本系统诊断"""
        diagnosis = self.system.diagnose_system_status_v3('513500')
        
        # 验证诊断结果结构
        self.assertIn('fund_code', diagnosis)
        self.assertIn('timestamp', diagnosis)
        self.assertIn('overall_status', diagnosis)
        self.assertIn('issues_found', diagnosis)
        self.assertIn('recommendations', diagnosis)
        self.assertIn('component_status', diagnosis)
        self.assertIn('v3_features_status', diagnosis)


class TestMultiAgentCoordinatorV3(unittest.TestCase):
    """测试多智能体协调器V3"""
    
    def setUp(self):
        self.coordinator = MultiAgentCoordinatorV3()
    
    def test_coordinator_initialization(self):
        """测试协调器初始化"""
        self.assertIsNotNone(self.coordinator.technical_agent)
        self.assertIsNotNone(self.coordinator.gua_agent)
        self.assertIsNotNone(self.coordinator.flow_agent)
        self.assertIsNotNone(self.coordinator.enhanced_decision_agent)
    
    def test_coordinate_analysis(self):
        """测试协调分析"""
        result = self.coordinator.coordinate_analysis('513500')
        
        # 验证协调分析结果
        self.assertIn('fund_code', result)


class TestFundTradingExecutorV3(unittest.TestCase):
    """测试基金交易执行器V3"""
    
    def setUp(self):
        self.executor = FundTradingExecutorV3()
    
    def test_executor_initialization(self):
        """测试执行器初始化"""
        self.assertEqual(self.executor.initial_capital, 100000.0)
        self.assertEqual(self.executor.current_capital, 100000.0)
        self.assertIsInstance(self.executor.positions, dict)
        self.assertIsInstance(self.executor.transaction_history, list)
        self.assertIn('max_position_size', self.executor.risk_limits)
    
    def test_risk_limits(self):
        """测试风险限制"""
        self.assertEqual(self.executor.risk_limits['max_position_size'], 0.2)
        self.assertEqual(self.executor.risk_limits['max_daily_loss'], 0.05)
        self.assertEqual(self.executor.risk_limits['min_confidence'], 0.6)


if __name__ == '__main__':
    unittest.main()
