"""
快速执行引擎
专注于"快"的交易执行，与研究分析模块分离
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import threading
from queue import Queue, PriorityQueue
import json

from core.enums import TrendState, SignalStrength


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class FastOrder:
    """快速订单"""
    order_id: str
    symbol: str
    side: str  # buy/sell
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # GTC, IOC, FOK
    status: OrderStatus = OrderStatus.PENDING
    created_time: datetime = field(default_factory=datetime.now)
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    commission: float = 0.0
    metadata: Dict = field(default_factory=dict)


@dataclass
class ExecutionSignal:
    """执行信号"""
    signal_id: str
    symbol: str
    action: str  # buy/sell/hold
    urgency: int  # 1-10, 10最紧急
    quantity: float
    price_limit: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    max_latency_ms: int = 1000  # 最大延迟毫秒
    created_time: datetime = field(default_factory=datetime.now)
    metadata: Dict = field(default_factory=dict)


class FastExecutionEngine:
    """快速执行引擎"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 执行队列 - 使用优先级队列
        self.execution_queue = PriorityQueue()
        self.order_queue = Queue()
        
        # 订单管理
        self.active_orders: Dict[str, FastOrder] = {}
        self.order_history: List[FastOrder] = []
        
        # 执行统计
        self.execution_stats = {
            'total_signals': 0,
            'executed_orders': 0,
            'avg_execution_time_ms': 0.0,
            'success_rate': 0.0,
            'total_latency_ms': 0.0,
            'rejected_orders': 0
        }
        
        # 执行线程
        self.execution_thread = None
        self.order_thread = None
        self.running = False
        
        # 回调函数
        self.order_callback: Optional[Callable] = None
        self.execution_callback: Optional[Callable] = None
        
        # 风险控制
        self.risk_limits = {
            'max_position_size': 100000,
            'max_daily_trades': 1000,
            'max_order_value': 50000,
            'daily_loss_limit': 10000
        }
        
        self.daily_stats = {
            'trades_count': 0,
            'total_pnl': 0.0,
            'last_reset': datetime.now().date()
        }
        
        self.logger.info("快速执行引擎初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'execution': {
                'max_latency_ms': 100,  # 最大执行延迟
                'retry_attempts': 3,
                'retry_delay_ms': 50,
                'batch_size': 10,
                'enable_pre_trade_checks': True
            },
            'order_management': {
                'default_time_in_force': 'GTC',
                'auto_cancel_timeout_sec': 300,
                'partial_fill_handling': 'continue',
                'slippage_tolerance': 0.001
            },
            'risk_control': {
                'enable_real_time_risk': True,
                'position_check_interval_ms': 100,
                'max_concurrent_orders': 50
            },
            'performance': {
                'enable_async_execution': True,
                'thread_pool_size': 4,
                'queue_max_size': 1000
            }
        }
    
    def start(self):
        """启动执行引擎"""
        if self.running:
            self.logger.warning("执行引擎已在运行")
            return
        
        self.running = True
        
        # 启动执行线程
        self.execution_thread = threading.Thread(target=self._execution_loop, daemon=True)
        self.execution_thread.start()
        
        # 启动订单处理线程
        self.order_thread = threading.Thread(target=self._order_processing_loop, daemon=True)
        self.order_thread.start()
        
        self.logger.info("快速执行引擎已启动")
    
    def stop(self):
        """停止执行引擎"""
        self.running = False
        
        if self.execution_thread:
            self.execution_thread.join(timeout=5)
        
        if self.order_thread:
            self.order_thread.join(timeout=5)
        
        self.logger.info("快速执行引擎已停止")
    
    def submit_signal(self, signal: ExecutionSignal) -> bool:
        """提交执行信号"""
        try:
            # 重置每日统计
            self._reset_daily_stats_if_needed()
            
            # 预交易检查
            if self.config['execution']['enable_pre_trade_checks']:
                if not self._pre_trade_check(signal):
                    self.logger.warning(f"信号 {signal.signal_id} 未通过预交易检查")
                    return False
            
            # 添加到优先级队列（优先级越高数字越小）
            priority = 10 - signal.urgency
            self.execution_queue.put((priority, time.time(), signal))
            
            self.execution_stats['total_signals'] += 1
            self.logger.debug(f"信号 {signal.signal_id} 已提交到执行队列")
            
            return True
            
        except Exception as e:
            self.logger.error(f"提交执行信号失败: {e}")
            return False
    
    def _execution_loop(self):
        """执行循环"""
        self.logger.info("执行循环已启动")
        
        while self.running:
            try:
                # 获取信号（阻塞等待，超时1秒）
                try:
                    priority, submit_time, signal = self.execution_queue.get(timeout=1.0)
                except:
                    continue
                
                # 检查信号是否过期
                current_time = time.time()
                latency_ms = (current_time - submit_time) * 1000
                
                if latency_ms > signal.max_latency_ms:
                    self.logger.warning(f"信号 {signal.signal_id} 已过期，延迟 {latency_ms:.1f}ms")
                    continue
                
                # 执行信号
                start_time = time.time()
                success = self._execute_signal(signal)
                execution_time = (time.time() - start_time) * 1000
                
                # 更新统计
                self._update_execution_stats(execution_time, success)
                
                # 回调通知
                if self.execution_callback:
                    try:
                        self.execution_callback(signal, success, execution_time)
                    except Exception as e:
                        self.logger.error(f"执行回调失败: {e}")
                
            except Exception as e:
                self.logger.error(f"执行循环异常: {e}")
    
    def _order_processing_loop(self):
        """订单处理循环"""
        self.logger.info("订单处理循环已启动")
        
        while self.running:
            try:
                # 处理订单状态更新
                self._process_order_updates()
                
                # 检查超时订单
                self._check_timeout_orders()
                
                # 短暂休眠
                time.sleep(0.01)  # 10ms
                
            except Exception as e:
                self.logger.error(f"订单处理循环异常: {e}")
    
    def _execute_signal(self, signal: ExecutionSignal) -> bool:
        """执行信号"""
        try:
            self.logger.debug(f"开始执行信号 {signal.signal_id}")
            
            if signal.action == 'hold':
                return True
            
            # 创建订单
            order = self._create_order_from_signal(signal)
            if not order:
                return False
            
            # 提交订单
            success = self._submit_order(order)
            
            if success:
                self.active_orders[order.order_id] = order
                self.logger.info(f"订单 {order.order_id} 提交成功")
            else:
                self.logger.error(f"订单 {order.order_id} 提交失败")
                self.execution_stats['rejected_orders'] += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"执行信号失败: {e}")
            return False
    
    def _create_order_from_signal(self, signal: ExecutionSignal) -> Optional[FastOrder]:
        """从信号创建订单"""
        try:
            order_id = f"{signal.signal_id}_{int(time.time() * 1000)}"
            
            # 确定订单类型
            if signal.price_limit:
                order_type = OrderType.LIMIT
                price = signal.price_limit
            else:
                order_type = OrderType.MARKET
                price = None
            
            order = FastOrder(
                order_id=order_id,
                symbol=signal.symbol,
                side=signal.action,
                order_type=order_type,
                quantity=signal.quantity,
                price=price,
                metadata={
                    'signal_id': signal.signal_id,
                    'urgency': signal.urgency,
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit
                }
            )
            
            return order
            
        except Exception as e:
            self.logger.error(f"创建订单失败: {e}")
            return None
    
    def _submit_order(self, order: FastOrder) -> bool:
        """提交订单（模拟实现）"""
        try:
            # 这里应该调用实际的交易接口
            # 现在使用模拟实现
            
            self.logger.info(f"提交订单: {order.symbol} {order.side} {order.quantity} @ {order.price}")
            
            # 模拟订单提交延迟
            time.sleep(0.001)  # 1ms
            
            # 模拟成功率（95%）
            import random
            if random.random() < 0.95:
                order.status = OrderStatus.SUBMITTED
                
                # 模拟快速成交（市价单）
                if order.order_type == OrderType.MARKET:
                    # 模拟成交
                    order.status = OrderStatus.FILLED
                    order.filled_quantity = order.quantity
                    order.avg_fill_price = order.price or 100.0  # 模拟价格
                    
                    # 更新每日统计
                    self.daily_stats['trades_count'] += 1
                
                return True
            else:
                order.status = OrderStatus.REJECTED
                return False
                
        except Exception as e:
            self.logger.error(f"提交订单失败: {e}")
            order.status = OrderStatus.REJECTED
            return False
    
    def _process_order_updates(self):
        """处理订单状态更新"""
        # 这里应该从交易接口获取订单状态更新
        # 现在使用模拟实现
        
        for order_id, order in list(self.active_orders.items()):
            if order.status == OrderStatus.SUBMITTED:
                # 模拟限价单成交
                if order.order_type == OrderType.LIMIT:
                    import random
                    if random.random() < 0.1:  # 10%概率成交
                        order.status = OrderStatus.FILLED
                        order.filled_quantity = order.quantity
                        order.avg_fill_price = order.price
                        
                        # 移到历史记录
                        self.order_history.append(order)
                        del self.active_orders[order_id]
                        
                        # 回调通知
                        if self.order_callback:
                            try:
                                self.order_callback(order)
                            except Exception as e:
                                self.logger.error(f"订单回调失败: {e}")
    
    def _check_timeout_orders(self):
        """检查超时订单"""
        timeout_sec = self.config['order_management']['auto_cancel_timeout_sec']
        current_time = datetime.now()
        
        for order_id, order in list(self.active_orders.items()):
            if order.status == OrderStatus.SUBMITTED:
                elapsed = (current_time - order.created_time).total_seconds()
                if elapsed > timeout_sec:
                    self.logger.info(f"订单 {order_id} 超时，自动取消")
                    self.cancel_order(order_id)
    
    def _pre_trade_check(self, signal: ExecutionSignal) -> bool:
        """预交易检查"""
        try:
            # 检查每日交易次数限制
            if self.daily_stats['trades_count'] >= self.risk_limits['max_daily_trades']:
                self.logger.warning("已达到每日交易次数限制")
                return False
            
            # 检查订单价值限制
            order_value = signal.quantity * (signal.price_limit or 100.0)  # 模拟价格
            if order_value > self.risk_limits['max_order_value']:
                self.logger.warning(f"订单价值 {order_value} 超过限制 {self.risk_limits['max_order_value']}")
                return False
            
            # 检查每日亏损限制
            if self.daily_stats['total_pnl'] < -self.risk_limits['daily_loss_limit']:
                self.logger.warning("已达到每日亏损限制")
                return False
            
            # 检查并发订单数量
            if len(self.active_orders) >= self.config['risk_control']['max_concurrent_orders']:
                self.logger.warning("活跃订单数量已达上限")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"预交易检查失败: {e}")
            return False
    
    def _reset_daily_stats_if_needed(self):
        """如果需要则重置每日统计"""
        today = datetime.now().date()
        if self.daily_stats['last_reset'] != today:
            self.daily_stats = {
                'trades_count': 0,
                'total_pnl': 0.0,
                'last_reset': today
            }
            self.logger.info("每日统计已重置")
    
    def _update_execution_stats(self, execution_time_ms: float, success: bool):
        """更新执行统计"""
        if success:
            self.execution_stats['executed_orders'] += 1
        
        # 更新平均执行时间
        total_latency = self.execution_stats['total_latency_ms']
        total_signals = self.execution_stats['total_signals']
        
        self.execution_stats['total_latency_ms'] = total_latency + execution_time_ms
        self.execution_stats['avg_execution_time_ms'] = (
            self.execution_stats['total_latency_ms'] / total_signals
        )
        
        # 更新成功率
        if self.execution_stats['total_signals'] > 0:
            self.execution_stats['success_rate'] = (
                self.execution_stats['executed_orders'] / self.execution_stats['total_signals']
            )
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            if order_id not in self.active_orders:
                self.logger.warning(f"订单 {order_id} 不存在")
                return False
            
            order = self.active_orders[order_id]
            
            # 这里应该调用实际的取消订单接口
            # 现在使用模拟实现
            order.status = OrderStatus.CANCELLED
            
            # 移到历史记录
            self.order_history.append(order)
            del self.active_orders[order_id]
            
            self.logger.info(f"订单 {order_id} 已取消")
            return True
            
        except Exception as e:
            self.logger.error(f"取消订单失败: {e}")
            return False
    
    def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """获取订单状态"""
        if order_id in self.active_orders:
            return self.active_orders[order_id].status
        
        # 在历史记录中查找
        for order in self.order_history:
            if order.order_id == order_id:
                return order.status
        
        return None
    
    def get_active_orders(self) -> List[FastOrder]:
        """获取活跃订单"""
        return list(self.active_orders.values())
    
    def get_execution_statistics(self) -> Dict:
        """获取执行统计信息"""
        stats = self.execution_stats.copy()
        stats.update({
            'active_orders_count': len(self.active_orders),
            'daily_trades': self.daily_stats['trades_count'],
            'daily_pnl': self.daily_stats['total_pnl'],
            'queue_size': self.execution_queue.qsize()
        })
        return stats
    
    def set_order_callback(self, callback: Callable[[FastOrder], None]):
        """设置订单回调函数"""
        self.order_callback = callback
    
    def set_execution_callback(self, callback: Callable[[ExecutionSignal, bool, float], None]):
        """设置执行回调函数"""
        self.execution_callback = callback
    
    def update_risk_limits(self, limits: Dict):
        """更新风险限制"""
        self.risk_limits.update(limits)
        self.logger.info(f"风险限制已更新: {limits}")
    
    async def async_submit_signal(self, signal: ExecutionSignal) -> bool:
        """异步提交信号"""
        if self.config['performance']['enable_async_execution']:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self.submit_signal, signal)
        else:
            return self.submit_signal(signal)


# 使用示例和测试函数
def create_test_signal(symbol: str = "TEST", urgency: int = 5) -> ExecutionSignal:
    """创建测试信号"""
    return ExecutionSignal(
        signal_id=f"test_{int(time.time() * 1000)}",
        symbol=symbol,
        action="buy",
        urgency=urgency,
        quantity=100,
        price_limit=100.0,
        max_latency_ms=500
    )


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    engine = FastExecutionEngine()
    engine.start()
    
    # 提交测试信号
    for i in range(5):
        signal = create_test_signal(urgency=i+1)
        engine.submit_signal(signal)
        time.sleep(0.1)
    
    # 等待执行
    time.sleep(2)
    
    # 打印统计信息
    stats = engine.get_execution_statistics()
    print("执行统计:", json.dumps(stats, indent=2, default=str))
    
    engine.stop()