"""
增强信号质量示例
演示如何使用backtest_modules增强CZSC信号质量
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from backtest_modules import (
        BacktestIntegrationFactory,
        SignalQualityEnhancer,
        RiskEnhancedEvaluationResult,
        check_module_availability,
        is_fully_integrated
    )
    from core.enums import TrendState, SignalStrength
    from core.data_structures import DimensionEvaluationResult
except ImportError as e:
    logger.error(f"模块导入失败: {e}")
    sys.exit(1)

def generate_sample_data(days=252):
    """生成示例数据"""
    logger.info(f"生成 {days} 天的示例数据")
    
    # 生成时间序列
    dates = pd.date_range(start='2023-01-01', periods=days, freq='D')
    
    # 生成价格数据（随机游走 + 趋势）
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.02, days)  # 日收益率
    trend = np.sin(np.arange(days) * 2 * np.pi / 60) * 0.005  # 周期性趋势
    returns += trend
    
    prices = 100 * np.cumprod(1 + returns)
    
    # 生成其他数据
    volumes = np.random.lognormal(10, 0.5, days)
    
    df = pd.DataFrame({
        'dt': dates,
        'open': prices * (1 + np.random.normal(0, 0.005, days)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, days))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, days))),
        'close': prices,
        'volume': volumes,
        'future_return': np.roll(returns, -5)  # 5天后的收益率作为目标
    })
    
    # 确保high >= close >= low
    df['high'] = np.maximum(df['high'], df['close'])
    df['low'] = np.minimum(df['low'], df['close'])
    
    logger.info("示例数据生成完成")
    return df

def generate_sample_czsc_signals(df):
    """生成示例CZSC信号"""
    logger.info("生成示例CZSC信号")
    
    signals = []
    
    # 计算简单的技术指标用于生成信号
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['rsi'] = calculate_rsi(df['close'], 14)
    
    # 生成不同类型的信号
    for i in range(20, len(df), 10):  # 每10天生成一个信号
        current_data = df.iloc[i]
        
        # 基于移动平均线生成趋势信号
        if current_data['ma5'] > current_data['ma20']:
            if current_data['rsi'] < 70:  # 避免超买
                state = TrendState.STRONG_UPTREND if current_data['rsi'] > 60 else TrendState.WEAK_UPTREND
                score = min(0.9, (current_data['ma5'] / current_data['ma20'] - 1) * 10 + 0.5)
            else:
                continue
        else:
            if current_data['rsi'] > 30:  # 避免超卖
                state = TrendState.STRONG_DOWNTREND if current_data['rsi'] < 40 else TrendState.WEAK_DOWNTREND
                score = min(0.9, (current_data['ma20'] / current_data['ma5'] - 1) * 10 + 0.5)
            else:
                continue
        
        # 创建信号
        signal = DimensionEvaluationResult(
            dimension_name=f"趋势信号_{i}",
            state=state,
            score=score,
            confidence=np.random.uniform(0.6, 0.95),
            signals=[f"MA5{'上穿' if state.name.endswith('UPTREND') else '下穿'}MA20"],
            data_quality="good",
            details={
                "ma5": current_data['ma5'],
                "ma20": current_data['ma20'],
                "rsi": current_data['rsi'],
                "signal_date": current_data['dt']
            },
            indicators={
                "ma5": current_data['ma5'],
                "ma20": current_data['ma20'],
                "rsi": current_data['rsi']
            }
        )
        
        signals.append(signal)
    
    logger.info(f"生成了 {len(signals)} 个CZSC信号")
    return signals

def calculate_rsi(prices, window=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def main():
    """主函数"""
    logger.info("=== 增强信号质量示例开始 ===")
    
    # 1. 检查模块可用性
    logger.info("检查模块可用性...")
    availability = check_module_availability()
    
    if not is_fully_integrated():
        logger.warning("部分模块不可用，示例可能无法完全运行")
    
    # 2. 生成示例数据
    df = generate_sample_data(252)
    czsc_signals = generate_sample_czsc_signals(df)
    
    if not czsc_signals:
        logger.error("没有生成CZSC信号，退出示例")
        return
    
    # 3. 初始化增强系统
    try:
        logger.info("初始化增强系统...")
        
        # 创建回测系统
        backtest_system = BacktestIntegrationFactory.create_enhanced_system()
        
        # 创建信号增强器
        signal_enhancer = BacktestIntegrationFactory.create_signal_enhancer(backtest_system)
        
        # 创建风险管理器
        risk_manager = BacktestIntegrationFactory.create_risk_manager()
        
        logger.info("增强系统初始化完成")
        
    except Exception as e:
        logger.error(f"增强系统初始化失败: {e}")
        return
    
    # 4. 增强信号质量
    try:
        logger.info("开始增强信号质量...")
        
        enhanced_signals = signal_enhancer.enhance_czsc_signals(df, czsc_signals)
        
        logger.info(f"信号增强完成: {len(czsc_signals)} -> {len(enhanced_signals)}")
        
    except Exception as e:
        logger.error(f"信号增强失败: {e}")
        enhanced_signals = czsc_signals
    
    # 5. 应用风险管理
    try:
        logger.info("应用风险管理...")
        
        current_price = df['close'].iloc[-1]
        portfolio_value = 100000
        
        risk_enhanced_signals = []
        
        for signal in enhanced_signals:
            # 创建风险增强结果
            risk_signal = RiskEnhancedEvaluationResult(
                dimension_name=signal.dimension_name,
                state=signal.state,
                score=signal.score,
                confidence=signal.confidence,
                signals=signal.signals,
                data_quality=signal.data_quality,
                details=signal.details,
                indicators=signal.indicators
            )
            
            # 设置增强属性（模拟值）
            risk_signal.ml_confidence = np.random.uniform(0.5, 0.9)
            risk_signal.technical_consistency = np.random.uniform(0.4, 0.8)
            risk_signal.historical_success_rate = np.random.uniform(0.5, 0.8)
            risk_signal.market_environment_fit = np.random.uniform(0.4, 0.9)
            
            # 应用风险管理
            is_valid = risk_signal.apply_risk_management(risk_manager, current_price, portfolio_value)
            
            if is_valid:
                risk_enhanced_signals.append(risk_signal)
        
        logger.info(f"风险管理完成: {len(enhanced_signals)} -> {len(risk_enhanced_signals)}")
        
    except Exception as e:
        logger.error(f"风险管理应用失败: {e}")
        risk_enhanced_signals = []
    
    # 6. 显示结果
    logger.info("\n=== 信号增强结果 ===")
    
    if risk_enhanced_signals:
        for i, signal in enumerate(risk_enhanced_signals[:5]):  # 显示前5个信号
            logger.info(f"\n信号 {i+1}: {signal.dimension_name}")
            logger.info(f"  状态: {signal.state.name}")
            logger.info(f"  评分: {signal.score:.3f}")
            logger.info(f"  置信度: {signal.confidence:.3f}")
            logger.info(f"  ML置信度: {signal.ml_confidence:.3f}")
            logger.info(f"  技术一致性: {signal.technical_consistency:.3f}")
            logger.info(f"  历史成功率: {signal.historical_success_rate:.3f}")
            logger.info(f"  市场适应性: {signal.market_environment_fit:.3f}")
            logger.info(f"  建议仓位: {signal.position_size_recommendation:.2%}")
            logger.info(f"  止损位: {signal.stop_loss_level:.2f}")
            logger.info(f"  止盈位: {signal.take_profit_level:.2f}")
            logger.info(f"  风险等级: {signal.risk_level}")
            
            # 获取交易建议
            recommendation = signal.get_trading_recommendation()
            logger.info(f"  交易建议: {recommendation['action']}")
            logger.info(f"  建议原因: {recommendation['reason']}")
            
            if signal.risk_warnings:
                logger.info(f"  风险警告: {', '.join(signal.risk_warnings)}")
    else:
        logger.warning("没有通过风险管理的信号")
    
    # 7. 运行回测验证（如果可用）
    try:
        if availability.get('advanced_backtest', False):
            logger.info("\n=== 回测验证 ===")
            
            results = backtest_system.run_complete_pipeline(
                df,
                target_col='future_return',
                time_col='dt',
                price_col='close'
            )
            
            if 'backtest' in results.get('pipeline_results', {}):
                metrics = results['pipeline_results']['backtest']['metrics']
                logger.info(f"年化收益率: {metrics.get('annualized_return', 0):.2%}")
                logger.info(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
                logger.info(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}")
                logger.info(f"胜率: {metrics.get('win_rate', 0):.2%}")
            else:
                logger.warning("回测结果不完整")
        else:
            logger.info("回测模块不可用，跳过回测验证")
            
    except Exception as e:
        logger.error(f"回测验证失败: {e}")
    
    # 8. 获取增强统计信息
    try:
        stats = signal_enhancer.get_enhancement_statistics()
        logger.info(f"\n=== 增强统计信息 ===")
        logger.info(f"总增强次数: {stats.get('total_enhancements', 0)}")
        logger.info(f"平均改进比例: {stats.get('average_improvement_ratio', 0):.2%}")
        logger.info(f"信号历史记录: {stats.get('signal_history_size', 0)}")
        logger.info(f"模型已训练: {stats.get('model_trained', False)}")
        
    except Exception as e:
        logger.warning(f"获取统计信息失败: {e}")
    
    logger.info("\n=== 增强信号质量示例完成 ===")

if __name__ == "__main__":
    main()