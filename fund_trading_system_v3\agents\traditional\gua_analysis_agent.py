"""
卦象分析智能体
负责基于易经卦象的市场分析
"""

import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.base_agent import BaseAgent
from core.utils import *


class GuaAnalysisAgent(BaseAgent):
    """
    @class GuaAnalysisAgent
    @brief 卦象分析智能体
    @details 负责基于易经卦象的市场分析
    """
    
    def __init__(self, name: str = "GuaAnalysisAgent"):
        super().__init__(name, "gua_analysis")
        # 定义64卦
        self.gua_64 = [
            "乾", "坤", "屯", "蒙", "需", "讼", "师", "比",
            "小畜", "履", "泰", "否", "同人", "大有", "谦", "豫",
            "随", "蛊", "临", "观", "噬嗑", "贲", "剥", "复",
            "无妄", "大畜", "颐", "大过", "坎", "离", "咸", "恒",
            "遁", "大壮", "晋", "明夷", "家人", "睽", "蹇", "解",
            "损", "益", "夬", "姤", "萃", "升", "困", "井",
            "革", "鼎", "震", "艮", "渐", "归妹", "丰", "旅",
            "巽", "兑", "涣", "节", "中孚", "小过", "既济", "未济"
        ]
        # 定义选股卦象（吉卦）
        self.select_gua_list = ["乾", "坤", "泰", "大有", "谦", "豫", "临", "益", "升", "井", "震", "既济"]
        # 定义买入卦象
        self.buy_gua_list = ["需", "比", "小畜", "履", "同人", "随", "观", "复", "大畜", "离", "咸", "解", "萃", "鼎", "渐", "丰", "巽", "涣", "中孚"]
        # 定义卖出卦象
        self.sell_gua_list = ["讼", "师", "否", "蛊", "噬嗑", "剥", "无妄", "遁", "明夷", "睽", "蹇", "损", "夬", "困", "革", "艮", "归妹", "旅", "兑", "节", "小过", "未济"]
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理卦象分析请求"""
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            gua_result = self.analyze_gua(fund_code)
            return gua_result
        except Exception as e:
            self.logger.error(f"Gua analysis failed for {fund_code}: {str(e)}")
            return {'error': str(e)}
    
    def analyze_gua(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 分析卦象 - 基于真实价格行为
        @param fund_code: 基金代码
        @return: 卦象分析结果
        """
        try:
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            # 使用真实价格行为分析卦象
            gua_result = analyze_real_gua_from_price_action(fund_code)
            
            # 验证数据质量
            quality_result = validate_data_quality(fund_code, gua_result)
            if not quality_result['overall_quality']:
                raise ValueError(f"Gua analysis data quality issues: {quality_result['issues']}")
            
            # 提取卦象信息
            main_gua = gua_result['main_gua']
            gua_lines = gua_result['gua_lines']
            
            # 判断卦象类型（基于真实分析结果）
            is_select_gua = gua_result['is_select_gua']
            is_buy_gua = gua_result['is_buy_gua']
            is_sell_gua = gua_result['is_sell_gua']
            
            # 基于真实数据的卦象评分
            gua_score = 0.0
            if is_select_gua:
                # 强势卦象，加权真实强度
                gua_score = gua_result['strength_score'] * 1.2
            elif is_buy_gua:
                gua_score = gua_result['strength_score'] * 0.8
            elif is_sell_gua:
                gua_score = -gua_result['strength_score'] * 0.8
            else:
                gua_score = 0.0
            
            # 生成配卦（基于主卦的变化）
            gua_list = [main_gua]
            if len(gua_lines) >= 3:
                # 根据阴阳爻组合生成配卦
                pattern_variants = self._generate_pattern_variants(gua_lines)
                gua_list.extend(pattern_variants[:2])  # 最多3个卦
            
            return {
                'fund_code': fund_code,
                'gua_list': gua_list,
                'main_gua': main_gua,
                'gua_pattern': gua_result['gua_pattern'],
                'gua_lines': gua_lines,
                'is_select_gua': is_select_gua,
                'is_buy_gua': is_buy_gua,
                'is_sell_gua': is_sell_gua,
                'gua_score': gua_score,
                'strength_score': gua_result['strength_score'],
                'price_momentum': gua_result['price_momentum'],
                'volume_momentum': gua_result['volume_momentum'],
                'analysis_time': gua_result['analysis_time'],
                'gua_interpretation': f"主卦：{main_gua}，价格动量：{gua_result['price_momentum']:.2%}",
                'confidence': gua_result['confidence'],
                'data_quality': quality_result['quality_score'],
                'data_source': 'real_price_action'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze gua for {fund_code}: {str(e)}")
            # 不再使用随机卦象，直接返回错误
            return {
                'fund_code': fund_code,
                'error': str(e),
                'gua_score': 0.0,
                'is_select_gua': False,
                'is_buy_gua': False,
                'is_sell_gua': False,
                'data_source': 'error'
            }

    def _generate_pattern_variants(self, gua_lines: List[str]) -> List[str]:
        """基于阴阳爻生成配卦变体"""
        try:
            variants = []
            if len(gua_lines) >= 3:
                # 简化的配卦生成逻辑
                base_pattern = ''.join(gua_lines[:3])

                # 生成互卦（中间三爻的变化）
                if len(gua_lines) >= 5:
                    mutual_pattern = ''.join(gua_lines[1:4])
                    mutual_gua = self._pattern_to_gua(mutual_pattern)
                    if mutual_gua and mutual_gua != '未知卦象':
                        variants.append(mutual_gua)

                # 生成综卦（阴阳互换）
                inverted_pattern = ''.join(['阴' if line == '阳' else '阳' for line in gua_lines[:3]])
                inverted_gua = self._pattern_to_gua(inverted_pattern)
                if inverted_gua and inverted_gua != '未知卦象':
                    variants.append(inverted_gua)

            return variants
        except Exception:
            return []

    def _pattern_to_gua(self, pattern: str) -> str:
        """将阴阳爻模式转换为卦名"""
        try:
            # 简化的卦象映射
            pattern_mapping = {
                '阳阳阳': '乾为天',
                '阴阴阴': '坤为地',
                '阳阴阳': '离为火',
                '阴阳阴': '坎为水',
                '阳阳阴': '兑为泽',
                '阴阴阳': '艮为山',
                '阳阴阴': '震为雷',
                '阴阳阳': '巽为风',
                '阳阴阳': '履',
                '阴阳阳': '泰',
                '阳阳阴': '否',
                '阴阴阳': '同人'
            }
            return pattern_mapping.get(pattern, '未知卦象')
        except Exception:
            return '未知卦象'
