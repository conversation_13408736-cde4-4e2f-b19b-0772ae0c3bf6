"""
CZSC数据适配器
提供与CZSC库的数据转换和集成功能
"""

import logging
import sys
import os
from typing import List, Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.utils import *
from core.data_structures import CzscBiStructure


class CzscDataAdapter:
    """
    @class CzscDataAdapter
    @brief CZSC数据适配器
    @details 提供与CZSC库的数据转换和集成功能
    """
    
    @staticmethod
    def freq_str_to_enum(freq_str: str):
        """将频率字符串转换为CZSC频率枚举"""
        try:
            if not CZSC_AVAILABLE:
                return None
                
            from czsc import Freq
            freq_mapping = {
                '1min': Freq.F1,
                '5min': Freq.F5,
                '15min': Freq.F15,
                '30min': Freq.F30,
                '60min': Freq.F60,
                'D': Freq.D,
                'W': Freq.W,
                'M': Freq.M
            }
            return freq_mapping.get(freq_str, Freq.F1)
        except Exception:
            return None  # CZSC不可用时返回None
    
    @staticmethod
    def pandas_to_rawbars(df: pd.DataFrame, symbol: str, freq: str) -> List[Any]:
        """将Pandas DataFrame转换为CZSC RawBar列表"""
        try:
            if not CZSC_AVAILABLE:
                logging.warning("CZSC library not available")
                return []
                
            from czsc import RawBar as CzscRawBar
            
            bars = []
            freq_enum = CzscDataAdapter.freq_str_to_enum(freq)
            
            if freq_enum is None:
                logging.error("Invalid frequency or CZSC not available")
                return []
            
            for i, row in df.iterrows():
                try:
                    bar = CzscRawBar(
                        symbol=symbol,
                        id=i,
                        freq=freq_enum, 
                        dt=pd.to_datetime(row['dt']) if 'dt' in row else pd.to_datetime(row.name),
                        open=float(row['open']),
                        close=float(row['close']),
                        high=float(row['high']),
                        low=float(row['low']),
                        vol=float(row.get('vol', row.get('volume', 0))),
                        amount=float(row.get('amount', 0))
                    )
                    bars.append(bar)
                except Exception as e:
                    logging.warning(f"Failed to convert row {i}: {e}")
                    continue
            
            return bars
        except Exception as e:
            logging.error(f"数据转换失败: {e}")
            return []
    
    @staticmethod
    def bi_to_czsc_structure(bi: Any, macd_data: Dict[str, np.ndarray], df: pd.DataFrame = None) -> CzscBiStructure:
        """将CZSC BI转换为系统的笔结构数据"""
        try:
            if not CZSC_AVAILABLE or bi is None:
                return CzscDataAdapter._get_default_bi_structure()
            
            # 获取笔的基本信息
            bi_direction = "up" if hasattr(bi, 'direction') and str(bi.direction.value) == "向上" else "down"
            bi_start_price = bi.fx_a.fx if hasattr(bi, 'fx_a') else 0.0
            bi_end_price = bi.fx_b.fx if hasattr(bi, 'fx_b') else 0.0
            bi_amplitude = abs(bi_end_price - bi_start_price)
            bi_duration = len(bi.bars) if hasattr(bi, 'bars') else 0
            
            # 计算对应的MACD面积和DIF峰值
            start_idx = 0
            end_idx = len(macd_data.get('macd', [])) - 1
            
            if df is not None and hasattr(bi, 'fx_a') and hasattr(bi, 'fx_b'):
                try:
                    start_dt = bi.fx_a.dt
                    end_dt = bi.fx_b.dt
                    
                    df_with_dt = df.copy()
                    if 'dt' not in df_with_dt.columns:
                        df_with_dt.reset_index(inplace=True)
                    
                    df_with_dt['dt'] = pd.to_datetime(df_with_dt['dt'])
                    start_matches = df_with_dt[df_with_dt['dt'] <= start_dt]
                    end_matches = df_with_dt[df_with_dt['dt'] <= end_dt]
                    
                    start_idx = start_matches.index[-1] if len(start_matches) > 0 else 0
                    end_idx = end_matches.index[-1] if len(end_matches) > 0 else len(df_with_dt) - 1
                except Exception:
                    # 如果时间匹配失败，使用默认索引
                    pass
            
            # 计算MACD面积
            macd_slice = macd_data.get('macd', np.array([]))[start_idx:end_idx+1]
            macd_area = np.sum(np.abs(macd_slice)) if len(macd_slice) > 0 else 0.0
            
            # 计算DIF峰值
            dif_slice = macd_data.get('dif', np.array([]))[start_idx:end_idx+1]
            if len(dif_slice) > 0:
                if bi_direction == "up":
                    dif_peak = np.max(dif_slice)
                else:
                    dif_peak = np.min(dif_slice)
            else:
                dif_peak = 0.0
            
            return CzscBiStructure(
                bi_direction=bi_direction,
                bi_start_price=bi_start_price,
                bi_end_price=bi_end_price,
                bi_amplitude=bi_amplitude,
                bi_duration=bi_duration,
                macd_area=macd_area,
                dif_peak=dif_peak
            )
            
        except Exception as e:
            logging.error(f"BI结构转换失败: {e}")
            return CzscDataAdapter._get_default_bi_structure()
    
    @staticmethod
    def _get_default_bi_structure() -> CzscBiStructure:
        """获取默认的BI结构"""
        return CzscBiStructure(
            bi_direction="unknown",
            bi_start_price=0.0,
            bi_end_price=0.0,
            bi_amplitude=0.0,
            bi_duration=0,
            macd_area=0.0,
            dif_peak=0.0
        )
    
    @staticmethod
    def is_czsc_available() -> bool:
        """检查CZSC库是否可用"""
        return CZSC_AVAILABLE
    
    @staticmethod
    def get_supported_frequencies() -> List[str]:
        """获取支持的频率列表"""
        return ['1min', '5min', '15min', '30min', '60min', 'D', 'W', 'M']
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame) -> Dict[str, Any]:
        """验证DataFrame格式是否符合CZSC要求"""
        try:
            required_columns = ['open', 'close', 'high', 'low']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                return {
                    'valid': False,
                    'error': f"Missing required columns: {missing_columns}"
                }
            
            # 检查数据类型
            for col in required_columns:
                if not pd.api.types.is_numeric_dtype(df[col]):
                    return {
                        'valid': False,
                        'error': f"Column {col} must be numeric"
                    }
            
            # 检查数据完整性
            if df.empty:
                return {
                    'valid': False,
                    'error': "DataFrame is empty"
                }
            
            return {
                'valid': True,
                'rows': len(df),
                'columns': list(df.columns)
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f"Validation failed: {str(e)}"
            }
