"""
测试凯利协调器修复效果
验证凯利公式是否能正确计算胜率、盈亏比和仓位
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from coordinators.kelly_position_coordinator import KellyPositionCoordinator


def test_kelly_coordinator_with_real_data():
    """使用真实数据结构测试凯利协调器"""
    
    print("🧪 测试凯利协调器修复效果")
    print("=" * 60)
    
    # 模拟真实的分析结果数据结构
    mock_analysis_result = {
        'fund_code': '518880',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.72
        },
        'final_decision': 'buy',
        'final_confidence': 0.72,
        'technical_data': {
            'buy_signal': True,
            'signal_strength': 'medium',
            'confidence_score': 0.68,
            'technical_indicators': {
                'ma5': 7.4536,
                'ma20': 7.4024,
                'rsi': 55.2,
                'macd': 0.0165
            }
        },
        'gua_data': {
            'is_buy_gua': True,
            'is_sell_gua': False,
            'gua_score': 0.15,
            'main_gua': '需'
        },
        'flow_data': {
            'high_liquidity': True,
            'capital_flow': '净流入',
            'price_data': {
                'price': 7.525,
                'change_rate': 1.2,
                'volume': 8500
            }
        },
        'llm_analysis': {
            'confidence': 0.75,
            'market_sentiment': '乐观'
        }
    }
    
    # 初始化凯利协调器
    kelly_coordinator = KellyPositionCoordinator({
        'enable_display': False,
        'enable_detailed_logging': True,
        'kelly_method': 'fractional',
        'kelly_fraction': 0.25,
        'max_position': 0.25
    })
    
    print("📊 输入数据:")
    print(f"   基金代码: {mock_analysis_result['fund_code']}")
    print(f"   最终决策: {mock_analysis_result['final_decision']}")
    print(f"   技术信号: 买入({mock_analysis_result['technical_data']['signal_strength']})")
    print(f"   卦象信号: {'买入卦' if mock_analysis_result['gua_data']['is_buy_gua'] else '非买入卦'}")
    print(f"   资金流向: {mock_analysis_result['flow_data']['capital_flow']}")
    print(f"   当前价格: {mock_analysis_result['flow_data']['price_data']['price']}")
    
    # 执行凯利计算
    try:
        kelly_result = kelly_coordinator.calculate_kelly_position(mock_analysis_result)
        
        print("\n✅ 凯利计算结果:")
        kelly_calc = kelly_result['kelly_calculation']
        print(f"   胜率: {kelly_calc['win_probability']:.1%}")
        print(f"   盈亏比: {kelly_calc['risk_reward_ratio']:.2f}")
        print(f"   置信度: {kelly_calc['confidence']:.1%}")
        print(f"   凯利分数: {kelly_calc['kelly_fraction']:.2%}")
        print(f"   建议仓位: {kelly_calc['optimal_position']:.2%}")
        print(f"   风险等级: {kelly_calc['risk_level']}")
        
        print(f"\n💡 仓位推理:")
        print(f"   {kelly_result['position_reasoning']}")
        
        # 验证结果是否合理
        if kelly_calc['win_probability'] > 0.5 and kelly_calc['optimal_position'] > 0:
            print("\n🎉 测试成功！凯利公式正确计算了胜率和仓位")
            return True
        else:
            print(f"\n⚠️ 结果异常：胜率={kelly_calc['win_probability']:.1%}, 仓位={kelly_calc['optimal_position']:.2%}")
            return False
            
    except Exception as e:
        print(f"\n❌ 凯利计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_scenarios():
    """测试不同场景下的凯利计算"""
    
    print("\n" + "=" * 60)
    print("🔄 测试不同场景")
    print("=" * 60)
    
    scenarios = [
        {
            'name': '强买入信号',
            'data': {
                'fund_code': '513030',
                'final_decision': 'buy',
                'final_confidence': 0.85,
                'technical_data': {
                    'buy_signal': True,
                    'signal_strength': 'strong',
                    'confidence_score': 0.82
                },
                'gua_data': {
                    'is_buy_gua': True,
                    'gua_score': 0.25
                },
                'flow_data': {
                    'high_liquidity': True,
                    'capital_flow': '净流入',
                    'price_data': {'price': 1.85, 'change_rate': 2.5}
                },
                'llm_analysis': {'confidence': 0.80}
            }
        },
        {
            'name': '观望信号',
            'data': {
                'fund_code': '513500',
                'final_decision': 'hold',
                'final_confidence': 0.55,
                'technical_data': {
                    'buy_signal': False,
                    'signal_strength': 'none',
                    'confidence_score': 0.50
                },
                'gua_data': {
                    'is_buy_gua': False,
                    'is_sell_gua': False,
                    'gua_score': 0.05
                },
                'flow_data': {
                    'high_liquidity': False,
                    'capital_flow': '平衡',
                    'price_data': {'price': 5.12, 'change_rate': 0.1}
                },
                'llm_analysis': {'confidence': 0.52}
            }
        },
        {
            'name': '卖出信号',
            'data': {
                'fund_code': '159567',
                'final_decision': 'sell',
                'final_confidence': 0.70,
                'technical_data': {
                    'buy_signal': False,
                    'signal_strength': 'none',
                    'confidence_score': 0.45
                },
                'gua_data': {
                    'is_buy_gua': False,
                    'is_sell_gua': True,
                    'gua_score': -0.20
                },
                'flow_data': {
                    'high_liquidity': True,
                    'capital_flow': '净流出',
                    'price_data': {'price': 0.95, 'change_rate': -1.8}
                },
                'llm_analysis': {'confidence': 0.68}
            }
        }
    ]
    
    kelly_coordinator = KellyPositionCoordinator({
        'enable_display': False,
        'enable_detailed_logging': False
    })
    
    results = []
    
    for scenario in scenarios:
        print(f"\n📋 场景: {scenario['name']}")
        print("-" * 30)
        
        try:
            kelly_result = kelly_coordinator.calculate_kelly_position(scenario['data'])
            kelly_calc = kelly_result['kelly_calculation']
            
            print(f"   决策: {scenario['data']['final_decision'].upper()}")
            print(f"   胜率: {kelly_calc['win_probability']:.1%}")
            print(f"   仓位: {kelly_calc['optimal_position']:.1%}")
            print(f"   风险: {kelly_calc['risk_level']}")
            
            results.append({
                'scenario': scenario['name'],
                'decision': scenario['data']['final_decision'],
                'win_prob': kelly_calc['win_probability'],
                'position': kelly_calc['optimal_position'],
                'success': True
            })
            
        except Exception as e:
            print(f"   ❌ 计算失败: {e}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': str(e)
            })
    
    return results


def main():
    """主测试函数"""
    
    print("🚀 凯利协调器修复测试")
    print("=" * 80)
    
    # 测试1：基础功能测试
    basic_test_success = test_kelly_coordinator_with_real_data()
    
    # 测试2：多场景测试
    scenario_results = test_different_scenarios()
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    print(f"基础功能测试: {'✅ 通过' if basic_test_success else '❌ 失败'}")
    
    successful_scenarios = len([r for r in scenario_results if r.get('success', False)])
    total_scenarios = len(scenario_results)
    print(f"场景测试: {successful_scenarios}/{total_scenarios} 通过")
    
    if basic_test_success and successful_scenarios == total_scenarios:
        print("\n🎉 所有测试通过！凯利协调器修复成功")
        print("\n💡 现在运行主系统应该能看到正确的凯利分析结果：")
        print("   - 胜率不再是默认的50%")
        print("   - 盈亏比不再是默认的1.00")
        print("   - 凯利仓位会根据实际信号计算")
        print("   - 风险等级会正确评估")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 80)


if __name__ == "__main__":
    main()