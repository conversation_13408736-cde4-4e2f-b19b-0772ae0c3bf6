"""
智能体基类定义
包含所有智能体的基础接口和通用功能
"""

import logging
import sys
import os
from abc import ABC, abstractmethod
from collections import deque
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.utils import *


class BaseAgent(ABC):
    """
    @class BaseAgent
    @brief 智能体基类
    @details 定义智能体的基本接口和通用功能
    """
    
    def __init__(self, name: str, agent_type: str):
        """
        @brief 初始化智能体
        @param name: 智能体名称
        @param agent_type: 智能体类型
        """
        self.name = name
        self.agent_type = agent_type
        self.role = agent_type  # 保持向后兼容
        self.logger = logging.getLogger(f"{self.__class__.__name__}_{name}")
        self.memory = {}
        self.message_queue = deque(maxlen=100)
        
    @abstractmethod
    def process(self, data: Any) -> Any:
        """
        @brief 处理数据的抽象方法
        @param data: 输入数据
        @return: 处理结果
        """
        pass
        
    def communicate(self, message: Dict[str, Any]) -> None:
        """
        @brief 发送消息给其他智能体
        @param message: 消息内容
        """
        self.logger.info(f"Sending message: {message}")
        
    def receive_message(self, message: Dict[str, Any]) -> None:
        """
        @brief 接收来自其他智能体的消息
        @param message: 消息内容
        """
        self.message_queue.append(message)
        self.logger.info(f"Received message: {message}")
        
    def store_in_memory(self, key: str, value: Any) -> None:
        """
        @brief 存储信息到内存
        @param key: 键
        @param value: 值
        """
        self.memory[key] = value
        
    def retrieve_from_memory(self, key: str) -> Optional[Any]:
        """
        @brief 从内存中检索信息
        @param key: 键
        @return: 存储的值，如果不存在则返回None
        """
        return self.memory.get(key)
