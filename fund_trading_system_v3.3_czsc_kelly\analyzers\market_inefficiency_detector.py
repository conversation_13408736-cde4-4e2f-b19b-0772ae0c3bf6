"""
市场无效性检测器
专门用于捕捉短周期市场无效性机会
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from core.data_structures import DimensionEvaluationResult
from core.enums import TrendState, SignalStrength


@dataclass
class InefficiencySignal:
    """市场无效性信号"""
    signal_type: str  # 信号类型
    strength: float  # 信号强度 0-1
    confidence: float  # 置信度 0-1
    expected_duration: int  # 预期持续时间(分钟)
    expected_return: float  # 预期收益率
    risk_level: str  # 风险等级
    entry_price: float  # 建议入场价格
    exit_price: float  # 建议出场价格
    stop_loss: float  # 止损价格
    timestamp: datetime  # 信号时间
    metadata: Dict  # 额外信息


class MarketInefficiencyDetector:
    """市场无效性检测器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 检测器状态
        self.price_history = []
        self.volume_history = []
        self.signal_history = []
        
        # 统计信息
        self.detection_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'false_signals': 0,
            'avg_return': 0.0,
            'detection_accuracy': 0.0
        }
        
        self.logger.info("市场无效性检测器初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            # 价格异常检测
            'price_anomaly': {
                'lookback_window': 20,
                'zscore_threshold': 2.0,
                'min_volume_ratio': 1.5
            },
            
            # 成交量异常检测
            'volume_anomaly': {
                'lookback_window': 15,
                'volume_spike_threshold': 3.0,
                'price_change_threshold': 0.02
            },
            
            # 价差套利检测
            'spread_arbitrage': {
                'correlation_threshold': 0.8,
                'spread_threshold': 0.01,
                'reversion_window': 10
            },
            
            # 动量反转检测
            'momentum_reversal': {
                'momentum_window': 5,
                'reversal_threshold': 0.015,
                'volume_confirmation': True
            },
            
            # 流动性缺口检测
            'liquidity_gap': {
                'bid_ask_threshold': 0.005,
                'depth_ratio_threshold': 0.3,
                'gap_persistence': 3
            },
            
            # 信号过滤
            'signal_filter': {
                'min_confidence': 0.6,
                'min_expected_return': 0.005,
                'max_risk_level': 'medium'
            }
        }
    
    def detect_inefficiencies(self, market_data: pd.DataFrame) -> List[InefficiencySignal]:
        """检测市场无效性"""
        self.logger.info("开始检测市场无效性")
        
        signals = []
        
        try:
            # 更新历史数据
            self._update_history(market_data)
            
            # 1. 价格异常检测
            price_signals = self._detect_price_anomalies(market_data)
            signals.extend(price_signals)
            
            # 2. 成交量异常检测
            volume_signals = self._detect_volume_anomalies(market_data)
            signals.extend(volume_signals)
            
            # 3. 动量反转检测
            momentum_signals = self._detect_momentum_reversals(market_data)
            signals.extend(momentum_signals)
            
            # 4. 流动性缺口检测
            liquidity_signals = self._detect_liquidity_gaps(market_data)
            signals.extend(liquidity_signals)
            
            # 5. 价差套利检测（如果有多个标的）
            if len(market_data.columns) > 5:  # 假设有多个价格列
                spread_signals = self._detect_spread_arbitrage(market_data)
                signals.extend(spread_signals)
            
            # 过滤和排序信号
            filtered_signals = self._filter_signals(signals)
            
            # 更新统计信息
            self._update_stats(filtered_signals)
            
            self.logger.info(f"检测到 {len(filtered_signals)} 个有效的无效性信号")
            
            return filtered_signals
            
        except Exception as e:
            self.logger.error(f"市场无效性检测失败: {e}")
            return []
    
    def _update_history(self, market_data: pd.DataFrame):
        """更新历史数据"""
        if 'close' in market_data.columns:
            self.price_history.extend(market_data['close'].tolist())
            # 保持历史数据长度
            if len(self.price_history) > 1000:
                self.price_history = self.price_history[-1000:]
        
        if 'volume' in market_data.columns:
            self.volume_history.extend(market_data['volume'].tolist())
            if len(self.volume_history) > 1000:
                self.volume_history = self.volume_history[-1000:]
    
    def _detect_price_anomalies(self, data: pd.DataFrame) -> List[InefficiencySignal]:
        """检测价格异常"""
        signals = []
        
        if len(data) < self.config['price_anomaly']['lookback_window']:
            return signals
        
        try:
            # 计算价格的Z-score
            window = self.config['price_anomaly']['lookback_window']
            prices = data['close'].rolling(window).mean()
            price_std = data['close'].rolling(window).std()
            z_scores = (data['close'] - prices) / price_std
            
            # 检测异常点
            threshold = self.config['price_anomaly']['zscore_threshold']
            anomalies = np.abs(z_scores) > threshold
            
            for idx in data.index[anomalies]:
                if pd.isna(z_scores.loc[idx]):
                    continue
                
                current_price = data.loc[idx, 'close']
                z_score = z_scores.loc[idx]
                
                # 判断信号方向
                if z_score > 0:  # 价格过高，预期回落
                    signal_type = "price_reversion_short"
                    expected_return = -abs(z_score) * 0.01
                    exit_price = current_price * 0.98
                    stop_loss = current_price * 1.02
                else:  # 价格过低，预期上涨
                    signal_type = "price_reversion_long"
                    expected_return = abs(z_score) * 0.01
                    exit_price = current_price * 1.02
                    stop_loss = current_price * 0.98
                
                # 成交量确认
                volume_ratio = 1.0
                if 'volume' in data.columns and len(self.volume_history) > window:
                    avg_volume = np.mean(self.volume_history[-window:])
                    current_volume = data.loc[idx, 'volume']
                    volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
                
                # 只有成交量放大才认为是有效信号
                if volume_ratio >= self.config['price_anomaly']['min_volume_ratio']:
                    signal = InefficiencySignal(
                        signal_type=signal_type,
                        strength=min(abs(z_score) / 3.0, 1.0),
                        confidence=min(volume_ratio / 3.0, 0.9),
                        expected_duration=30,  # 30分钟
                        expected_return=expected_return,
                        risk_level='medium',
                        entry_price=current_price,
                        exit_price=exit_price,
                        stop_loss=stop_loss,
                        timestamp=datetime.now(),
                        metadata={
                            'z_score': z_score,
                            'volume_ratio': volume_ratio,
                            'detection_method': 'price_anomaly'
                        }
                    )
                    signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"价格异常检测失败: {e}")
        
        return signals
    
    def _detect_volume_anomalies(self, data: pd.DataFrame) -> List[InefficiencySignal]:
        """检测成交量异常"""
        signals = []
        
        if 'volume' not in data.columns or len(data) < self.config['volume_anomaly']['lookback_window']:
            return signals
        
        try:
            window = self.config['volume_anomaly']['lookback_window']
            avg_volume = data['volume'].rolling(window).mean()
            volume_spikes = data['volume'] / avg_volume
            
            threshold = self.config['volume_anomaly']['volume_spike_threshold']
            price_threshold = self.config['volume_anomaly']['price_change_threshold']
            
            # 计算价格变化
            price_change = data['close'].pct_change()
            
            # 检测成交量异常且价格变化较小的情况
            anomalies = (volume_spikes > threshold) & (np.abs(price_change) < price_threshold)
            
            for idx in data.index[anomalies]:
                if pd.isna(volume_spikes.loc[idx]):
                    continue
                
                current_price = data.loc[idx, 'close']
                volume_spike = volume_spikes.loc[idx]
                price_chg = price_change.loc[idx]
                
                # 成交量异常但价格变化小，可能是信息不对称
                signal_type = "volume_price_divergence"
                
                # 根据最近价格趋势判断方向
                recent_trend = data['close'].iloc[-3:].pct_change().mean()
                if recent_trend > 0:
                    expected_return = 0.01
                    exit_price = current_price * 1.01
                    stop_loss = current_price * 0.99
                else:
                    expected_return = -0.01
                    exit_price = current_price * 0.99
                    stop_loss = current_price * 1.01
                
                signal = InefficiencySignal(
                    signal_type=signal_type,
                    strength=min(volume_spike / 5.0, 1.0),
                    confidence=0.7,
                    expected_duration=15,  # 15分钟
                    expected_return=expected_return,
                    risk_level='low',
                    entry_price=current_price,
                    exit_price=exit_price,
                    stop_loss=stop_loss,
                    timestamp=datetime.now(),
                    metadata={
                        'volume_spike': volume_spike,
                        'price_change': price_chg,
                        'recent_trend': recent_trend,
                        'detection_method': 'volume_anomaly'
                    }
                )
                signals.append(signal)
                
        except Exception as e:
            self.logger.error(f"成交量异常检测失败: {e}")
        
        return signals
    
    def _detect_momentum_reversals(self, data: pd.DataFrame) -> List[InefficiencySignal]:
        """检测动量反转"""
        signals = []
        
        if len(data) < self.config['momentum_reversal']['momentum_window']:
            return signals
        
        try:
            window = self.config['momentum_reversal']['momentum_window']
            threshold = self.config['momentum_reversal']['reversal_threshold']
            
            # 计算短期动量
            momentum = data['close'].pct_change(window)
            
            # 检测极端动量
            extreme_momentum = np.abs(momentum) > threshold
            
            for idx in data.index[extreme_momentum]:
                if pd.isna(momentum.loc[idx]):
                    continue
                
                current_price = data.loc[idx, 'close']
                mom_value = momentum.loc[idx]
                
                # 动量反转信号
                if mom_value > 0:  # 强势上涨后可能回调
                    signal_type = "momentum_reversal_short"
                    expected_return = -mom_value * 0.3  # 预期回调30%的涨幅
                    exit_price = current_price * (1 + expected_return)
                    stop_loss = current_price * 1.01
                else:  # 强势下跌后可能反弹
                    signal_type = "momentum_reversal_long"
                    expected_return = -mom_value * 0.3  # 预期反弹30%的跌幅
                    exit_price = current_price * (1 + expected_return)
                    stop_loss = current_price * 0.99
                
                # 成交量确认（如果配置要求）
                volume_confirmed = True
                if self.config['momentum_reversal']['volume_confirmation'] and 'volume' in data.columns:
                    if len(self.volume_history) > window:
                        avg_volume = np.mean(self.volume_history[-window:])
                        current_volume = data.loc[idx, 'volume']
                        volume_confirmed = current_volume > avg_volume
                
                if volume_confirmed:
                    signal = InefficiencySignal(
                        signal_type=signal_type,
                        strength=min(abs(mom_value) / 0.05, 1.0),
                        confidence=0.75,
                        expected_duration=60,  # 60分钟
                        expected_return=expected_return,
                        risk_level='medium',
                        entry_price=current_price,
                        exit_price=exit_price,
                        stop_loss=stop_loss,
                        timestamp=datetime.now(),
                        metadata={
                            'momentum': mom_value,
                            'volume_confirmed': volume_confirmed,
                            'detection_method': 'momentum_reversal'
                        }
                    )
                    signals.append(signal)
                    
        except Exception as e:
            self.logger.error(f"动量反转检测失败: {e}")
        
        return signals
    
    def _detect_liquidity_gaps(self, data: pd.DataFrame) -> List[InefficiencySignal]:
        """检测流动性缺口"""
        signals = []
        
        # 这里需要更详细的订单簿数据，暂时用价格跳空来模拟
        try:
            # 检测价格跳空
            price_gaps = data['open'] - data['close'].shift(1)
            gap_ratio = price_gaps / data['close'].shift(1)
            
            significant_gaps = np.abs(gap_ratio) > 0.01  # 1%以上的跳空
            
            for idx in data.index[significant_gaps]:
                if pd.isna(gap_ratio.loc[idx]):
                    continue
                
                current_price = data.loc[idx, 'close']
                gap_value = gap_ratio.loc[idx]
                
                # 跳空回补信号
                if gap_value > 0:  # 向上跳空，预期回补
                    signal_type = "gap_fill_short"
                    expected_return = -gap_value * 0.5  # 预期回补一半
                    exit_price = current_price * (1 + expected_return)
                    stop_loss = current_price * 1.015
                else:  # 向下跳空，预期回补
                    signal_type = "gap_fill_long"
                    expected_return = -gap_value * 0.5
                    exit_price = current_price * (1 + expected_return)
                    stop_loss = current_price * 0.985
                
                signal = InefficiencySignal(
                    signal_type=signal_type,
                    strength=min(abs(gap_value) / 0.03, 1.0),
                    confidence=0.65,
                    expected_duration=120,  # 120分钟
                    expected_return=expected_return,
                    risk_level='low',
                    entry_price=current_price,
                    exit_price=exit_price,
                    stop_loss=stop_loss,
                    timestamp=datetime.now(),
                    metadata={
                        'gap_ratio': gap_value,
                        'detection_method': 'liquidity_gap'
                    }
                )
                signals.append(signal)
                
        except Exception as e:
            self.logger.error(f"流动性缺口检测失败: {e}")
        
        return signals
    
    def _detect_spread_arbitrage(self, data: pd.DataFrame) -> List[InefficiencySignal]:
        """检测价差套利机会"""
        signals = []
        
        # 这里需要多个相关资产的数据
        # 暂时跳过实现，因为需要更复杂的数据结构
        
        return signals
    
    def _filter_signals(self, signals: List[InefficiencySignal]) -> List[InefficiencySignal]:
        """过滤信号"""
        filtered = []
        
        filter_config = self.config['signal_filter']
        
        for signal in signals:
            # 置信度过滤
            if signal.confidence < filter_config['min_confidence']:
                continue
            
            # 预期收益过滤
            if abs(signal.expected_return) < filter_config['min_expected_return']:
                continue
            
            # 风险等级过滤
            risk_levels = ['low', 'medium', 'high']
            max_risk_idx = risk_levels.index(filter_config['max_risk_level'])
            signal_risk_idx = risk_levels.index(signal.risk_level)
            if signal_risk_idx > max_risk_idx:
                continue
            
            filtered.append(signal)
        
        # 按强度和置信度排序
        filtered.sort(key=lambda x: x.strength * x.confidence, reverse=True)
        
        return filtered
    
    def _update_stats(self, signals: List[InefficiencySignal]):
        """更新统计信息"""
        self.detection_stats['total_signals'] += len(signals)
        
        # 这里需要实际的交易结果来更新成功率等统计
        # 暂时使用模拟数据
        if signals:
            avg_expected_return = np.mean([s.expected_return for s in signals])
            self.detection_stats['avg_return'] = (
                self.detection_stats['avg_return'] * 0.9 + avg_expected_return * 0.1
            )
    
    def get_detection_statistics(self) -> Dict:
        """获取检测统计信息"""
        return self.detection_stats.copy()
    
    def update_signal_result(self, signal_id: str, actual_return: float, success: bool):
        """更新信号结果（用于学习和改进）"""
        if success:
            self.detection_stats['successful_signals'] += 1
        else:
            self.detection_stats['false_signals'] += 1
        
        # 更新准确率
        total_evaluated = self.detection_stats['successful_signals'] + self.detection_stats['false_signals']
        if total_evaluated > 0:
            self.detection_stats['detection_accuracy'] = (
                self.detection_stats['successful_signals'] / total_evaluated
            )
        
        self.logger.info(f"信号结果更新: 成功={success}, 实际收益={actual_return:.4f}")