# 核心模块 API 文档

## 枚举类型 (core.enums)

### TrendState
趋势状态枚举，定义市场趋势的不同状态。

```python
from core.enums import TrendState

# 可用状态
TrendState.STRONG_UPTREND     # 强上涨趋势
TrendState.WEAK_UPTREND       # 弱上涨趋势  
TrendState.SIDEWAYS           # 横盘整理
TrendState.WEAK_DOWNTREND     # 弱下跌趋势
TrendState.STRONG_DOWNTREND   # 强下跌趋势
```

### VolatilityState
波动性状态枚举，定义市场波动性的不同级别。

```python
from core.enums import VolatilityState

# 可用状态
VolatilityState.EXTREMELY_LOW   # 极低波动
VolatilityState.LOW            # 低波动
VolatilityState.NORMAL         # 正常波动
VolatilityState.HIGH           # 高波动
VolatilityState.EXTREMELY_HIGH # 极高波动
```

### SignalStrength
信号强度枚举，定义交易信号的强度级别。

```python
from core.enums import SignalStrength

# 可用强度
SignalStrength.VERY_WEAK   # 0.1 - 非常弱
SignalStrength.WEAK        # 0.3 - 弱
SignalStrength.MEDIUM      # 0.5 - 中等
SignalStrength.STRONG      # 0.7 - 强
SignalStrength.VERY_STRONG # 0.9 - 非常强
```

## 数据结构 (core.data_structures)

### DimensionEvaluationResult
维度评估结果数据结构，用于存储单个维度的评估结果。

```python
from core.data_structures import DimensionEvaluationResult
from core.enums import TrendState

# 创建评估结果
result = DimensionEvaluationResult(
    dimension_name="趋势",                    # 维度名称
    state=TrendState.STRONG_UPTREND,         # 状态
    score=0.8,                               # 评分 (0-1)
    confidence=0.9,                          # 置信度 (0-1)
    signals=["上涨信号", "强势信号"],          # 信号列表
    data_quality="good",                     # 数据质量
    details={"ma_trend": "上涨"},             # 详细信息
    indicators={"ma5": 100, "ma20": 95}      # 技术指标
)

# 访问属性
print(result.dimension_name)  # "趋势"
print(result.score)           # 0.8
print(result.confidence)      # 0.9
```

### 属性说明

| 属性 | 类型 | 说明 |
|------|------|------|
| dimension_name | str | 维度名称 |
| state | Enum | 维度状态 |
| score | float | 评分 (0-1) |
| confidence | float | 置信度 (0-1) |
| signals | List[str] | 信号列表 |
| data_quality | str | 数据质量 |
| details | Dict | 详细信息 |
| indicators | Dict | 技术指标 |

## 工具函数 (core.utils)

### 可用性检查

```python
from core.utils import CZSC_FUNC_AVAILABLE, PUPPET_AVAILABLE, CZSC_AVAILABLE

# 检查功能可用性
if CZSC_FUNC_AVAILABLE:
    print("CZSC函数可用")
    
if PUPPET_AVAILABLE:
    print("Puppet交易库可用")
    
if CZSC_AVAILABLE:
    print("CZSC库可用")
```

### 常用工具函数

```python
from core.utils import *

# 获取实时报价 (需要CZSC函数可用)
if CZSC_FUNC_AVAILABLE:
    quote = get_realtime_quote('513500')
    
# 获取K线数据 (需要CZSC函数可用)
if CZSC_FUNC_AVAILABLE:
    kline = get_kline('513500', 'D')
```

## 回测增强模块 (backtest_modules)

### AdvancedBacktestSystem
高级回测系统，提供完整的回测流程和信号质量评估。

```python
from backtest_modules.advanced_backtest_system import AdvancedBacktestSystem

# 初始化回测系统
backtest_system = AdvancedBacktestSystem({
    'data_preprocessor': {
        'missing_strategy': 'interpolate',
        'outlier_threshold': 3.0
    },
    'feature_engineer': {
        'correlation_threshold': 0.8,
        'variance_threshold': 0.01
    },
    'backtest_engine': {
        'initial_capital': 100000,
        'transaction_cost': 0.001,
        'signal_method': 'threshold',
        'signal_threshold': 0.6
    }
})

# 运行完整回测流程
results = backtest_system.run_complete_pipeline(
    df, 
    target_col='future_return',
    time_col='dt',
    price_col='close'
)
```

### SignalQualityEnhancer
信号质量增强器，结合机器学习提升CZSC信号质量。

```python
from backtest_modules.signal_quality_enhancer import SignalQualityEnhancer
from core.data_structures import DimensionEvaluationResult

class SignalQualityEnhancer:
    def __init__(self, backtest_system):
        self.backtest_system = backtest_system
        self.feature_engineer = backtest_system.feature_engineer
        self.model_trainer = backtest_system.model_trainer
        
    def enhance_czsc_signals(self, df, czsc_results):
        """增强CZSC信号质量"""
        # 1. 特征工程增强
        df_featured = self.feature_engineer.create_technical_features(df)
        df_featured = self.feature_engineer.create_statistical_features(df_featured)
        
        # 2. 信号质量评估
        signal_quality_scores = self._evaluate_signal_quality(czsc_results, df_featured)
        
        # 3. 机器学习过滤
        enhanced_results = self._ml_signal_filter(czsc_results, signal_quality_scores)
        
        return enhanced_results
        
    def _evaluate_signal_quality(self, czsc_results, df_featured):
        """评估信号质量"""
        quality_scores = []
        
        for result in czsc_results:
            # 计算技术指标一致性
            technical_consistency = self._check_technical_consistency(result, df_featured)
            
            # 计算历史成功率
            historical_success = self._calculate_historical_success_rate(result)
            
            # 综合质量评分
            quality_score = (technical_consistency * 0.6 + historical_success * 0.4)
            quality_scores.append(quality_score)
            
        return quality_scores
```

### RiskEnhancedEvaluationResult
增强的评估结果，集成风险管理。

```python
from backtest_modules.risk_manager import RiskManager
from core.data_structures import DimensionEvaluationResult

class RiskEnhancedEvaluationResult(DimensionEvaluationResult):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.risk_metrics = {}
        self.position_size_recommendation = 0.0
        self.stop_loss_level = 0.0
        self.take_profit_level = 0.0
        
    def apply_risk_management(self, risk_manager: RiskManager, 
                            current_price: float, portfolio_value: float):
        """应用风险管理"""
        # 验证仓位大小
        is_valid, adjusted_size = risk_manager.validate_position_size(
            self.dimension_name, 
            self.score * portfolio_value * 0.1,  # 基于信号强度的初始仓位
            portfolio_value
        )
        
        self.position_size_recommendation = adjusted_size / portfolio_value
        
        # 设置止损止盈
        if self.state.name.endswith('UPTREND'):
            self.stop_loss_level = current_price * (1 - risk_manager.stop_loss_pct)
            self.take_profit_level = current_price * (1 + risk_manager.take_profit_pct)
        elif self.state.name.endswith('DOWNTREND'):
            self.stop_loss_level = current_price * (1 + risk_manager.stop_loss_pct)
            self.take_profit_level = current_price * (1 - risk_manager.take_profit_pct)
            
        return is_valid
```

## 配置 (core.config)

### 日志配置

```python
from core.config import setup_logging

# 设置日志
setup_logging()
```

### 配置参数

系统支持以下配置参数:
- 日志级别
- 输出格式
- 文件路径
- 缓存设置
- 回测模块配置
- 风险管理参数

## 使用示例

### 基本使用

```python
from core.enums import TrendState, SignalStrength
from core.data_structures import DimensionEvaluationResult

# 创建趋势评估结果
trend_result = DimensionEvaluationResult(
    dimension_name="趋势",
    state=TrendState.STRONG_UPTREND,
    score=0.85,
    confidence=0.92,
    signals=["MA5上穿MA20", "MACD金叉"],
    data_quality="excellent",
    details={
        "ma_trend": "强势上涨",
        "macd_signal": "金叉确认"
    },
    indicators={
        "ma5": 102.5,
        "ma20": 98.3,
        "macd": 0.45
    }
)

print(f"维度: {trend_result.dimension_name}")
print(f"状态: {trend_result.state.value}")
print(f"评分: {trend_result.score}")
print(f"置信度: {trend_result.confidence}")
```

### 集成回测模块增强信号质量

```python
import pandas as pd
from backtest_modules.advanced_backtest_system import AdvancedBacktestSystem
from backtest_modules.signal_quality_enhancer import SignalQualityEnhancer
from backtest_modules.risk_manager import RiskManager
from core.data_structures import DimensionEvaluationResult
from core.enums import TrendState

# 1. 初始化增强系统
def initialize_enhanced_system():
    """初始化增强的交易系统"""
    
    # 配置回测系统
    config = {
        'data_preprocessor': {
            'missing_strategy': 'interpolate',
            'outlier_threshold': 3.0
        },
        'feature_engineer': {
            'correlation_threshold': 0.8,
            'variance_threshold': 0.01
        },
        'backtest_engine': {
            'initial_capital': 100000,
            'transaction_cost': 0.001,
            'signal_method': 'threshold',
            'signal_threshold': 0.6
        },
        'risk_manager': {
            'max_position_size': 0.1,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.15,
            'max_drawdown_limit': 0.2
        }
    }
    
    # 初始化系统组件
    backtest_system = AdvancedBacktestSystem(config)
    signal_enhancer = SignalQualityEnhancer(backtest_system)
    risk_manager = RiskManager(**config['risk_manager'])
    
    return backtest_system, signal_enhancer, risk_manager

# 2. 增强CZSC信号质量
def enhance_czsc_signals_with_ml(df, czsc_results, signal_enhancer):
    """使用机器学习增强CZSC信号"""
    
    # 数据预处理
    df_processed, quality_report = signal_enhancer.backtest_system.data_preprocessor.preprocess_pipeline(df)
    
    # 特征工程
    df_featured, feature_report = signal_enhancer.feature_engineer.feature_engineering_pipeline(
        df_processed, 
        target_col='future_return',
        price_col='close',
        volume_col='volume'
    )
    
    # 增强信号质量
    enhanced_results = signal_enhancer.enhance_czsc_signals(df_featured, czsc_results)
    
    return enhanced_results, df_featured, quality_report

# 3. 应用风险管理
def apply_risk_management_to_signals(enhanced_results, risk_manager, current_price, portfolio_value):
    """为增强信号应用风险管理"""
    
    risk_enhanced_results = []
    
    for result in enhanced_results:
        # 创建风险增强结果
        risk_result = RiskEnhancedEvaluationResult(
            dimension_name=result.dimension_name,
            state=result.state,
            score=result.score,
            confidence=result.confidence,
            signals=result.signals,
            data_quality=result.data_quality,
            details=result.details,
            indicators=result.indicators
        )
        
        # 应用风险管理
        is_valid = risk_result.apply_risk_management(risk_manager, current_price, portfolio_value)
        
        if is_valid:
            risk_enhanced_results.append(risk_result)
    
    return risk_enhanced_results

# 4. 完整的信号增强流程
def complete_signal_enhancement_pipeline(fund_code, start_date, end_date):
    """完整的信号增强流程"""
    
    # 初始化系统
    backtest_system, signal_enhancer, risk_manager = initialize_enhanced_system()
    
    # 获取历史数据 (假设有数据获取函数)
    df = get_fund_historical_data(fund_code, start_date, end_date)
    
    # 运行原始CZSC分析 (假设有CZSC分析函数)
    original_czsc_results = run_czsc_analysis(df)
    
    # 增强信号质量
    enhanced_results, df_featured, quality_report = enhance_czsc_signals_with_ml(
        df, original_czsc_results, signal_enhancer
    )
    
    # 应用风险管理
    current_price = df['close'].iloc[-1]
    portfolio_value = 100000  # 假设组合价值
    
    final_results = apply_risk_management_to_signals(
        enhanced_results, risk_manager, current_price, portfolio_value
    )
    
    # 回测验证
    backtest_results = backtest_system.run_complete_pipeline(
        df_featured,
        target_col='future_return',
        time_col='dt',
        price_col='close'
    )
    
    return {
        'enhanced_signals': final_results,
        'backtest_results': backtest_results,
        'data_quality_report': quality_report,
        'performance_metrics': backtest_results['pipeline_results']['backtest']['metrics']
    }

# 5. 实时信号质量监控
class RealTimeSignalMonitor:
    """实时信号质量监控"""
    
    def __init__(self, backtest_system, signal_enhancer, risk_manager):
        self.backtest_system = backtest_system
        self.signal_enhancer = signal_enhancer
        self.risk_manager = risk_manager
        self.signal_history = []
        
    def evaluate_real_time_signal(self, current_data, czsc_signal):
        """评估实时信号质量"""
        
        # 特征提取
        features = self.signal_enhancer.feature_engineer.create_technical_features(current_data)
        
        # 信号质量评估
        quality_score = self._calculate_signal_quality(czsc_signal, features)
        
        # 风险评估
        risk_assessment = self.risk_manager.assess_portfolio_risk(
            current_data['portfolio_value'], 
            current_data['positions']
        )
        
        # 综合评估
        final_score = quality_score * (1 - risk_assessment['risk_level'])
        
        # 记录信号历史
        self.signal_history.append({
            'timestamp': current_data['timestamp'],
            'signal': czsc_signal,
            'quality_score': quality_score,
            'risk_level': risk_assessment['risk_level'],
            'final_score': final_score
        })
        
        return {
            'signal_valid': final_score > 0.6,
            'quality_score': quality_score,
            'risk_assessment': risk_assessment,
            'final_score': final_score,
            'recommendations': self._generate_recommendations(final_score, risk_assessment)
        }
    
    def _calculate_signal_quality(self, signal, features):
        """计算信号质量分数"""
        # 技术指标一致性检查
        consistency_score = self._check_technical_consistency(signal, features)
        
        # 历史成功率
        historical_success = self._get_historical_success_rate(signal)
        
        # 市场环境适应性
        market_fit = self._assess_market_environment_fit(signal, features)
        
        return (consistency_score * 0.4 + historical_success * 0.3 + market_fit * 0.3)
    
    def _generate_recommendations(self, final_score, risk_assessment):
        """生成交易建议"""
        recommendations = []
        
        if final_score > 0.8:
            recommendations.append("信号质量优秀，建议执行交易")
        elif final_score > 0.6:
            recommendations.append("信号质量良好，可考虑小仓位试探")
        else:
            recommendations.append("信号质量不佳，建议观望")
            
        if risk_assessment['risk_level'] > 0.7:
            recommendations.append("当前风险较高，建议降低仓位")
            
        return recommendations

# 使用示例
if __name__ == "__main__":
    # 运行完整的信号增强流程
    results = complete_signal_enhancement_pipeline('513500', '2023-01-01', '2024-01-01')
    
    print("=== 信号增强结果 ===")
    for signal in results['enhanced_signals']:
        print(f"维度: {signal.dimension_name}")
        print(f"信号强度: {signal.score:.2f}")
        print(f"建议仓位: {signal.position_size_recommendation:.2%}")
        print(f"止损位: {signal.stop_loss_level:.2f}")
        print(f"止盈位: {signal.take_profit_level:.2f}")
        print("-" * 40)
    
    print("=== 回测性能 ===")
    metrics = results['performance_metrics']
    print(f"年化收益率: {metrics.get('annualized_return', 0):.2%}")
    print(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
    print(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}")
    print(f"胜率: {metrics.get('win_rate', 0):.2%}")
```

### 错误处理

```python
try:
    # 执行分析
    result = some_analysis_function()
except Exception as e:
    logger.error(f"分析失败: {str(e)}")
    # 返回默认结果
    result = DimensionEvaluationResult(
        dimension_name="未知",
        state=TrendState.SIDEWAYS,
        score=0.0,
        confidence=0.0,
        signals=[],
        data_quality="poor",
        details={"error": str(e)},
        indicators={}
    )
```
