"""
核心枚举类型定义
包含系统中使用的所有枚举状态类型
"""

from enum import Enum


class TrendState(Enum):
    """趋势状态枚举"""
    STRONG_UPTREND = "strong_uptrend"
    WEAK_UPTREND = "weak_uptrend"
    SIDEWAYS = "sideways"
    WEAK_DOWNTREND = "weak_downtrend"
    STRONG_DOWNTREND = "strong_downtrend"


class VolatilityState(Enum):
    """波动性状态枚举"""
    EXTREMELY_LOW = "extremely_low"
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    EXTREMELY_HIGH = "extremely_high"


class LiquidityState(Enum):
    """流动性状态枚举"""
    EXTREMELY_LOW = "extremely_low"
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    SUPER_HIGH = "super_high"


class SentimentState(Enum):
    """情绪状态枚举"""
    EXTREME_PANIC = "extreme_panic"
    PANIC = "panic"
    NEUTRAL = "neutral"
    GREED = "greed"
    EXTREME_GREED = "extreme_greed"


class StructuralState(Enum):
    """结构状态枚举"""
    BREAKDOWN = "breakdown"
    WEAKENING = "weakening"
    STABLE = "stable"
    STRENGTHENING = "strengthening"
    BREAKOUT = "breakout"


class TransitionState(Enum):
    """转换状态枚举"""
    NO_TRANSITION = "no_transition"
    EARLY_TRANSITION = "early_transition"
    MID_TRANSITION = "mid_transition"
    LATE_TRANSITION = "late_transition"
    TRANSITION_COMPLETE = "transition_complete"


class SignalStrength(Enum):
    """信号强度枚举"""
    VERY_WEAK = 0.1
    WEAK = 0.3
    MEDIUM = 0.5
    STRONG = 0.7
    VERY_STRONG = 0.9


class MarketRegime(Enum):
    """市场状态枚举"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
