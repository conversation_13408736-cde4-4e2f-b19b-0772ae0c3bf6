# -*- coding: utf-8 -*-
"""
author: zengbin93
email: <EMAIL>
create_dt: 2023/11/20
describe: 同花顺Puppet连接器，用于实现CZSC与同花顺客户端的自动化交易
"""
import os
import time
import pandas as pd
from typing import Dict, List, Union, Optional
import loguru
from datetime import datetime

# 导入CZSC相关模块
from czsc.traders.base import CzscTrader
from czsc.objects import RawBar, Position
from czsc.utils import get_trade_date

# 导入Puppet模块
try:
    from puppet import Account, login
except ImportError:
    raise ImportError("请安装puppet库: pip install puppet")


class PuppetConnector:
    """CZSC与Puppet的连接器，用于实现同花顺实时交易"""

    def __init__(self, 
                 acc_info: Dict, 
                 trader: Optional[CzscTrader] = None,
                 **kwargs):
        """
        初始化连接器
        
        :param acc_info: 账户信息，包含同花顺登录所需的信息，格式如下：
            {
                'account_no': '资金账号',
                'password': '交易密码',
                'comm_password': '通讯密码',
                'client_path': '同花顺客户端路径'
            }
        :param trader: CZSC交易员对象
        :param kwargs: 其他参数
            - logger: 日志对象
            - check_interval: 检查信号间隔(秒)
        """
        self.logger = kwargs.get("logger", loguru.logger)
        self.check_interval = kwargs.get("check_interval", 60)  # 默认60秒检查一次
        
        # 初始化同花顺账户
        self.account = login(acc_info)
        self.logger.info(f"同花顺账户登录成功: {self.account}")
        
        # 初始化CZSC交易员
        self.trader = trader
        self.last_check_time = datetime.now()
        self.running = False
    
    def set_trader(self, trader: CzscTrader):
        """设置CZSC交易员
        
        :param trader: CZSC交易员对象
        """
        self.trader = trader
        self.logger.info(f"设置交易员: {trader}")
    
    def get_account_info(self) -> Dict:
        """获取账户信息
        
        :return: 账户信息字典
        """
        balance = self.account.balance
        return {
            "cash": balance['可用金额'],
            "total_assets": balance['总资产'],
            "market_value": balance['最新市值'],
            "frozen": balance['冻结金额'],
        }
    
    def get_positions(self) -> List[Dict]:
        """获取当前持仓
        
        :return: 持仓列表
        """
        positions = self.account.position
        return positions
    
    def execute_trade(self, symbol: str, direction: str, price: float, volume: int) -> Dict:
        """执行交易
        
        :param symbol: 股票代码
        :param direction: 交易方向，'buy'或'sell'
        :param price: 交易价格
        :param volume: 交易数量
        :return: 交易结果
        """
        try:
            if direction.lower() == 'buy':
                result = self.account.buy(symbol, price, volume)
            elif direction.lower() == 'sell':
                result = self.account.sell(symbol, price, volume)
            else:
                raise ValueError(f"不支持的交易方向: {direction}")
            
            self.logger.info(f"执行交易: {direction} {symbol} {volume}股 @ {price}, 结果: {result}")
            return result
        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")
            return {"status": "error", "message": str(e)}
    
    def process_signals(self):
        """处理CZSC交易员的信号并执行交易"""
        if not self.trader:
            self.logger.warning("未设置交易员，无法处理信号")
            return
        
        # 获取当前持仓
        current_positions = self.get_positions()
        current_symbols = {p['证券代码']: p for p in current_positions}
        
        # 获取交易员的仓位建议
        ensemble_pos = self.trader.get_ensemble_pos(method="vote")  # 使用投票法确定仓位
        
        # 如果交易员有仓位变化，执行交易
        if self.trader.pos_changed:
            symbol = self.trader.symbol
            latest_price = self.trader.latest_price
            
            # 根据仓位信号决定买入或卖出
            if ensemble_pos > 0 and symbol not in current_symbols:
                # 计算买入数量
                cash = self.get_account_info()['cash']
                volume = int((cash * 0.1) // (latest_price * 100)) * 100  # 使用10%资金，确保为100的整数倍
                
                if volume >= 100:  # 最小交易单位为100股
                    self.execute_trade(symbol, 'buy', latest_price, volume)
            
            elif ensemble_pos <= 0 and symbol in current_symbols:
                # 卖出持有的股票
                position = current_symbols[symbol]
                volume = int(position['可用余额'])
                
                if volume > 0:
                    self.execute_trade(symbol, 'sell', latest_price, volume)
    
    def start(self):
        """启动连接器，开始实时交易"""
        self.running = True
        self.logger.info("启动CZSC-Puppet连接器，开始实时交易")
        
        while self.running:
            try:
                now = datetime.now()
                # 检查是否是交易时间
                if self._is_trading_time(now):
                    # 检查是否到了检查信号的时间
                    if (now - self.last_check_time).total_seconds() >= self.check_interval:
                        self.process_signals()
                        self.last_check_time = now
                
                time.sleep(1)  # 避免CPU占用过高
            except Exception as e:
                self.logger.error(f"实时交易过程中发生错误: {e}")
                time.sleep(5)  # 发生错误后等待一段时间再继续
    
    def stop(self):
        """停止连接器"""
        self.running = False
        self.logger.info("停止CZSC-Puppet连接器")
    
    def _is_trading_time(self, dt: datetime) -> bool:
        """判断是否是交易时间
        
        :param dt: 日期时间
        :return: 是否是交易时间
        """
        # 判断是否是交易日
        if not get_trade_date(dt.strftime('%Y%m%d')):
            return False
        
        # 判断是否在交易时间段内
        hour, minute = dt.hour, dt.minute
        time_int = hour * 100 + minute
        
        # 上午9:30-11:30，下午13:00-15:00
        return (930 <= time_int <= 1130) or (1300 <= time_int <= 1500)


def create_puppet_connector(acc_info: Dict, trader: CzscTrader = None, **kwargs) -> PuppetConnector:
    """创建同花顺Puppet连接器
    
    :param acc_info: 账户信息，包含同花顺登录所需的信息
    :param trader: CZSC交易员对象
    :param kwargs: 其他参数
    :return: PuppetConnector对象
    """
    return PuppetConnector(acc_info=acc_info, trader=trader, **kwargs)