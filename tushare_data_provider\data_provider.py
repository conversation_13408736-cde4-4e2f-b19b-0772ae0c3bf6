"""
Tushare数据提供器主类
实现50维度数据获取功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Union
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 使用项目中的dataNew作为数据入口
from dataNew import ts_pro

from .config import DataConfig, DEFAULT_CONFIG
from .data_structures import *
from .technical_calculator import TechnicalCalculator


class TushareDataProvider:
    """Tushare数据提供器"""
    
    def __init__(self, config: DataConfig = None):
        """初始化数据提供器"""
        self.config = config or DEFAULT_CONFIG
        self.pro = ts_pro()  # 使用项目统一的数据入口
        self.logger = self._setup_logger()
        self.cache = {}  # 简单内存缓存
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('TushareDataProvider')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _get_cache_key(self, method: str, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [method]
        for k, v in sorted(kwargs.items()):
            if isinstance(v, list):
                v = ','.join(map(str, v))
            key_parts.append(f"{k}={v}")
        return '|'.join(key_parts)
    
    def _is_cache_valid(self, cache_time: datetime) -> bool:
        """检查缓存是否有效"""
        if not self.config.enable_cache:
            return False
        expire_time = timedelta(hours=self.config.cache_expire_hours)
        return datetime.now() - cache_time < expire_time
    
    def _request_with_retry(self, func, *args, **kwargs) -> Optional[pd.DataFrame]:
        """带重试的请求"""
        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.request_delay)
                result = func(*args, **kwargs)
                if result is not None and not result.empty:
                    return result
                else:
                    self.logger.warning(f"Empty result on attempt {attempt + 1}")
            except Exception as e:
                self.logger.warning(f"Request failed on attempt {attempt + 1}: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    self.logger.error(f"All retry attempts failed: {e}")
        return None
    
    def get_basic_data(self, ts_codes: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """获取基础市场数据"""
        self.logger.info(f"获取基础数据: {len(ts_codes)} 只股票, {start_date} 到 {end_date}")
        
        results = {}
        
        for ts_code in ts_codes:
            try:
                # 检查缓存
                cache_key = self._get_cache_key('daily', ts_code=ts_code, 
                                              start_date=start_date, end_date=end_date)
                
                if cache_key in self.cache:
                    cache_data, cache_time = self.cache[cache_key]
                    if self._is_cache_valid(cache_time):
                        results[ts_code] = cache_data
                        continue
                
                # 获取日线数据
                df = self._request_with_retry(
                    self.pro.daily,
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if df is not None and not df.empty:
                    # 数据预处理
                    df = df.sort_values('trade_date').reset_index(drop=True)
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                    
                    # 计算额外字段
                    df['volume_ratio'] = df['vol'] / df['vol'].rolling(5).mean()
                    df['price_change'] = df['close'] - df['pre_close']
                    
                    results[ts_code] = df
                    
                    # 更新缓存
                    if self.config.enable_cache:
                        self.cache[cache_key] = (df, datetime.now())
                        
                else:
                    self.logger.warning(f"无法获取 {ts_code} 的基础数据")
                    
            except Exception as e:
                self.logger.error(f"获取 {ts_code} 基础数据失败: {e}")
        
        return results
    
    def get_technical_indicators(self, basic_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """计算技术指标"""
        self.logger.info(f"计算技术指标: {len(basic_data)} 只股票")
        
        results = {}
        
        for ts_code, df in basic_data.items():
            try:
                if df is None or df.empty:
                    continue
                    
                # 使用技术指标计算器
                df_with_indicators = TechnicalCalculator.calculate_all_indicators(df)
                results[ts_code] = df_with_indicators
                
            except Exception as e:
                self.logger.error(f"计算 {ts_code} 技术指标失败: {e}")
                results[ts_code] = df  # 返回原始数据
        
        return results
    
    def get_macro_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """获取宏观经济数据"""
        self.logger.info(f"获取宏观数据: {start_date} 到 {end_date}")
        
        macro_data = pd.DataFrame()
        
        try:
            # 根据Tushare Pro官方文档，使用正确的接口名称和参数格式
            
            # 1. 获取Shibor数据 - 使用正确的接口名称和日期参数
            try:
                shibor_df = self._request_with_retry(
                    self.pro.shibor,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if shibor_df is not None and not shibor_df.empty:
                    shibor_df['trade_date'] = pd.to_datetime(shibor_df['date'])
                    macro_data = shibor_df
                    self.logger.info(f"成功获取Shibor数据: {len(shibor_df)} 条")
                    
            except Exception as e:
                self.logger.warning(f"获取Shibor数据失败: {e}")
            
            # 2. 获取GDP数据 - 季度数据，需要转换日期格式
            try:
                # 将日期转换为季度格式 (YYYYQ1, YYYYQ2, etc.)
                start_year = int(start_date[:4])
                end_year = int(end_date[:4])
                start_month = int(start_date[4:6])
                end_month = int(end_date[4:6])
                
                # 计算起始和结束季度
                start_quarter = (start_month - 1) // 3 + 1
                end_quarter = (end_month - 1) // 3 + 1
                
                start_q = f"{start_year}Q{start_quarter}"
                end_q = f"{end_year}Q{end_quarter}"
                
                gdp_df = self._request_with_retry(
                    self.pro.cn_gdp,
                    start_q=start_q,
                    end_q=end_q
                )
                
                if gdp_df is not None and not gdp_df.empty:
                    # 将季度转换为日期格式
                    gdp_df['trade_date'] = pd.to_datetime(gdp_df['quarter'].str.replace('Q', '-') + '-01')
                    
                    if not macro_data.empty:
                        macro_data = pd.merge(macro_data, gdp_df, on='trade_date', how='outer')
                    else:
                        macro_data = gdp_df
                    self.logger.info(f"成功获取GDP数据: {len(gdp_df)} 条")
                        
            except Exception as e:
                self.logger.warning(f"获取GDP数据失败: {e}")
            
            # 3. 获取CPI数据 - 月度数据，需要转换日期格式
            try:
                # 将日期转换为月度格式 (YYYYMM)
                start_m = start_date[:6]  # YYYYMM
                end_m = end_date[:6]      # YYYYMM
                
                cpi_df = self._request_with_retry(
                    self.pro.cn_cpi,
                    start_m=start_m,
                    end_m=end_m
                )
                
                if cpi_df is not None and not cpi_df.empty:
                    # 将月份转换为日期格式
                    cpi_df['trade_date'] = pd.to_datetime(cpi_df['month'] + '01')
                    
                    if not macro_data.empty:
                        macro_data = pd.merge(macro_data, cpi_df, on='trade_date', how='outer')
                    else:
                        macro_data = cpi_df
                    self.logger.info(f"成功获取CPI数据: {len(cpi_df)} 条")
                        
            except Exception as e:
                self.logger.warning(f"获取CPI数据失败: {e}")
            
            # 4. 获取PPI数据 - 月度数据，需要转换日期格式
            try:
                # 将日期转换为月度格式 (YYYYMM)
                start_m = start_date[:6]  # YYYYMM
                end_m = end_date[:6]      # YYYYMM
                
                ppi_df = self._request_with_retry(
                    self.pro.cn_ppi,
                    start_m=start_m,
                    end_m=end_m
                )
                
                if ppi_df is not None and not ppi_df.empty:
                    # 将月份转换为日期格式
                    ppi_df['trade_date'] = pd.to_datetime(ppi_df['month'] + '01')
                    
                    if not macro_data.empty:
                        macro_data = pd.merge(macro_data, ppi_df, on='trade_date', how='outer')
                    else:
                        macro_data = ppi_df
                    self.logger.info(f"成功获取PPI数据: {len(ppi_df)} 条")
                        
            except Exception as e:
                self.logger.warning(f"获取PPI数据失败: {e}")
            
            # 5. 获取货币供应量数据 - 月度数据
            try:
                start_m = start_date[:6]  # YYYYMM
                end_m = end_date[:6]      # YYYYMM
                
                money_df = self._request_with_retry(
                    self.pro.cn_m,
                    start_m=start_m,
                    end_m=end_m
                )
                
                if money_df is not None and not money_df.empty:
                    # 将月份转换为日期格式
                    money_df['trade_date'] = pd.to_datetime(money_df['month'] + '01')
                    
                    if not macro_data.empty:
                        macro_data = pd.merge(macro_data, money_df, on='trade_date', how='outer')
                    else:
                        macro_data = money_df
                    self.logger.info(f"成功获取货币供应量数据: {len(money_df)} 条")
                        
            except Exception as e:
                self.logger.warning(f"获取货币供应量数据失败: {e}")
            
        except Exception as e:
            self.logger.error(f"获取宏观数据失败: {e}")
        
        # 如果没有获取到任何宏观数据，创建一个空的DataFrame避免后续错误
        if macro_data.empty:
            self.logger.warning("未获取到任何宏观数据，创建空DataFrame")
            macro_data = pd.DataFrame({
                'trade_date': [pd.to_datetime(start_date)],
                'on': [None],        # Shibor隔夜利率
                '1w': [None],        # Shibor 1周利率
                '1m': [None],        # Shibor 1月利率
                '3m': [None],        # Shibor 3月利率
                '6m': [None],        # Shibor 6月利率
                '1y': [None],        # Shibor 1年利率
                'gdp_yoy': [None],   # GDP同比增长率
                'cpi': [None],       # CPI
                'ppi': [None],       # PPI
                'm0': [None],        # 货币供应量M0
                'm1': [None],        # 货币供应量M1
                'm2': [None]         # 货币供应量M2
            })
        
        return macro_data
    
    def get_fund_flow_data(self, ts_codes: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """获取资金流向数据"""
        self.logger.info(f"获取资金流向数据: {len(ts_codes)} 只股票")
        
        results = {}
        
        for ts_code in ts_codes:
            try:
                # 获取资金流向数据
                df = self._request_with_retry(
                    self.pro.moneyflow,
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if df is not None and not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                    df = df.sort_values('trade_date').reset_index(drop=True)
                    results[ts_code] = df
                    
            except Exception as e:
                self.logger.warning(f"获取 {ts_code} 资金流向数据失败: {e}")
        
        return results
    
    def get_news_data(self, start_date: str, end_date: str, limit: int = 1000) -> List[NewsData]:
        """获取新闻数据"""
        self.logger.info(f"获取新闻数据: {start_date} 到 {end_date}")
        
        news_list = []
        
        try:
            # 根据Tushare文档，使用正确的新闻接口
            # 将日期格式转换为 YYYY-MM-DD HH:MM:SS 格式
            start_datetime = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]} 00:00:00"
            end_datetime = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]} 23:59:59"
            
            df = self._request_with_retry(
                self.pro.major_news,
                src='',  # 空字符串表示获取所有来源
                start_date=start_datetime,
                end_date=end_datetime
            )
            
            if df is not None and not df.empty:
                for _, row in df.iterrows():
                    news = NewsData(
                        datetime=row.get('datetime', ''),
                        title=row.get('title', ''),
                        content=row.get('content', ''),
                        source=row.get('src', ''),  # 注意字段名是src
                        url=row.get('url', '')
                    )
                    news_list.append(news)
                self.logger.info(f"成功获取新闻数据: {len(news_list)} 条")
                    
        except Exception as e:
            self.logger.warning(f"获取新闻数据失败: {e}")
            # 如果新闻接口失败，不影响整体流程，返回空列表
        
        return news_list
    
    def get_comprehensive_data(self, request: DataRequest) -> DataResponse:
        """获取综合数据 - 主要接口"""
        self.logger.info(f"开始获取综合数据: {len(request.ts_codes)} 只股票")
        
        try:
            start_time = datetime.now()
            comprehensive_data = []
            
            # 1. 获取基础数据
            basic_data = {}
            if request.include_basic:
                basic_data = self.get_basic_data(
                    request.ts_codes, 
                    request.start_date, 
                    request.end_date
                )
            
            # 2. 计算技术指标
            technical_data = {}
            if request.include_technical and basic_data:
                technical_data = self.get_technical_indicators(basic_data)
            
            # 3. 获取宏观数据
            macro_df = pd.DataFrame()
            if request.include_macro:
                macro_df = self.get_macro_data(request.start_date, request.end_date)
            
            # 4. 获取资金流向数据
            fund_flow_data = {}
            if request.include_fund_flow:
                fund_flow_data = self.get_fund_flow_data(
                    request.ts_codes,
                    request.start_date,
                    request.end_date
                )
            
            # 5. 获取新闻数据
            news_data = []
            if request.include_news:
                news_data = self.get_news_data(request.start_date, request.end_date)
            
            # 6. 组合数据
            for ts_code in request.ts_codes:
                if ts_code not in basic_data:
                    continue
                    
                df = technical_data.get(ts_code, basic_data.get(ts_code))
                if df is None or df.empty:
                    continue
                
                for _, row in df.iterrows():
                    try:
                        # 构建市场数据
                        market_data = MarketData(
                            ts_code=ts_code,
                            trade_date=row['trade_date'].strftime('%Y%m%d'),
                            open=float(row.get('open', 0)),
                            high=float(row.get('high', 0)),
                            low=float(row.get('low', 0)),
                            close=float(row.get('close', 0)),
                            pre_close=float(row.get('pre_close', 0)),
                            change=float(row.get('change', 0)),
                            pct_chg=float(row.get('pct_chg', 0)),
                            vol=float(row.get('vol', 0)),
                            amount=float(row.get('amount', 0)),
                            turnover_rate=row.get('turnover_rate'),
                            volume_ratio=row.get('volume_ratio')
                        )
                        
                        # 构建技术指标数据
                        technical_indicators = TechnicalIndicators(
                            ts_code=ts_code,
                            trade_date=row['trade_date'].strftime('%Y%m%d'),
                            ma5=row.get('ma5'),
                            ma10=row.get('ma10'),
                            ma20=row.get('ma20'),
                            ma60=row.get('ma60'),
                            ema12=row.get('ema12'),
                            ema26=row.get('ema26'),
                            macd_dif=row.get('macd_dif'),
                            macd_dea=row.get('macd_dea'),
                            macd_hist=row.get('macd_hist'),
                            rsi=row.get('rsi'),
                            rsi6=row.get('rsi6'),
                            rsi12=row.get('rsi12'),
                            rsi24=row.get('rsi24'),
                            kdj_k=row.get('kdj_k'),
                            kdj_d=row.get('kdj_d'),
                            kdj_j=row.get('kdj_j'),
                            boll_upper=row.get('boll_upper'),
                            boll_mid=row.get('boll_mid'),
                            boll_lower=row.get('boll_lower'),
                            atr=row.get('atr'),
                            volatility=row.get('volatility'),
                            obv=row.get('obv'),
                            vr=row.get('vr'),
                            wr=row.get('wr'),
                            wr6=row.get('wr6'),
                            wr10=row.get('wr10'),
                            cci=row.get('cci'),
                            adx=row.get('adx'),
                            momentum=row.get('momentum'),
                            roc=row.get('roc')
                        )
                        
                        # 构建宏观数据（使用最近的数据）
                        macro_data = MacroData(
                            trade_date=row['trade_date'].strftime('%Y%m%d')
                        )
                        
                        if not macro_df.empty:
                            # 找到最近的宏观数据
                            trade_date = pd.to_datetime(row['trade_date'])
                            recent_macro = macro_df[macro_df['trade_date'] <= trade_date]
                            if not recent_macro.empty:
                                recent_row = recent_macro.iloc[-1]
                                macro_data.shibor_on = recent_row.get('on')
                                macro_data.shibor_1w = recent_row.get('1w')
                                macro_data.shibor_2w = recent_row.get('2w')
                                macro_data.shibor_1m = recent_row.get('1m')
                                macro_data.shibor_3m = recent_row.get('3m')
                                macro_data.shibor_6m = recent_row.get('6m')
                                macro_data.shibor_9m = recent_row.get('9m')
                                macro_data.shibor_1y = recent_row.get('1y')
                                macro_data.lpr_1y = recent_row.get('lpr_1y')
                                macro_data.lpr_5y = recent_row.get('lpr_5y')
                                macro_data.m0 = recent_row.get('m0')
                                macro_data.m1 = recent_row.get('m1')
                                macro_data.m2 = recent_row.get('m2')
                        
                        # 构建资金流向数据
                        fund_flow = FundFlowData(
                            ts_code=ts_code,
                            trade_date=row['trade_date'].strftime('%Y%m%d')
                        )
                        
                        if ts_code in fund_flow_data:
                            flow_df = fund_flow_data[ts_code]
                            trade_date = pd.to_datetime(row['trade_date'])
                            matching_flow = flow_df[flow_df['trade_date'] == trade_date]
                            if not matching_flow.empty:
                                flow_row = matching_flow.iloc[0]
                                fund_flow.main_net_inflow = flow_row.get('net_mf_amount')
                                fund_flow.main_net_inflow_rate = flow_row.get('net_mf_rate')
                                fund_flow.xl_net_inflow = flow_row.get('net_amount_xl')
                                fund_flow.l_net_inflow = flow_row.get('net_amount_l')
                                fund_flow.m_net_inflow = flow_row.get('net_amount_m')
                                fund_flow.s_net_inflow = flow_row.get('net_amount_s')
                        
                        # 构建综合数据
                        comp_data = ComprehensiveData(
                            ts_code=ts_code,
                            trade_date=row['trade_date'].strftime('%Y%m%d'),
                            market_data=market_data,
                            technical_indicators=technical_indicators,
                            macro_data=macro_data,
                            fund_flow=fund_flow,
                            news_sentiment=0.0,  # 简化处理
                            news_count=len(news_data),
                            positive_news_ratio=0.5,
                            negative_news_ratio=0.3
                        )
                        
                        comprehensive_data.append(comp_data)
                        
                    except Exception as e:
                        self.logger.warning(f"处理 {ts_code} 数据行失败: {e}")
                        continue
            
            return DataResponse(
                success=True,
                data=comprehensive_data,
                request_time=start_time,
                data_count=len(comprehensive_data)
            )
            
        except Exception as e:
            self.logger.error(f"获取综合数据失败: {e}")
            return DataResponse(
                success=False,
                error_message=str(e),
                request_time=datetime.now()
            )
    
    def get_5year_data(self, ts_codes: List[str]) -> DataResponse:
        """获取5年历史数据"""
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=5*365)).strftime("%Y%m%d")
        
        request = DataRequest(
            ts_codes=ts_codes,
            start_date=start_date,
            end_date=end_date,
            include_basic=True,
            include_technical=True,
            include_macro=True,
            include_news=False,  # 5年新闻数据太多，关闭
            include_fund_flow=True
        )
        
        return self.get_comprehensive_data(request)
    
    def get_realtime_data(self, ts_codes: List[str]) -> DataResponse:
        """获取实时数据"""
        today = datetime.now().strftime("%Y%m%d")
        
        request = DataRequest(
            ts_codes=ts_codes,
            start_date=today,
            end_date=today,
            include_basic=True,
            include_technical=True,
            include_macro=True,
            include_news=True,
            include_fund_flow=True
        )
        
        return self.get_comprehensive_data(request)
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.logger.info("缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self.cache),
            'cache_keys': list(self.cache.keys())
        }