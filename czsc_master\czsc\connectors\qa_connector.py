# -*- coding: utf-8 -*-
"""
author: Augment Agent
create_dt: 2024/5/15
describe: QATdx数据源连接器，用于将QATdx获取的数据转换为czsc库的RawBar对象

使用前需要安装QUANTAXIS：
pip install quantaxis
"""
import os
import pandas as pd
from datetime import datetime
from typing import List, Union, Optional
import loguru

from czsc_master.czsc import Freq, RawBar

try:
    from QUANTAXIS.QAFetch import QATdx
except ImportError:
    loguru.logger.error("请先安装QUANTAXIS: pip install quantaxis")
    raise


def format_kline(kline: pd.DataFrame, freq: Freq) -> List[RawBar]:
    """QATdx K线数据转换为RawBar对象列表

    :param kline: QATdx 数据接口返回的K线数据
    :param freq: K线周期
    :return: 转换好的K线数据
    """
    bars = []
    
    # 确保日期列是datetime类型
    if 'date' in kline.columns and not pd.api.types.is_datetime64_any_dtype(kline['date']):
        kline['date'] = pd.to_datetime(kline['date'])
    
    # 确保datetime列是datetime类型
    if 'datetime' in kline.columns and not pd.api.types.is_datetime64_any_dtype(kline['datetime']):
        kline['datetime'] = pd.to_datetime(kline['datetime'])
    
    # 根据频率选择日期列
    dt_key = "datetime" if "分钟" in freq.value else "date"
    if dt_key not in kline.columns and 'date' in kline.columns:
        dt_key = "date"
    
    # 按日期排序
    kline = kline.sort_values(dt_key, ascending=True, ignore_index=True)
    records = kline.to_dict("records")

    for i, record in enumerate(records):
        # 根据频率处理成交量和成交额
        if freq == Freq.D:
            vol = int(record.get("vol", 0) * 100) if record.get("vol", 0) > 0 else 0
            amount = int(record.get("amount", 0) * 1000)
        else:
            vol = int(record.get("vol", 0)) if record.get("vol", 0) > 0 else 0
            amount = int(record.get("amount", 0))

        # 将每一根K线转换成 RawBar 对象
        bar = RawBar(
            symbol=record.get("code", ""),
            dt=pd.to_datetime(record[dt_key]),
            id=i,
            freq=freq,
            open=record.get("open", 0),
            close=record.get("close", 0),
            high=record.get("high", 0),
            low=record.get("low", 0),
            vol=vol,  # 成交量，单位：股
            amount=amount,  # 成交额，单位：元
        )
        bars.append(bar)
    return bars


def get_stock_day(code: str, start_date: str, end_date: str, if_fq: str = '00') -> pd.DataFrame:
    """获取股票日线数据

    :param code: 股票代码，如 '000001'
    :param start_date: 开始日期，格式为 'YYYY-MM-DD'
    :param end_date: 结束日期，格式为 'YYYY-MM-DD'
    :param if_fq: 复权类型，'00'或'bfq'表示不复权，'01'或'qfq'表示前复权，'02'或'hfq'表示后复权
    :return: 日线数据DataFrame
    """
    return QATdx.QA_fetch_get_stock_day(code, start_date, end_date, if_fq)


def get_stock_min(code: str, start_date: str, end_date: str, frequence: str = '1min', if_fq: str = '00') -> pd.DataFrame:
    """获取股票分钟线数据

    :param code: 股票代码，如 '000001'
    :param start_date: 开始日期，格式为 'YYYY-MM-DD'
    :param end_date: 结束日期，格式为 'YYYY-MM-DD'
    :param frequence: 分钟线频率，可选 '1min', '5min', '15min', '30min', '60min'
    :param if_fq: 复权类型，'00'或'bfq'表示不复权，'01'或'qfq'表示前复权，'02'或'hfq'表示后复权
    :return: 分钟线数据DataFrame
    """
    return QATdx.QA_fetch_get_stock_min(code, start_date, end_date, frequence, if_fq)


def get_index_day(code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """获取指数日线数据

    :param code: 指数代码，如 '000001'
    :param start_date: 开始日期，格式为 'YYYY-MM-DD'
    :param end_date: 结束日期，格式为 'YYYY-MM-DD'
    :return: 指数日线数据DataFrame
    """
    return QATdx.QA_fetch_get_index_day(code, start_date, end_date)


def get_index_min(code: str, start_date: str, end_date: str, frequence: str = '1min') -> pd.DataFrame:
    """获取指数分钟线数据

    :param code: 指数代码，如 '000001'
    :param start_date: 开始日期，格式为 'YYYY-MM-DD'
    :param end_date: 结束日期，格式为 'YYYY-MM-DD'
    :param frequence: 分钟线频率，可选 '1min', '5min', '15min', '30min', '60min'
    :return: 指数分钟线数据DataFrame
    """
    return QATdx.QA_fetch_get_index_min(code, start_date, end_date, frequence)


def get_raw_bars(symbol: str, freq: Union[str, Freq], sdt: str, edt: str, fq: str = "前复权", **kwargs) -> List[RawBar]:
    """获取 CZSC 库定义的标准 RawBar 对象列表

    :param symbol: 标的代码，格式为 code#market，如 000001#STOCK 或 000001#INDEX
    :param freq: 周期，支持 Freq 对象，或者字符串，如
            '1分钟', '5分钟', '15分钟', '30分钟', '60分钟', '日线', '周线', '月线'
    :param sdt: 开始时间，格式为 'YYYY-MM-DD'
    :param edt: 结束时间，格式为 'YYYY-MM-DD'
    :param fq: 除权类型，可选值：'前复权', '后复权', '不复权'
    :param kwargs: 其他参数
    :return: RawBar 对象列表
    """

    logger = kwargs.get("logger", loguru.logger)
    freq = Freq(freq)
    
    # 解析标的代码和市场类型
    if "#" in symbol:
        code, market = symbol.split("#")
    else:
        code = symbol
        market = "STOCK"  # 默认为股票
    
    # 转换复权类型
    if fq == "前复权":
        if_fq = "01"
    elif fq == "后复权":
        if_fq = "02"
    else:
        if_fq = "00"  # 不复权
    
    # 转换频率
    freq_map = {
        Freq.F1: "1min",
        Freq.F5: "5min",
        Freq.F15: "15min",
        Freq.F30: "30min",
        Freq.F60: "60min",
        Freq.D: "day",
    }
    
    qa_freq = freq_map.get(freq)
    if not qa_freq:
        logger.warning(f"不支持的频率: {freq.value}，将使用日线数据")
        qa_freq = "day"
    
    # 获取数据
    try:
        if market.upper() == "INDEX":
            if "分钟" in freq.value:
                kline = get_index_min(code, sdt, edt, qa_freq)
            else:
                kline = get_index_day(code, sdt, edt)
        else:  # 默认为股票
            if "分钟" in freq.value:
                kline = get_stock_min(code, sdt, edt, qa_freq, if_fq)
            else:
                kline = get_stock_day(code, sdt, edt, if_fq)
        
        if kline.empty:
            logger.warning(f"获取 {symbol} 的 {freq.value} 数据为空")
            return []
        
        # 转换为RawBar对象列表
        bars = format_kline(kline, freq)
        return bars
    
    except Exception as e:
        logger.exception(f"获取 {symbol} 的 {freq.value} 数据失败: {e}")
        return []


def get_stock_list() -> List[str]:
    """获取股票列表

    :return: 股票代码列表
    """
    df = QATdx.QA_fetch_get_stock_list()
    return [f"{code}#STOCK" for code in df['code'].tolist()]


def get_index_list() -> List[str]:
    """获取指数列表

    :return: 指数代码列表
    """
    df = QATdx.QA_fetch_get_index_list()
    return [f"{code}#INDEX" for code in df['code'].tolist()]


def get_symbols(name: str = "ALL", **kwargs) -> List[str]:
    """获取指定分组下的所有标的代码

    :param name: 分组名称，可选值：'ALL', 'STOCK', 'INDEX'
    :param kwargs: 其他参数
    :return: 标的代码列表
    """
    name = name.upper()
    if name == "ALL":
        return get_stock_list() + get_index_list()
    elif name == "STOCK":
        return get_stock_list()
    elif name == "INDEX":
        return get_index_list()
    else:
        loguru.logger.warning(f"不支持的分组名称: {name}，将返回所有标的")
        return get_stock_list() + get_index_list()
