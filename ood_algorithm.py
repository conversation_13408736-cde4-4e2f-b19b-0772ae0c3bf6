#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Out-of-Distribution (OOD) Robust Learning Algorithm
基于MetaTrader论文思想的通用OOD处理框架

Core Ideas:
1. Bilevel Optimization: Inner loop optimizes on original data, outer loop on transformed data
2. Data Transformation: Simulate distribution shifts through various transformations
3. Conservative Estimation: Use ensemble of worst-case estimates to avoid overoptimism

Author: Research Mode Implementation
Date: 2024
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from typing import List, Dict, Callable, Tuple, Optional
import random
from abc import ABC, abstractmethod


class DataTransformation(ABC):
    """抽象数据变换基类"""
    
    @abstractmethod
    def transform(self, data: torch.Tensor, intensity: float = 0.1) -> torch.Tensor:
        """应用数据变换
        
        Args:
            data: 输入数据
            intensity: 变换强度 [0, 1]
        
        Returns:
            变换后的数据
        """
        pass


class TimeSeriesTransformation(DataTransformation):
    """时间序列数据变换（适用于金融数据等）"""
    
    def __init__(self, transform_type: str = 'noise'):
        self.transform_type = transform_type
    
    def transform(self, data: torch.Tensor, intensity: float = 0.1) -> torch.Tensor:
        if self.transform_type == 'noise':
            # 添加高斯噪声模拟短期随机性
            noise = torch.randn_like(data) * intensity * data.std()
            return data + noise
        
        elif self.transform_type == 'trend':
            # 添加趋势变化模拟长期分布偏移
            seq_len = data.shape[-1]
            trend = torch.linspace(-intensity, intensity, seq_len).to(data.device)
            return data + trend.unsqueeze(0).expand_as(data)
        
        elif self.transform_type == 'scale':
            # 缩放变换模拟市场波动性变化
            scale_factor = 1 + intensity * (2 * torch.rand(1).item() - 1)
            return data * scale_factor
        
        else:
            raise ValueError(f"Unknown transform type: {self.transform_type}")


class ImageTransformation(DataTransformation):
    """图像数据变换"""
    
    def __init__(self, transform_type: str = 'rotation'):
        self.transform_type = transform_type
    
    def transform(self, data: torch.Tensor, intensity: float = 0.1) -> torch.Tensor:
        if self.transform_type == 'rotation':
            # 旋转变换
            angle = intensity * 30  # 最大30度
            return self._rotate_tensor(data, angle)
        
        elif self.transform_type == 'brightness':
            # 亮度变换
            brightness_factor = 1 + intensity * (2 * torch.rand(1).item() - 1)
            return torch.clamp(data * brightness_factor, 0, 1)
        
        elif self.transform_type == 'adversarial':
            # 对抗扰动
            perturbation = torch.randn_like(data) * intensity
            return torch.clamp(data + perturbation, 0, 1)
        
        else:
            raise ValueError(f"Unknown transform type: {self.transform_type}")
    
    def _rotate_tensor(self, tensor: torch.Tensor, angle: float) -> torch.Tensor:
        # 简化的旋转实现（实际应用中可使用torchvision.transforms）
        return tensor  # 占位符实现


class ConservativeQNetwork(nn.Module):
    """保守Q网络，实现ensemble最小值估计"""
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int, num_networks: int = 3):
        super().__init__()
        self.num_networks = num_networks
        self.networks = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, output_dim)
            ) for _ in range(num_networks)
        ])
    
    def forward(self, x: torch.Tensor, conservative: bool = True) -> torch.Tensor:
        """前向传播
        
        Args:
            x: 输入特征
            conservative: 是否使用保守估计（取最小值）
        
        Returns:
            Q值估计
        """
        q_values = [net(x) for net in self.networks]
        
        if conservative:
            # 保守估计：取最小值
            return torch.min(torch.stack(q_values), dim=0)[0]
        else:
            # 平均估计
            return torch.mean(torch.stack(q_values), dim=0)


class OODRobustLearner:
    """OOD鲁棒学习器主类"""
    
    def __init__(
        self,
        model: nn.Module,
        transformations: List[DataTransformation],
        device: str = 'cpu',
        lambda_ood: float = 0.5,
        conservative_weight: float = 0.8
    ):
        """
        Args:
            model: 主模型
            transformations: 数据变换列表
            device: 计算设备
            lambda_ood: OOD损失权重
            conservative_weight: 保守估计权重
        """
        self.model = model.to(device)
        self.transformations = transformations
        self.device = device
        self.lambda_ood = lambda_ood
        self.conservative_weight = conservative_weight
        
        # 优化器
        self.optimizer = optim.Adam(self.model.parameters(), lr=1e-3)
        
        # 损失函数
        self.criterion = nn.MSELoss()
        
        # 训练历史
        self.training_history = {
            'original_loss': [],
            'ood_loss': [],
            'total_loss': []
        }
    
    def bilevel_optimization_step(
        self,
        batch_data: torch.Tensor,
        batch_targets: torch.Tensor,
        transform_intensity: float = 0.1
    ) -> Dict[str, float]:
        """双层优化步骤
        
        Args:
            batch_data: 批次数据
            batch_targets: 批次目标
            transform_intensity: 变换强度
        
        Returns:
            损失字典
        """
        self.optimizer.zero_grad()
        
        # 内层优化：原始数据损失
        original_pred = self.model(batch_data)
        original_loss = self.criterion(original_pred, batch_targets)
        
        # 外层优化：变换数据损失
        ood_losses = []
        for transform in self.transformations:
            # 应用数据变换
            transformed_data = transform.transform(batch_data, transform_intensity)
            
            # 计算变换数据上的预测
            transformed_pred = self.model(transformed_data)
            
            # 保守估计：假设目标也会相应变化（根据具体任务调整）
            ood_loss = self.criterion(transformed_pred, batch_targets)
            ood_losses.append(ood_loss)
        
        # 取最大的OOD损失（最坏情况）
        max_ood_loss = torch.max(torch.stack(ood_losses))
        
        # 总损失：原始损失 + λ * OOD损失
        total_loss = original_loss + self.lambda_ood * max_ood_loss
        
        # 反向传播
        total_loss.backward()
        self.optimizer.step()
        
        # 记录损失
        losses = {
            'original_loss': original_loss.item(),
            'ood_loss': max_ood_loss.item(),
            'total_loss': total_loss.item()
        }
        
        return losses
    
    def train_epoch(
        self,
        dataloader: DataLoader,
        transform_intensity: float = 0.1
    ) -> Dict[str, float]:
        """训练一个epoch
        
        Args:
            dataloader: 数据加载器
            transform_intensity: 变换强度
        
        Returns:
            平均损失字典
        """
        self.model.train()
        epoch_losses = {'original_loss': 0, 'ood_loss': 0, 'total_loss': 0}
        
        for batch_idx, (data, targets) in enumerate(dataloader):
            data, targets = data.to(self.device), targets.to(self.device)
            
            # 双层优化步骤
            losses = self.bilevel_optimization_step(data, targets, transform_intensity)
            
            # 累积损失
            for key in epoch_losses:
                epoch_losses[key] += losses[key]
        
        # 计算平均损失
        num_batches = len(dataloader)
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
            self.training_history[key].append(epoch_losses[key])
        
        return epoch_losses
    
    def evaluate_ood_robustness(
        self,
        test_dataloader: DataLoader,
        transform_intensities: List[float] = [0.1, 0.2, 0.3, 0.4, 0.5]
    ) -> Dict[str, List[float]]:
        """评估OOD鲁棒性
        
        Args:
            test_dataloader: 测试数据加载器
            transform_intensities: 不同的变换强度列表
        
        Returns:
            不同强度下的性能字典
        """
        self.model.eval()
        results = {f'intensity_{intensity}': [] for intensity in transform_intensities}
        results['original'] = []
        
        with torch.no_grad():
            for data, targets in test_dataloader:
                data, targets = data.to(self.device), targets.to(self.device)
                
                # 原始数据性能
                original_pred = self.model(data)
                original_error = self.criterion(original_pred, targets).item()
                results['original'].append(original_error)
                
                # 不同强度变换下的性能
                for intensity in transform_intensities:
                    transform_errors = []
                    
                    for transform in self.transformations:
                        transformed_data = transform.transform(data, intensity)
                        transformed_pred = self.model(transformed_data)
                        error = self.criterion(transformed_pred, targets).item()
                        transform_errors.append(error)
                    
                    # 记录最坏情况性能
                    worst_error = max(transform_errors)
                    results[f'intensity_{intensity}'].append(worst_error)
        
        # 计算平均性能
        for key in results:
            results[key] = np.mean(results[key])
        
        return results
    
    def adaptive_transform_intensity(
        self,
        current_epoch: int,
        total_epochs: int,
        initial_intensity: float = 0.05,
        final_intensity: float = 0.3
    ) -> float:
        """自适应调整变换强度
        
        Args:
            current_epoch: 当前epoch
            total_epochs: 总epoch数
            initial_intensity: 初始强度
            final_intensity: 最终强度
        
        Returns:
            当前应使用的变换强度
        """
        # 线性增长策略
        progress = current_epoch / total_epochs
        intensity = initial_intensity + (final_intensity - initial_intensity) * progress
        return min(intensity, final_intensity)


def create_sample_model(input_dim: int, output_dim: int) -> nn.Module:
    """创建示例模型"""
    return nn.Sequential(
        nn.Linear(input_dim, 128),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(128, 64),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(64, output_dim)
    )


def main_training_loop_example():
    """主训练循环示例"""
    # 设置参数
    input_dim = 10
    output_dim = 1
    batch_size = 32
    num_epochs = 100
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建模型
    model = create_sample_model(input_dim, output_dim)
    
    # 创建数据变换
    transformations = [
        TimeSeriesTransformation('noise'),
        TimeSeriesTransformation('trend'),
        TimeSeriesTransformation('scale')
    ]
    
    # 创建OOD学习器
    ood_learner = OODRobustLearner(
        model=model,
        transformations=transformations,
        device=device,
        lambda_ood=0.5,
        conservative_weight=0.8
    )
    
    print("[MODE: RESEARCH] OOD鲁棒学习算法初始化完成")
    print(f"设备: {device}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
    print(f"数据变换类型: {len(transformations)}种")
    
    # 注意：这里需要实际的数据加载器
    # train_dataloader = DataLoader(...)
    # test_dataloader = DataLoader(...)
    
    # 训练循环示例
    # for epoch in range(num_epochs):
    #     # 自适应调整变换强度
    #     intensity = ood_learner.adaptive_transform_intensity(epoch, num_epochs)
    #     
    #     # 训练一个epoch
    #     losses = ood_learner.train_epoch(train_dataloader, intensity)
    #     
    #     if epoch % 10 == 0:
    #         print(f"Epoch {epoch}: Original Loss = {losses['original_loss']:.4f}, "
    #               f"OOD Loss = {losses['ood_loss']:.4f}, Total Loss = {losses['total_loss']:.4f}")
    #         
    #         # 评估OOD鲁棒性
    #         robustness = ood_learner.evaluate_ood_robustness(test_dataloader)
    #         print(f"OOD Robustness: {robustness}")


if __name__ == "__main__":
    main_training_loop_example()