"""
战略战术层 - 实现分层协作决策架构
战略层(LLM主导): 市场环境分析、投资策略制定
战术层(RL主导): 具体交易执行、仓位管理
"""

import logging
import numpy as np
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from analyzers.llm_market_analyzer import LLMMarketAnalyzer


class MarketRegime(Enum):
    """市场状态枚举"""
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"  
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    TRANSITIONAL = "transitional"


class StrategyType(Enum):
    """策略类型枚举"""
    AGGRESSIVE = "aggressive"
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    DEFENSIVE = "defensive"
    OPPORTUNISTIC = "opportunistic"


class StrategyLayer:
    """
    @class StrategyLayer
    @brief 战略决策层 - LLM主导
    @details 负责长期战略制定、市场环境评估和投资风格调整
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 初始化LLM分析器
        self.llm_analyzer = LLMMarketAnalyzer()
        
        # 战略相关参数
        self.strategy_update_frequency = self.config.get('strategy_update_frequency', '1D')  # 每日更新
        self.market_regime_threshold = self.config.get('market_regime_threshold', 0.6)
        self.strategy_confidence_threshold = self.config.get('strategy_confidence_threshold', 0.7)
        
        # 当前战略状态
        self.current_strategy = None
        self.current_market_regime = None
        self.strategy_history = []
        self.last_strategy_update = None
        
        # 市场环境评估
        self.market_environment = {
            'regime': None,
            'volatility_level': 0.5,
            'trend_strength': 0.5,
            'sentiment_stability': 0.5,
            'regime_confidence': 0.5
        }
        
        self.logger.info("战略层初始化完成")
    
    def generate_investment_strategy(self, macro_data: Dict[str, Any], 
                                   market_data: Dict[str, Any],
                                   fund_code: str = None) -> Dict[str, Any]:
        """
        @brief 生成投资策略
        @param macro_data: 宏观数据
        @param market_data: 市场数据  
        @param fund_code: 基金代码
        @return: 投资策略
        """
        try:
            self.logger.info(f"开始生成投资策略 - 基金: {fund_code}")
            
            # 第一阶段：市场环境评估
            market_assessment = self._assess_market_environment(macro_data, market_data)
            
            # 第二阶段：LLM深度分析
            llm_strategic_analysis = self._perform_strategic_llm_analysis(
                macro_data, market_data, market_assessment, fund_code
            )
            
            # 第三阶段：策略制定
            strategy_framework = self._formulate_strategy_framework(
                market_assessment, llm_strategic_analysis
            )
            
            # 第四阶段：风险偏好设定
            risk_framework = self._establish_risk_framework(
                strategy_framework, market_assessment
            )
            
            # 第五阶段：时间框架设定
            temporal_framework = self._establish_temporal_framework(
                strategy_framework, market_assessment
            )
            
            # 综合战略决策
            comprehensive_strategy = self._synthesize_comprehensive_strategy(
                strategy_framework, risk_framework, temporal_framework, 
                market_assessment, llm_strategic_analysis
            )
            
            # 更新当前策略状态
            self._update_strategy_state(comprehensive_strategy)
            
            return comprehensive_strategy
            
        except Exception as e:
            self.logger.error(f"生成投资策略失败: {str(e)}")
            return self._get_fallback_strategy(macro_data, market_data, fund_code, error=str(e))
    
    def _assess_market_environment(self, macro_data: Dict[str, Any], 
                                 market_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估市场环境"""
        try:
            # 市场波动性评估
            volatility_assessment = self._assess_market_volatility(market_data)
            
            # 趋势强度评估
            trend_assessment = self._assess_trend_strength(market_data)
            
            # 市场情绪稳定性
            sentiment_assessment = self._assess_sentiment_stability(market_data)
            
            # 市场状态识别
            regime_identification = self._identify_market_regime(
                volatility_assessment, trend_assessment, sentiment_assessment
            )
            
            market_environment = {
                'regime': regime_identification['regime'],
                'regime_confidence': regime_identification['confidence'],
                'volatility_level': volatility_assessment['level'],
                'volatility_trend': volatility_assessment['trend'],
                'trend_strength': trend_assessment['strength'],
                'trend_direction': trend_assessment['direction'],
                'sentiment_stability': sentiment_assessment['stability'],
                'sentiment_bias': sentiment_assessment['bias'],
                'assessment_timestamp': datetime.now().isoformat(),
                'environment_score': self._calculate_environment_score(
                    volatility_assessment, trend_assessment, sentiment_assessment
                )
            }
            
            self.market_environment = market_environment
            return market_environment
            
        except Exception as e:
            self.logger.error(f"市场环境评估失败: {str(e)}")
            return self._get_default_market_environment(error=str(e))
    
    def _perform_strategic_llm_analysis(self, macro_data: Dict[str, Any],
                                      market_data: Dict[str, Any],
                                      market_assessment: Dict[str, Any],
                                      fund_code: str) -> Dict[str, Any]:
        """执行战略层LLM分析"""
        try:
            # 构建战略分析上下文
            strategic_context = {
                **market_data,
                'macro_environment': macro_data,
                'market_assessment': market_assessment,
                'strategic_focus': True,
                'analysis_horizon': 'medium_to_long_term'
            }
            
            # 执行LLM分析
            llm_analysis = self.llm_analyzer.analyze_market_narrative(
                strategic_context, fund_code
            )
            
            # 提取战略洞察
            strategic_insights = self._extract_strategic_insights(llm_analysis, market_assessment)
            
            return {
                'raw_llm_analysis': llm_analysis,
                'strategic_insights': strategic_insights,
                'market_outlook': strategic_insights.get('market_outlook', '中性'),
                'sector_rotation': strategic_insights.get('sector_analysis', {}),
                'risk_preference': strategic_insights.get('risk_assessment', 0.5),
                'investment_horizon': strategic_insights.get('investment_horizon', 'medium_term'),
                'strategic_themes': strategic_insights.get('strategic_themes', []),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"战略LLM分析失败: {str(e)}")
            return self._get_default_strategic_analysis(error=str(e))
    
    def _formulate_strategy_framework(self, market_assessment: Dict[str, Any],
                                    llm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """制定策略框架"""
        try:
            market_regime = market_assessment.get('regime', 'sideways')
            regime_confidence = market_assessment.get('regime_confidence', 0.5)
            strategic_insights = llm_analysis.get('strategic_insights', {})
            
            # 基于市场状态选择策略类型
            strategy_type = self._determine_strategy_type(market_regime, regime_confidence, strategic_insights)
            
            # 策略配置参数
            strategy_config = self._configure_strategy_parameters(strategy_type, market_assessment)
            
            # 策略目标设定
            strategy_objectives = self._define_strategy_objectives(strategy_type, strategic_insights)
            
            framework = {
                'strategy_type': strategy_type.value,
                'strategy_config': strategy_config,
                'strategy_objectives': strategy_objectives,
                'expected_performance': self._estimate_strategy_performance(strategy_type, market_assessment),
                'framework_confidence': self._calculate_framework_confidence(market_assessment, llm_analysis),
                'framework_timestamp': datetime.now().isoformat()
            }
            
            return framework
            
        except Exception as e:
            self.logger.error(f"制定策略框架失败: {str(e)}")
            return self._get_default_strategy_framework(error=str(e))
    
    def _establish_risk_framework(self, strategy_framework: Dict[str, Any],
                                market_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """建立风险框架"""
        try:
            strategy_type = StrategyType(strategy_framework.get('strategy_type', 'balanced'))
            volatility_level = market_assessment.get('volatility_level', 0.5)
            regime_confidence = market_assessment.get('regime_confidence', 0.5)
            
            # 基础风险参数
            base_risk_params = self._get_base_risk_parameters(strategy_type)
            
            # 根据市场环境调整风险参数
            adjusted_risk_params = self._adjust_risk_for_market_conditions(
                base_risk_params, volatility_level, regime_confidence
            )
            
            # 风险限制设定
            risk_limits = self._set_risk_limits(strategy_type, market_assessment)
            
            # 风险监控配置
            risk_monitoring = self._configure_risk_monitoring(adjusted_risk_params, risk_limits)
            
            risk_framework = {
                'risk_appetite': adjusted_risk_params['risk_appetite'],
                'max_position_size': adjusted_risk_params['max_position_size'],
                'max_drawdown_tolerance': adjusted_risk_params['max_drawdown'],
                'volatility_tolerance': adjusted_risk_params['volatility_tolerance'],
                'risk_limits': risk_limits,
                'risk_monitoring': risk_monitoring,
                'dynamic_adjustment': True,
                'framework_timestamp': datetime.now().isoformat()
            }
            
            return risk_framework
            
        except Exception as e:
            self.logger.error(f"建立风险框架失败: {str(e)}")
            return self._get_default_risk_framework(error=str(e))
    
    def _establish_temporal_framework(self, strategy_framework: Dict[str, Any],
                                    market_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """建立时间框架"""
        try:
            strategy_type = StrategyType(strategy_framework.get('strategy_type', 'balanced'))
            market_regime = market_assessment.get('regime', 'sideways')
            
            # 基础时间参数
            if strategy_type in [StrategyType.AGGRESSIVE, StrategyType.OPPORTUNISTIC]:
                base_horizon = 'short_to_medium'
                rebalance_frequency = 'weekly'
            elif strategy_type == StrategyType.CONSERVATIVE:
                base_horizon = 'medium_to_long'
                rebalance_frequency = 'monthly'
            else:
                base_horizon = 'medium_term'
                rebalance_frequency = 'bi_weekly'
            
            # 根据市场状态调整
            if market_regime == 'volatile':
                rebalance_frequency = 'daily'
                review_frequency = 'intraday'
            elif market_regime in ['bull_market', 'bear_market']:
                review_frequency = 'daily'
            else:
                review_frequency = 'daily'
            
            temporal_framework = {
                'investment_horizon': base_horizon,
                'strategy_review_frequency': '1D',  # 每日战略回顾
                'tactical_rebalance_frequency': rebalance_frequency,
                'position_review_frequency': review_frequency,
                'performance_evaluation_period': '1W',  # 每周绩效评估
                'strategy_major_review_period': '1M',  # 每月大回顾
                'emergency_review_triggers': self._define_emergency_triggers(),
                'framework_timestamp': datetime.now().isoformat()
            }
            
            return temporal_framework
            
        except Exception as e:
            self.logger.error(f"建立时间框架失败: {str(e)}")
            return self._get_default_temporal_framework(error=str(e))
    
    # 辅助方法
    def _assess_market_volatility(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估市场波动性"""
        try:
            evaluations = market_data.get('evaluations', {})
            volatility_eval = evaluations.get('波动性')
            
            if volatility_eval and hasattr(volatility_eval, 'score'):
                volatility_level = volatility_eval.score
            else:
                # 从技术指标估算
                technical_data = market_data.get('technical_analysis', {})
                atr = technical_data.get('indicators', {}).get('atr', 0.5)
                volatility_level = min(atr / 100, 1.0)
            
            # 波动性趋势
            if volatility_level > 0.7:
                trend = 'increasing'
            elif volatility_level < 0.3:
                trend = 'decreasing'
            else:
                trend = 'stable'
            
            return {
                'level': volatility_level,
                'trend': trend,
                'category': 'high' if volatility_level > 0.6 else 'medium' if volatility_level > 0.3 else 'low'
            }
            
        except Exception as e:
            return {'level': 0.5, 'trend': 'stable', 'category': 'medium', 'error': str(e)}
    
    def _assess_trend_strength(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估趋势强度"""
        try:
            evaluations = market_data.get('evaluations', {})
            trend_eval = evaluations.get('趋势')
            
            if trend_eval and hasattr(trend_eval, 'score'):
                trend_strength = trend_eval.score
                # 从评估详情推断方向
                details = getattr(trend_eval, 'details', {})
                if isinstance(details, dict) and 'trend' in details:
                    direction = details['trend']
                else:
                    direction = 'neutral'
            else:
                trend_strength = 0.5
                direction = 'neutral'
            
            return {
                'strength': trend_strength,
                'direction': direction,
                'category': 'strong' if trend_strength > 0.6 else 'moderate' if trend_strength > 0.3 else 'weak'
            }
            
        except Exception as e:
            return {'strength': 0.5, 'direction': 'neutral', 'category': 'moderate', 'error': str(e)}
    
    def _assess_sentiment_stability(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估情绪稳定性"""
        try:
            evaluations = market_data.get('evaluations', {})
            sentiment_eval = evaluations.get('情绪')
            
            if sentiment_eval and hasattr(sentiment_eval, 'score'):
                sentiment_score = sentiment_eval.score
                stability = sentiment_eval.confidence  # 使用置信度作为稳定性指标
            else:
                sentiment_score = 0.5
                stability = 0.5
            
            # 情绪偏向
            if sentiment_score > 0.6:
                bias = 'positive'
            elif sentiment_score < 0.4:
                bias = 'negative'
            else:
                bias = 'neutral'
            
            return {
                'stability': stability,
                'bias': bias,
                'score': sentiment_score,
                'category': 'stable' if stability > 0.6 else 'volatile' if stability < 0.3 else 'moderate'
            }
            
        except Exception as e:
            return {'stability': 0.5, 'bias': 'neutral', 'score': 0.5, 'category': 'moderate', 'error': str(e)}
    
    def _identify_market_regime(self, volatility_assessment: Dict[str, Any],
                              trend_assessment: Dict[str, Any],
                              sentiment_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """识别市场状态"""
        try:
            volatility_level = volatility_assessment.get('level', 0.5)
            trend_strength = trend_assessment.get('strength', 0.5)
            trend_direction = trend_assessment.get('direction', 'neutral')
            sentiment_stability = sentiment_assessment.get('stability', 0.5)
            
            # 市场状态识别逻辑
            if volatility_level > 0.7:
                regime = MarketRegime.VOLATILE
                confidence = 0.8
            elif trend_strength > 0.6:
                if trend_direction == '上升' or trend_direction == 'up':
                    regime = MarketRegime.BULL_MARKET
                elif trend_direction == '下降' or trend_direction == 'down':
                    regime = MarketRegime.BEAR_MARKET
                else:
                    regime = MarketRegime.SIDEWAYS
                confidence = trend_strength
            elif sentiment_stability < 0.3:
                regime = MarketRegime.TRANSITIONAL
                confidence = 1.0 - sentiment_stability
            else:
                regime = MarketRegime.SIDEWAYS
                confidence = 0.6
            
            return {
                'regime': regime.value,
                'confidence': confidence,
                'factors': {
                    'volatility_influence': volatility_level * 0.4,
                    'trend_influence': trend_strength * 0.4,
                    'sentiment_influence': (1.0 - sentiment_stability) * 0.2
                }
            }
            
        except Exception as e:
            return {'regime': 'sideways', 'confidence': 0.5, 'error': str(e)}
    
    def _calculate_environment_score(self, volatility_assessment: Dict[str, Any],
                                   trend_assessment: Dict[str, Any],
                                   sentiment_assessment: Dict[str, Any]) -> float:
        """计算环境评分"""
        try:
            # 各因子权重
            weights = {'volatility': 0.3, 'trend': 0.4, 'sentiment': 0.3}
            
            # 标准化评分
            volatility_score = 1.0 - volatility_assessment.get('level', 0.5)  # 低波动为好
            trend_score = trend_assessment.get('strength', 0.5)
            sentiment_score = sentiment_assessment.get('stability', 0.5)
            
            environment_score = (
                weights['volatility'] * volatility_score +
                weights['trend'] * trend_score +
                weights['sentiment'] * sentiment_score
            )
            
            return max(0.0, min(1.0, environment_score))
            
        except Exception as e:
            return 0.5
    
    def _extract_strategic_insights(self, llm_analysis: Dict[str, Any],
                                  market_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """提取战略洞察"""
        try:
            insights = {}
            
            # 市场前景
            sentiment = llm_analysis.get('market_sentiment', '中性')
            if sentiment in ['积极', '乐观']:
                insights['market_outlook'] = 'positive'
            elif sentiment in ['谨慎', '悲观']:
                insights['market_outlook'] = 'negative'
            else:
                insights['market_outlook'] = 'neutral'
            
            # 风险评估
            risk_level = llm_analysis.get('risk_level', 0.5)
            insights['risk_assessment'] = risk_level
            
            # 投资时限
            strategy_text = str(llm_analysis.get('strategy_suggestion', ''))
            if any(keyword in strategy_text for keyword in ['长期', '持续', '战略']):
                insights['investment_horizon'] = 'long_term'
            elif any(keyword in strategy_text for keyword in ['短期', '当前', '即时']):
                insights['investment_horizon'] = 'short_term'
            else:
                insights['investment_horizon'] = 'medium_term'
            
            # 战略主题
            themes = []
            if '成长' in strategy_text:
                themes.append('growth')
            if '价值' in strategy_text:
                themes.append('value')
            if '防御' in strategy_text:
                themes.append('defensive')
            if '周期' in strategy_text:
                themes.append('cyclical')
            
            insights['strategic_themes'] = themes if themes else ['balanced']
            
            return insights
            
        except Exception as e:
            return {
                'market_outlook': 'neutral',
                'risk_assessment': 0.5,
                'investment_horizon': 'medium_term',
                'strategic_themes': ['balanced'],
                'error': str(e)
            }
    
    def _determine_strategy_type(self, market_regime: str, regime_confidence: float,
                               strategic_insights: Dict[str, Any]) -> StrategyType:
        """确定策略类型"""
        try:
            market_outlook = strategic_insights.get('market_outlook', 'neutral')
            risk_assessment = strategic_insights.get('risk_assessment', 0.5)
            
            # 策略选择逻辑
            if market_regime == 'bull_market' and regime_confidence > 0.7:
                if risk_assessment < 0.4:
                    return StrategyType.AGGRESSIVE
                else:
                    return StrategyType.BALANCED
            elif market_regime == 'bear_market' and regime_confidence > 0.7:
                return StrategyType.DEFENSIVE
            elif market_regime == 'volatile':
                if market_outlook == 'positive':
                    return StrategyType.OPPORTUNISTIC
                else:
                    return StrategyType.CONSERVATIVE
            else:
                return StrategyType.BALANCED
                
        except Exception as e:
            return StrategyType.BALANCED
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'strategy_update_frequency': '1D',
            'market_regime_threshold': 0.6,
            'strategy_confidence_threshold': 0.7,
            'risk_monitoring_enabled': True,
            'dynamic_adjustment_enabled': True
        }
    
    def _update_strategy_state(self, strategy: Dict[str, Any]) -> None:
        """更新策略状态"""
        self.current_strategy = strategy
        self.last_strategy_update = datetime.now()
        
        # 记录策略历史
        strategy_record = {
            'timestamp': datetime.now().isoformat(),
            'strategy': strategy.copy()
        }
        self.strategy_history.append(strategy_record)
        
        # 保持历史记录限制
        if len(self.strategy_history) > 30:
            self.strategy_history = self.strategy_history[-30:]


class TacticalLayer:
    """
    @class TacticalLayer
    @brief 战术执行层 - RL主导
    @details 负责具体交易执行、仓位管理和短期调整
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 战术执行参数
        self.execution_frequency = self.config.get('execution_frequency', '1H')  # 每小时执行
        self.position_adjustment_threshold = self.config.get('position_adjustment_threshold', 0.05)
        self.tactical_confidence_threshold = self.config.get('tactical_confidence_threshold', 0.6)
        
        # 当前战术状态
        self.current_positions = {}
        self.tactical_history = []
        self.execution_metrics = {
            'total_executions': 0,
            'successful_executions': 0,
            'average_execution_quality': 0.0
        }
        
        self.logger.info("战术层初始化完成")
    
    def execute_strategy(self, strategy_guidance: Dict[str, Any], 
                        real_time_data: Dict[str, Any],
                        fund_code: str = None) -> Dict[str, Any]:
        """
        @brief 执行具体交易策略
        @param strategy_guidance: 战略层指导
        @param real_time_data: 实时市场数据
        @param fund_code: 基金代码
        @return: 执行结果
        """
        try:
            self.logger.info(f"开始战术执行 - 基金: {fund_code}")
            
            # 第一阶段：解析战略指导
            parsed_guidance = self._parse_strategy_guidance(strategy_guidance)
            
            # 第二阶段：调整奖励函数
            adjusted_reward_function = self._adapt_reward_function(parsed_guidance)
            
            # 第三阶段：实时市场评估
            tactical_market_assessment = self._assess_tactical_market_conditions(real_time_data)
            
            # 第四阶段：生成执行计划
            execution_plan = self._generate_execution_plan(
                parsed_guidance, tactical_market_assessment, real_time_data
            )
            
            # 第五阶段：执行交易决策
            execution_result = self._execute_trading_decision(
                execution_plan, adjusted_reward_function, real_time_data
            )
            
            # 第六阶段：验证和确认
            validated_result = self._validate_execution_result(
                execution_result, parsed_guidance, tactical_market_assessment
            )
            
            # 记录执行历史
            self._record_tactical_execution(parsed_guidance, execution_result, validated_result)
            
            return validated_result
            
        except Exception as e:
            self.logger.error(f"战术执行失败: {str(e)}")
            return self._get_fallback_execution(strategy_guidance, real_time_data, fund_code, error=str(e))
    
    def _parse_strategy_guidance(self, strategy_guidance: Dict[str, Any]) -> Dict[str, Any]:
        """解析战略指导"""
        try:
            risk_framework = strategy_guidance.get('risk_framework', {})
            strategy_framework = strategy_guidance.get('strategy_framework', {})
            
            parsed = {
                'risk_appetite': risk_framework.get('risk_appetite', 0.5),
                'max_position_size': risk_framework.get('max_position_size', 0.2),
                'volatility_tolerance': risk_framework.get('volatility_tolerance', 0.5),
                'strategy_type': strategy_framework.get('strategy_type', 'balanced'),
                'expected_performance': strategy_framework.get('expected_performance', {}),
                'rebalance_triggers': self._extract_rebalance_triggers(strategy_guidance),
                'tactical_constraints': self._extract_tactical_constraints(risk_framework)
            }
            
            return parsed
            
        except Exception as e:
            self.logger.error(f"解析战略指导失败: {str(e)}")
            return self._get_default_parsed_guidance(error=str(e))
    
    def _adapt_reward_function(self, parsed_guidance: Dict[str, Any]) -> Dict[str, Any]:
        """根据战略指导调整RL奖励函数"""
        try:
            strategy_type = parsed_guidance.get('strategy_type', 'balanced')
            risk_appetite = parsed_guidance.get('risk_appetite', 0.5)
            
            # 基础奖励参数
            base_alpha = 1.0  # 收益权重
            base_beta = 0.5   # 风险权重
            base_gamma = 0.3  # 回撤权重
            
            # 根据策略类型调整
            if strategy_type == 'aggressive':
                alpha_multiplier = 1.3
                beta_multiplier = 0.7
                gamma_multiplier = 0.8
            elif strategy_type == 'conservative':
                alpha_multiplier = 0.8
                beta_multiplier = 1.4
                gamma_multiplier = 1.2
            elif strategy_type == 'defensive':
                alpha_multiplier = 0.7
                beta_multiplier = 1.5
                gamma_multiplier = 1.3
            elif strategy_type == 'opportunistic':
                alpha_multiplier = 1.2
                beta_multiplier = 0.8
                gamma_multiplier = 0.9
            else:  # balanced
                alpha_multiplier = 1.0
                beta_multiplier = 1.0
                gamma_multiplier = 1.0
            
            # 根据风险偏好微调
            risk_adjustment = (risk_appetite - 0.5) * 0.4  # [-0.2, 0.2]
            alpha_multiplier += risk_adjustment
            beta_multiplier -= risk_adjustment
            
            adapted_reward = {
                'alpha': base_alpha * alpha_multiplier,
                'beta': base_beta * beta_multiplier,
                'gamma': base_gamma * gamma_multiplier,
                'strategy_alignment_bonus': 0.1,
                'adaptation_metadata': {
                    'strategy_type': strategy_type,
                    'risk_appetite': risk_appetite,
                    'multipliers': {
                        'alpha': alpha_multiplier,
                        'beta': beta_multiplier,
                        'gamma': gamma_multiplier
                    }
                }
            }
            
            return adapted_reward
            
        except Exception as e:
            self.logger.error(f"调整奖励函数失败: {str(e)}")
            return {'alpha': 1.0, 'beta': 0.5, 'gamma': 0.3, 'error': str(e)}
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'execution_frequency': '1H',
            'position_adjustment_threshold': 0.05,
            'tactical_confidence_threshold': 0.6,
            'max_single_adjustment': 0.1,
            'risk_override_enabled': True
        } 