"""
信号冲突解决器
负责解决不同维度之间的信号冲突，提供智能决策建议
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult


class SignalConflictResolver:
    """
    @class SignalConflictResolver
    @brief 信号冲突解决器
    @details 负责解决不同维度之间的信号冲突，提供智能决策建议
    """
    
    def __init__(self):
        self.name = "SignalConflictResolver"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 冲突解决权重配置
        self.conflict_weights = {
            "趋势": 0.25,    # 趋势权重
            "波动性": 0.15,  # 波动性权重
            "流动性": 0.20,  # 流动性权重
            "情绪": 0.15,    # 情绪权重
            "结构": 0.15,    # 结构权重
            "转换": 0.10     # 转换权重
        }
        
        # 冲突类型识别阈值
        self.conflict_threshold = 0.3
        
    def resolve_conflicts(self, evaluations: Dict[str, DimensionEvaluationResult], 
                         fund_code: str) -> Dict[str, Any]:
        """
        @brief 解决维度间信号冲突
        @param evaluations: 各维度评估结果字典
        @param fund_code: 基金代码
        @return: 冲突解决结果
        """
        try:
            # 1. 识别冲突
            conflicts = self._identify_conflicts(evaluations)
            
            # 2. 分析冲突严重程度
            conflict_severity = self._analyze_conflict_severity(conflicts, evaluations)
            
            # 3. 应用解决策略
            resolution_strategy = self._determine_resolution_strategy(conflicts, evaluations)
            
            # 4. 计算调整后的权重
            adjusted_weights = self._calculate_adjusted_weights(conflicts, evaluations)
            
            # 5. 生成最终建议
            final_recommendation = self._generate_final_recommendation(
                evaluations, adjusted_weights, conflicts
            )
            
            return {
                'fund_code': fund_code,
                'conflicts_detected': conflicts,
                'conflict_severity': conflict_severity,
                'resolution_strategy': resolution_strategy,
                'adjusted_weights': adjusted_weights,
                'final_recommendation': final_recommendation,
                'confidence': self._calculate_resolution_confidence(conflicts, evaluations),
                'resolution_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Conflict resolution failed for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'conflicts_detected': [],
                'final_recommendation': 'hold'
            }
    
    def _identify_conflicts(self, evaluations: Dict[str, DimensionEvaluationResult]) -> List[Dict]:
        """识别维度间冲突"""
        conflicts = []
        dimensions = list(evaluations.keys())
        
        for i in range(len(dimensions)):
            for j in range(i + 1, len(dimensions)):
                dim1, dim2 = dimensions[i], dimensions[j]
                
                eval1 = evaluations[dim1]
                eval2 = evaluations[dim2]
                
                # 检查评分差异
                score_diff = abs(eval1.score - eval2.score)
                if score_diff >= self.conflict_threshold:
                    conflict_type = self._classify_conflict_type(eval1, eval2)
                    
                    conflicts.append({
                        'dimensions': [dim1, dim2],
                        'conflict_type': conflict_type,
                        'score_difference': score_diff,
                        'severity': 'high' if score_diff >= 0.6 else 'medium' if score_diff >= 0.4 else 'low'
                    })
        
        return conflicts
    
    def _classify_conflict_type(self, eval1: DimensionEvaluationResult, 
                               eval2: DimensionEvaluationResult) -> str:
        """分类冲突类型"""
        if eval1.score > 0 and eval2.score < 0:
            return "方向性冲突"
        elif eval1.score < 0 and eval2.score > 0:
            return "方向性冲突"
        elif abs(eval1.score - eval2.score) >= 0.6:
            return "强度冲突"
        else:
            return "微弱冲突"
    
    def _analyze_conflict_severity(self, conflicts: List[Dict], 
                                  evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """分析冲突严重程度"""
        if not conflicts:
            return "无冲突"
        
        high_severity_count = sum(1 for c in conflicts if c['severity'] == 'high')
        medium_severity_count = sum(1 for c in conflicts if c['severity'] == 'medium')
        
        if high_severity_count >= 2:
            return "严重冲突"
        elif high_severity_count >= 1 or medium_severity_count >= 3:
            return "中等冲突"
        else:
            return "轻微冲突"

    def _determine_resolution_strategy(self, conflicts: List[Dict],
                                     evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """确定解决策略"""
        if not conflicts:
            return "无需解决"

        # 计算高置信度维度数量
        high_confidence_dims = sum(1 for eval_result in evaluations.values()
                                  if eval_result.confidence >= 0.8)

        if high_confidence_dims >= 3:
            return "权重优先策略"  # 根据置信度调整权重
        elif len(conflicts) >= 3:
            return "保守策略"      # 倾向于保守决策
        else:
            return "平衡策略"      # 平衡各维度建议

    def _calculate_adjusted_weights(self, conflicts: List[Dict],
                                   evaluations: Dict[str, DimensionEvaluationResult]) -> Dict[str, float]:
        """计算调整后的权重"""
        adjusted_weights = self.conflict_weights.copy()

        # 根据置信度调整权重
        for dim_name, eval_result in evaluations.items():
            if dim_name in adjusted_weights:
                confidence_factor = eval_result.confidence
                data_quality_factor = 1.0 if eval_result.data_quality == "good" else 0.7

                # 调整权重
                adjustment_factor = confidence_factor * data_quality_factor
                adjusted_weights[dim_name] *= adjustment_factor

        # 标准化权重
        total_weight = sum(adjusted_weights.values())
        if total_weight > 0:
            for dim_name in adjusted_weights:
                adjusted_weights[dim_name] /= total_weight

        return adjusted_weights

    def _generate_final_recommendation(self, evaluations: Dict[str, DimensionEvaluationResult],
                                     adjusted_weights: Dict[str, float],
                                     conflicts: List[Dict]) -> tuple:
        """生成最终推荐，并返回加权分数"""
        # 计算加权综合评分
        weighted_score = 0.0
        for dim_name, eval_result in evaluations.items():
            if dim_name in adjusted_weights:
                weighted_score += eval_result.score * adjusted_weights[dim_name]

        # 根据冲突情况调整决策阈值
        decision_threshold = 0.3
        if len(conflicts) >= 2:
            decision_threshold = 0.5  # 有冲突时提高决策阈值

        # 生成推荐
        if weighted_score >= decision_threshold:
            return ("buy", weighted_score)
        elif weighted_score <= -decision_threshold:
            return ("sell", weighted_score)
        else:
            return ("hold", weighted_score)

    def _calculate_resolution_confidence(self, conflicts: List[Dict],
                                       evaluations: Dict[str, DimensionEvaluationResult]) -> float:
        """计算解决方案置信度"""
        if not conflicts:
            return 0.9

        # 基础置信度
        base_confidence = 0.7

        # 根据冲突数量调整
        conflict_penalty = min(0.3, len(conflicts) * 0.1)

        # 根据数据质量调整
        good_quality_count = sum(1 for eval_result in evaluations.values()
                               if eval_result.data_quality == "good")
        quality_bonus = (good_quality_count / len(evaluations)) * 0.2

        final_confidence = base_confidence - conflict_penalty + quality_bonus
        return max(0.1, min(0.95, final_confidence))
