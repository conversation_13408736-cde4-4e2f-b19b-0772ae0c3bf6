"""
回测引擎
Backtest Engine
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化回测引擎
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.initial_capital = self.config.get('initial_capital', 100000)
        self.transaction_cost = self.config.get('transaction_cost', 0.001)
        self.signal_method = self.config.get('signal_method', 'threshold')
        self.signal_threshold = self.config.get('signal_threshold', 0.6)
        self.max_position = self.config.get('max_position', 1.0)
        self.rebalance_frequency = self.config.get('rebalance_frequency', 'daily')
        
        # 回测状态
        self.positions = {}
        self.cash = self.initial_capital
        self.portfolio_value = self.initial_capital
        self.trades = []
        self.daily_returns = []
        self.capital_curve = [self.initial_capital]
        
        logger.info("BacktestEngine 初始化完成")
    
    def run_backtest(self, data: pd.DataFrame, signals: List[Dict] = None, 
                    time_col: str = 'dt', price_col: str = 'close') -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            data: 市场数据
            signals: 交易信号列表
            time_col: 时间列名
            price_col: 价格列名
            
        Returns:
            回测结果
        """
        logger.info(f"开始回测，数据长度: {len(data)}")
        
        try:
            # 重置回测状态
            self._reset_backtest_state()
            
            # 如果没有提供信号，生成默认信号
            if signals is None:
                signals = self._generate_default_signals(data, price_col)
            
            # 执行回测
            results = self._execute_backtest(data, signals, time_col, price_col)
            
            # 计算性能指标
            performance_metrics = self._calculate_performance_metrics()
            
            # 整合结果
            backtest_results = {
                'trades': self.trades,
                'daily_returns': self.daily_returns,
                'capital_curve': self.capital_curve,
                'final_portfolio_value': self.portfolio_value,
                'total_return': (self.portfolio_value - self.initial_capital) / self.initial_capital,
                'metrics': performance_metrics,
                'positions_history': results.get('positions_history', []),
                'signals_used': len(signals),
                'execution_details': results.get('execution_details', {})
            }
            
            logger.info(f"回测完成，最终收益率: {backtest_results['total_return']:.2%}")
            
            return backtest_results
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            return {
                'error': str(e),
                'trades': [],
                'daily_returns': [],
                'capital_curve': [self.initial_capital],
                'final_portfolio_value': self.initial_capital,
                'total_return': 0.0,
                'metrics': {}
            }
    
    def _reset_backtest_state(self):
        """重置回测状态"""
        self.positions = {}
        self.cash = self.initial_capital
        self.portfolio_value = self.initial_capital
        self.trades = []
        self.daily_returns = []
        self.capital_curve = [self.initial_capital]
    
    def _generate_default_signals(self, data: pd.DataFrame, price_col: str) -> List[Dict]:
        """生成默认交易信号"""
        signals = []
        
        try:
            # 计算简单移动平均
            if len(data) > 20:
                data = data.copy()
                data['ma_5'] = data[price_col].rolling(5).mean()
                data['ma_20'] = data[price_col].rolling(20).mean()
                
                for i in range(20, len(data)):
                    current_price = data[price_col].iloc[i]
                    ma5_current = data['ma_5'].iloc[i]
                    ma20_current = data['ma_20'].iloc[i]
                    ma5_prev = data['ma_5'].iloc[i-1]
                    ma20_prev = data['ma_20'].iloc[i-1]
                    
                    signal_strength = 0.0
                    signal_type = 'hold'
                    
                    # 金叉信号
                    if ma5_prev <= ma20_prev and ma5_current > ma20_current:
                        signal_strength = 0.8
                        signal_type = 'buy'
                    # 死叉信号
                    elif ma5_prev >= ma20_prev and ma5_current < ma20_current:
                        signal_strength = 0.8
                        signal_type = 'sell'
                    # 趋势持续
                    elif ma5_current > ma20_current:
                        signal_strength = min(0.6, (ma5_current - ma20_current) / ma20_current * 10)
                        signal_type = 'buy'
                    elif ma5_current < ma20_current:
                        signal_strength = min(0.6, (ma20_current - ma5_current) / ma20_current * 10)
                        signal_type = 'sell'
                    
                    if signal_strength > 0.1:  # 只保留有意义的信号
                        signals.append({
                            'timestamp': data.index[i] if hasattr(data.index, 'to_pydatetime') else i,
                            'signal_type': signal_type,
                            'signal_strength': signal_strength,
                            'price': current_price,
                            'confidence': min(0.9, signal_strength + 0.1)
                        })
            
            logger.info(f"生成了 {len(signals)} 个默认信号")
            
        except Exception as e:
            logger.error(f"默认信号生成失败: {e}")
            # 生成随机信号作为备选
            np.random.seed(42)
            for i in range(0, len(data), 10):
                signals.append({
                    'timestamp': data.index[i] if hasattr(data.index, 'to_pydatetime') else i,
                    'signal_type': np.random.choice(['buy', 'sell', 'hold'], p=[0.3, 0.3, 0.4]),
                    'signal_strength': np.random.uniform(0.3, 0.9),
                    'price': data[price_col].iloc[i],
                    'confidence': np.random.uniform(0.5, 0.9)
                })
        
        return signals
    
    def _execute_backtest(self, data: pd.DataFrame, signals: List[Dict], 
                         time_col: str, price_col: str) -> Dict[str, Any]:
        """执行回测逻辑"""
        positions_history = []
        execution_details = {
            'total_signals': len(signals),
            'executed_trades': 0,
            'rejected_trades': 0,
            'reasons': []
        }
        
        # 将信号按时间排序
        signals = sorted(signals, key=lambda x: x['timestamp'])
        signal_idx = 0
        
        for i in range(len(data)):
            current_time = data.index[i] if hasattr(data.index, 'to_pydatetime') else i
            current_price = data[price_col].iloc[i]
            
            # 检查是否有信号需要执行
            while signal_idx < len(signals) and signals[signal_idx]['timestamp'] <= current_time:
                signal = signals[signal_idx]
                self._process_signal(signal, current_price, execution_details)
                signal_idx += 1
            
            # 更新投资组合价值
            self._update_portfolio_value(current_price)
            
            # 记录每日收益率
            if len(self.capital_curve) > 1:
                daily_return = (self.capital_curve[-1] - self.capital_curve[-2]) / self.capital_curve[-2]
                self.daily_returns.append(daily_return)
            
            # 记录持仓历史
            positions_history.append({
                'timestamp': current_time,
                'cash': self.cash,
                'positions': self.positions.copy(),
                'portfolio_value': self.portfolio_value
            })
        
        return {
            'positions_history': positions_history,
            'execution_details': execution_details
        }
    
    def _process_signal(self, signal: Dict, current_price: float, execution_details: Dict):
        """处理交易信号"""
        try:
            signal_type = signal['signal_type']
            signal_strength = signal['signal_strength']
            confidence = signal.get('confidence', 0.5)
            
            # 检查信号强度是否达到阈值
            if signal_strength < self.signal_threshold:
                execution_details['rejected_trades'] += 1
                execution_details['reasons'].append(f"信号强度不足: {signal_strength}")
                return
            
            # 计算交易数量
            if signal_type == 'buy':
                position_size = min(signal_strength * confidence, self.max_position)
                trade_value = self.cash * position_size
                shares = int(trade_value / current_price)
                
                if shares > 0 and trade_value <= self.cash:
                    # 执行买入
                    cost = shares * current_price * (1 + self.transaction_cost)
                    if cost <= self.cash:
                        self.cash -= cost
                        self.positions['stock'] = self.positions.get('stock', 0) + shares
                        
                        self.trades.append({
                            'timestamp': signal['timestamp'],
                            'type': 'buy',
                            'shares': shares,
                            'price': current_price,
                            'cost': cost,
                            'signal_strength': signal_strength,
                            'confidence': confidence
                        })
                        
                        execution_details['executed_trades'] += 1
                    else:
                        execution_details['rejected_trades'] += 1
                        execution_details['reasons'].append("资金不足")
                else:
                    execution_details['rejected_trades'] += 1
                    execution_details['reasons'].append("计算的股数为0或资金不足")
            
            elif signal_type == 'sell':
                current_shares = self.positions.get('stock', 0)
                if current_shares > 0:
                    # 计算卖出数量
                    sell_ratio = min(signal_strength * confidence, 1.0)
                    shares_to_sell = int(current_shares * sell_ratio)
                    
                    if shares_to_sell > 0:
                        # 执行卖出
                        proceeds = shares_to_sell * current_price * (1 - self.transaction_cost)
                        self.cash += proceeds
                        self.positions['stock'] -= shares_to_sell
                        
                        self.trades.append({
                            'timestamp': signal['timestamp'],
                            'type': 'sell',
                            'shares': shares_to_sell,
                            'price': current_price,
                            'proceeds': proceeds,
                            'signal_strength': signal_strength,
                            'confidence': confidence
                        })
                        
                        execution_details['executed_trades'] += 1
                    else:
                        execution_details['rejected_trades'] += 1
                        execution_details['reasons'].append("计算的卖出股数为0")
                else:
                    execution_details['rejected_trades'] += 1
                    execution_details['reasons'].append("没有持仓可卖")
            
        except Exception as e:
            logger.error(f"信号处理失败: {e}")
            execution_details['rejected_trades'] += 1
            execution_details['reasons'].append(f"处理错误: {str(e)}")
    
    def _update_portfolio_value(self, current_price: float):
        """更新投资组合价值"""
        try:
            stock_value = self.positions.get('stock', 0) * current_price
            self.portfolio_value = self.cash + stock_value
            self.capital_curve.append(self.portfolio_value)
            
        except Exception as e:
            logger.error(f"投资组合价值更新失败: {e}")
            self.capital_curve.append(self.capital_curve[-1] if self.capital_curve else self.initial_capital)
    
    def _calculate_performance_metrics(self) -> Dict[str, float]:
        """计算性能指标"""
        try:
            if len(self.daily_returns) == 0:
                return self._get_default_metrics()
            
            returns = np.array(self.daily_returns)
            
            # 基础收益指标
            total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
            annualized_return = (1 + total_return) ** (252 / len(returns)) - 1 if len(returns) > 0 else 0
            
            # 风险指标
            volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # 回撤指标
            capital_curve = np.array(self.capital_curve)
            peak = np.maximum.accumulate(capital_curve)
            drawdown = (capital_curve - peak) / peak
            max_drawdown = np.min(drawdown)
            
            # 交易指标
            total_trades = len(self.trades)
            winning_trades = sum(1 for trade in self.trades if self._is_winning_trade(trade))
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 其他指标
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # 下行风险
            negative_returns = returns[returns < 0]
            downside_deviation = np.std(negative_returns) * np.sqrt(252) if len(negative_returns) > 0 else 0
            sortino_ratio = annualized_return / downside_deviation if downside_deviation > 0 else 0
            
            return {
                'total_return': float(total_return),
                'annualized_return': float(annualized_return),
                'volatility': float(volatility),
                'sharpe_ratio': float(sharpe_ratio),
                'max_drawdown': float(max_drawdown),
                'calmar_ratio': float(calmar_ratio),
                'sortino_ratio': float(sortino_ratio),
                'win_rate': float(win_rate),
                'total_trades': int(total_trades),
                'winning_trades': int(winning_trades),
                'average_return': float(np.mean(returns)),
                'downside_deviation': float(downside_deviation)
            }
            
        except Exception as e:
            logger.error(f"性能指标计算失败: {e}")
            return self._get_default_metrics()
    
    def _is_winning_trade(self, trade: Dict) -> bool:
        """判断是否为盈利交易"""
        try:
            if trade['type'] == 'buy':
                return False  # 买入交易本身不算盈利
            else:  # sell
                # 简化判断：如果卖出价格高于某个基准价格
                return trade.get('proceeds', 0) > trade.get('shares', 0) * trade.get('price', 0) * 0.99
        except:
            return False
    
    def _get_default_metrics(self) -> Dict[str, float]:
        """获取默认性能指标"""
        return {
            'total_return': 0.0,
            'annualized_return': 0.0,
            'volatility': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'calmar_ratio': 0.0,
            'sortino_ratio': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'winning_trades': 0,
            'average_return': 0.0,
            'downside_deviation': 0.0
        }
    
    def get_trade_analysis(self) -> Dict[str, Any]:
        """获取交易分析"""
        try:
            if not self.trades:
                return {'message': '没有交易记录'}
            
            buy_trades = [t for t in self.trades if t['type'] == 'buy']
            sell_trades = [t for t in self.trades if t['type'] == 'sell']
            
            analysis = {
                'total_trades': len(self.trades),
                'buy_trades': len(buy_trades),
                'sell_trades': len(sell_trades),
                'average_buy_price': np.mean([t['price'] for t in buy_trades]) if buy_trades else 0,
                'average_sell_price': np.mean([t['price'] for t in sell_trades]) if sell_trades else 0,
                'total_shares_bought': sum(t['shares'] for t in buy_trades),
                'total_shares_sold': sum(t['shares'] for t in sell_trades),
                'total_cost': sum(t.get('cost', 0) for t in buy_trades),
                'total_proceeds': sum(t.get('proceeds', 0) for t in sell_trades),
                'average_signal_strength': np.mean([t['signal_strength'] for t in self.trades]),
                'average_confidence': np.mean([t['confidence'] for t in self.trades])
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"交易分析失败: {e}")
            return {'error': str(e)}