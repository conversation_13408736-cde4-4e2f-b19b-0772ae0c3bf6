"""
调试胜率计算 - 分析为什么胜率总是85%
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from coordinators.enhanced_kelly_coordinator import EnhancedKellyPositionCoordinator


def debug_win_probability_calculation():
    """调试胜率计算的详细过程"""
    
    print("🔍 调试胜率计算过程")
    print("=" * 80)
    
    # 创建一个修改版的协调器来调试
    class DebugKellyCoordinator(EnhancedKellyPositionCoordinator):
        def _calculate_enhanced_win_probability(self, analysis_result):
            """重写胜率计算函数，添加详细调试信息"""
            
            print(f"\n📊 开始计算胜率 - {analysis_result.get('fund_code', 'unknown')}")
            print("-" * 50)
            
            # 基础胜率
            base_prob = 0.5
            print(f"基础胜率: {base_prob:.3f}")
            
            # 1. 技术分析贡献
            tech_contribution = self._get_technical_contribution(analysis_result)
            print(f"技术分析贡献: {tech_contribution:.3f}")
            
            # 2. 资金流向贡献
            flow_contribution = self._get_flow_contribution(analysis_result)
            print(f"资金流向贡献: {flow_contribution:.3f}")
            
            # 3. 卦象分析贡献
            gua_contribution = self._get_gua_contribution(analysis_result)
            print(f"卦象分析贡献: {gua_contribution:.3f}")
            
            # 4. LLM分析贡献
            llm_contribution = self._get_llm_contribution(analysis_result)
            print(f"LLM分析贡献: {llm_contribution:.3f}")
            
            # 5. 增强决策贡献
            enhanced_contribution = self._get_enhanced_decision_contribution(analysis_result)
            print(f"增强决策贡献: {enhanced_contribution:.3f}")
            
            # 综合胜率
            total_prob_raw = (base_prob + 
                             tech_contribution + 
                             flow_contribution + 
                             gua_contribution + 
                             llm_contribution + 
                             enhanced_contribution)
            
            print(f"原始总胜率: {total_prob_raw:.3f}")
            
            # 限制在合理范围
            import numpy as np
            total_prob_clipped = np.clip(total_prob_raw, 0.35, 0.85)
            print(f"限制后胜率: {total_prob_clipped:.3f}")
            
            print(f"是否被上限限制: {'是' if total_prob_raw > 0.85 else '否'}")
            print(f"是否被下限限制: {'是' if total_prob_raw < 0.35 else '否'}")
            
            return total_prob_clipped
        
        def _get_technical_contribution(self, analysis_result):
            """重写技术分析贡献，添加调试信息"""
            
            technical_data = analysis_result.get('technical_data', {})
            contribution = 0.0
            
            print(f"  🔧 技术分析详情:")
            
            # 买入信号强度
            buy_signal = technical_data.get('buy_signal', False)
            signal_strength = technical_data.get('signal_strength', 'none')
            print(f"    买入信号: {buy_signal}, 强度: {signal_strength}")
            
            if buy_signal:
                if signal_strength == 'strong':
                    contribution += 0.15
                    print(f"    强信号贡献: +0.15")
                elif signal_strength == 'medium':
                    contribution += 0.10
                    print(f"    中信号贡献: +0.10")
                elif signal_strength == 'weak':
                    contribution += 0.05
                    print(f"    弱信号贡献: +0.05")
            
            # 置信度评分
            confidence_score = technical_data.get('confidence_score', 0.5)
            confidence_contrib = (confidence_score - 0.5) * 0.12
            contribution += confidence_contrib
            print(f"    置信度评分: {confidence_score:.3f}, 贡献: {confidence_contrib:+.3f}")
            
            # 技术指标分析
            tech_indicators = technical_data.get('technical_indicators', {})
            if tech_indicators:
                print(f"    技术指标: {tech_indicators}")
                
                # RSI分析
                rsi = tech_indicators.get('rsi', 50)
                rsi_contrib = 0
                if 30 <= rsi <= 70:
                    rsi_contrib = 0.03
                elif rsi < 30:  # 超卖
                    rsi_contrib = 0.08
                elif rsi > 70:  # 超买
                    rsi_contrib = -0.02
                contribution += rsi_contrib
                print(f"    RSI({rsi}): {rsi_contrib:+.3f}")
                
                # MACD分析
                macd = tech_indicators.get('macd', 0)
                macd_contrib = 0.05 if macd > 0 else 0
                contribution += macd_contrib
                print(f"    MACD({macd}): {macd_contrib:+.3f}")
                
                # 均线分析
                ma5 = tech_indicators.get('ma5', 0)
                ma20 = tech_indicators.get('ma20', 0)
                ma_contrib = 0
                if ma5 > 0 and ma20 > 0 and ma5 > ma20:
                    ma_contrib = 0.06
                contribution += ma_contrib
                print(f"    均线(MA5:{ma5}, MA20:{ma20}): {ma_contrib:+.3f}")
            
            print(f"    技术分析总贡献: {contribution:.3f}")
            return contribution
        
        def _get_flow_contribution(self, analysis_result):
            """重写资金流向贡献，添加调试信息"""
            
            flow_data = analysis_result.get('flow_data', {})
            contribution = 0.0
            
            print(f"  💰 资金流向详情:")
            
            # 流动性
            high_liquidity = flow_data.get('high_liquidity', False)
            liquidity_contrib = 0.05 if high_liquidity else 0
            contribution += liquidity_contrib
            print(f"    高流动性: {high_liquidity}, 贡献: {liquidity_contrib:+.3f}")
            
            # 资金流向
            capital_flow = flow_data.get('capital_flow', '平衡')
            flow_contrib = 0
            if capital_flow == '净流入':
                flow_contrib = 0.08
            elif capital_flow == '净流出':
                flow_contrib = -0.03
            contribution += flow_contrib
            print(f"    资金流向: {capital_flow}, 贡献: {flow_contrib:+.3f}")
            
            # 价格和成交量配合
            price_data = flow_data.get('price_data', {})
            change_rate = price_data.get('change_rate', 0)
            volume = price_data.get('volume', 0)
            
            volume_contrib = 0
            if change_rate > 0 and volume > 5000:
                volume_contrib = 0.06
            elif change_rate < 0 and volume > 5000:
                volume_contrib = 0.03
            contribution += volume_contrib
            print(f"    价格变化: {change_rate}%, 成交量: {volume}, 贡献: {volume_contrib:+.3f}")
            
            print(f"    资金流向总贡献: {contribution:.3f}")
            return contribution
    
    # 初始化调试协调器
    debug_coordinator = DebugKellyCoordinator({
        'enable_detailed_logging': False
    })
    
    # 测试不同强度的场景
    test_scenarios = [
        {
            'name': '超强买入信号',
            'data': {
                'fund_code': '518880',
                'enhanced_decision': {
                    'decision': 'buy',
                    'confidence': 0.95,
                    'coordination_score': 0.90,
                    'weighted_score': 0.45
                },
                'final_decision': 'buy',
                'final_confidence': 0.95,
                'technical_data': {
                    'buy_signal': True,
                    'signal_strength': 'strong',
                    'confidence_score': 0.95,
                    'technical_indicators': {
                        'ma5': 7.60,
                        'ma20': 7.20,  # 大幅上穿
                        'rsi': 25,      # 超卖
                        'macd': 0.035   # 强势金叉
                    }
                },
                'gua_data': {
                    'is_buy_gua': True,
                    'gua_score': 0.35
                },
                'flow_data': {
                    'high_liquidity': True,
                    'capital_flow': '净流入',
                    'price_data': {
                        'price': 7.65,
                        'change_rate': 4.5,  # 大涨
                        'volume': 35000     # 大成交量
                    }
                },
                'llm_analysis': {
                    'confidence_level': 0.90,
                    'market_sentiment': '乐观'
                }
            }
        },
        {
            'name': '弱买入信号',
            'data': {
                'fund_code': '513030',
                'enhanced_decision': {
                    'decision': 'buy',
                    'confidence': 0.55,
                    'coordination_score': 0.52,
                    'weighted_score': 0.08
                },
                'final_decision': 'buy',
                'final_confidence': 0.58,
                'technical_data': {
                    'buy_signal': True,
                    'signal_strength': 'weak',
                    'confidence_score': 0.55,
                    'technical_indicators': {
                        'ma5': 1.851,
                        'ma20': 1.850,  # 微弱上穿
                        'rsi': 52,      # 中性
                        'macd': 0.002   # 微弱金叉
                    }
                },
                'gua_data': {
                    'is_buy_gua': True,
                    'gua_score': 0.08
                },
                'flow_data': {
                    'high_liquidity': False,
                    'capital_flow': '平衡',
                    'price_data': {
                        'price': 1.852,
                        'change_rate': 0.3,  # 微涨
                        'volume': 3200      # 低成交量
                    }
                },
                'llm_analysis': {
                    'confidence_level': 0.58,
                    'market_sentiment': '中性'
                }
            }
        },
        {
            'name': '观望信号',
            'data': {
                'fund_code': '159567',
                'enhanced_decision': {
                    'decision': 'hold',
                    'confidence': 0.50,
                    'coordination_score': 0.48,
                    'weighted_score': 0.02
                },
                'final_decision': 'hold',
                'final_confidence': 0.52,
                'technical_data': {
                    'buy_signal': False,
                    'signal_strength': 'none',
                    'confidence_score': 0.50,
                    'technical_indicators': {
                        'ma5': 0.950,
                        'ma20': 0.952,  # 空头排列
                        'rsi': 48,      # 中性偏弱
                        'macd': -0.001  # 微弱死叉
                    }
                },
                'gua_data': {
                    'is_buy_gua': False,
                    'gua_score': 0.02
                },
                'flow_data': {
                    'high_liquidity': False,
                    'capital_flow': '平衡',
                    'price_data': {
                        'price': 0.951,
                        'change_rate': -0.1,  # 微跌
                        'volume': 2800       # 低成交量
                    }
                },
                'llm_analysis': {
                    'confidence_level': 0.52,
                    'market_sentiment': '中性'
                }
            }
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🎯 测试场景: {scenario['name']}")
        print("=" * 60)
        
        result = debug_coordinator.calculate_kelly_position(scenario['data'])
        calc = result['kelly_calculation']
        
        print(f"\n📈 最终结果:")
        print(f"   胜率: {calc['win_probability']:.1%}")
        print(f"   盈亏比: {calc['risk_reward_ratio']:.2f}")
        print(f"   建议仓位: {calc['optimal_position']:.2%}")


if __name__ == "__main__":
    debug_win_probability_calculation()