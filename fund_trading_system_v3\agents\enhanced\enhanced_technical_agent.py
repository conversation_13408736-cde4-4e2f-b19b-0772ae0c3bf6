"""
增强版技术分析智能体
提供传统技术分析、量化分析和时间周期背驰分析的综合技术分析
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.base_agent import BaseAgent
from core.utils import *


class EnhancedTechnicalAgent(BaseAgent):
    """
    @class EnhancedTechnicalAgent
    @brief 增强版技术分析智能体
    @details 提供传统技术分析、量化分析和时间周期背驰分析的综合技术分析
    """
    
    def __init__(self, name: str = "EnhancedTechnicalAgent"):
        super().__init__(name, "enhanced_technical_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据并进行增强技术分析"""
        fund_code = data.get('fund_code')
        self.logger.info(f"Processing enhanced technical analysis for {fund_code}")
        
        try:
            # 1. 传统技术分析
            traditional_analysis = self._analyze_traditional_signals(fund_code)
            
            # 2. 量化技术分析
            quantitative_analysis = self._analyze_quantitative_signals(fund_code)
            
            # 3. 时间周期背驰分析
            timeframe_divergence = self._analyze_timeframe_divergence(fund_code)
            
            # 4. 融合分析结果
            enhanced_result = self._fuse_analysis_results(traditional_analysis, quantitative_analysis, timeframe_divergence)
            
            self.store_in_memory(fund_code, enhanced_result)
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"Error in enhanced technical analysis for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'timestamp': datetime.now().isoformat(),
                'buy_signal': False,
                'error': str(e),
                'confidence_score': 0.0
            }
    
    def _analyze_traditional_signals(self, fund_code: str) -> Dict[str, Any]:
        """传统技术分析 - 基于真实数据"""
        try:
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            # 获取真实技术指标
            tech_data = get_enhanced_technical_indicators(fund_code, 'D')
            indicators = tech_data['indicators']
            
            # 移动平均信号
            if indicators['ma5'] > indicators['ma20'] * 1.02:
                ma_signal = 'buy'
            elif indicators['ma5'] < indicators['ma20'] * 0.98:
                ma_signal = 'sell'
            else:
                ma_signal = 'hold'
            
            # RSI信号
            rsi = indicators['rsi']
            if rsi < 30:
                rsi_signal = 'buy'  # 超卖
            elif rsi > 70:
                rsi_signal = 'sell'  # 超买
            else:
                rsi_signal = 'hold'
            
            # MACD信号
            if indicators['macd_bullish'] and indicators['macd'] > 0.01:
                macd_signal = 'buy'
            elif not indicators['macd_bullish'] and indicators['macd'] < -0.01:
                macd_signal = 'sell'
            else:
                macd_signal = 'hold'
            
            # 计算置信度
            confidence = get_real_confidence_metrics(fund_code, indicators)
            
            return {
                'ma_signal': ma_signal,
                'rsi_signal': rsi_signal,
                'macd_signal': macd_signal,
                'confidence': confidence,
                'technical_indicators': indicators,
                'data_quality': tech_data.get('data_quality', 'unknown'),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error in traditional analysis for {fund_code}: {e}")
            return {'error': str(e)}
    
    def _analyze_quantitative_signals(self, fund_code: str) -> Dict[str, Any]:
        """量化技术分析 - 基于真实数据"""
        try:
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            # 获取趋势强度指标
            trend_data = calculate_trend_strength_metrics(fund_code)
            
            # 获取市场情绪指标
            sentiment_data = calculate_market_sentiment_indicators(fund_code)
            
            # 获取成交量分析
            volume_data = get_volume_profile_analysis(fund_code)
            
            # 动量信号
            if trend_data['trend_direction'] == 'up' and trend_data['adx_strength'] > 25:
                momentum_signal = 'buy'
            elif trend_data['trend_direction'] == 'down' and trend_data['adx_strength'] > 25:
                momentum_signal = 'sell'
            else:
                momentum_signal = 'hold'
            
            # 波动率信号
            if sentiment_data['vix_like_indicator'] < 15 and sentiment_data['sentiment_direction'] == 'bullish':
                volatility_signal = 'buy'  # 低波动率上涨环境
            elif sentiment_data['vix_like_indicator'] > 30:
                volatility_signal = 'sell'  # 高波动率避险
            else:
                volatility_signal = 'hold'
            
            # 成交量信号
            if volume_data['volume_strength'] > 1 and volume_data['obv_trend'] > 0.1:
                volume_signal = 'buy'
            elif volume_data['volume_strength'] < -0.5 and volume_data['obv_trend'] < -0.1:
                volume_signal = 'sell'
            else:
                volume_signal = 'hold'
            
            # 综合置信度
            confidence = (
                abs(trend_data['adx_strength'] / 50) * 0.4 +
                sentiment_data['sentiment_strength'] * 0.3 +
                min(1.0, abs(volume_data['volume_strength'])) * 0.3
            )
            
            return {
                'momentum_signal': momentum_signal,
                'volatility_signal': volatility_signal,
                'volume_signal': volume_signal,
                'confidence': min(0.95, max(0.1, confidence)),
                'trend_analysis': trend_data,
                'sentiment_analysis': sentiment_data,
                'volume_analysis': volume_data,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error in quantitative analysis for {fund_code}: {e}")
            return {'error': str(e)}

    def _analyze_timeframe_divergence(self, fund_code: str) -> Dict[str, Any]:
        """时间周期背驰分析"""
        try:
            # 简化的背驰分析（避免循环依赖）
            # 在实际应用中，这里应该调用专门的背驰分析模块

            # 模拟背驰分析结果
            transition_probability = random.uniform(0.1, 0.8)
            consensus_direction = random.choice(['bullish', 'bearish', 'neutral'])
            transition_signal = random.choice(['strong', 'medium', 'weak', 'none'])

            return {
                'transition_probability': transition_probability,
                'consensus_direction': consensus_direction,
                'transition_signal': transition_signal,
                'confidence_level': random.uniform(0.3, 0.9),
                'resonance_score': random.uniform(0.2, 0.8),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in timeframe divergence analysis for {fund_code}: {str(e)}")
            return {
                'transition_probability': 0.0,
                'consensus_direction': 'neutral',
                'transition_signal': 'none',
                'error': str(e)
            }

    def _fuse_analysis_results(self, traditional: Dict[str, Any],
                              quantitative: Dict[str, Any],
                              timeframe_divergence: Dict[str, Any] = None) -> Dict[str, Any]:
        """融合分析结果"""
        try:
            # 综合各分析结果
            signals = []
            confidences = []

            # 处理传统分析信号
            if 'error' not in traditional:
                trad_signals = [traditional.get('ma_signal'), traditional.get('rsi_signal'), traditional.get('macd_signal')]
                buy_count = trad_signals.count('buy')
                sell_count = trad_signals.count('sell')

                if buy_count >= 2:
                    signals.append('buy')
                elif sell_count >= 2:
                    signals.append('sell')
                else:
                    signals.append('hold')

                confidences.append(traditional.get('confidence', 0.5))

            # 处理量化分析信号
            if 'error' not in quantitative:
                quant_signals = [quantitative.get('momentum_signal'), quantitative.get('volatility_signal'), quantitative.get('volume_signal')]
                buy_count = quant_signals.count('buy')
                sell_count = quant_signals.count('sell')

                if buy_count >= 2:
                    signals.append('buy')
                elif sell_count >= 2:
                    signals.append('sell')
                else:
                    signals.append('hold')

                confidences.append(quantitative.get('confidence', 0.5))

            # 处理背驰分析信号
            if timeframe_divergence and 'error' not in timeframe_divergence:
                transition_prob = timeframe_divergence.get('transition_probability', 0.0)
                consensus = timeframe_divergence.get('consensus_direction', 'neutral')

                if transition_prob > 0.6 and consensus == 'bullish':
                    signals.append('buy')
                elif transition_prob > 0.6 and consensus == 'bearish':
                    signals.append('sell')
                else:
                    signals.append('hold')

                confidences.append(timeframe_divergence.get('confidence_level', 0.5))

            # 综合决策
            if not signals:
                final_signal = 'hold'
                final_confidence = 0.1
            else:
                buy_count = signals.count('buy')
                sell_count = signals.count('sell')

                if buy_count > sell_count:
                    final_signal = 'buy'
                elif sell_count > buy_count:
                    final_signal = 'sell'
                else:
                    final_signal = 'hold'

                final_confidence = np.mean(confidences) if confidences else 0.5

            return {
                'fund_code': traditional.get('fund_code', quantitative.get('fund_code', 'UNKNOWN')),
                'final_signal': final_signal,
                'buy_signal': final_signal == 'buy',
                'confidence_score': final_confidence,
                'traditional_analysis': traditional,
                'quantitative_analysis': quantitative,
                'timeframe_divergence': timeframe_divergence,
                'signal_breakdown': {
                    'signals': signals,
                    'buy_count': signals.count('buy'),
                    'sell_count': signals.count('sell'),
                    'hold_count': signals.count('hold')
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in fusing analysis results: {str(e)}")
            return {
                'fund_code': 'UNKNOWN',
                'final_signal': 'hold',
                'buy_signal': False,
                'confidence_score': 0.0,
                'error': str(e)
            }
