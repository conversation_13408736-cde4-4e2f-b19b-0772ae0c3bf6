"""
系统配置和日志设置
包含日志格式化器和配置函数
"""

import logging


class SafeFormatter(logging.Formatter):
    """安全的日志格式化器，避免编码问题"""
    def format(self, record):
        # 移除或替换可能导致编码问题的字符
        if hasattr(record, 'msg'):
            # 将emoji和特殊字符替换为简单文本
            msg = str(record.msg)
            # 替换常见的emoji
            msg = msg.replace('🔄', '[CYCLE]')
            msg = msg.replace('✅', '[OK]')
            msg = msg.replace('❌', '[ERROR]')
            msg = msg.replace('⚠️', '[WARNING]')
            msg = msg.replace('🎯', '[TARGET]')
            msg = msg.replace('📊', '[CHART]')
            msg = msg.replace('🚀', '[ROCKET]')
            msg = msg.replace('🔧', '[TOOL]')
            msg = msg.replace('💡', '[IDEA]')
            record.msg = msg
        return super().format(record)


def setup_logging():
    """设置日志配置 - 只保留核心系统日志"""
    # 只配置文件日志，不输出到控制台
    file_handler = logging.FileHandler('fund_trading_v3.log', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # 配置根日志记录器 - 只输出到文件
    logging.basicConfig(
        level=logging.WARNING,  # 提高日志级别
        handlers=[file_handler]  # 只保留文件输出
    )
    
    # 彻底禁用所有第三方库的日志输出
    logging.getLogger("matplotlib").setLevel(logging.ERROR)
    logging.getLogger("asyncio").setLevel(logging.ERROR)
    logging.getLogger("httpx").setLevel(logging.ERROR)
    logging.getLogger("httpcore").setLevel(logging.ERROR)
    logging.getLogger("openai").setLevel(logging.ERROR)
    logging.getLogger("urllib3").setLevel(logging.ERROR)
    logging.getLogger("QUANTAXIS").setLevel(logging.ERROR)
    logging.getLogger("pytdx").setLevel(logging.ERROR)
    logging.getLogger("requests").setLevel(logging.ERROR)
    logging.getLogger("numexpr").setLevel(logging.ERROR)
    logging.getLogger("urllib3.connectionpool").setLevel(logging.ERROR)
    
    # 只保留系统核心日志，但也减少输出
    logging.getLogger("EnhancedFundTradingSystemV3").setLevel(logging.WARNING)
    logging.getLogger("MultiAgentCoordinatorV3").setLevel(logging.WARNING)
    logging.getLogger("EnhancedKellyPositionCoordinator").setLevel(logging.WARNING)


# 创建默认logger
logger = logging.getLogger(__name__)
