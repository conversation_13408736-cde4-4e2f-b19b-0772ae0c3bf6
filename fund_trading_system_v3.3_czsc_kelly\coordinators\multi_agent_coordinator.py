"""
多智能体协调器 V3
负责协调所有智能体的工作，集成传统智能体和增强版决策智能体
"""

import logging
import sys
import os
from datetime import datetime
from collections import deque
from typing import Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.traditional import TechnicalAgent, GuaAnalysisAgent, FundFlowAgent
from agents.enhanced import EnhancedDecisionAgentV3, RiskControlAgent
from analyzers.llm_market_analyzer import LLMMarketAnalyzer
from core.market_environment_assessor import MarketEnvironmentAssessor
from core.decision_explainer import DecisionExplainer
from coordinators.enhanced_kelly_coordinator import EnhancedKellyPositionCoordinator as KellyPositionCoordinator


class MultiAgentCoordinatorV3:
    """
    @class MultiAgentCoordinatorV3
    @brief 多智能体协调器 V3版本
    @details 负责协调所有智能体的工作，集成传统智能体和增强版决策智能体
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

        # 初始化传统智能体
        self.technical_agent = TechnicalAgent("TechnicalAgent_V3")
        self.gua_agent = GuaAnalysisAgent("GuaAgent_V3")
        self.flow_agent = FundFlowAgent("FlowAgent_V3")

        # 初始化增强版决策智能体
        self.enhanced_decision_agent = EnhancedDecisionAgentV3("EnhancedDecisionV3")

        # 初始化风控智能体
        self.risk_control_agent = RiskControlAgent()

        # 初始化LLM市场分析器
        self.llm_analyzer = LLMMarketAnalyzer()

        # 初始化市场环境评估器
        self.market_assessor = MarketEnvironmentAssessor()

        # 初始化决策解释器
        self.decision_explainer = DecisionExplainer()

        # 初始化凯利仓位协调器
        self.kelly_coordinator = KellyPositionCoordinator({
            'enable_display': False,  # 禁用详细显示，改为整合显示
            'enable_detailed_logging': True,
            'kelly_method': 'fractional',
            'kelly_fraction': 0.25,
            'max_position': 0.25,
            'min_position': 0.01,
            'confidence_threshold': 0.5
        })

        # 协调历史
        self.coordination_history = deque(maxlen=50)
        
    def coordinate_analysis(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 协调所有智能体进行综合分析
        @param fund_code: 基金代码
        @return: 综合分析结果
        """
        try:
            start_time = datetime.now()
            # 减少日志输出
            
            # ===============================
            # 第一阶段：传统智能体数据收集
            # ===============================
            
            # 技术分析 - 添加异常处理
            try:
                technical_data = self.technical_agent.process({'fund_code': fund_code})
            except Exception as e:
                self.logger.error(f"技术分析失败 {fund_code}: {e}")
                technical_data = {'error': str(e), 'buy_signal': False}
            
            # 卦象分析 - 添加异常处理
            try:
                gua_data = self.gua_agent.process({'fund_code': fund_code})
            except Exception as e:
                self.logger.error(f"卦象分析失败 {fund_code}: {e}")
                gua_data = {'error': str(e), 'is_buy_gua': False}
            
            # 资金流向分析 - 添加异常处理
            try:
                flow_data = self.flow_agent.process({'fund_code': fund_code})
            except Exception as e:
                self.logger.error(f"资金流向分析失败 {fund_code}: {e}")
                flow_data = {'error': str(e), 'high_liquidity': False}
            
            # ===============================
            # 第二阶段：数据整合和预处理
            # ===============================
            
            # 整合所有数据 - 添加异常处理
            try:
                integrated_data = self._integrate_agent_data(
                    fund_code, technical_data, gua_data, flow_data
                )
            except Exception as e:
                self.logger.error(f"数据整合失败 {fund_code}: {e}")
                integrated_data = {'fund_code': fund_code, 'error': str(e)}
            
            # ===============================
            # 第三阶段：LLM市场分析
            # ===============================
            
            # 执行LLM市场分析 - 添加异常处理
            try:
                llm_analysis = self._perform_llm_analysis(fund_code, integrated_data)
            except Exception as e:
                self.logger.error(f"LLM分析失败 {fund_code}: {e}")
                llm_analysis = {'error': str(e), 'market_sentiment': '未知', 'confidence_level': 0.0}
            
            # 将LLM分析结果添加到整合数据中
            integrated_data['llm_analysis'] = llm_analysis

            # ===============================
            # 第四阶段：增强版决策分析
            # ===============================

            # 执行增强版决策分析 - 添加异常处理
            try:
                enhanced_result = self.enhanced_decision_agent.process(integrated_data)
            except Exception as e:
                self.logger.error(f"增强决策分析失败 {fund_code}: {e}")
                enhanced_result = {'error': str(e), 'decision': 'hold', 'confidence': 0.0}

            # ===============================
            # 第五阶段：风控验证
            # ===============================

            # 执行风控验证 - 添加异常处理
            try:
                risk_validation = self._perform_risk_validation(
                    fund_code, enhanced_result, integrated_data
                )
            except Exception as e:
                self.logger.error(f"风控验证失败 {fund_code}: {e}")
                risk_validation = {'error': str(e), 'final_decision': 'hold', 'confidence': 0.0}

            # ===============================
            # 第六阶段：凯利公式仓位计算
            # ===============================
            
            # 准备凯利计算所需的数据
            kelly_input_data = {
                'fund_code': fund_code,
                'technical_data': technical_data,
                'gua_data': gua_data,
                'flow_data': flow_data,
                'llm_analysis': llm_analysis,
                'enhanced_decision': enhanced_result,
                'final_decision': risk_validation.get('final_decision', 'hold'),
                'final_confidence': risk_validation.get('confidence', 0.0)
            }
            
            # 执行凯利仓位计算 - 添加异常处理
            try:
                kelly_analysis = self.kelly_coordinator.calculate_kelly_position(kelly_input_data)
            except Exception as e:
                self.logger.error(f"凯利仓位计算失败 {fund_code}: {e}")
                kelly_analysis = {'error': str(e), 'kelly_calculation': {'optimal_position': 0.0, 'risk_level': 'high'}}
            
            # ===============================
            # 第七阶段：结果整合和输出
            # ===============================
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 整合最终结果 - 添加异常处理
            try:
                coordination_summary = self._generate_coordination_summary_v2(
                    technical_data, gua_data, flow_data, enhanced_result, risk_validation
                )
            except Exception as e:
                self.logger.error(f"协调摘要生成失败 {fund_code}: {e}")
                coordination_summary = {'error': str(e), 'summary': '摘要生成失败'}
            
            final_result = {
                # 基础信息
                'fund_code': fund_code,
                'analysis_time': start_time.isoformat(),
                'total_processing_time': processing_time,
                'coordinator_version': 'V3.2',  # 更新版本号，包含LLM分析

                # 传统智能体结果
                'traditional_agents': {
                    'technical_analysis': technical_data,
                    'gua_analysis': gua_data,
                    'flow_analysis': flow_data
                },

                # LLM市场分析结果
                'llm_analysis': llm_analysis,

                # 增强版决策结果
                'enhanced_decision': enhanced_result,

                # 风控验证结果
                'risk_control': risk_validation,

                # 最终决策（经过风控验证后的决策）
                'final_decision': risk_validation.get('final_decision', 'hold'),
                'final_confidence': risk_validation.get('confidence', 0.0),

                # 凯利公式仓位分析结果
                'kelly_analysis': kelly_analysis,

                # 协调摘要
                'coordination_summary': coordination_summary
            }
            
            # 记录协调历史
            self._record_coordination(final_result)
            
            # 只在出错时记录日志
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"协调分析失败 {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'analysis_time': datetime.now().isoformat(),
                'coordinator_version': 'V3.1',
                'final_decision': 'hold',  # 出错时保守决策
                'final_confidence': 0.0
            }

    def _perform_risk_validation(self, fund_code: str, enhanced_result: Dict[str, Any],
                               integrated_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 执行风控验证
        @param fund_code: 基金代码
        @param enhanced_result: 增强版决策结果
        @param integrated_data: 整合数据
        @return: 风控验证结果
        """
        try:
            # 获取建议决策
            proposed_decision = enhanced_result.get('decision', 'hold')

            # 准备风控验证数据
            risk_data = {
                'fund_code': fund_code,
                'analysis_result': integrated_data,
                'proposed_decision': proposed_decision
            }

            # 执行风控验证
            risk_result = self.risk_control_agent.process(risk_data)

            # 生成市场环境评估
            dimension_evaluations = integrated_data.get('dimension_evaluations', {})
            market_assessment = self.market_assessor.assess_market_environment(dimension_evaluations, fund_code)

            # 生成决策解释（安全处理）
            try:
                decision_explanation = self.decision_explainer.explain_risk_decision(
                    risk_result.get('risk_validation', {}),
                    integrated_data.get('technical_analysis', {}),
                    {'market_assessment': market_assessment}
                )
            except Exception as e:
                self.logger.warning(f"决策解释生成失败 {fund_code}: {str(e)}")
                decision_explanation = {
                    'fund_code': fund_code,
                    'error': str(e),
                    'summary': '决策解释生成失败'
                }

            # 整合风控结果
            final_risk_result = {
                'risk_validation_result': risk_result,
                'market_environment_assessment': market_assessment,
                'decision_explanation': decision_explanation,
                'final_decision': risk_result.get('final_decision', 'hold'),
                'confidence': risk_result.get('risk_validation', {}).get('confidence', 0.0),
                'risk_level': risk_result.get('risk_level', 'unknown')
            }

            # 减少日志输出

            return final_risk_result

        except Exception as e:
            self.logger.error(f"风控验证失败 {fund_code}: {str(e)}")
            return {
                'error': str(e),
                'final_decision': 'hold',  # 出错时保守决策
                'confidence': 0.0,
                'risk_level': 'critical'
            }
    
    def _perform_llm_analysis(self, fund_code: str, integrated_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 执行LLM市场分析
        @param fund_code: 基金代码
        @param integrated_data: 整合的市场数据
        @return: LLM分析结果
        """
        try:
            # 减少日志输出
            
            # 准备LLM分析的市场数据
            market_data = {
                'fund_code': fund_code,
                'price_data': integrated_data.get('price_data', {}),
                'technical_analysis': integrated_data.get('technical_analysis', {}),
                'gua_data': integrated_data.get('gua_data', {}),
                'flow_data': integrated_data.get('flow_data', {}),
                'volume_analysis': integrated_data.get('volume_analysis', {}),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            # 获取CZSC的BI(笔)和FX(分型)数据
            czsc_structure_data = self._extract_czsc_structure_data(fund_code, integrated_data)
            if czsc_structure_data:
                market_data['czsc_structure'] = czsc_structure_data
            
            # 调用LLM分析器进行市场叙事分析
            llm_result = self.llm_analyzer.analyze_market_narrative(market_data, fund_code)
            
            # 记录分析结果
            if 'error' not in llm_result:
                pass  # 减少日志输出
            else:
                self.logger.warning(f"LLM分析失败 - {fund_code}: {llm_result.get('error', '未知错误')}")
            
            return llm_result
            
        except Exception as e:
            self.logger.error(f"LLM市场分析异常 {fund_code}: {str(e)}")
            return {
                'analysis_type': 'llm_analysis_error',
                'fund_code': fund_code,
                'error': str(e),
                'analysis_time': datetime.now().isoformat(),
                'market_sentiment': '未知',
                'confidence_level': 0.0,
                'strategy_suggestion': '建议使用传统分析方法'
            }
    
    def _extract_czsc_structure_data(self, fund_code: str, integrated_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 提取CZSC的BI(笔)和FX(分型)结构数据
        @param fund_code: 基金代码
        @param integrated_data: 整合数据
        @return: CZSC结构数据
        """
        try:
            from core.utils import CZSC_FUNC_AVAILABLE, get_kline
            from simpleczsc import KlineAnalyze
            
            if not CZSC_FUNC_AVAILABLE:
                self.logger.warning(f"CZSC函数不可用，无法提取结构数据 - {fund_code}")
                return {}
            
            # 获取K线数据进行CZSC分析
            kline_data = get_kline(fund_code, freq='30min')
            if kline_data is None or len(kline_data) < 50:
                self.logger.warning(f"K线数据不足，无法进行CZSC结构分析 - {fund_code}")
                return {}
            
            # 创建CZSC分析器
            ka = KlineAnalyze(kline_data)
            
            # 提取FX(分型)数据
            fx_data = []
            if hasattr(ka, 'fx_list') and ka.fx_list:
                for fx in ka.fx_list[-10:]:  # 取最近10个分型
                    # 兼容字典和对象两种格式
                    if isinstance(fx, dict):
                        fx_info = {
                            'dt': fx.get('dt', ''),
                            'fx_mark': fx.get('fx_mark', ''),
                            'fx_price': fx.get('fx', 0),
                            'start_dt': fx.get('start_dt', ''),
                            'end_dt': fx.get('end_dt', ''),
                            'power': fx.get('power', 0)
                        }
                    else:
                        # 处理对象格式
                        fx_info = {
                            'dt': getattr(fx, 'dt', ''),
                            'fx_mark': getattr(fx, 'fx_mark', ''),
                            'fx_price': getattr(fx, 'fx', 0),
                            'start_dt': getattr(fx, 'start_dt', ''),
                            'end_dt': getattr(fx, 'end_dt', ''),
                            'power': getattr(fx, 'power', 0)
                        }
                    fx_data.append(fx_info)
            
            # 提取BI(笔)数据
            bi_data = []
            if hasattr(ka, 'bi_list') and ka.bi_list:
                for bi in ka.bi_list[-10:]:  # 取最近10个笔
                    # 兼容字典和对象两种格式
                    if isinstance(bi, dict):
                        bi_info = {
                            'dt': bi.get('dt', ''),
                            'fx_mark': bi.get('fx_mark', ''),
                            'bi_price': bi.get('bi', 0),
                            'start_dt': bi.get('start_dt', ''),
                            'end_dt': bi.get('end_dt', ''),
                            'power': bi.get('power', 0),
                            'direction': '向上' if bi.get('fx_mark') == 'g' else '向下'
                        }
                    else:
                        # 处理对象格式
                        fx_mark = getattr(bi, 'fx_mark', '')
                        bi_info = {
                            'dt': getattr(bi, 'dt', ''),
                            'fx_mark': fx_mark,
                            'bi_price': getattr(bi, 'bi', 0),
                            'start_dt': getattr(bi, 'start_dt', ''),
                            'end_dt': getattr(bi, 'end_dt', ''),
                            'power': getattr(bi, 'power', 0),
                            'direction': '向上' if fx_mark == 'g' else '向下'
                        }
                    bi_data.append(bi_info)
            
            # 提取XD(线段)数据
            xd_data = []
            if hasattr(ka, 'xd_list') and ka.xd_list:
                for xd in ka.xd_list[-5:]:  # 取最近5个线段
                    # 兼容字典和对象两种格式
                    if isinstance(xd, dict):
                        xd_info = {
                            'dt': xd.get('dt', ''),
                            'fx_mark': xd.get('fx_mark', ''),
                            'xd_price': xd.get('xd', 0),
                            'start_dt': xd.get('start_dt', ''),
                            'end_dt': xd.get('end_dt', ''),
                            'direction': '向上' if xd.get('fx_mark') == 'g' else '向下'
                        }
                    else:
                        # 处理对象格式
                        fx_mark = getattr(xd, 'fx_mark', '')
                        xd_info = {
                            'dt': getattr(xd, 'dt', ''),
                            'fx_mark': fx_mark,
                            'xd_price': getattr(xd, 'xd', 0),
                            'start_dt': getattr(xd, 'start_dt', ''),
                            'end_dt': getattr(xd, 'end_dt', ''),
                            'direction': '向上' if fx_mark == 'g' else '向下'
                        }
                    xd_data.append(xd_info)
            
            # 分析当前市场结构状态
            structure_analysis = self._analyze_czsc_structure_state(fx_data, bi_data, xd_data)
            
            czsc_structure = {
                'fx_list': fx_data,
                'bi_list': bi_data,
                'xd_list': xd_data,
                'structure_analysis': structure_analysis,
                'data_source': 'czsc_kline_analyze',
                'analysis_time': datetime.now().isoformat()
            }
            
            self.logger.info(f"CZSC结构数据提取完成 - {fund_code}: FX={len(fx_data)}, BI={len(bi_data)}, XD={len(xd_data)}")
            return czsc_structure
            
        except Exception as e:
            self.logger.error(f"CZSC结构数据提取失败 {fund_code}: {str(e)}")
            return {}
    
    def _analyze_czsc_structure_state(self, fx_data: list, bi_data: list, xd_data: list) -> Dict[str, Any]:
        """
        @brief 分析CZSC结构状态
        @param fx_data: 分型数据
        @param bi_data: 笔数据
        @param xd_data: 线段数据
        @return: 结构分析结果
        """
        try:
            analysis = {
                'current_trend': '未知',
                'structure_strength': 0.5,
                'key_levels': [],
                'structure_signals': []
            }
            
            # 分析当前趋势
            if bi_data and len(bi_data) >= 2:
                last_bi = bi_data[-1]
                prev_bi = bi_data[-2]
                
                # 确保bi_data中的元素是字典格式
                if isinstance(last_bi, dict) and isinstance(prev_bi, dict):
                    if last_bi.get('fx_mark') == 'g' and prev_bi.get('fx_mark') == 'd':
                        if last_bi.get('bi_price', 0) > prev_bi.get('bi_price', 0):
                            analysis['current_trend'] = '上升趋势'
                            analysis['structure_strength'] = 0.7
                        else:
                            analysis['current_trend'] = '震荡上行'
                            analysis['structure_strength'] = 0.6
                    elif last_bi.get('fx_mark') == 'd' and prev_bi.get('fx_mark') == 'g':
                        if last_bi.get('bi_price', 0) < prev_bi.get('bi_price', 0):
                            analysis['current_trend'] = '下降趋势'
                            analysis['structure_strength'] = 0.3
                        else:
                            analysis['current_trend'] = '震荡下行'
                            analysis['structure_strength'] = 0.4
            
            # 提取关键价位
            if bi_data:
                for bi in bi_data[-5:]:  # 最近5个笔的关键价位
                    # 确保bi是字典格式
                    if isinstance(bi, dict):
                        level_type = '阻力位' if bi.get('fx_mark') == 'g' else '支撑位'
                        analysis['key_levels'].append({
                            'price': bi.get('bi_price', 0),
                            'type': level_type,
                            'time': bi.get('dt', '')
                        })
            
            # 生成结构信号
            if len(bi_data) >= 3:
                # 检查是否有背驰信号
                recent_bis = bi_data[-3:]
                if len(recent_bis) == 3:
                    # 确保所有元素都是字典格式
                    if all(isinstance(bi, dict) for bi in recent_bis):
                        if (recent_bis[0].get('fx_mark') == recent_bis[2].get('fx_mark') and
                            recent_bis[0].get('fx_mark') == 'g' and
                            recent_bis[2].get('bi_price', 0) < recent_bis[0].get('bi_price', 0)):
                            analysis['structure_signals'].append('顶背驰信号')
                        elif (recent_bis[0].get('fx_mark') == recent_bis[2].get('fx_mark') and
                              recent_bis[0].get('fx_mark') == 'd' and
                              recent_bis[2].get('bi_price', 0) > recent_bis[0].get('bi_price', 0)):
                            analysis['structure_signals'].append('底背驰信号')
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"CZSC结构状态分析失败: {str(e)}")
            return {
                'current_trend': '未知',
                'structure_strength': 0.5,
                'key_levels': [],
                'structure_signals': []
            }
    
    def _integrate_agent_data(self, fund_code: str, 
                             technical_data: Dict, 
                             gua_data: Dict, 
                             flow_data: Dict) -> Dict[str, Any]:
        """整合各智能体数据"""
        try:
            # 构建增强版决策智能体需要的数据结构
            integrated_data = {
                'fund_code': fund_code,
                
                # 价格相关数据
                'price_data': flow_data.get('price_data', {}),
                
                # 技术指标数据
                'technical_data': technical_data.get('technical_indicators', {}),

                # 风控agent需要的技术分析数据结构
                'technical_analysis': {
                    'indicators': technical_data.get('technical_indicators', {}),
                    'signals': technical_data.get('signals', {}),
                    'trend_analysis': technical_data.get('trend_analysis', {})
                },
                
                # 卦象数据
                'gua_data': {
                    'gua_score': gua_data.get('gua_score', 0.0),
                    'main_gua': gua_data.get('main_gua', ''),
                    'is_select_gua': gua_data.get('is_select_gua', False),
                    'is_buy_gua': gua_data.get('is_buy_gua', False),
                    'is_sell_gua': gua_data.get('is_sell_gua', False)
                },
                
                # 资金流向数据
                'flow_data': flow_data,
                
                # 成交量分析数据
                'volume_analysis': {
                    'current_volume_ratio': flow_data.get('volume_ratio', 1.0),
                    'volume_level': flow_data.get('volume_level', 'normal')
                },
                
                # 时间框架背驰数据（如果有的话）
                'timeframe_divergence': {
                    'transition_probability': 0.5,  # 默认值
                    'consensus_direction': 'neutral',
                    'transition_signal': 'none'
                }
            }
            
            return integrated_data
            
        except Exception as e:
            self.logger.error(f"数据整合失败: {str(e)}")
            return {'fund_code': fund_code, 'error': str(e)}
    
    def _generate_coordination_summary_v2(self, technical_data: Dict,
                                         gua_data: Dict,
                                         flow_data: Dict,
                                         enhanced_result: Dict,
                                         risk_validation: Dict) -> Dict[str, Any]:
        """生成包含风控验证的协调摘要V2"""
        try:
            # 获取原始协调摘要
            base_summary = self._generate_coordination_summary(
                technical_data, gua_data, flow_data, enhanced_result
            )

            # 添加风控信息
            risk_info = {
                'risk_control_passed': risk_validation.get('risk_validation_result', {}).get('risk_validation', {}).get('passed', False),
                'risk_level': risk_validation.get('risk_level', 'unknown'),
                'final_decision_after_risk_control': risk_validation.get('final_decision', 'hold'),
                'decision_changed_by_risk_control': enhanced_result.get('decision', 'hold') != risk_validation.get('final_decision', 'hold'),
                'risk_rejection_reasons': risk_validation.get('risk_validation_result', {}).get('risk_validation', {}).get('rejection_reasons', []),
                'market_environment_score': getattr(risk_validation.get('market_environment_assessment', {}), 'overall_risk_level', 'medium') if hasattr(risk_validation.get('market_environment_assessment', {}), 'overall_risk_level') else 'medium'
            }

            # 合并摘要
            base_summary.update(risk_info)
            base_summary['summary_version'] = 'V2_with_risk_control'

            return base_summary

        except Exception as e:
            self.logger.error(f"协调摘要V2生成失败: {str(e)}")
            return {'error': str(e)}

    def _generate_coordination_summary(self, technical_data: Dict,
                                     gua_data: Dict,
                                     flow_data: Dict,
                                     enhanced_result: Dict) -> Dict[str, Any]:
        """生成协调摘要"""
        try:
            # 提取关键信息
            technical_signal = technical_data.get('buy_signal', False)
            gua_score = gua_data.get('gua_score', 0.0)
            flow_liquidity = flow_data.get('high_liquidity', False)
            enhanced_decision = enhanced_result.get('decision', 'hold')
            enhanced_confidence = enhanced_result.get('confidence', 0.0)
            
            # 信号一致性分析
            signals = []
            if technical_signal:
                signals.append('technical_buy')
            if gua_score > 0.3:
                signals.append('gua_positive')
            if flow_liquidity:
                signals.append('flow_positive')
            
            signal_consistency = len(signals) / 3  # 标准化到0-1
            
            # 风险评估
            risk_factors = []
            if not flow_liquidity:
                risk_factors.append('low_liquidity')
            if enhanced_confidence < 0.5:
                risk_factors.append('low_confidence')
            if gua_score < -0.3:
                risk_factors.append('negative_gua')
            
            risk_level = 'high' if len(risk_factors) >= 2 else 'medium' if len(risk_factors) == 1 else 'low'
            
            return {
                'signal_consistency': signal_consistency,
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'active_signals': signals,
                'final_decision': enhanced_decision,
                'decision_confidence': enhanced_confidence,
                'recommendation': self._generate_recommendation(enhanced_decision, enhanced_confidence, risk_level)
            }
            
        except Exception as e:
            self.logger.error(f"协调摘要生成失败: {str(e)}")
            return {'error': str(e)}
    
    def _generate_recommendation(self, decision: str, confidence: float, risk_level: str) -> str:
        """生成投资建议"""
        try:
            if decision == 'buy' and confidence >= 0.7 and risk_level == 'low':
                return "强烈建议买入"
            elif decision == 'buy' and confidence >= 0.5:
                return "建议买入"
            elif decision == 'sell' and confidence >= 0.7:
                return "建议卖出"
            elif decision == 'sell' and confidence >= 0.5:
                return "考虑卖出"
            elif risk_level == 'high':
                return "建议观望，风险较高"
            else:
                return "建议观望"
        except Exception:
            return "建议观望"
    
    def _record_coordination(self, result: Dict[str, Any]) -> None:
        """记录协调历史"""
        try:
            coordination_record = {
                'timestamp': datetime.now(),
                'fund_code': result.get('fund_code'),
                'decision': result.get('enhanced_decision', {}).get('decision'),
                'confidence': result.get('enhanced_decision', {}).get('confidence'),
                'processing_time': result.get('total_processing_time')
            }
            self.coordination_history.append(coordination_record)
        except Exception as e:
            self.logger.error(f"记录协调历史失败: {str(e)}")
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """获取协调统计信息"""
        try:
            if not self.coordination_history:
                return {'total_coordinations': 0}
            
            total_coordinations = len(self.coordination_history)
            avg_processing_time = sum(r.get('processing_time', 0) for r in self.coordination_history) / total_coordinations
            
            decisions = [r.get('decision') for r in self.coordination_history if r.get('decision')]
            decision_distribution = {
                'buy': decisions.count('buy'),
                'sell': decisions.count('sell'),
                'hold': decisions.count('hold')
            }
            
            return {
                'total_coordinations': total_coordinations,
                'avg_processing_time': avg_processing_time,
                'decision_distribution': decision_distribution,
                'last_coordination': self.coordination_history[-1]['timestamp'].isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取协调统计失败: {str(e)}")
            return {'error': str(e)}
