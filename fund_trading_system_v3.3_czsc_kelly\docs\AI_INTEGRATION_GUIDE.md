# AI集成使用指南

## 📖 概述

本指南介绍如何在基金交易系统中使用AI大模型功能，包括市场分析、决策解释和自然语言查询等功能。

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
pip install openai
```

### 2. API密钥配置

#### 方法一：环境变量（推荐）

```bash
# Windows
set MOONSHOT_API_KEY=sk-your-api-key-here

# Linux/Mac
export MOONSHOT_API_KEY=sk-your-api-key-here
```

#### 方法二：配置文件

1. 复制示例配置文件：
```bash
cp config/ai_settings_sample.json config/ai_settings.json
```

2. 编辑配置文件，填入您的API密钥：
```json
{
  "llm_providers": {
    "moonshot": {
      "api_key": "sk-your-actual-api-key-here",
      "base_url": "https://api.moonshot.cn/v1",
      "model": "kimi-k2-0711-preview",
      "enabled": true
    }
  }
}
```

### 3. 运行测试

```bash
python test_ai_integration.py
```

### 4. 查看演示

```bash
python examples/ai_integration_demo.py
```

## 🔧 核心功能

### 1. LLM市场分析器

提供基于大语言模型的市场分析和投资建议。

```python
from analyzers import LLMMarketAnalyzer

# 初始化分析器
analyzer = LLMMarketAnalyzer()

# 准备市场数据
market_data = {
    'evaluations': evaluations,  # 六大维度评估结果
    'price_data': {
        'current_price': 1.2345,
        'change_pct': 2.15,
        'volume': 1500000
    }
}

# 进行分析
result = analyzer.analyze_market_narrative(market_data, fund_code='000001')

print(f"市场情绪: {result['market_sentiment']}")
print(f"投资建议: {result['strategy_suggestion']}")
```

### 2. 自然语言查询接口

允许用户使用自然语言查询市场状态。

```python
from analyzers import NaturalLanguageInterface

# 初始化接口
nl_interface = NaturalLanguageInterface()

# 准备上下文数据
context_data = {
    'fund_data': {'000001': {'nav': 1.2345, 'change_pct': 2.15}},
    'market_analysis': {'market_sentiment': '积极'}
}

# 处理查询
result = nl_interface.process_query("当前市场状态如何？", context_data)
print(result['response'])
```

### 3. 智能决策解释

为投资决策生成清晰的解释。

```python
# 准备决策数据
decision_data = {
    'decision_type': '买入',
    'fund_code': '000001',
    'decision_confidence': 0.75,
    'key_factors': ['趋势向上突破', '成交量放大']
}

# 生成解释
explanation = analyzer.generate_decision_explanation(decision_data)
print(explanation)
```

## 📊 配置选项

### AI提供商配置

```json
{
  "llm_providers": {
    "moonshot": {
      "api_key": "your-api-key",
      "base_url": "https://api.moonshot.cn/v1",
      "model": "kimi-k2-0711-preview",
      "enabled": true
    },
    "openai": {
      "api_key": "your-openai-key",
      "base_url": "https://api.openai.com/v1",
      "model": "gpt-4",
      "enabled": false
    }
  }
}
```

### 分析设置

```json
{
  "analysis_settings": {
    "default_temperature": 0.3,
    "max_tokens": 2000,
    "timeout": 30,
    "retry_attempts": 3
  }
}
```

### 功能开关

```json
{
  "features": {
    "market_analysis": true,
    "decision_explanation": true,
    "natural_language_query": true,
    "risk_assessment": true
  }
}
```

## 🛡️ 兜底机制

当AI服务不可用时，系统会自动启用兜底机制：

1. **传统分析方法**：使用技术指标和统计方法
2. **预设响应模板**：提供基础的分析结果
3. **错误处理**：优雅地处理服务异常

## 📝 使用示例

### 完整的市场分析流程

```python
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analyzers import (
    LLMMarketAnalyzer, 
    NaturalLanguageInterface, 
    MultiDimensionalMarketClassifier
)
from core.data_structures import DimensionEvaluationResult

# 1. 准备数据
evaluations = {
    '趋势': DimensionEvaluationResult(
        dimension='趋势',
        score=0.65,
        signal='买入',
        confidence=0.8,
        details={'trend': '上升'}
    ),
    # ... 其他维度
}

# 2. 市场分类
classifier = MultiDimensionalMarketClassifier()
classification = classifier.classify_market(evaluations, fund_code='000001')

# 3. LLM分析
analyzer = LLMMarketAnalyzer()
market_data = {
    'evaluations': evaluations,
    'classification': classification,
    'price_data': {'current_price': 1.2345, 'change_pct': 2.15}
}
analysis = analyzer.analyze_market_narrative(market_data, fund_code='000001')

# 4. 自然语言查询
nl_interface = NaturalLanguageInterface(analyzer)
context_data = {
    'fund_data': {'000001': {'nav': 1.2345, 'change_pct': 2.15}},
    'market_analysis': analysis
}

queries = [
    "当前市场状态如何？",
    "基金000001的表现怎么样？",
    "现在适合买入吗？"
]

for query in queries:
    result = nl_interface.process_query(query, context_data)
    print(f"Q: {query}")
    print(f"A: {result['response']}")
    print("-" * 50)
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查环境变量是否正确设置
   - 确认API密钥有效且有足够余额

2. **网络连接问题**
   - 检查网络连接
   - 确认API服务地址可访问

3. **配置文件问题**
   - 检查JSON格式是否正确
   - 确认文件路径和权限

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 测试连接

```python
from config.ai_config import AIConfig

config = AIConfig()
validation = config.validate_config()
print(validation)
```

## 📈 性能优化

### 1. 缓存策略

对于相似的查询，可以实现缓存机制：

```python
import hashlib
import json
from functools import lru_cache

@lru_cache(maxsize=100)
def cached_analysis(data_hash):
    # 实现缓存逻辑
    pass
```

### 2. 异步处理

对于批量分析，可以使用异步处理：

```python
import asyncio
import aiohttp

async def async_analyze_batch(data_list):
    # 实现异步批量分析
    pass
```

### 3. 超时设置

合理设置超时时间：

```json
{
  "analysis_settings": {
    "timeout": 30,
    "retry_attempts": 3
  }
}
```

## 🔒 安全考虑

1. **API密钥安全**
   - 不要在代码中硬编码API密钥
   - 使用环境变量或安全的配置管理

2. **数据隐私**
   - 避免发送敏感的个人信息到AI服务
   - 考虑数据脱敏处理

3. **访问控制**
   - 限制AI功能的访问权限
   - 实现适当的用户认证

## 📚 扩展开发

### 添加新的AI提供商

1. 在配置文件中添加新提供商：
```json
{
  "llm_providers": {
    "new_provider": {
      "api_key": "your-key",
      "base_url": "https://api.newprovider.com/v1",
      "model": "new-model",
      "enabled": true
    }
  }
}
```

2. 在`LLMMarketAnalyzer`中添加支持：
```python
def _initialize_llm_client(self):
    # 添加新提供商的初始化逻辑
    pass
```

### 自定义分析模板

创建自定义的分析提示词模板：

```python
def _get_custom_system_prompt(self, analysis_type: str) -> str:
    templates = {
        'risk_analysis': "你是一个风险管理专家...",
        'trend_analysis': "你是一个趋势分析师...",
        # 添加更多模板
    }
    return templates.get(analysis_type, self._get_system_prompt())
```

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📞 支持

如有问题，请：

1. 查看本文档的故障排除部分
2. 运行测试脚本检查配置
3. 查看日志文件获取详细错误信息
4. 提交Issue描述问题

---

**注意**：AI功能需要网络连接和有效的API密钥。在生产环境中使用前，请充分测试所有功能。