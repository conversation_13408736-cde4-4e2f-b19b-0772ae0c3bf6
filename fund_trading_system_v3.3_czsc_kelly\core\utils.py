"""
核心工具函数和通用导入
包含系统中使用的通用工具函数和导入语句
"""

import os
import sys
import time
import random
import logging
import numpy as np
import pandas as pd
import talib as ta
import json
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Union, Optional, Tuple, Any, Callable
from collections import OrderedDict, deque
from abc import ABC, abstractmethod
from scipy.stats import norm
from concurrent.futures import ThreadPoolExecutor, as_completed

# 忽略警告
warnings.filterwarnings('ignore')

# 导入真实数据分析模块
try:
    # 优先尝试使用最小化版本的CZSC函数
    current_file_dir = os.path.dirname(os.path.abspath(__file__))  # fund_trading_system_v3/core
    project_v3_dir = os.path.dirname(current_file_dir)  # fund_trading_system_v3
    project_root_dir = os.path.dirname(project_v3_dir)  # CzscSystem
    
    if project_root_dir not in sys.path:
        sys.path.insert(0, project_root_dir)
    
    # 首先尝试导入最小化版本
    try:
        from czsc_func_minimal import get_kline, get_realtime_quote
        logging.info("使用最小化CZSC函数版本")
        CZSC_FUNC_AVAILABLE = True
    except ImportError:
        # 如果最小化版本不可用，尝试原版本
        from czsc_func import get_kline, get_realtime_quote
        logging.info("使用原版CZSC函数")
        CZSC_FUNC_AVAILABLE = True
    
    # 尝试导入增强功能（可选）
    try:
        from enhanced_czsc_func import (
            get_enhanced_technical_indicators,
            calculate_real_fund_flow_strength,
            calculate_market_sentiment_indicators,
            calculate_multi_timeframe_coordination,
            get_real_confidence_metrics,
            analyze_real_gua_from_price_action,
            get_volume_profile_analysis,
            calculate_trend_strength_metrics,
            validate_data_quality
        )
        logging.info("增强CZSC功能可用")
    except ImportError:
        logging.warning("增强CZSC功能不可用，使用基础功能")
        # 提供基础的兜底实现
        def get_enhanced_technical_indicators(symbol, freq):
            return {'indicators': {
                'ma5': 10.0, 'ma20': 10.0, 'ma60': 10.0,  # 添加ma60
                'rsi': 50, 'macd': 0, 'macd_signal': 0, 'macd_bullish': False, 
                'volume_ratio': 1.0, 'bb_position': 0.5, 'atr_ratio': 1.0
            }}
        
        def get_real_confidence_metrics(symbol, indicators):
            return 0.5
        
        def validate_data_quality(symbol, data):
            return {'overall_quality': True, 'quality_score': 0.8, 'issues': []}
        
        def calculate_trend_strength_metrics(symbol):
            return {'trend_strength': 0.5, 'trend_direction': 'neutral'}
except ImportError as e:
    CZSC_FUNC_AVAILABLE = False
    logging.warning(f"CZSC functions not available: {e}")

# 导入puppet交易库
try:
    import puppet
    PUPPET_AVAILABLE = True
except ImportError:
    PUPPET_AVAILABLE = False
    logging.warning("Puppet trading library not available")

# 导入CZSC库
try:
    from czsc_master.czsc import CZSC, Freq, RawBar as CzscRawBar
    from czsc_master.czsc.analyze import BI, FX
    CZSC_AVAILABLE = True
except ImportError:
    CZSC_AVAILABLE = False
    logging.warning("CZSC library not available")

# 检查是否有增强版功能
try:
    from czsc_master.czsc.utils.corr import cross_correlation  # 测试特定功能
    CZSC_ENHANCED_AVAILABLE = True
except ImportError:
    CZSC_ENHANCED_AVAILABLE = False

# ==================== 兜底函数定义 ====================
# 当CZSC函数不可用时，提供基本的兜底实现

def get_fallback_kline_data(symbol: str, period: str = "1d", count: int = 100) -> pd.DataFrame:
    """
    兜底K线数据获取函数
    当CZSC函数不可用时，返回模拟数据
    """
    logging.warning(f"使用兜底模式获取K线数据: {symbol}")
    
    # 生成模拟K线数据
    dates = pd.date_range(end=datetime.now(), periods=count, freq='D')
    base_price = 1.0 + random.random() * 10  # 基础价格1-11之间
    
    data = []
    for i, date in enumerate(dates):
        # 模拟价格波动
        price_change = (random.random() - 0.5) * 0.1  # -5%到+5%的波动
        open_price = base_price * (1 + price_change)
        high_price = open_price * (1 + random.random() * 0.05)  # 最高价
        low_price = open_price * (1 - random.random() * 0.05)   # 最低价
        close_price = open_price + (random.random() - 0.5) * 0.02 * open_price
        volume = random.randint(100000, 1000000)  # 随机成交量
        
        data.append({
            'dt': date,
            'open': round(open_price, 4),
            'high': round(high_price, 4),
            'low': round(low_price, 4),
            'close': round(close_price, 4),
            'volume': volume
        })
        
        base_price = close_price  # 下一根K线的基础价格
    
    return pd.DataFrame(data)

def get_fallback_realtime_quote(symbol: str) -> Dict[str, Any]:
    """
    兜底实时行情获取函数
    当CZSC函数不可用时，返回模拟数据
    """
    logging.warning(f"使用兜底模式获取实时行情: {symbol}")
    
    base_price = 1.0 + random.random() * 10
    return {
        'symbol': symbol,
        'price': round(base_price, 4),
        'change': round((random.random() - 0.5) * 0.2, 4),  # -10%到+10%
        'change_pct': round((random.random() - 0.5) * 20, 2),  # -10%到+10%
        'volume': random.randint(100000, 1000000),
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

def get_fallback_technical_indicators(df: pd.DataFrame) -> Dict[str, Any]:
    """
    兜底技术指标计算函数
    """
    if df.empty:
        return {}
    
    try:
        close_prices = df['close'].values
        high_prices = df['high'].values
        low_prices = df['low'].values
        volumes = df['volume'].values
        
        # 计算基本技术指标
        indicators = {}
        
        # 移动平均线
        if len(close_prices) >= 5:
            indicators['ma5'] = np.mean(close_prices[-5:])
        if len(close_prices) >= 20:
            indicators['ma20'] = np.mean(close_prices[-20:])
        
        # RSI
        if len(close_prices) >= 14:
            indicators['rsi'] = ta.RSI(close_prices, timeperiod=14)[-1] if len(close_prices) >= 14 else 50
        
        # MACD
        if len(close_prices) >= 26:
            macd, macdsignal, macdhist = ta.MACD(close_prices)
            indicators['macd'] = macd[-1] if not np.isnan(macd[-1]) else 0
            indicators['macd_signal'] = macdsignal[-1] if not np.isnan(macdsignal[-1]) else 0
            indicators['macd_hist'] = macdhist[-1] if not np.isnan(macdhist[-1]) else 0
        
        # 布林带
        if len(close_prices) >= 20:
            upper, middle, lower = ta.BBANDS(close_prices, timeperiod=20)
            indicators['bb_upper'] = upper[-1] if not np.isnan(upper[-1]) else close_prices[-1] * 1.02
            indicators['bb_middle'] = middle[-1] if not np.isnan(middle[-1]) else close_prices[-1]
            indicators['bb_lower'] = lower[-1] if not np.isnan(lower[-1]) else close_prices[-1] * 0.98
        
        return indicators
        
    except Exception as e:
        logging.warning(f"技术指标计算失败: {e}")
        return {}

# 根据CZSC函数可用性，设置实际使用的函数
if not CZSC_FUNC_AVAILABLE:
    # 当CZSC函数不可用时，使用兜底函数
    get_kline = get_fallback_kline_data
    get_realtime_quote = get_fallback_realtime_quote
    get_enhanced_technical_indicators = get_fallback_technical_indicators
    
    # 其他兜底函数
    def calculate_real_fund_flow_strength(symbol, price_data, volume_data=None, *args, **kwargs):
        """基于价格和成交量的资金流向分析兜底实现"""
        import random
        
        try:
            # 基于价格数据分析资金流向
            if isinstance(price_data, dict):
                change_rate = price_data.get('change_rate', 0)
                volume = price_data.get('volume', 0)
                price = price_data.get('price', 0)
                
                # 简单的资金流向判断逻辑
                if change_rate > 2 and volume > 10000:
                    flow_direction = '净流入'
                    flow_strength = min(0.8, 0.5 + abs(change_rate) * 0.05)
                    high_liquidity = volume > 20000
                elif change_rate < -2 and volume > 10000:
                    flow_direction = '净流出'
                    flow_strength = min(0.7, 0.4 + abs(change_rate) * 0.05)
                    high_liquidity = volume > 20000
                else:
                    flow_direction = '平衡'
                    flow_strength = 0.5
                    high_liquidity = volume > 15000
                
                # 计算成交量比率
                volume_ratio = max(0.5, min(3.0, volume / 10000)) if volume > 0 else 1.0
                
                return {
                    'flow_strength': flow_strength,
                    'flow_direction': flow_direction,
                    'capital_flow': flow_direction,
                    'high_liquidity': high_liquidity,
                    'volume_ratio': volume_ratio,
                    'volume_level': 'high' if volume > 20000 else 'normal' if volume > 5000 else 'low',
                    'price_data': {
                        'price': price,
                        'change_rate': change_rate,
                        'volume': volume
                    }
                }
            else:
                # 默认模拟数据
                return {
                    'flow_strength': 0.5,
                    'flow_direction': '平衡',
                    'capital_flow': '平衡',
                    'high_liquidity': False,
                    'volume_ratio': 1.0,
                    'volume_level': 'normal',
                    'price_data': {
                        'price': 10.0,
                        'change_rate': 0.0,
                        'volume': 10000
                    }
                }
                
        except Exception as e:
            logging.warning(f"资金流向分析失败: {e}")
            return {
                'flow_strength': 0.5,
                'flow_direction': '平衡',
                'capital_flow': '平衡',
                'high_liquidity': False,
                'volume_ratio': 1.0,
                'volume_level': 'normal',
                'price_data': {'price': 10.0, 'change_rate': 0.0, 'volume': 10000}
            }
    
    def calculate_market_sentiment_indicators(*args, **kwargs):
        return {'sentiment_score': 0.5, 'sentiment_label': 'neutral'}
    
    def calculate_multi_timeframe_coordination(*args, **kwargs):
        return {'coordination_score': 0.5}
    
    def get_real_confidence_metrics(*args, **kwargs):
        return {'confidence': 0.5}
    
    def analyze_real_gua_from_price_action(symbol, price_data, *args, **kwargs):
        """基于价格行为的卦象分析兜底实现"""
        import random
        
        # 模拟卦象分析
        gua_names = ['乾', '坤', '震', '巽', '坎', '离', '艮', '兑']
        gua_meanings = {
            '乾': '强势上涨，宜买入',
            '坤': '温和下跌，宜观望', 
            '震': '震荡整理，谨慎操作',
            '巽': '缓慢上升，可适量买入',
            '坎': '风险较大，建议减仓',
            '离': '热点题材，短线机会',
            '艮': '止跌企稳，等待时机',
            '兑': '获利了结，适合卖出'
        }
        
        # 基于价格数据简单判断
        if isinstance(price_data, dict) and 'change_rate' in price_data:
            change_rate = price_data.get('change_rate', 0)
            if change_rate > 3:
                gua_name = '乾'
                confidence = 0.7
            elif change_rate > 1:
                gua_name = '巽'
                confidence = 0.6
            elif change_rate < -3:
                gua_name = '坤'
                confidence = 0.7
            elif change_rate < -1:
                gua_name = '坎'
                confidence = 0.6
            else:
                gua_name = '震'
                confidence = 0.5
        else:
            gua_name = random.choice(gua_names)
            confidence = 0.4
        
        return {
            'gua_name': gua_name,
            'gua_meaning': gua_meanings.get(gua_name, '未知'),
            'confidence': confidence,
            'is_buy_gua': gua_name in ['乾', '巽', '离'],
            'is_sell_gua': gua_name in ['坤', '坎', '兑'],
            'gua_score': confidence if gua_name in ['乾', '巽', '离'] else -confidence if gua_name in ['坤', '坎', '兑'] else 0
        }
    
    def get_volume_profile_analysis(*args, **kwargs):
        return {'volume_profile': 'normal'}
    
    def calculate_trend_strength_metrics(*args, **kwargs):
        return {'trend_strength': 0.5}
    
    def validate_data_quality(*args, **kwargs):
        return True

# ==================== 通用工具函数 ====================

def safe_divide(a: float, b: float, default: float = 0.0) -> float:
    """安全除法，避免除零错误"""
    try:
        return a / b if b != 0 else default
    except (TypeError, ZeroDivisionError):
        return default

def normalize_score(score: float, min_val: float = 0.0, max_val: float = 1.0) -> float:
    """标准化分数到指定范围"""
    try:
        return max(min_val, min(max_val, float(score)))
    except (TypeError, ValueError):
        return (min_val + max_val) / 2

def calculate_confidence_from_signals(signals: List[Dict]) -> float:
    """从信号列表计算综合置信度"""
    if not signals:
        return 0.5
    
    try:
        confidences = [s.get('confidence', 0.5) for s in signals if isinstance(s, dict)]
        if not confidences:
            return 0.5
        
        # 使用加权平均，权重基于信号强度
        weights = [abs(s.get('strength', 1.0)) for s in signals if isinstance(s, dict)]
        if not weights:
            weights = [1.0] * len(confidences)
        
        weighted_sum = sum(c * w for c, w in zip(confidences, weights))
        weight_sum = sum(weights)
        
        return weighted_sum / weight_sum if weight_sum > 0 else 0.5
        
    except Exception as e:
        logging.warning(f"置信度计算失败: {e}")
        return 0.5

def format_percentage(value: float, decimal_places: int = 2) -> str:
    """格式化百分比显示"""
    try:
        return f"{value * 100:.{decimal_places}f}%"
    except (TypeError, ValueError):
        return "N/A"

def get_market_status() -> str:
    """获取市场状态"""
    now = datetime.now()
    hour = now.hour
    
    # 简单的市场时间判断
    if 9 <= hour < 11.5 or 13 <= hour < 15:
        return "trading"
    elif 15 <= hour < 21:
        return "after_hours"
    else:
        return "closed"

# 日志辅助函数
def log_performance(func_name: str, start_time: float, success: bool = True):
    """记录函数性能"""
    duration = time.time() - start_time
    status = "成功" if success else "失败"
    logging.debug(f"{func_name} {status}, 耗时: {duration:.3f}秒")

# 数据验证函数
def validate_fund_code(fund_code: str) -> bool:
    """验证基金代码格式"""
    if not isinstance(fund_code, str):
        return False
    
    # 基金代码通常是6位数字
    return fund_code.isdigit() and len(fund_code) == 6

def validate_price_data(data: Dict) -> bool:
    """验证价格数据的有效性"""
    if not isinstance(data, dict):
        return False
    
    required_fields = ['open', 'high', 'low', 'close']
    for field in required_fields:
        if field not in data:
            return False
        try:
            float(data[field])
        except (TypeError, ValueError):
            return False
    
    # 价格逻辑验证
    try:
        o, h, l, c = float(data['open']), float(data['high']), float(data['low']), float(data['close'])
        return h >= max(o, c) and l <= min(o, c) and all(p > 0 for p in [o, h, l, c])
    except:
        return False
