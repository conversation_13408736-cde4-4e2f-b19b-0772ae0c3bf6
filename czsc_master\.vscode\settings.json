{"python.testing.pytestArgs": ["test"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.formatting.provider": "none", "python.formatting.autopep8Args": ["--max-line-length", "120"], "[python]": {"editor.defaultFormatter": "ms-python.black-formatter"}, "python.formatting.blackArgs": ["--line-length", "120"], "typescript.locale": "zh-CN", "python.analysis.typeCheckingMode": "basic"}