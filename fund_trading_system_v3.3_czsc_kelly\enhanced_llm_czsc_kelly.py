"""
LLM + CZSC + 凯利公式集成决策系统
结合大语言模型分析、缠中说禅技术分析和凯利公式仓位管理
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import json
from dataclasses import dataclass
from abc import ABC, abstractmethod

@dataclass
class TradingSignal:
    """交易信号数据结构"""
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0-1之间的置信度
    target_price: float
    stop_loss: float
    take_profit: float
    kelly_position: float  # 凯利公式计算的仓位
    czsc_level: int  # CZSC信号强度等级
    llm_reasoning: str  # LLM分析推理
    risk_reward_ratio: float
    win_probability: float

class LLMCZSCKellyIntegrator:
    """LLM + CZSC + 凯利公式集成分析器"""
    
    def __init__(self, llm_client=None, czsc_analyzer=None):
        self.llm_client = llm_client or self._init_llm_client()
        self.czsc_analyzer = czsc_analyzer or self._init_czsc_analyzer()
        self.kelly_calculator = KellyFormulaCalculator()
        self.position_manager = PositionManager()
        
    def analyze_and_decide(self, symbol: str, market_data: Dict) -> TradingSignal:
        """综合分析并生成交易决策"""
        
        # 1. CZSC技术分析
        czsc_result = self._czsc_analysis(market_data)
        
        # 2. LLM市场分析
        llm_analysis = self._llm_market_analysis(symbol, market_data, czsc_result)
        
        # 3. 整合分析结果
        integrated_signal = self._integrate_signals(czsc_result, llm_analysis)
        
        # 4. 凯利公式仓位计算
        kelly_position = self._calculate_kelly_position(integrated_signal)
        
        # 5. 生成最终交易信号
        return self._generate_trading_signal(
            symbol, integrated_signal, kelly_position
        )
    
    def _czsc_analysis(self, market_data: Dict) -> Dict:
        """CZSC技术分析"""
        # 基于现有的czsc相关文件进行分析
        klines = market_data['klines']
        
        # 计算CZSC信号
        czsc_signals = {
            'trend_direction': self._analyze_trend(klines),
            'support_resistance': self._find_support_resistance(klines),
            'signal_strength': self._calculate_signal_strength(klines),
            'entry_points': self._identify_entry_points(klines),
            'risk_levels': self._assess_risk_levels(klines)
        }
        
        return czsc_signals
    
    def _llm_market_analysis(self, symbol: str, market_data: Dict, czsc_result: Dict) -> Dict:
        """LLM市场分析"""
        
        # 构建LLM分析提示词
        prompt = self._build_analysis_prompt(symbol, market_data, czsc_result)
        
        # 调用LLM进行分析
        llm_response = self.llm_client.analyze(prompt)
        
        # 解析LLM响应
        return self._parse_llm_response(llm_response)
    
    def _build_analysis_prompt(self, symbol: str, market_data: Dict, czsc_result: Dict) -> str:
        """构建LLM分析提示词"""
        
        prompt = f"""
        作为专业的量化交易分析师，请基于以下信息进行综合分析：

        标的代码：{symbol}
        
        技术分析结果（CZSC）：
        - 趋势方向：{czsc_result['trend_direction']}
        - 支撑阻力：{czsc_result['support_resistance']}
        - 信号强度：{czsc_result['signal_strength']}
        - 入场点位：{czsc_result['entry_points']}
        - 风险等级：{czsc_result['risk_levels']}
        
        市场数据：
        - 当前价格：{market_data.get('current_price', 'N/A')}
        - 成交量：{market_data.get('volume', 'N/A')}
        - 波动率：{market_data.get('volatility', 'N/A')}
        
        请从以下维度进行分析并给出具体建议：
        
        1. 交易方向判断（买入/卖出/观望）
        2. 具体入场价位区间
        3. 止损价位设置
        4. 目标价位预期
        5. 胜率评估（0-100%）
        6. 风险收益比评估
        7. 建议仓位比例（考虑风险控制）
        8. 详细分析推理过程
        
        请以JSON格式返回分析结果：
        {{
            "direction": "buy/sell/hold",
            "entry_price_range": [min_price, max_price],
            "stop_loss": price,
            "take_profit": price,
            "win_probability": 0.0-1.0,
            "risk_reward_ratio": ratio,
            "suggested_position": 0.0-1.0,
            "confidence": 0.0-1.0,
            "reasoning": "详细分析过程"
        }}
        """
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> Dict:
        """解析LLM响应"""
        try:
            # 尝试解析JSON响应
            analysis = json.loads(response)
            return analysis
        except json.JSONDecodeError:
            # 如果JSON解析失败，使用文本解析
            return self._parse_text_response(response)
    
    def _integrate_signals(self, czsc_result: Dict, llm_analysis: Dict) -> Dict:
        """整合CZSC和LLM分析结果"""
        
        # 信号一致性检查
        signal_consistency = self._check_signal_consistency(czsc_result, llm_analysis)
        
        # 综合置信度计算
        combined_confidence = self._calculate_combined_confidence(
            czsc_result, llm_analysis, signal_consistency
        )
        
        # 整合后的信号
        integrated_signal = {
            'direction': llm_analysis.get('direction', 'hold'),
            'entry_price': self._determine_entry_price(czsc_result, llm_analysis),
            'stop_loss': llm_analysis.get('stop_loss'),
            'take_profit': llm_analysis.get('take_profit'),
            'win_probability': llm_analysis.get('win_probability', 0.5),
            'risk_reward_ratio': llm_analysis.get('risk_reward_ratio', 1.0),
            'confidence': combined_confidence,
            'czsc_strength': czsc_result.get('signal_strength', 0),
            'reasoning': llm_analysis.get('reasoning', '')
        }
        
        return integrated_signal
    
    def _calculate_kelly_position(self, signal: Dict) -> float:
        """使用凯利公式计算最优仓位"""
        
        win_prob = signal['win_probability']
        risk_reward = signal['risk_reward_ratio']
        confidence = signal['confidence']
        
        # 凯利公式：f = (bp - q) / b
        # 其中：b = 赔率，p = 胜率，q = 败率
        if risk_reward <= 0 or win_prob <= 0:
            return 0.0
        
        kelly_fraction = self.kelly_calculator.calculate_optimal_position(
            win_probability=win_prob,
            win_loss_ratio=risk_reward,
            confidence_adjustment=confidence
        )
        
        return kelly_fraction
    
    def _generate_trading_signal(self, symbol: str, signal: Dict, kelly_position: float) -> TradingSignal:
        """生成最终交易信号"""
        
        return TradingSignal(
            symbol=symbol,
            signal_type=signal['direction'],
            confidence=signal['confidence'],
            target_price=signal['entry_price'],
            stop_loss=signal['stop_loss'],
            take_profit=signal['take_profit'],
            kelly_position=kelly_position,
            czsc_level=signal['czsc_strength'],
            llm_reasoning=signal['reasoning'],
            risk_reward_ratio=signal['risk_reward_ratio'],
            win_probability=signal['win_probability']
        )

class KellyFormulaCalculator:
    """凯利公式计算器"""
    
    def __init__(self, max_position: float = 0.25, min_position: float = 0.01):
        self.max_position = max_position  # 最大仓位限制
        self.min_position = min_position  # 最小仓位限制
    
    def calculate_optimal_position(self, 
                                 win_probability: float,
                                 win_loss_ratio: float,
                                 confidence_adjustment: float = 1.0) -> float:
        """
        计算凯利公式最优仓位
        
        Args:
            win_probability: 胜率 (0-1)
            win_loss_ratio: 盈亏比 (盈利/亏损)
            confidence_adjustment: 置信度调整因子 (0-1)
        
        Returns:
            最优仓位比例 (0-1)
        """
        
        # 基础凯利公式
        lose_probability = 1 - win_probability
        kelly_fraction = (win_probability * win_loss_ratio - lose_probability) / win_loss_ratio
        
        # 置信度调整
        adjusted_kelly = kelly_fraction * confidence_adjustment
        
        # 应用仓位限制
        final_position = np.clip(adjusted_kelly, self.min_position, self.max_position)
        
        return max(0.0, final_position)
    
    def calculate_fractional_kelly(self, kelly_fraction: float, fraction: float = 0.25) -> float:
        """
        计算分数凯利仓位（更保守的策略）
        
        Args:
            kelly_fraction: 完整凯利公式结果
            fraction: 分数比例，通常为0.25（四分之一凯利）
        
        Returns:
            调整后的仓位
        """
        return kelly_fraction * fraction

class PositionManager:
    """仓位管理器"""
    
    def __init__(self):
        self.current_positions = {}
        self.risk_limits = {
            'max_single_position': 0.20,  # 单个标的最大仓位
            'max_total_position': 0.80,   # 总仓位上限
            'max_sector_exposure': 0.30   # 单个行业最大敞口
        }
    
    def validate_position(self, symbol: str, proposed_position: float) -> Tuple[bool, float, str]:
        """
        验证仓位是否符合风险管理要求
        
        Returns:
            (是否通过, 调整后仓位, 说明信息)
        """
        
        # 检查单个标的仓位限制
        if proposed_position > self.risk_limits['max_single_position']:
            adjusted_position = self.risk_limits['max_single_position']
            return False, adjusted_position, f"单个标的仓位超限，调整至{adjusted_position:.2%}"
        
        # 检查总仓位限制
        current_total = sum(self.current_positions.values())
        if current_total + proposed_position > self.risk_limits['max_total_position']:
            max_allowed = self.risk_limits['max_total_position'] - current_total
            return False, max_allowed, f"总仓位将超限，最大可用仓位{max_allowed:.2%}"
        
        return True, proposed_position, "仓位验证通过"

# 使用示例
def example_usage():
    """使用示例"""
    
    # 初始化集成分析器
    integrator = LLMCZSCKellyIntegrator()
    
    # 模拟市场数据
    market_data = {
        'klines': pd.DataFrame(),  # K线数据
        'current_price': 10.50,
        'volume': 1000000,
        'volatility': 0.25
    }
    
    # 进行综合分析
    signal = integrator.analyze_and_decide('000001', market_data)
    
    print(f"交易信号：{signal.signal_type}")
    print(f"目标价位：{signal.target_price}")
    print(f"凯利仓位：{signal.kelly_position:.2%}")
    print(f"置信度：{signal.confidence:.2%}")
    print(f"分析推理：{signal.llm_reasoning}")

if __name__ == "__main__":
    example_usage()