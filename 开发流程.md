## 请帮我通过7个维度分析我的项目需求，将模糊想法细化为明确目标：

1. 用户：谁会使用这个产品/功能？
2. 场景：在什么情况下使用？
3. 痛点：用户面临什么问题？
4. 当前方案：用户现在如何解决这个问题？
5. 使用频度：用户多久会使用一次？
6. 使用广度：有多少用户会使用？
7. 用户希望获得的能力：用户期望通过这个产品获得什么？

请通过提问引导我思考这些问题，然后整理成表格形式呈现。

## 基于我们刚才的需求分析，请帮我使用"三三原则"收敛为v0.1版本的MVP：

- 最多选择3个核心需求
- 每个需求最多拆解为3个功能点

请以表格形式呈现这些核心需求和功能点，并解释为什么选择这些作为首要实现的功能。

## 按照前面讨论的第一版MVP，请你帮我完成以下几件事：

1. 写出一份产品需求文档（prd.md）
2. 基于PRD，生成一份工程结构和数据结构设计文档（erd.md）
3. 把erd.md中的模块拆解为任务清单，输出一份开发待办列表（todolist.md）

这些文档将用于指导AI辅助开发过程。

## 我是一个编程小白，我希望从零开始开发一个「[项目名称]」，这是一个全新的项目。请你阅读`doc`目录下的以下三个文档：
- `@prd.md`（产品需求文档）
- `@erd.md`（工程结构与数据结构设计）
- `@todolist.md`（开发任务清单）

你的任务是：
1. **严格按照@todolist.md中的顺序**，每次只执行一个待办任务（Todo），从项目结构搭建开始，逐步推进。
2. 每完成一个子任务，**在@todolist.md中勾选对应项**，并注明执行人/时间（可用注释）。
3. 每完成一个阶段，请说明你做了哪些修改（包含文件名、文件位置、代码逻辑、UI变动），并用注释或总结简洁说明。
4. 所有代码都必须加清晰注释，特别是变量和函数部分，方便我理解。
5. 不要提前做未列出的功能，不要重构已有内容，不要修改或删除todolist中的其他条目。
6. 如果遇到依赖未完成、结构不清等问题，可以跳过，但需要写清楚原因并在todolist中注明。

## 你刚刚完成了一个ToDo开发任务，请将本次开发内容以日志的形式写入doc目录下的`cursor-log.md`中。日志格式如下：

## ✅ 功能名称：[从todolist中复制本项标题]

**完成时间**：[时间]

**修改内容**：
- 文件：[文件路径]
- 变更：[简要描述代码变更]

**实现思路**：
[简述实现逻辑和关键点]

**遇到的问题**：
[如有问题，记录问题和解决方法]

**下一步计划**：
[下一个要实现的功能点]

## 请对我当前完成的功能进行代码审查，重点关注以下几个方面：

1. 代码质量：是否有冗余、重复或难以理解的代码？
2. 性能优化：有没有可能的性能瓶颈？
3. 用户体验：从用户角度，交互是否流畅直观？
4. 可维护性：代码结构是否清晰，注释是否充分？
5. 潜在问题：是否有潜在的bug或边缘情况未处理？

请提供具体的优化建议，并说明优先级。

## 基于当前v0.1版本的实现情况，请帮我规划下一个版本（v0.2）的开发内容：

1. 评估当前版本中的不足和用户可能的新需求
2. 应用"三三原则"，确定v0.2版本的3个核心需求和各自的3个功能点
3. 更新todolist.md，添加v0.2版本的开发任务

请以表格形式呈现v0.2版本的核心需求和功能点。