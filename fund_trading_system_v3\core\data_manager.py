"""
数据管理器 - 集成Tushare数据提供器
为fund_trading_system_v3提供统一的数据接口
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Union
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加tushare_data_provider到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
tushare_provider_path = os.path.join(project_root, 'tushare_data_provider')
sys.path.insert(0, tushare_provider_path)

# 导入tushare数据提供器
from tushare_data_provider import TushareDataProvider, DataRequest, FIVE_YEAR_CONFIG, REALTIME_CONFIG
from tushare_data_provider.data_structures import ComprehensiveData

# 导入系统核心数据结构
from .data_structures import RawBar, MultiDimensionalMarketState, DimensionEvaluationResult
from .enums import MarketRegime


class DataManager:
    """数据管理器 - 系统数据获取和管理的统一入口"""
    
    def __init__(self, config_type: str = 'default'):
        """
        初始化数据管理器
        
        Args:
            config_type: 配置类型 ('default', 'realtime', '5year')
        """
        self.logger = self._setup_logger()
        
        # 根据配置类型选择数据提供器配置
        if config_type == 'realtime':
            self.data_provider = TushareDataProvider(REALTIME_CONFIG)
        elif config_type == '5year':
            self.data_provider = TushareDataProvider(FIVE_YEAR_CONFIG)
        else:
            self.data_provider = TushareDataProvider()
            
        self.logger.info(f"数据管理器初始化完成，配置类型: {config_type}")
        
        # 数据缓存
        self._cache = {}
        self._last_update = {}
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('DataManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def get_market_data(self, 
                       symbols: List[str], 
                       start_date: str = None, 
                       end_date: str = None,
                       freq: str = 'D') -> Dict[str, List[RawBar]]:
        """
        获取市场数据并转换为系统格式
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            freq: 频率 ('D', 'W', 'M')
            
        Returns:
            Dict[symbol, List[RawBar]]: 按股票代码分组的K线数据
        """
        self.logger.info(f"获取市场数据: {len(symbols)} 只股票")
        
        # 设置默认日期
        if end_date is None:
            end_date = datetime.now().strftime("%Y%m%d")
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
        
        # 创建数据请求
        request = DataRequest(
            ts_codes=symbols,
            start_date=start_date,
            end_date=end_date,
            freq=freq,
            include_basic=True,
            include_technical=False,  # 基础数据不需要技术指标
            include_macro=False,
            include_news=False,
            include_fund_flow=False
        )
        
        # 获取数据
        response = self.data_provider.get_comprehensive_data(request)
        
        if not response.success:
            self.logger.error(f"获取市场数据失败: {response.error_message}")
            return {}
        
        # 转换为系统格式
        result = {}
        for data_item in response.data:
            symbol = data_item.ts_code
            
            if symbol not in result:
                result[symbol] = []
            
            # 转换为RawBar格式
            raw_bar = RawBar(
                symbol=symbol,
                dt=datetime.strptime(data_item.trade_date, '%Y%m%d'),
                id=int(data_item.trade_date),
                freq=freq,
                open=data_item.market_data.open,
                close=data_item.market_data.close,
                high=data_item.market_data.high,
                low=data_item.market_data.low,
                vol=data_item.market_data.vol,
                amount=data_item.market_data.amount
            )
            
            result[symbol].append(raw_bar)
        
        # 按时间排序
        for symbol in result:
            result[symbol].sort(key=lambda x: x.dt)
        
        self.logger.info(f"成功获取 {len(result)} 只股票的市场数据")
        return result
    
    def get_comprehensive_analysis_data(self, 
                                      symbols: List[str], 
                                      start_date: str = None, 
                                      end_date: str = None) -> Dict[str, List[ComprehensiveData]]:
        """
        获取综合分析数据（包含50维特征）
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict[symbol, List[ComprehensiveData]]: 综合数据
        """
        self.logger.info(f"获取综合分析数据: {len(symbols)} 只股票")
        
        # 设置默认日期
        if end_date is None:
            end_date = datetime.now().strftime("%Y%m%d")
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=90)).strftime("%Y%m%d")
        
        # 创建完整数据请求
        request = DataRequest(
            ts_codes=symbols,
            start_date=start_date,
            end_date=end_date,
            include_basic=True,
            include_technical=True,
            include_macro=True,
            include_news=True,
            include_fund_flow=True
        )
        
        # 获取数据
        response = self.data_provider.get_comprehensive_data(request)
        
        if not response.success:
            self.logger.error(f"获取综合分析数据失败: {response.error_message}")
            return {}
        
        # 按股票代码分组
        result = {}
        for data_item in response.data:
            symbol = data_item.ts_code
            
            if symbol not in result:
                result[symbol] = []
            
            result[symbol].append(data_item)
        
        # 按时间排序
        for symbol in result:
            result[symbol].sort(key=lambda x: x.trade_date)
        
        self.logger.info(f"成功获取 {len(result)} 只股票的综合分析数据")
        return result
    
    def get_feature_matrix(self, 
                          symbols: List[str], 
                          start_date: str = None, 
                          end_date: str = None) -> Dict[str, np.ndarray]:
        """
        获取50维特征矩阵
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict[symbol, np.ndarray]: 特征矩阵 (时间步数, 50)
        """
        self.logger.info(f"获取特征矩阵: {len(symbols)} 只股票")
        
        # 获取综合数据
        comprehensive_data = self.get_comprehensive_analysis_data(symbols, start_date, end_date)
        
        result = {}
        for symbol, data_list in comprehensive_data.items():
            if not data_list:
                continue
                
            # 转换为特征矩阵
            feature_matrix = []
            for data_item in data_list:
                feature_vector = data_item.to_feature_vector()
                feature_matrix.append(feature_vector)
            
            result[symbol] = np.array(feature_matrix)
            self.logger.info(f"{symbol}: 特征矩阵形状 {result[symbol].shape}")
        
        return result
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, ComprehensiveData]:
        """
        获取实时数据
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            Dict[symbol, ComprehensiveData]: 最新数据
        """
        self.logger.info(f"获取实时数据: {len(symbols)} 只股票")
        
        response = self.data_provider.get_realtime_data(symbols)
        
        if not response.success:
            self.logger.error(f"获取实时数据失败: {response.error_message}")
            return {}
        
        # 获取每只股票的最新数据
        result = {}
        for data_item in response.data:
            symbol = data_item.ts_code
            
            # 保留最新的数据
            if symbol not in result or data_item.trade_date > result[symbol].trade_date:
                result[symbol] = data_item
        
        self.logger.info(f"成功获取 {len(result)} 只股票的实时数据")
        return result
    
    def get_5year_historical_data(self, symbols: List[str]) -> Dict[str, List[ComprehensiveData]]:
        """
        获取5年历史数据
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            Dict[symbol, List[ComprehensiveData]]: 5年历史数据
        """
        self.logger.info(f"获取5年历史数据: {len(symbols)} 只股票")
        
        response = self.data_provider.get_5year_data(symbols)
        
        if not response.success:
            self.logger.error(f"获取5年历史数据失败: {response.error_message}")
            return {}
        
        # 按股票代码分组
        result = {}
        for data_item in response.data:
            symbol = data_item.ts_code
            
            if symbol not in result:
                result[symbol] = []
            
            result[symbol].append(data_item)
        
        # 按时间排序
        for symbol in result:
            result[symbol].sort(key=lambda x: x.trade_date)
        
        self.logger.info(f"成功获取 {len(result)} 只股票的5年历史数据")
        return result
    
    def convert_to_multidimensional_state(self, 
                                        comprehensive_data: ComprehensiveData) -> MultiDimensionalMarketState:
        """
        将综合数据转换为多维度市场状态
        
        Args:
            comprehensive_data: 综合数据
            
        Returns:
            MultiDimensionalMarketState: 多维度市场状态
        """
        # 基于技术指标构建维度评估结果
        tech = comprehensive_data.technical_indicators
        market = comprehensive_data.market_data
        
        # 趋势维度评估
        trend_score = 0.5  # 默认中性
        if tech.ma5 and tech.ma20 and market.close:
            if tech.ma5 > tech.ma20 and market.close > tech.ma5:
                trend_score = 0.8  # 上升趋势
            elif tech.ma5 < tech.ma20 and market.close < tech.ma5:
                trend_score = 0.2  # 下降趋势
        
        trend_result = DimensionEvaluationResult(
            dimension_name="trend",
            state="bullish" if trend_score > 0.6 else "bearish" if trend_score < 0.4 else "neutral",
            score=trend_score,
            confidence=0.8,
            signals=["MA交叉", "价格位置"],
            data_quality="good",
            indicators={"ma5": tech.ma5, "ma20": tech.ma20, "close": market.close}
        )
        
        # 波动性维度评估
        volatility_score = 0.5
        if tech.atr and market.close:
            atr_ratio = tech.atr / market.close
            volatility_score = min(atr_ratio * 10, 1.0)  # 归一化
        
        volatility_result = DimensionEvaluationResult(
            dimension_name="volatility",
            state="high" if volatility_score > 0.7 else "low" if volatility_score < 0.3 else "medium",
            score=volatility_score,
            confidence=0.7,
            signals=["ATR指标"],
            data_quality="good",
            indicators={"atr": tech.atr, "volatility": tech.volatility}
        )
        
        # 流动性维度评估
        liquidity_score = 0.5
        if market.vol and market.turnover_rate:
            # 基于成交量和换手率评估流动性
            liquidity_score = min(market.turnover_rate / 10, 1.0)
        
        liquidity_result = DimensionEvaluationResult(
            dimension_name="liquidity",
            state="good" if liquidity_score > 0.6 else "poor" if liquidity_score < 0.3 else "fair",
            score=liquidity_score,
            confidence=0.6,
            signals=["成交量", "换手率"],
            data_quality="good",
            indicators={"vol": market.vol, "turnover_rate": market.turnover_rate}
        )
        
        # 情绪维度评估
        sentiment_score = 0.5
        if tech.rsi:
            if tech.rsi > 70:
                sentiment_score = 0.8  # 超买
            elif tech.rsi < 30:
                sentiment_score = 0.2  # 超卖
            else:
                sentiment_score = tech.rsi / 100
        
        sentiment_result = DimensionEvaluationResult(
            dimension_name="sentiment",
            state="bullish" if sentiment_score > 0.6 else "bearish" if sentiment_score < 0.4 else "neutral",
            score=sentiment_score,
            confidence=0.7,
            signals=["RSI指标"],
            data_quality="good",
            indicators={"rsi": tech.rsi}
        )
        
        # 结构维度评估（基于布林带）
        structural_score = 0.5
        if tech.boll_upper and tech.boll_lower and market.close:
            boll_position = (market.close - tech.boll_lower) / (tech.boll_upper - tech.boll_lower)
            structural_score = boll_position
        
        structural_result = DimensionEvaluationResult(
            dimension_name="structural",
            state="strong" if structural_score > 0.7 else "weak" if structural_score < 0.3 else "neutral",
            score=structural_score,
            confidence=0.6,
            signals=["布林带位置"],
            data_quality="good",
            indicators={"boll_upper": tech.boll_upper, "boll_lower": tech.boll_lower}
        )
        
        # 转换维度评估（基于MACD）
        transition_score = 0.5
        if tech.macd_dif and tech.macd_dea:
            if tech.macd_dif > tech.macd_dea and tech.macd_dif > 0:
                transition_score = 0.8  # 金叉且在零轴上方
            elif tech.macd_dif < tech.macd_dea and tech.macd_dif < 0:
                transition_score = 0.2  # 死叉且在零轴下方
            else:
                transition_score = 0.5
        
        transition_result = DimensionEvaluationResult(
            dimension_name="transition",
            state="bullish" if transition_score > 0.6 else "bearish" if transition_score < 0.4 else "neutral",
            score=transition_score,
            confidence=0.7,
            signals=["MACD交叉"],
            data_quality="good",
            indicators={"macd_dif": tech.macd_dif, "macd_dea": tech.macd_dea}
        )
        
        # 计算综合得分
        composite_score = (
            trend_score * 0.25 +
            volatility_score * 0.15 +
            liquidity_score * 0.15 +
            sentiment_score * 0.20 +
            structural_score * 0.15 +
            transition_score * 0.10
        )
        
        # 确定市场制度
        if composite_score > 0.7:
            market_regime = MarketRegime.BULL
        elif composite_score < 0.3:
            market_regime = MarketRegime.BEAR
        else:
            market_regime = MarketRegime.SIDEWAYS
        
        # 推荐行动
        if composite_score > 0.6:
            recommended_action = "BUY"
        elif composite_score < 0.4:
            recommended_action = "SELL"
        else:
            recommended_action = "HOLD"
        
        # 风险等级
        risk_level = "HIGH" if volatility_score > 0.7 else "LOW" if volatility_score < 0.3 else "MEDIUM"
        
        return MultiDimensionalMarketState(
            timestamp=datetime.strptime(comprehensive_data.trade_date, '%Y%m%d'),
            trend=trend_result,
            volatility=volatility_result,
            liquidity=liquidity_result,
            sentiment=sentiment_result,
            structural=structural_result,
            transition=transition_result,
            composite_score=composite_score,
            overall_confidence=0.7,
            market_regime=market_regime,
            recommended_action=recommended_action,
            risk_level=risk_level
        )
    
    def get_multidimensional_analysis(self, 
                                    symbols: List[str], 
                                    start_date: str = None, 
                                    end_date: str = None) -> Dict[str, List[MultiDimensionalMarketState]]:
        """
        获取多维度市场分析
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict[symbol, List[MultiDimensionalMarketState]]: 多维度分析结果
        """
        self.logger.info(f"获取多维度分析: {len(symbols)} 只股票")
        
        # 获取综合数据
        comprehensive_data = self.get_comprehensive_analysis_data(symbols, start_date, end_date)
        
        result = {}
        for symbol, data_list in comprehensive_data.items():
            result[symbol] = []
            
            for data_item in data_list:
                multi_state = self.convert_to_multidimensional_state(data_item)
                result[symbol].append(multi_state)
        
        self.logger.info(f"成功生成 {len(result)} 只股票的多维度分析")
        return result
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self._last_update.clear()
        self.data_provider.clear_cache()
        self.logger.info("数据管理器缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        provider_cache = self.data_provider.get_cache_info()
        return {
            'manager_cache_size': len(self._cache),
            'provider_cache_size': provider_cache['cache_size'],
            'last_update_count': len(self._last_update)
        }