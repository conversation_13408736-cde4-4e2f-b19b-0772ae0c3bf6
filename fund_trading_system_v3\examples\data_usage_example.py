"""
数据模块使用示例
展示如何在fund_trading_system_v3中使用新的数据管理器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.data_manager import DataManager
from datetime import datetime, timedelta
import numpy as np


def example_basic_market_data():
    """基础市场数据获取示例"""
    print("=== 基础市场数据获取示例 ===")
    
    # 初始化数据管理器
    data_manager = DataManager(config_type='default')
    
    # 定义股票池
    stock_pool = ['000001.SZ', '600000.SH', '000002.SZ']  # 平安银行、浦发银行、万科A
    
    # 获取最近30天的市场数据
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
    
    print(f"获取股票池数据: {stock_pool}")
    print(f"时间范围: {start_date} - {end_date}")
    
    market_data = data_manager.get_market_data(
        symbols=stock_pool,
        start_date=start_date,
        end_date=end_date
    )
    
    # 分析结果
    for symbol, bars in market_data.items():
        if bars:
            print(f"\n{symbol} 数据统计:")
            print(f"  数据条数: {len(bars)}")
            print(f"  日期范围: {bars[0].dt.strftime('%Y-%m-%d')} 到 {bars[-1].dt.strftime('%Y-%m-%d')}")
            
            # 计算基本统计
            closes = [bar.close for bar in bars]
            volumes = [bar.vol for bar in bars]
            
            print(f"  收盘价范围: {min(closes):.2f} - {max(closes):.2f}")
            print(f"  平均收盘价: {np.mean(closes):.2f}")
            print(f"  平均成交量: {np.mean(volumes):.0f}")
            
            # 计算收益率
            if len(closes) > 1:
                returns = [(closes[i] - closes[i-1]) / closes[i-1] for i in range(1, len(closes))]
                print(f"  平均日收益率: {np.mean(returns)*100:.2f}%")
                print(f"  收益率波动率: {np.std(returns)*100:.2f}%")


def example_feature_matrix_analysis():
    """特征矩阵分析示例"""
    print("\n=== 特征矩阵分析示例 ===")
    
    data_manager = DataManager()
    
    # 选择一只股票进行详细分析
    symbol = '000001.SZ'
    
    # 获取最近60天的特征矩阵
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=60)).strftime("%Y%m%d")
    
    print(f"分析股票: {symbol}")
    print(f"获取50维特征矩阵...")
    
    feature_matrices = data_manager.get_feature_matrix(
        symbols=[symbol],
        start_date=start_date,
        end_date=end_date
    )
    
    if symbol in feature_matrices:
        matrix = feature_matrices[symbol]
        print(f"特征矩阵形状: {matrix.shape}")
        
        # 特征统计分析
        print(f"\n特征统计分析:")
        print(f"  总特征数: {matrix.shape[1]}")
        print(f"  时间步数: {matrix.shape[0]}")
        
        # 检查缺失值
        nan_mask = np.isnan(matrix)
        nan_count = np.sum(nan_mask)
        print(f"  缺失值数量: {nan_count}")
        print(f"  缺失值比例: {nan_count / matrix.size * 100:.2f}%")
        
        # 特征有效性分析
        valid_features = []
        for i in range(matrix.shape[1]):
            col = matrix[:, i]
            valid_ratio = np.sum(~np.isnan(col)) / len(col)
            if valid_ratio > 0.5:  # 超过50%的数据有效
                valid_features.append(i)
        
        print(f"  有效特征数: {len(valid_features)}/50")
        
        # 特征相关性分析（简化版）
        if len(valid_features) > 5:
            # 选择前6个有效特征进行相关性分析
            sample_features = matrix[:, valid_features[:6]]
            # 移除NaN行
            valid_rows = ~np.any(np.isnan(sample_features), axis=1)
            clean_data = sample_features[valid_rows]
            
            if len(clean_data) > 1:
                corr_matrix = np.corrcoef(clean_data.T)
                print(f"  前6个特征相关性矩阵形状: {corr_matrix.shape}")
                print(f"  平均相关系数: {np.mean(np.abs(corr_matrix)):.4f}")


def example_multidimensional_analysis():
    """多维度分析示例"""
    print("\n=== 多维度分析示例 ===")
    
    data_manager = DataManager()
    
    # 分析股票池
    symbols = ['000001.SZ', '600000.SH']
    
    # 获取最近10天的多维度分析
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=10)).strftime("%Y%m%d")
    
    print(f"多维度分析股票: {symbols}")
    
    multidimensional_analysis = data_manager.get_multidimensional_analysis(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date
    )
    
    for symbol, analysis_list in multidimensional_analysis.items():
        if analysis_list:
            print(f"\n{symbol} 多维度分析:")
            print(f"  分析数据点: {len(analysis_list)}")
            
            # 获取最新分析结果
            latest = analysis_list[-1]
            print(f"  最新分析 ({latest.timestamp.strftime('%Y-%m-%d')}):")
            print(f"    综合得分: {latest.composite_score:.4f}")
            print(f"    市场制度: {latest.market_regime}")
            print(f"    推荐行动: {latest.recommended_action}")
            print(f"    风险等级: {latest.risk_level}")
            print(f"    整体置信度: {latest.overall_confidence:.4f}")
            
            # 六大维度详情
            print(f"    六大维度评估:")
            dimensions = [
                ('趋势', latest.trend),
                ('波动性', latest.volatility),
                ('流动性', latest.liquidity),
                ('情绪', latest.sentiment),
                ('结构', latest.structural),
                ('转换', latest.transition)
            ]
            
            for dim_name, dim_result in dimensions:
                print(f"      {dim_name}: {dim_result.state} (得分: {dim_result.score:.4f}, 置信度: {dim_result.confidence:.4f})")
            
            # 分析趋势
            if len(analysis_list) > 5:
                recent_scores = [item.composite_score for item in analysis_list[-5:]]
                trend_direction = "上升" if recent_scores[-1] > recent_scores[0] else "下降"
                score_volatility = np.std(recent_scores)
                print(f"    近期趋势: {trend_direction}")
                print(f"    得分波动性: {score_volatility:.4f}")


def example_realtime_monitoring():
    """实时监控示例"""
    print("\n=== 实时监控示例 ===")
    
    # 使用实时配置
    data_manager = DataManager(config_type='realtime')
    
    # 监控股票池
    watch_list = ['000001.SZ', '600000.SH', '000002.SZ']
    
    print(f"实时监控股票池: {watch_list}")
    
    realtime_data = data_manager.get_realtime_data(watch_list)
    
    print(f"\n实时数据快照:")
    print("-" * 80)
    print(f"{'股票代码':<12} {'交易日期':<10} {'收盘价':<8} {'涨跌幅':<8} {'成交量':<12} {'RSI':<8}")
    print("-" * 80)
    
    for symbol, data in realtime_data.items():
        rsi_value = data.technical_indicators.rsi if data.technical_indicators.rsi else 0
        print(f"{symbol:<12} {data.trade_date:<10} {data.market_data.close:<8.2f} "
              f"{data.market_data.pct_chg:<8.2f}% {data.market_data.vol:<12.0f} {rsi_value:<8.1f}")
    
    print("-" * 80)
    
    # 简单的预警逻辑
    print(f"\n预警信息:")
    for symbol, data in realtime_data.items():
        alerts = []
        
        # 涨跌幅预警
        if abs(data.market_data.pct_chg) > 5:
            alerts.append(f"大幅波动: {data.market_data.pct_chg:.2f}%")
        
        # RSI预警
        if data.technical_indicators.rsi:
            if data.technical_indicators.rsi > 70:
                alerts.append("RSI超买")
            elif data.technical_indicators.rsi < 30:
                alerts.append("RSI超卖")
        
        # 成交量预警（简化逻辑）
        if data.market_data.vol > 2000000:  # 200万手
            alerts.append("成交量放大")
        
        if alerts:
            print(f"  {symbol}: {', '.join(alerts)}")
        else:
            print(f"  {symbol}: 正常")


def example_portfolio_analysis():
    """投资组合分析示例"""
    print("\n=== 投资组合分析示例 ===")
    
    data_manager = DataManager()
    
    # 定义投资组合
    portfolio = {
        '000001.SZ': 0.4,  # 平安银行 40%
        '600000.SH': 0.3,  # 浦发银行 30%
        '000002.SZ': 0.3   # 万科A 30%
    }
    
    symbols = list(portfolio.keys())
    weights = list(portfolio.values())
    
    print(f"投资组合构成:")
    for symbol, weight in portfolio.items():
        print(f"  {symbol}: {weight*100:.1f}%")
    
    # 获取最近30天数据
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
    
    # 获取特征矩阵
    feature_matrices = data_manager.get_feature_matrix(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date
    )
    
    # 计算组合特征
    if all(symbol in feature_matrices for symbol in symbols):
        print(f"\n组合分析:")
        
        # 获取收盘价特征（第4个特征）
        portfolio_returns = []
        
        for symbol in symbols:
            matrix = feature_matrices[symbol]
            if matrix.shape[0] > 1:
                # 提取收盘价（假设是第4个特征）
                closes = matrix[:, 3]  # 收盘价特征
                valid_closes = closes[~np.isnan(closes)]
                
                if len(valid_closes) > 1:
                    returns = np.diff(valid_closes) / valid_closes[:-1]
                    portfolio_returns.append(returns * portfolio[symbol])
        
        if portfolio_returns:
            # 计算组合收益
            min_length = min(len(returns) for returns in portfolio_returns)
            portfolio_returns = [returns[:min_length] for returns in portfolio_returns]
            combined_returns = np.sum(portfolio_returns, axis=0)
            
            print(f"  组合统计:")
            print(f"    平均日收益率: {np.mean(combined_returns)*100:.3f}%")
            print(f"    收益率波动率: {np.std(combined_returns)*100:.3f}%")
            print(f"    最大单日收益: {np.max(combined_returns)*100:.3f}%")
            print(f"    最大单日亏损: {np.min(combined_returns)*100:.3f}%")
            
            # 简单的夏普比率计算（假设无风险利率为0）
            if np.std(combined_returns) > 0:
                sharpe_ratio = np.mean(combined_returns) / np.std(combined_returns) * np.sqrt(252)
                print(f"    年化夏普比率: {sharpe_ratio:.3f}")


if __name__ == "__main__":
    print("🚀 Fund Trading System V3 - 数据模块使用示例")
    print("=" * 80)
    
    try:
        # 运行所有示例
        example_basic_market_data()
        example_feature_matrix_analysis()
        example_multidimensional_analysis()
        example_realtime_monitoring()
        example_portfolio_analysis()
        
        print("\n" + "=" * 80)
        print("🎉 所有示例运行完成！")
        print("\n💡 提示:")
        print("- 数据管理器已成功集成到fund_trading_system_v3")
        print("- 支持50维特征数据获取")
        print("- 支持多维度市场分析")
        print("- 支持实时数据监控")
        print("- 可用于投资组合分析和风险管理")
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()