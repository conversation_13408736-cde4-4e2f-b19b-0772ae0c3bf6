"""
核心模块测试
测试枚举、数据结构和工具函数
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.enums import TrendState, VolatilityState, LiquidityState, SentimentState
from core.enums import StructuralState, TransitionState, SignalStrength, MarketRegime
from core.data_structures import DimensionEvaluationResult


class TestCoreEnums(unittest.TestCase):
    """测试核心枚举类型"""
    
    def test_trend_state_enum(self):
        """测试趋势状态枚举"""
        self.assertEqual(TrendState.STRONG_UPTREND.value, "strong_uptrend")
        self.assertEqual(TrendState.WEAK_UPTREND.value, "weak_uptrend")
        self.assertEqual(TrendState.SIDEWAYS.value, "sideways")
        self.assertEqual(TrendState.WEAK_DOWNTREND.value, "weak_downtrend")
        self.assertEqual(TrendState.STRONG_DOWNTREND.value, "strong_downtrend")
    
    def test_volatility_state_enum(self):
        """测试波动性状态枚举"""
        self.assertEqual(VolatilityState.EXTREMELY_LOW.value, "extremely_low")
        self.assertEqual(VolatilityState.LOW.value, "low")
        self.assertEqual(VolatilityState.NORMAL.value, "normal")
        self.assertEqual(VolatilityState.HIGH.value, "high")
        self.assertEqual(VolatilityState.EXTREMELY_HIGH.value, "extremely_high")
    
    def test_signal_strength_enum(self):
        """测试信号强度枚举"""
        self.assertEqual(SignalStrength.VERY_WEAK.value, 0.1)
        self.assertEqual(SignalStrength.WEAK.value, 0.3)
        self.assertEqual(SignalStrength.MEDIUM.value, 0.5)
        self.assertEqual(SignalStrength.STRONG.value, 0.7)
        self.assertEqual(SignalStrength.VERY_STRONG.value, 0.9)


class TestDataStructures(unittest.TestCase):
    """测试数据结构"""
    
    def test_dimension_evaluation_result(self):
        """测试维度评估结果数据结构"""
        result = DimensionEvaluationResult(
            dimension_name="趋势",
            state=TrendState.STRONG_UPTREND,
            score=0.8,
            confidence=0.9,
            signals=["上涨信号", "强势信号"],
            data_quality="good",
            details={"ma_trend": "上涨"},
            indicators={"ma5": 100, "ma20": 95}
        )
        
        self.assertEqual(result.dimension_name, "趋势")
        self.assertEqual(result.state, TrendState.STRONG_UPTREND)
        self.assertEqual(result.score, 0.8)
        self.assertEqual(result.confidence, 0.9)
        self.assertEqual(len(result.signals), 2)
        self.assertEqual(result.data_quality, "good")
        self.assertIn("ma_trend", result.details)
        self.assertIn("ma5", result.indicators)


class TestCoreUtils(unittest.TestCase):
    """测试核心工具函数"""
    
    def test_imports_available(self):
        """测试导入可用性检查"""
        from core.utils import CZSC_FUNC_AVAILABLE, PUPPET_AVAILABLE, CZSC_AVAILABLE
        
        # 这些变量应该是布尔值
        self.assertIsInstance(CZSC_FUNC_AVAILABLE, bool)
        self.assertIsInstance(PUPPET_AVAILABLE, bool)
        self.assertIsInstance(CZSC_AVAILABLE, bool)


if __name__ == '__main__':
    unittest.main()
