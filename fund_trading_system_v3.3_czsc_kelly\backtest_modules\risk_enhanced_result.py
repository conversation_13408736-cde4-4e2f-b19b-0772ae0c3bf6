"""
风险增强的评估结果
集成风险管理功能的DimensionEvaluationResult扩展
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

@dataclass
class RiskEnhancedEvaluationResult:
    """风险增强的评估结果"""
    
    # 基础属性 (继承自DimensionEvaluationResult)
    dimension_name: str
    state: Any
    score: float
    confidence: float
    signals: list = field(default_factory=list)
    data_quality: str = "unknown"
    details: Dict[str, Any] = field(default_factory=dict)
    indicators: Dict[str, Any] = field(default_factory=dict)
    
    # 风险管理属性
    risk_metrics: Dict[str, float] = field(default_factory=dict)
    position_size_recommendation: float = 0.0
    stop_loss_level: float = 0.0
    take_profit_level: float = 0.0
    max_position_pct: float = 0.1
    risk_level: str = "medium"
    risk_warnings: list = field(default_factory=list)
    
    # 增强属性
    ml_confidence: float = 0.0
    technical_consistency: float = 0.0
    historical_success_rate: float = 0.0
    market_environment_fit: float = 0.0
    
    def apply_risk_management(self, risk_manager, current_price: float, 
                            portfolio_value: float) -> bool:
        """应用风险管理"""
        try:
            logger.debug(f"为信号 {self.dimension_name} 应用风险管理")
            
            # 1. 计算基础仓位大小
            base_position_value = self.score * portfolio_value * self.max_position_pct
            
            # 2. 验证仓位大小
            base_position_ratio = base_position_value / portfolio_value
            is_valid, warnings = risk_manager.validate_position_size(
                base_position_ratio, 
                current_price,
                portfolio_value
            )
            
            # 如果验证失败，调整仓位大小
            if not is_valid:
                self.position_size_recommendation = min(base_position_ratio * 0.5, risk_manager.max_position_size * 0.5)
                self.risk_warnings.extend(warnings)
            else:
                self.position_size_recommendation = base_position_ratio
            
            # 3. 设置止损止盈
            self._set_stop_loss_take_profit(risk_manager, current_price)
            
            # 4. 计算风险指标
            self._calculate_risk_metrics(risk_manager, current_price, portfolio_value)
            
            # 5. 评估风险等级
            self._assess_risk_level(risk_manager)
            
            # 6. 生成风险警告
            self._generate_risk_warnings(risk_manager, current_price, portfolio_value)
            
            logger.debug(f"风险管理应用完成，仓位建议: {self.position_size_recommendation:.2%}")
            return is_valid
            
        except Exception as e:
            logger.error(f"风险管理应用失败: {str(e)}")
            self.risk_warnings.append(f"风险管理计算失败: {str(e)}")
            return False
    
    def _set_stop_loss_take_profit(self, risk_manager, current_price: float):
        """设置止损止盈"""
        try:
            if hasattr(self.state, 'name'):
                state_name = self.state.name
                
                if 'UPTREND' in state_name:
                    # 上涨趋势：设置下方止损，上方止盈
                    self.stop_loss_level = current_price * (1 - risk_manager.stop_loss_pct)
                    self.take_profit_level = current_price * (1 + risk_manager.take_profit_pct)
                    
                elif 'DOWNTREND' in state_name:
                    # 下跌趋势：设置上方止损，下方止盈（做空）
                    self.stop_loss_level = current_price * (1 + risk_manager.stop_loss_pct)
                    self.take_profit_level = current_price * (1 - risk_manager.take_profit_pct)
                    
                else:
                    # 横盘或其他状态：保守设置
                    self.stop_loss_level = current_price * (1 - risk_manager.stop_loss_pct * 0.5)
                    self.take_profit_level = current_price * (1 + risk_manager.take_profit_pct * 0.5)
            else:
                # 默认设置
                self.stop_loss_level = current_price * (1 - risk_manager.stop_loss_pct)
                self.take_profit_level = current_price * (1 + risk_manager.take_profit_pct)
                
        except Exception as e:
            logger.warning(f"止损止盈设置失败: {str(e)}")
            # 设置默认值
            self.stop_loss_level = current_price * 0.95
            self.take_profit_level = current_price * 1.05
    
    def _calculate_risk_metrics(self, risk_manager, current_price: float, portfolio_value: float):
        """计算风险指标"""
        try:
            # 仓位风险
            position_risk = self.position_size_recommendation
            
            # 价格风险（基于止损距离）
            price_risk = abs(current_price - self.stop_loss_level) / current_price
            
            # 信号风险（基于置信度）
            signal_risk = 1 - self.confidence
            
            # 市场风险（基于波动率）
            market_risk = 1 - self.market_environment_fit
            
            # 综合风险评分
            total_risk = (
                position_risk * 0.3 +
                price_risk * 0.3 +
                signal_risk * 0.2 +
                market_risk * 0.2
            )
            
            self.risk_metrics = {
                'position_risk': position_risk,
                'price_risk': price_risk,
                'signal_risk': signal_risk,
                'market_risk': market_risk,
                'total_risk': total_risk,
                'risk_reward_ratio': abs(self.take_profit_level - current_price) / abs(current_price - self.stop_loss_level)
            }
            
        except Exception as e:
            logger.warning(f"风险指标计算失败: {str(e)}")
            self.risk_metrics = {'total_risk': 0.5}
    
    def _assess_risk_level(self, risk_manager):
        """评估风险等级"""
        try:
            total_risk = self.risk_metrics.get('total_risk', 0.5)
            
            if total_risk < 0.3:
                self.risk_level = "low"
            elif total_risk < 0.6:
                self.risk_level = "medium"
            elif total_risk < 0.8:
                self.risk_level = "high"
            else:
                self.risk_level = "very_high"
                
        except Exception as e:
            logger.warning(f"风险等级评估失败: {str(e)}")
            self.risk_level = "medium"
    
    def _generate_risk_warnings(self, risk_manager, current_price: float, portfolio_value: float):
        """生成风险警告"""
        self.risk_warnings.clear()
        
        try:
            # 仓位过大警告
            if self.position_size_recommendation > risk_manager.max_position_size * 0.8:
                self.risk_warnings.append("建议仓位接近上限，注意控制风险")
            
            # 信号置信度低警告
            if self.confidence < 0.6:
                self.risk_warnings.append("信号置信度较低，建议谨慎操作")
            
            # 技术指标不一致警告
            if self.technical_consistency < 0.5:
                self.risk_warnings.append("技术指标存在分歧，增加不确定性")
            
            # 历史成功率低警告
            if self.historical_success_rate < 0.5:
                self.risk_warnings.append("类似信号历史成功率较低")
            
            # 市场环境不适合警告
            if self.market_environment_fit < 0.4:
                self.risk_warnings.append("当前市场环境可能不适合此类信号")
            
            # 风险回报比不佳警告
            risk_reward_ratio = self.risk_metrics.get('risk_reward_ratio', 1.0)
            if risk_reward_ratio < 1.5:
                self.risk_warnings.append("风险回报比不够理想，建议重新评估")
            
            # 高风险等级警告
            if self.risk_level in ['high', 'very_high']:
                self.risk_warnings.append(f"当前风险等级为{self.risk_level}，请谨慎操作")
                
        except Exception as e:
            logger.warning(f"风险警告生成失败: {str(e)}")
            self.risk_warnings.append("风险评估异常，请人工检查")
    
    def get_trading_recommendation(self) -> Dict[str, Any]:
        """获取交易建议"""
        try:
            # 综合评分
            overall_score = (
                self.score * 0.3 +
                self.confidence * 0.2 +
                self.ml_confidence * 0.2 +
                self.technical_consistency * 0.15 +
                self.historical_success_rate * 0.15
            )
            
            # 风险调整后评分
            risk_adjusted_score = overall_score * (1 - self.risk_metrics.get('total_risk', 0.5))
            
            # 生成建议
            if risk_adjusted_score > 0.7 and self.risk_level in ['low', 'medium']:
                action = "强烈建议"
                reason = "信号质量优秀且风险可控"
            elif risk_adjusted_score > 0.5 and self.risk_level != 'very_high':
                action = "建议"
                reason = "信号质量良好"
            elif risk_adjusted_score > 0.3:
                action = "谨慎考虑"
                reason = "信号质量一般或风险较高"
            else:
                action = "不建议"
                reason = "信号质量不佳或风险过高"
            
            return {
                'action': action,
                'reason': reason,
                'overall_score': overall_score,
                'risk_adjusted_score': risk_adjusted_score,
                'position_size': self.position_size_recommendation,
                'stop_loss': self.stop_loss_level,
                'take_profit': self.take_profit_level,
                'risk_level': self.risk_level,
                'warnings': self.risk_warnings.copy()
            }
            
        except Exception as e:
            logger.error(f"交易建议生成失败: {str(e)}")
            return {
                'action': '不建议',
                'reason': f'建议生成失败: {str(e)}',
                'overall_score': 0.0,
                'risk_adjusted_score': 0.0,
                'position_size': 0.0,
                'warnings': ['建议生成异常，请人工检查']
            }
    
    def update_performance(self, actual_return: float, holding_period: int):
        """更新信号表现"""
        try:
            # 计算信号成功与否
            is_success = False
            
            if hasattr(self.state, 'name'):
                state_name = self.state.name
                if 'UPTREND' in state_name and actual_return > 0:
                    is_success = True
                elif 'DOWNTREND' in state_name and actual_return < 0:
                    is_success = True
            
            # 更新详细信息
            self.details.update({
                'actual_return': actual_return,
                'holding_period': holding_period,
                'is_success': is_success,
                'performance_updated_at': str(pd.Timestamp.now())
            })
            
            logger.debug(f"信号 {self.dimension_name} 表现更新: 收益率={actual_return:.2%}, 成功={is_success}")
            
        except Exception as e:
            logger.warning(f"信号表现更新失败: {str(e)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'dimension_name': self.dimension_name,
            'state': str(self.state),
            'score': self.score,
            'confidence': self.confidence,
            'signals': self.signals,
            'data_quality': self.data_quality,
            'details': self.details,
            'indicators': self.indicators,
            'risk_metrics': self.risk_metrics,
            'position_size_recommendation': self.position_size_recommendation,
            'stop_loss_level': self.stop_loss_level,
            'take_profit_level': self.take_profit_level,
            'risk_level': self.risk_level,
            'risk_warnings': self.risk_warnings,
            'ml_confidence': self.ml_confidence,
            'technical_consistency': self.technical_consistency,
            'historical_success_rate': self.historical_success_rate,
            'market_environment_fit': self.market_environment_fit
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"RiskEnhancedResult(dimension={self.dimension_name}, "
                f"score={self.score:.2f}, confidence={self.confidence:.2f}, "
                f"risk_level={self.risk_level}, position={self.position_size_recommendation:.2%})")
    
    def __repr__(self) -> str:
        return self.__str__()