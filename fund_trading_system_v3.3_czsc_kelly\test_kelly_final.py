#!/usr/bin/env python3
"""
最终凯利修复验证测试
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3

def test_kelly_final():
    """最终凯利修复验证"""
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING, format='%(levelname)s:%(name)s:%(message)s')
    
    print("🎯 最终凯利修复验证测试")
    print("=" * 50)
    
    try:
        # 初始化协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 测试基金
        test_fund = '518880'
        print(f"📊 测试基金: {test_fund}")
        
        # 执行分析
        result = coordinator.coordinate_analysis(test_fund)
        
        # 提取凯利分析结果
        kelly_analysis = result.get('kelly_analysis', {})
        if kelly_analysis:
            kelly_calc = kelly_analysis.get('kelly_calculation', {})
            
            print("\n✅ 凯利分析结果:")
            print(f"   胜率: {kelly_calc.get('win_probability', 0):.1%}")
            print(f"   盈亏比: {kelly_calc.get('risk_reward_ratio', 0):.2f}")
            print(f"   建议仓位: {kelly_calc.get('optimal_position', 0):.2%}")
            print(f"   风险等级: {kelly_calc.get('risk_level', 'N/A')}")
            
            # 验证是否不再是默认值
            win_prob = kelly_calc.get('win_probability', 0)
            risk_reward = kelly_calc.get('risk_reward_ratio', 0)
            position = kelly_calc.get('optimal_position', 0)
            
            print("\n🔍 修复验证:")
            if win_prob != 0.5:
                print(f"   ✅ 胜率不再是默认值50%: {win_prob:.1%}")
            else:
                print(f"   ❌ 胜率仍是默认值: {win_prob:.1%}")
                
            if risk_reward != 1.0:
                print(f"   ✅ 盈亏比不再是默认值1.0: {risk_reward:.2f}")
            else:
                print(f"   ❌ 盈亏比仍是默认值: {risk_reward:.2f}")
                
            if position > 0:
                print(f"   ✅ 建议仓位不再是0%: {position:.2%}")
            else:
                print(f"   ❌ 建议仓位仍是0%: {position:.2%}")
            
            # 显示仓位推理
            reasoning = kelly_analysis.get('position_reasoning', '')
            if reasoning:
                print(f"\n💡 仓位推理:")
                print(f"   {reasoning}")
            
            print("\n🎉 凯利公式修复成功！")
            
        else:
            print("❌ 未找到凯利分析结果")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_kelly_final()