"""
LLM + CZSC + 凯利公式集成交易系统
完整的交易决策系统实现
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from dataclasses import dataclass
import json
from datetime import datetime, timedelta

# 导入自定义模块
from enhanced_llm_czsc_kelly import LLMCZSCKellyIntegrator, TradingSignal
from llm_analyzer import LLMMarketAnalyzer, LLMAnalysisResult
from kelly_position_manager import KellyFormulaCalculator, PortfolioKellyManager
from enhanced_czsc_analyzer import EnhancedCZSCAnalyzer, CZSCSignal

@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    klines: pd.DataFrame
    current_price: float
    volume: int
    volatility: float
    technical_indicators: Dict
    news_sentiment: Dict
    timestamp: datetime

class IntegratedTradingSystem:
    """集成交易系统"""
    
    def __init__(self, config: Dict = None):
        """
        初始化集成交易系统
        
        Args:
            config: 系统配置参数
        """
        self.config = config or self._default_config()
        
        # 初始化各个组件
        self.llm_analyzer = LLMMarketAnalyzer(
            model_type=self.config.get('llm_model', 'local')
        )
        
        self.czsc_analyzer = EnhancedCZSCAnalyzer(
            min_k_num=self.config.get('min_k_num', 7),
            max_k_num=self.config.get('max_k_num', 21)
        )
        
        self.kelly_calculator = KellyFormulaCalculator(
            max_position=self.config.get('max_position', 0.25),
            kelly_fraction=self.config.get('kelly_fraction', 0.25)
        )
        
        self.portfolio_manager = PortfolioKellyManager(
            max_total_position=self.config.get('max_total_position', 0.8)
        )
        
        # 交易记录
        self.trading_history = []
        self.position_history = []
        
    def _default_config(self) -> Dict:
        """默认配置"""
        return {
            'llm_model': 'local',
            'min_k_num': 7,
            'max_k_num': 21,
            'max_position': 0.25,
            'kelly_fraction': 0.25,
            'max_total_position': 0.8,
            'risk_free_rate': 0.03,
            'confidence_threshold': 0.6,
            'min_kelly_position': 0.01
        }
    
    def analyze_single_symbol(self, market_data: MarketData) -> TradingSignal:
        """
        分析单个标的
        
        Args:
            market_data: 市场数据
            
        Returns:
            TradingSignal: 交易信号
        """
        
        print(f"开始分析 {market_data.symbol}...")
        
        # 1. CZSC技术分析
        print("  执行CZSC分析...")
        czsc_signal = self.czsc_analyzer.analyze(market_data.klines)
        
        # 2. 构建LLM分析所需的数据
        llm_market_data = {
            'current_price': market_data.current_price,
            'volume': market_data.volume,
            'volatility': market_data.volatility,
            'technical_indicators': market_data.technical_indicators,
            'czsc_signals': {
                'signal_type': czsc_signal.signal_type,
                'strength': czsc_signal.strength.value,
                'trend_direction': czsc_signal.trend_direction.value,
                'support_levels': czsc_signal.support_levels,
                'resistance_levels': czsc_signal.resistance_levels,
                'confidence': czsc_signal.confidence
            },
            'news_sentiment': market_data.news_sentiment
        }
        
        # 3. LLM市场分析
        print("  执行LLM分析...")
        llm_result = self.llm_analyzer.analyze_market_narrative(llm_market_data)
        
        # 4. 整合分析结果
        print("  整合分析结果...")
        integrated_analysis = self._integrate_analyses(czsc_signal, llm_result, market_data)
        
        # 5. 凯利公式仓位计算
        print("  计算最优仓位...")
        kelly_result = self.kelly_calculator.calculate_optimal_position(
            win_probability=integrated_analysis['win_probability'],
            win_loss_ratio=integrated_analysis['risk_reward_ratio'],
            confidence=integrated_analysis['confidence'],
            method=self.config.get('kelly_method', 'fractional')
        )
        
        # 6. 生成最终交易信号
        trading_signal = self._generate_final_signal(
            market_data.symbol, integrated_analysis, kelly_result
        )
        
        print(f"  分析完成: {trading_signal.signal_type}, 仓位: {trading_signal.kelly_position:.2%}")
        
        return trading_signal
    
    def _integrate_analyses(self, czsc_signal: CZSCSignal, llm_result: LLMAnalysisResult, 
                          market_data: MarketData) -> Dict:
        """整合CZSC和LLM分析结果"""
        
        # 信号一致性检查
        czsc_direction = czsc_signal.signal_type
        llm_direction = llm_result.direction
        
        # 计算信号一致性得分
        if czsc_direction == llm_direction:
            consistency_score = 1.0
        elif (czsc_direction == 'hold' and llm_direction in ['buy', 'sell']) or \
             (llm_direction == 'hold' and czsc_direction in ['buy', 'sell']):
            consistency_score = 0.5
        else:
            consistency_score = 0.2  # 信号冲突
        
        # 综合置信度计算
        czsc_confidence = czsc_signal.confidence
        llm_confidence = llm_result.confidence
        
        # 加权平均置信度，考虑一致性
        combined_confidence = (
            czsc_confidence * 0.4 + 
            llm_confidence * 0.4 + 
            consistency_score * 0.2
        )
        
        # 确定最终方向
        if consistency_score >= 0.5:
            # 信号一致或部分一致，选择置信度更高的
            if czsc_confidence >= llm_confidence:
                final_direction = czsc_direction
                entry_price = czsc_signal.entry_price
                stop_loss = czsc_signal.stop_loss
                take_profit = czsc_signal.take_profit
            else:
                final_direction = llm_direction
                entry_price = np.mean(llm_result.entry_price_range) if llm_result.entry_price_range else market_data.current_price
                stop_loss = llm_result.stop_loss
                take_profit = llm_result.take_profit
        else:
            # 信号冲突，采用保守策略
            final_direction = 'hold'
            entry_price = market_data.current_price
            stop_loss = market_data.current_price * 0.95
            take_profit = market_data.current_price * 1.05
        
        # 风险收益比计算
        if final_direction != 'hold' and stop_loss != 0:
            if final_direction == 'buy':
                risk = abs(entry_price - stop_loss)
                reward = abs(take_profit - entry_price)
            else:  # sell
                risk = abs(stop_loss - entry_price)
                reward = abs(entry_price - take_profit)
            
            risk_reward_ratio = reward / risk if risk > 0 else 1.0
        else:
            risk_reward_ratio = 1.0
        
        # 胜率估算
        base_win_prob = 0.5
        
        # 基于CZSC信号强度调整
        czsc_strength_factor = czsc_signal.strength.value / 5.0
        
        # 基于LLM建议调整
        llm_win_prob = llm_result.win_probability
        
        # 综合胜率
        estimated_win_prob = (
            base_win_prob * 0.3 +
            (base_win_prob + czsc_strength_factor * 0.2) * 0.35 +
            llm_win_prob * 0.35
        )
        
        return {
            'direction': final_direction,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'confidence': combined_confidence,
            'win_probability': estimated_win_prob,
            'risk_reward_ratio': risk_reward_ratio,
            'consistency_score': consistency_score,
            'czsc_analysis': {
                'signal': czsc_direction,
                'strength': czsc_signal.strength.value,
                'confidence': czsc_confidence,
                'reasoning': czsc_signal.reasoning
            },
            'llm_analysis': {
                'signal': llm_direction,
                'confidence': llm_confidence,
                'reasoning': llm_result.reasoning,
                'market_sentiment': llm_result.market_sentiment
            }
        }
    
    def _generate_final_signal(self, symbol: str, analysis: Dict, kelly_result) -> TradingSignal:
        """生成最终交易信号"""
        
        # 应用置信度阈值过滤
        if analysis['confidence'] < self.config['confidence_threshold']:
            # 置信度不足，降级为观望
            final_direction = 'hold'
            kelly_position = 0.0
        else:
            final_direction = analysis['direction']
            kelly_position = kelly_result.optimal_position
        
        # 应用最小仓位过滤
        if kelly_position < self.config['min_kelly_position']:
            kelly_position = 0.0
            final_direction = 'hold'
        
        # 生成详细推理
        reasoning = self._generate_comprehensive_reasoning(analysis, kelly_result)
        
        return TradingSignal(
            symbol=symbol,
            signal_type=final_direction,
            confidence=analysis['confidence'],
            target_price=analysis['entry_price'],
            stop_loss=analysis['stop_loss'],
            take_profit=analysis['take_profit'],
            kelly_position=kelly_position,
            czsc_level=analysis['czsc_analysis']['strength'],
            llm_reasoning=reasoning,
            risk_reward_ratio=analysis['risk_reward_ratio'],
            win_probability=analysis['win_probability']
        )
    
    def _generate_comprehensive_reasoning(self, analysis: Dict, kelly_result) -> str:
        """生成综合推理说明"""
        
        reasoning_parts = []
        
        # 信号一致性
        consistency = analysis['consistency_score']
        if consistency >= 0.8:
            reasoning_parts.append("CZSC与LLM信号高度一致")
        elif consistency >= 0.5:
            reasoning_parts.append("CZSC与LLM信号部分一致")
        else:
            reasoning_parts.append("CZSC与LLM信号存在分歧，采用保守策略")
        
        # CZSC分析
        czsc_info = analysis['czsc_analysis']
        reasoning_parts.append(f"CZSC分析：{czsc_info['signal']}信号，强度{czsc_info['strength']}/5")
        
        # LLM分析
        llm_info = analysis['llm_analysis']
        reasoning_parts.append(f"LLM分析：{llm_info['signal']}信号，市场情绪{llm_info['market_sentiment']}")
        
        # 风险收益分析
        reasoning_parts.append(f"预期胜率{analysis['win_probability']:.1%}，风险收益比{analysis['risk_reward_ratio']:.2f}")
        
        # 凯利仓位
        reasoning_parts.append(f"凯利公式建议仓位{kelly_result.optimal_position:.2%}({kelly_result.risk_level})")
        
        # 最终建议
        final_direction = analysis['direction']
        if final_direction == 'buy':
            reasoning_parts.append("综合建议：适度做多")
        elif final_direction == 'sell':
            reasoning_parts.append("综合建议：适度做空")
        else:
            reasoning_parts.append("综合建议：保持观望")
        
        return "；".join(reasoning_parts)
    
    def analyze_portfolio(self, market_data_list: List[MarketData]) -> Dict[str, TradingSignal]:
        """
        分析投资组合
        
        Args:
            market_data_list: 市场数据列表
            
        Returns:
            各标的的交易信号字典
        """
        
        print(f"开始分析投资组合，共{len(market_data_list)}个标的...")
        
        # 1. 分析各个标的
        individual_signals = {}
        for market_data in market_data_list:
            signal = self.analyze_single_symbol(market_data)
            individual_signals[market_data.symbol] = signal
        
        # 2. 组合级别的仓位优化
        print("执行组合级别仓位优化...")
        
        # 构建组合分析所需的信号数据
        portfolio_signals = []
        for symbol, signal in individual_signals.items():
            if signal.signal_type != 'hold':
                portfolio_signals.append({
                    'symbol': symbol,
                    'win_probability': signal.win_probability,
                    'win_loss_ratio': signal.risk_reward_ratio,
                    'confidence': signal.confidence
                })
        
        # 组合级别凯利仓位计算
        if portfolio_signals:
            portfolio_results = self.portfolio_manager.calculate_portfolio_positions(portfolio_signals)
            
            # 更新个股信号的仓位
            for symbol, portfolio_result in portfolio_results.items():
                if symbol in individual_signals:
                    individual_signals[symbol].kelly_position = portfolio_result.optimal_position
        
        # 3. 生成组合摘要
        portfolio_summary = self._generate_portfolio_summary(individual_signals)
        
        print("投资组合分析完成")
        print(f"组合摘要：{portfolio_summary}")
        
        return individual_signals
    
    def _generate_portfolio_summary(self, signals: Dict[str, TradingSignal]) -> Dict:
        """生成投资组合摘要"""
        
        total_position = sum(signal.kelly_position for signal in signals.values())
        
        buy_signals = [s for s in signals.values() if s.signal_type == 'buy']
        sell_signals = [s for s in signals.values() if s.signal_type == 'sell']
        hold_signals = [s for s in signals.values() if s.signal_type == 'hold']
        
        avg_confidence = np.mean([s.confidence for s in signals.values()]) if signals else 0
        avg_win_prob = np.mean([s.win_probability for s in signals.values()]) if signals else 0
        
        return {
            'total_symbols': len(signals),
            'buy_count': len(buy_signals),
            'sell_count': len(sell_signals),
            'hold_count': len(hold_signals),
            'total_position': total_position,
            'position_utilization': total_position / self.config['max_total_position'],
            'average_confidence': avg_confidence,
            'average_win_probability': avg_win_prob,
            'active_positions': len(buy_signals) + len(sell_signals)
        }
    
    def backtest_strategy(self, historical_data: Dict[str, pd.DataFrame], 
                         start_date: str, end_date: str) -> Dict:
        """
        策略回测
        
        Args:
            historical_data: 历史数据字典 {symbol: klines_df}
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果
        """
        
        print(f"开始策略回测：{start_date} 至 {end_date}")
        
        # 回测逻辑实现
        # 这里提供一个简化的回测框架
        
        backtest_results = {
            'total_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'profitable_trades': 0,
            'daily_returns': [],
            'equity_curve': [],
            'trade_log': []
        }
        
        print("回测完成")
        return backtest_results

def create_sample_market_data() -> List[MarketData]:
    """创建示例市场数据"""
    
    symbols = ['000001', '000002', '000858']
    market_data_list = []
    
    for symbol in symbols:
        # 生成模拟K线数据
        np.random.seed(hash(symbol) % 1000)
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        
        base_price = np.random.uniform(8, 15)
        prices = [base_price]
        
        for _ in range(49):
            change = np.random.randn() * 0.03
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))  # 确保价格为正
        
        klines = pd.DataFrame({
            'open': [p * (1 + np.random.randn() * 0.005) for p in prices],
            'high': [p * (1 + abs(np.random.randn()) * 0.02) for p in prices],
            'low': [p * (1 - abs(np.random.randn()) * 0.02) for p in prices],
            'close': prices,
            'volume': np.random.randint(100000, 1000000, 50)
        }, index=dates)
        
        # 确保OHLC逻辑正确
        for i in range(len(klines)):
            high = max(klines.iloc[i]['open'], klines.iloc[i]['close'])
            low = min(klines.iloc[i]['open'], klines.iloc[i]['close'])
            klines.iloc[i, klines.columns.get_loc('high')] = max(klines.iloc[i]['high'], high)
            klines.iloc[i, klines.columns.get_loc('low')] = min(klines.iloc[i]['low'], low)
        
        # 计算技术指标
        current_price = prices[-1]
        
        market_data = MarketData(
            symbol=symbol,
            klines=klines,
            current_price=current_price,
            volume=int(klines['volume'].iloc[-1]),
            volatility=np.std(np.diff(prices) / prices[:-1]),
            technical_indicators={
                'RSI': np.random.uniform(30, 70),
                'MACD': np.random.uniform(-0.5, 0.5),
                'MA20': np.mean(prices[-20:]),
                'MA50': np.mean(prices[-50:]) if len(prices) >= 50 else np.mean(prices)
            },
            news_sentiment={
                'overall': np.random.choice(['positive', 'neutral', 'negative']),
                'positive_ratio': np.random.uniform(0.3, 0.7),
                'negative_ratio': np.random.uniform(0.1, 0.4),
                'key_news': ['市场消息1', '行业动态2']
            },
            timestamp=datetime.now()
        )
        
        market_data_list.append(market_data)
    
    return market_data_list

# 使用示例
if __name__ == "__main__":
    # 初始化交易系统
    config = {
        'llm_model': 'local',  # 使用模拟模式
        'max_position': 0.20,
        'kelly_fraction': 0.25,
        'confidence_threshold': 0.5,
        'min_kelly_position': 0.02
    }
    
    trading_system = IntegratedTradingSystem(config)
    
    # 创建示例数据
    market_data_list = create_sample_market_data()
    
    print("=" * 60)
    print("LLM + CZSC + 凯利公式集成交易系统演示")
    print("=" * 60)
    
    # 分析单个标的
    print("\n1. 单标的分析示例：")
    single_signal = trading_system.analyze_single_symbol(market_data_list[0])
    
    print(f"\n--- {single_signal.symbol} 分析结果 ---")
    print(f"交易信号：{single_signal.signal_type}")
    print(f"目标价位：{single_signal.target_price:.2f}")
    print(f"止损价位：{single_signal.stop_loss:.2f}")
    print(f"目标价位：{single_signal.take_profit:.2f}")
    print(f"凯利仓位：{single_signal.kelly_position:.2%}")
    print(f"置信度：{single_signal.confidence:.2%}")
    print(f"预期胜率：{single_signal.win_probability:.2%}")
    print(f"风险收益比：{single_signal.risk_reward_ratio:.2f}")
    print(f"CZSC等级：{single_signal.czsc_level}")
    print(f"分析推理：{single_signal.llm_reasoning}")
    
    # 分析投资组合
    print(f"\n2. 投资组合分析示例（{len(market_data_list)}个标的）：")
    portfolio_signals = trading_system.analyze_portfolio(market_data_list)
    
    print(f"\n--- 投资组合分析结果 ---")
    total_position = 0
    for symbol, signal in portfolio_signals.items():
        print(f"{symbol}: {signal.signal_type} | 仓位: {signal.kelly_position:.2%} | 置信度: {signal.confidence:.1%}")
        total_position += signal.kelly_position
    
    print(f"\n总仓位使用：{total_position:.2%}")
    print(f"仓位利用率：{total_position/config['max_total_position']:.1%}")
    
    active_signals = [s for s in portfolio_signals.values() if s.signal_type != 'hold']
    print(f"活跃信号数：{len(active_signals)}/{len(portfolio_signals)}")
    
    if active_signals:
        avg_confidence = np.mean([s.confidence for s in active_signals])
        avg_win_prob = np.mean([s.win_probability for s in active_signals])
        print(f"平均置信度：{avg_confidence:.1%}")
        print(f"平均胜率：{avg_win_prob:.1%}")
    
    print("\n系统演示完成！")