"""
简化的Kimi API测试
绕过SSL问题直接测试API连接
"""

import requests
import json

def test_kimi_direct():
    """直接使用requests测试Kimi API"""
    print("🧪 直接测试Kimi API")
    print("="*40)
    
    # API配置
    api_key = "sk-PmDHOgsUYCmBO1j4cmp9o2I0Zmg9aFj0GtLmI81vVCqtd6LC"
    base_url = "https://api.moonshot.cn/v1"
    
    # 构建请求
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "kimi-k2-0711-preview",
        "messages": [
            {"role": "user", "content": "你好，请简单回复确认连接正常。"}
        ],
        "max_tokens": 50,
        "temperature": 0.3
    }
    
    try:
        print("发送API请求...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            reply = result['choices'][0]['message']['content']
            print(f"✅ API连接成功!")
            print(f"📝 Kimi回复: {reply}")
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_market_analysis():
    """测试市场分析功能"""
    print("\n🔍 测试市场分析功能")
    print("="*40)
    
    api_key = "sk-PmDHOgsUYCmBO1j4cmp9o2I0Zmg9aFj0GtLmI81vVCqtd6LC"
    base_url = "https://api.moonshot.cn/v1"
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 市场分析提示词
    prompt = """请分析以下基金的市场情况：

基金代码: 000001
分析时间: 2025-07-16 17:30:00

技术指标分析:
- MA5: 1.2345
- MA20: 1.2100
- RSI: 65.5
- MACD: 0.0123

价格数据:
- 当前价格: 1.2345
- 涨跌幅: 2.15%
- 成交量: 1500000

请基于以上数据进行综合分析，并以JSON格式返回分析结果，包含以下字段：
{
    "market_drivers": ["驱动因素1", "驱动因素2"],
    "risk_points": ["风险点1", "风险点2"],
    "opportunities": ["机会1", "机会2"],
    "strategy_suggestion": "具体操作建议",
    "confidence_level": 0.8,
    "market_sentiment": "积极/中性/谨慎",
    "key_insights": "核心洞察"
}"""
    
    data = {
        "model": "kimi-k2-0711-preview",
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的量化投资分析师，擅长分析市场数据并提供投资建议。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "max_tokens": 1000,
        "temperature": 0.3
    }
    
    try:
        print("发送市场分析请求...")
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            analysis = result['choices'][0]['message']['content']
            print(f"✅ 市场分析成功!")
            print(f"📊 分析结果:")
            print(analysis)
            
            # 尝试解析JSON
            try:
                if '{' in analysis and '}' in analysis:
                    json_start = analysis.find('{')
                    json_end = analysis.rfind('}') + 1
                    json_str = analysis[json_start:json_end]
                    parsed = json.loads(json_str)
                    print(f"\n✅ JSON解析成功:")
                    print(f"市场情绪: {parsed.get('market_sentiment', 'N/A')}")
                    print(f"置信度: {parsed.get('confidence_level', 'N/A')}")
                    print(f"策略建议: {parsed.get('strategy_suggestion', 'N/A')}")
            except:
                print("⚠️ JSON解析失败，但分析内容正常")
            
            return True
        else:
            print(f"❌ 市场分析失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 分析异常: {e}")
        return False

if __name__ == "__main__":
    # 测试基本连接
    basic_ok = test_kimi_direct()
    
    # 如果基本连接成功，测试市场分析
    if basic_ok:
        analysis_ok = test_market_analysis()
        
        print("\n" + "="*50)
        if basic_ok and analysis_ok:
            print("🎉 Kimi API完全正常！")
            print("💡 建议: 可以在系统中启用LLM功能")
        else:
            print("⚠️ 部分功能异常")
    else:
        print("\n" + "="*50)
        print("❌ Kimi API连接失败")
        print("💡 建议: 检查网络连接或API密钥")