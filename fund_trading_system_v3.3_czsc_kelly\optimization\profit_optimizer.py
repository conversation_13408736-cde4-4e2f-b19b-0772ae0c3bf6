"""
盈利导向优化器
专注于提升系统盈利能力，而非技术完整性
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from scipy import optimize
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import TimeSeriesSplit

from core.data_structures import DimensionEvaluationResult


@dataclass
class ProfitMetrics:
    """盈利指标"""
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    total_trades: int
    profitable_trades: int
    roi: float  # 投资回报率
    profit_per_trade: float
    risk_adjusted_return: float


@dataclass
class OptimizationResult:
    """优化结果"""
    original_metrics: ProfitMetrics
    optimized_metrics: ProfitMetrics
    improvement_pct: float
    optimized_parameters: Dict
    optimization_method: str
    confidence_score: float
    backtest_period: str
    trade_count: int


class ProfitOptimizer:
    """盈利导向优化器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 优化历史
        self.optimization_history: List[OptimizationResult] = []
        
        # 参数空间定义
        self.parameter_space = self._define_parameter_space()
        
        # 盈利模型
        self.profit_model = None
        self.feature_importance = {}
        
        self.logger.info("盈利导向优化器初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'optimization': {
                'objective': 'sharpe_ratio',  # total_return, sharpe_ratio, profit_factor
                'min_trades': 30,  # 最小交易次数
                'lookback_days': 252,  # 回测天数
                'optimization_method': 'bayesian',  # grid, random, bayesian, genetic
                'max_iterations': 100,
                'early_stopping_rounds': 10
            },
            'constraints': {
                'max_drawdown_limit': 0.15,  # 最大回撤限制
                'min_win_rate': 0.4,  # 最小胜率
                'min_profit_factor': 1.2,  # 最小盈利因子
                'max_position_size': 0.1,  # 最大仓位
                'min_sharpe_ratio': 0.5
            },
            'risk_management': {
                'position_sizing_method': 'kelly',  # fixed, kelly, volatility
                'stop_loss_optimization': True,
                'take_profit_optimization': True,
                'dynamic_position_sizing': True
            },
            'feature_engineering': {
                'enable_ml_features': True,
                'feature_selection_method': 'importance',
                'max_features': 50
            }
        }
    
    def _define_parameter_space(self) -> Dict:
        """定义参数优化空间"""
        return {
            # 信号参数
            'signal_threshold': (0.3, 0.9, 0.1),  # (min, max, step)
            'confidence_threshold': (0.5, 0.95, 0.05),
            'signal_combination_weight': (0.1, 1.0, 0.1),
            
            # 风险管理参数
            'stop_loss_pct': (0.01, 0.1, 0.005),
            'take_profit_pct': (0.01, 0.2, 0.01),
            'position_size_pct': (0.01, 0.2, 0.01),
            'max_holding_days': (1, 30, 1),
            
            # 交易成本参数
            'commission_rate': (0.0001, 0.003, 0.0001),
            'slippage_rate': (0.0001, 0.002, 0.0001),
            
            # 市场环境参数
            'volatility_threshold': (0.1, 0.5, 0.05),
            'trend_strength_threshold': (0.3, 0.8, 0.05),
            'volume_threshold': (0.5, 2.0, 0.1)
        }
    
    def optimize_strategy(self, 
                         historical_data: pd.DataFrame,
                         signals: List[DimensionEvaluationResult],
                         current_parameters: Dict) -> OptimizationResult:
        """优化策略参数"""
        self.logger.info("开始策略优化")
        
        try:
            # 计算原始指标
            original_metrics = self._calculate_profit_metrics(
                historical_data, signals, current_parameters
            )
            
            # 选择优化方法
            method = self.config['optimization']['optimization_method']
            
            if method == 'bayesian':
                optimized_params = self._bayesian_optimization(
                    historical_data, signals, current_parameters
                )
            elif method == 'genetic':
                optimized_params = self._genetic_optimization(
                    historical_data, signals, current_parameters
                )
            elif method == 'grid':
                optimized_params = self._grid_search_optimization(
                    historical_data, signals, current_parameters
                )
            else:
                optimized_params = self._random_search_optimization(
                    historical_data, signals, current_parameters
                )
            
            # 计算优化后指标
            optimized_metrics = self._calculate_profit_metrics(
                historical_data, signals, optimized_params
            )
            
            # 计算改进程度
            improvement = self._calculate_improvement(original_metrics, optimized_metrics)
            
            # 创建优化结果
            result = OptimizationResult(
                original_metrics=original_metrics,
                optimized_metrics=optimized_metrics,
                improvement_pct=improvement,
                optimized_parameters=optimized_params,
                optimization_method=method,
                confidence_score=self._calculate_confidence_score(optimized_metrics),
                backtest_period=f"{len(historical_data)} days",
                trade_count=optimized_metrics.total_trades
            )
            
            # 保存优化历史
            self.optimization_history.append(result)
            
            self.logger.info(f"策略优化完成，改进 {improvement:.2%}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"策略优化失败: {e}")
            raise
    
    def _calculate_profit_metrics(self, 
                                 data: pd.DataFrame, 
                                 signals: List[DimensionEvaluationResult],
                                 parameters: Dict) -> ProfitMetrics:
        """计算盈利指标"""
        try:
            # 模拟交易执行
            trades = self._simulate_trading(data, signals, parameters)
            
            if not trades:
                return self._create_empty_metrics()
            
            # 计算收益序列
            returns = [trade['return'] for trade in trades]
            cumulative_returns = np.cumprod([1 + r for r in returns])
            
            # 基本指标
            total_return = cumulative_returns[-1] - 1
            annualized_return = (1 + total_return) ** (252 / len(data)) - 1
            
            # 夏普比率
            if len(returns) > 1:
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
            else:
                sharpe_ratio = 0
            
            # 最大回撤
            peak = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - peak) / peak
            max_drawdown = np.min(drawdown)
            
            # 胜率和盈利因子
            winning_trades = [r for r in returns if r > 0]
            losing_trades = [r for r in returns if r < 0]
            
            win_rate = len(winning_trades) / len(returns) if returns else 0
            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = np.mean(losing_trades) if losing_trades else 0
            
            profit_factor = (
                abs(avg_win * len(winning_trades)) / abs(avg_loss * len(losing_trades))
                if losing_trades else float('inf')
            )
            
            # 其他指标
            roi = total_return
            profit_per_trade = total_return / len(trades) if trades else 0
            risk_adjusted_return = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            return ProfitMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                avg_win=avg_win,
                avg_loss=avg_loss,
                total_trades=len(trades),
                profitable_trades=len(winning_trades),
                roi=roi,
                profit_per_trade=profit_per_trade,
                risk_adjusted_return=risk_adjusted_return
            )
            
        except Exception as e:
            self.logger.error(f"计算盈利指标失败: {e}")
            return self._create_empty_metrics()
    
    def _simulate_trading(self, 
                         data: pd.DataFrame, 
                         signals: List[DimensionEvaluationResult],
                         parameters: Dict) -> List[Dict]:
        """模拟交易执行"""
        trades = []
        
        try:
            # 参数提取
            signal_threshold = parameters.get('signal_threshold', 0.6)
            stop_loss_pct = parameters.get('stop_loss_pct', 0.05)
            take_profit_pct = parameters.get('take_profit_pct', 0.1)
            position_size_pct = parameters.get('position_size_pct', 0.05)
            commission_rate = parameters.get('commission_rate', 0.001)
            slippage_rate = parameters.get('slippage_rate', 0.0005)
            
            # 过滤信号
            valid_signals = [s for s in signals if s.score >= signal_threshold]
            
            for signal in valid_signals:
                # 模拟入场
                entry_price = 100.0  # 模拟价格
                entry_price *= (1 + slippage_rate)  # 滑点
                
                # 计算仓位大小
                position_size = self._calculate_position_size(
                    entry_price, position_size_pct, parameters
                )
                
                # 模拟持仓和出场
                exit_price, holding_days = self._simulate_holding(
                    entry_price, stop_loss_pct, take_profit_pct, parameters
                )
                
                # 计算收益
                gross_return = (exit_price - entry_price) / entry_price
                commission_cost = commission_rate * 2  # 买入卖出各一次
                net_return = gross_return - commission_cost
                
                trade = {
                    'signal_id': signal.dimension_name,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'position_size': position_size,
                    'holding_days': holding_days,
                    'gross_return': gross_return,
                    'net_return': net_return,
                    'return': net_return * position_size,  # 加权收益
                    'commission': commission_cost
                }
                
                trades.append(trade)
            
            return trades
            
        except Exception as e:
            self.logger.error(f"模拟交易失败: {e}")
            return []
    
    def _calculate_position_size(self, price: float, base_size_pct: float, parameters: Dict) -> float:
        """计算仓位大小"""
        method = self.config['risk_management']['position_sizing_method']
        
        if method == 'kelly':
            # 凯利公式（简化版）
            win_rate = 0.55  # 假设胜率
            avg_win = 0.02   # 假设平均盈利
            avg_loss = 0.015 # 假设平均亏损
            
            kelly_fraction = win_rate - (1 - win_rate) * (avg_loss / avg_win)
            kelly_fraction = max(0, min(kelly_fraction, 0.25))  # 限制在0-25%
            
            return kelly_fraction
            
        elif method == 'volatility':
            # 基于波动率的仓位调整
            volatility = parameters.get('volatility_threshold', 0.2)
            volatility_adjusted = base_size_pct / (1 + volatility)
            return min(volatility_adjusted, 0.1)
            
        else:
            # 固定仓位
            return base_size_pct
    
    def _simulate_holding(self, entry_price: float, stop_loss_pct: float, 
                         take_profit_pct: float, parameters: Dict) -> Tuple[float, int]:
        """模拟持仓过程"""
        # 简化的持仓模拟
        import random
        
        # 随机决定是否触发止损或止盈
        outcome = random.choice(['stop_loss', 'take_profit', 'time_exit'])
        
        if outcome == 'stop_loss':
            exit_price = entry_price * (1 - stop_loss_pct)
            holding_days = random.randint(1, 5)
        elif outcome == 'take_profit':
            exit_price = entry_price * (1 + take_profit_pct)
            holding_days = random.randint(1, 10)
        else:
            # 时间出场
            price_change = random.uniform(-0.03, 0.03)
            exit_price = entry_price * (1 + price_change)
            holding_days = random.randint(5, 20)
        
        return exit_price, holding_days
    
    def _bayesian_optimization(self, data: pd.DataFrame, signals: List, 
                              current_params: Dict) -> Dict:
        """贝叶斯优化"""
        self.logger.info("执行贝叶斯优化")
        
        # 简化的贝叶斯优化实现
        best_params = current_params.copy()
        best_score = 0
        
        max_iterations = self.config['optimization']['max_iterations']
        
        for i in range(max_iterations):
            # 生成候选参数
            candidate_params = self._generate_candidate_parameters(current_params)
            
            # 评估候选参数
            metrics = self._calculate_profit_metrics(data, signals, candidate_params)
            score = self._calculate_objective_score(metrics)
            
            # 更新最佳参数
            if score > best_score:
                best_score = score
                best_params = candidate_params.copy()
                self.logger.debug(f"迭代 {i+1}: 找到更好的参数，得分 {score:.4f}")
        
        return best_params
    
    def _genetic_optimization(self, data: pd.DataFrame, signals: List, 
                             current_params: Dict) -> Dict:
        """遗传算法优化"""
        self.logger.info("执行遗传算法优化")
        
        population_size = 20
        generations = 50
        mutation_rate = 0.1
        
        # 初始化种群
        population = [
            self._generate_candidate_parameters(current_params) 
            for _ in range(population_size)
        ]
        
        for generation in range(generations):
            # 评估种群
            fitness_scores = []
            for individual in population:
                metrics = self._calculate_profit_metrics(data, signals, individual)
                score = self._calculate_objective_score(metrics)
                fitness_scores.append(score)
            
            # 选择、交叉、变异
            new_population = []
            for _ in range(population_size):
                # 选择父母（轮盘赌选择）
                parent1 = self._roulette_selection(population, fitness_scores)
                parent2 = self._roulette_selection(population, fitness_scores)
                
                # 交叉
                child = self._crossover(parent1, parent2)
                
                # 变异
                if np.random.random() < mutation_rate:
                    child = self._mutate(child)
                
                new_population.append(child)
            
            population = new_population
        
        # 返回最佳个体
        final_scores = [
            self._calculate_objective_score(
                self._calculate_profit_metrics(data, signals, individual)
            )
            for individual in population
        ]
        
        best_idx = np.argmax(final_scores)
        return population[best_idx]
    
    def _grid_search_optimization(self, data: pd.DataFrame, signals: List, 
                                 current_params: Dict) -> Dict:
        """网格搜索优化"""
        self.logger.info("执行网格搜索优化")
        
        # 简化的网格搜索
        best_params = current_params.copy()
        best_score = 0
        
        # 只优化几个关键参数
        key_params = ['signal_threshold', 'stop_loss_pct', 'take_profit_pct']
        
        for param in key_params:
            if param in self.parameter_space:
                min_val, max_val, step = self.parameter_space[param]
                values = np.arange(min_val, max_val + step, step)
                
                for value in values:
                    test_params = current_params.copy()
                    test_params[param] = value
                    
                    metrics = self._calculate_profit_metrics(data, signals, test_params)
                    score = self._calculate_objective_score(metrics)
                    
                    if score > best_score:
                        best_score = score
                        best_params = test_params.copy()
        
        return best_params
    
    def _random_search_optimization(self, data: pd.DataFrame, signals: List, 
                                   current_params: Dict) -> Dict:
        """随机搜索优化"""
        self.logger.info("执行随机搜索优化")
        
        best_params = current_params.copy()
        best_score = 0
        
        max_iterations = self.config['optimization']['max_iterations']
        
        for i in range(max_iterations):
            candidate_params = self._generate_random_parameters()
            
            metrics = self._calculate_profit_metrics(data, signals, candidate_params)
            score = self._calculate_objective_score(metrics)
            
            if score > best_score:
                best_score = score
                best_params = candidate_params.copy()
        
        return best_params
    
    def _generate_candidate_parameters(self, base_params: Dict) -> Dict:
        """生成候选参数"""
        candidate = base_params.copy()
        
        # 随机调整几个参数
        params_to_adjust = np.random.choice(
            list(self.parameter_space.keys()), 
            size=min(3, len(self.parameter_space)), 
            replace=False
        )
        
        for param in params_to_adjust:
            min_val, max_val, step = self.parameter_space[param]
            candidate[param] = np.random.uniform(min_val, max_val)
        
        return candidate
    
    def _generate_random_parameters(self) -> Dict:
        """生成随机参数"""
        params = {}
        
        for param, (min_val, max_val, step) in self.parameter_space.items():
            params[param] = np.random.uniform(min_val, max_val)
        
        return params
    
    def _calculate_objective_score(self, metrics: ProfitMetrics) -> float:
        """计算目标得分"""
        objective = self.config['optimization']['objective']
        
        # 约束检查
        if not self._check_constraints(metrics):
            return -1000  # 惩罚不满足约束的解
        
        if objective == 'total_return':
            return metrics.total_return
        elif objective == 'sharpe_ratio':
            return metrics.sharpe_ratio
        elif objective == 'profit_factor':
            return metrics.profit_factor
        else:
            # 综合得分
            return (
                metrics.total_return * 0.3 +
                metrics.sharpe_ratio * 0.3 +
                metrics.win_rate * 0.2 +
                metrics.profit_factor * 0.2
            )
    
    def _check_constraints(self, metrics: ProfitMetrics) -> bool:
        """检查约束条件"""
        constraints = self.config['constraints']
        
        if abs(metrics.max_drawdown) > constraints['max_drawdown_limit']:
            return False
        
        if metrics.win_rate < constraints['min_win_rate']:
            return False
        
        if metrics.profit_factor < constraints['min_profit_factor']:
            return False
        
        if metrics.sharpe_ratio < constraints['min_sharpe_ratio']:
            return False
        
        if metrics.total_trades < self.config['optimization']['min_trades']:
            return False
        
        return True
    
    def _calculate_improvement(self, original: ProfitMetrics, optimized: ProfitMetrics) -> float:
        """计算改进程度"""
        objective = self.config['optimization']['objective']
        
        if objective == 'total_return':
            base_value = original.total_return
            new_value = optimized.total_return
        elif objective == 'sharpe_ratio':
            base_value = original.sharpe_ratio
            new_value = optimized.sharpe_ratio
        elif objective == 'profit_factor':
            base_value = original.profit_factor
            new_value = optimized.profit_factor
        else:
            base_value = self._calculate_objective_score(original)
            new_value = self._calculate_objective_score(optimized)
        
        if base_value == 0:
            return 0
        
        return (new_value - base_value) / abs(base_value)
    
    def _calculate_confidence_score(self, metrics: ProfitMetrics) -> float:
        """计算置信度得分"""
        # 基于交易次数和统计显著性
        if metrics.total_trades < 30:
            return 0.3
        elif metrics.total_trades < 100:
            return 0.6
        else:
            return 0.9
    
    def _create_empty_metrics(self) -> ProfitMetrics:
        """创建空的指标对象"""
        return ProfitMetrics(
            total_return=0, annualized_return=0, sharpe_ratio=0,
            max_drawdown=0, win_rate=0, profit_factor=0,
            avg_win=0, avg_loss=0, total_trades=0,
            profitable_trades=0, roi=0, profit_per_trade=0,
            risk_adjusted_return=0
        )
    
    def _roulette_selection(self, population: List, fitness_scores: List) -> Dict:
        """轮盘赌选择"""
        total_fitness = sum(max(0, score) for score in fitness_scores)
        if total_fitness == 0:
            return np.random.choice(population)
        
        pick = np.random.uniform(0, total_fitness)
        current = 0
        
        for i, score in enumerate(fitness_scores):
            current += max(0, score)
            if current >= pick:
                return population[i]
        
        return population[-1]
    
    def _crossover(self, parent1: Dict, parent2: Dict) -> Dict:
        """交叉操作"""
        child = {}
        
        for key in parent1.keys():
            if np.random.random() < 0.5:
                child[key] = parent1[key]
            else:
                child[key] = parent2[key]
        
        return child
    
    def _mutate(self, individual: Dict) -> Dict:
        """变异操作"""
        mutated = individual.copy()
        
        # 随机选择一个参数进行变异
        param = np.random.choice(list(mutated.keys()))
        
        if param in self.parameter_space:
            min_val, max_val, step = self.parameter_space[param]
            mutated[param] = np.random.uniform(min_val, max_val)
        
        return mutated
    
    def get_optimization_history(self) -> List[OptimizationResult]:
        """获取优化历史"""
        return self.optimization_history.copy()
    
    def get_best_parameters(self) -> Optional[Dict]:
        """获取历史最佳参数"""
        if not self.optimization_history:
            return None
        
        best_result = max(
            self.optimization_history,
            key=lambda x: self._calculate_objective_score(x.optimized_metrics)
        )
        
        return best_result.optimized_parameters
    
    def export_optimization_report(self, filepath: str):
        """导出优化报告"""
        try:
            report = {
                'optimization_summary': {
                    'total_optimizations': len(self.optimization_history),
                    'best_improvement': max(
                        [r.improvement_pct for r in self.optimization_history], 
                        default=0
                    ),
                    'avg_improvement': np.mean(
                        [r.improvement_pct for r in self.optimization_history]
                    ) if self.optimization_history else 0
                },
                'optimization_history': [
                    {
                        'method': r.optimization_method,
                        'improvement_pct': r.improvement_pct,
                        'confidence_score': r.confidence_score,
                        'trade_count': r.trade_count,
                        'optimized_metrics': {
                            'total_return': r.optimized_metrics.total_return,
                            'sharpe_ratio': r.optimized_metrics.sharpe_ratio,
                            'max_drawdown': r.optimized_metrics.max_drawdown,
                            'win_rate': r.optimized_metrics.win_rate,
                            'profit_factor': r.optimized_metrics.profit_factor
                        },
                        'optimized_parameters': r.optimized_parameters
                    }
                    for r in self.optimization_history
                ],
                'parameter_space': self.parameter_space,
                'config': self.config
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"优化报告已导出到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"导出优化报告失败: {e}")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    optimizer = ProfitOptimizer()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'close': np.random.randn(100).cumsum() + 100,
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    test_signals = []  # 空信号列表用于测试
    
    current_params = {
        'signal_threshold': 0.6,
        'stop_loss_pct': 0.05,
        'take_profit_pct': 0.1,
        'position_size_pct': 0.05
    }
    
    # 执行优化
    result = optimizer.optimize_strategy(test_data, test_signals, current_params)
    
    print(f"优化完成，改进: {result.improvement_pct:.2%}")
    print(f"最佳参数: {result.optimized_parameters}")