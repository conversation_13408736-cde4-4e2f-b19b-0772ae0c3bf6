"""
模型训练器
Model Trainer
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化模型训练器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.model_type = self.config.get('model_type', 'random_forest')
        self.test_size = self.config.get('test_size', 0.2)
        self.cv_folds = self.config.get('cv_folds', 5)
        self.random_state = self.config.get('random_state', 42)
        
        self.trained_models = {}
        self.model_performance = {}
        
        logger.info("ModelTrainer 初始化完成")
    
    def train_and_evaluate(self, features: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """
        训练和评估模型
        
        Args:
            features: 特征数据
            target: 目标变量
            
        Returns:
            训练和评估结果
        """
        logger.info(f"开始模型训练，特征数量: {features.shape[1]}, 样本数量: {len(features)}")
        
        try:
            # 数据预处理
            X, y = self._prepare_data(features, target)
            
            if len(X) == 0:
                return {'error': '没有有效的训练数据'}
            
            # 数据分割
            X_train, X_test, y_train, y_test = self._split_data(X, y)
            
            # 模型训练
            models = self._train_models(X_train, y_train)
            
            # 模型评估
            results = self._evaluate_models(models, X_test, y_test)
            
            # 交叉验证
            cv_results = self._cross_validate(X, y)
            
            # 特征重要性
            feature_importance = self._get_feature_importance(models, X.columns)
            
            # 整合结果
            final_results = {
                'models_trained': list(models.keys()),
                'best_model': self._select_best_model(results),
                'test_results': results,
                'cv_results': cv_results,
                'feature_importance': feature_importance,
                'data_info': {
                    'train_size': len(X_train),
                    'test_size': len(X_test),
                    'feature_count': X.shape[1],
                    'target_mean': float(y.mean()),
                    'target_std': float(y.std())
                }
            }
            
            logger.info("模型训练完成")
            return final_results
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return {'error': str(e)}
    
    def _prepare_data(self, features: pd.DataFrame, target: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """准备训练数据"""
        try:
            # 对齐索引
            common_index = features.index.intersection(target.index)
            X = features.loc[common_index]
            y = target.loc[common_index]
            
            # 选择数值特征
            numeric_features = X.select_dtypes(include=[np.number]).columns
            X = X[numeric_features]
            
            # 处理缺失值
            X = X.fillna(X.mean())
            y = y.fillna(y.mean())
            
            # 处理无穷值
            X = X.replace([np.inf, -np.inf], np.nan).fillna(X.mean())
            y = y.replace([np.inf, -np.inf], np.nan).fillna(y.mean())
            
            # 移除常数特征
            constant_features = X.columns[X.var() == 0]
            if len(constant_features) > 0:
                X = X.drop(columns=constant_features)
                logger.info(f"移除常数特征: {len(constant_features)} 个")
            
            return X, y
            
        except Exception as e:
            logger.error(f"数据准备失败: {e}")
            return pd.DataFrame(), pd.Series()
    
    def _split_data(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """分割训练和测试数据"""
        try:
            from sklearn.model_selection import train_test_split
            
            return train_test_split(X, y, test_size=self.test_size, random_state=self.random_state)
            
        except ImportError:
            # 如果sklearn不可用，使用简单分割
            split_idx = int(len(X) * (1 - self.test_size))
            return X.iloc[:split_idx], X.iloc[split_idx:], y.iloc[:split_idx], y.iloc[split_idx:]
    
    def _train_models(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict[str, Any]:
        """训练多个模型"""
        models = {}
        
        try:
            # 随机森林
            try:
                from sklearn.ensemble import RandomForestRegressor
                rf = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    random_state=self.random_state,
                    n_jobs=-1
                )
                rf.fit(X_train, y_train)
                models['random_forest'] = rf
                logger.debug("随机森林模型训练完成")
            except ImportError:
                logger.warning("sklearn不可用，跳过随机森林模型")
            
            # 梯度提升
            try:
                from sklearn.ensemble import GradientBoostingRegressor
                gb = GradientBoostingRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=self.random_state
                )
                gb.fit(X_train, y_train)
                models['gradient_boosting'] = gb
                logger.debug("梯度提升模型训练完成")
            except ImportError:
                logger.warning("sklearn不可用，跳过梯度提升模型")
            
            # 线性回归
            try:
                from sklearn.linear_model import LinearRegression
                lr = LinearRegression()
                lr.fit(X_train, y_train)
                models['linear_regression'] = lr
                logger.debug("线性回归模型训练完成")
            except ImportError:
                logger.warning("sklearn不可用，跳过线性回归模型")
            
            # 岭回归
            try:
                from sklearn.linear_model import Ridge
                ridge = Ridge(alpha=1.0, random_state=self.random_state)
                ridge.fit(X_train, y_train)
                models['ridge'] = ridge
                logger.debug("岭回归模型训练完成")
            except ImportError:
                logger.warning("sklearn不可用，跳过岭回归模型")
            
            # 如果没有可用的模型，创建简单的均值模型
            if not models:
                models['mean_model'] = SimpleMeanModel(y_train.mean())
                logger.info("使用简单均值模型")
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            models['mean_model'] = SimpleMeanModel(y_train.mean())
        
        self.trained_models = models
        return models
    
    def _evaluate_models(self, models: Dict[str, Any], X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Dict[str, float]]:
        """评估模型性能"""
        results = {}
        
        for name, model in models.items():
            try:
                # 预测
                if hasattr(model, 'predict'):
                    y_pred = model.predict(X_test)
                else:
                    y_pred = np.full(len(y_test), model.mean_value)
                
                # 计算指标
                mse = np.mean((y_test - y_pred) ** 2)
                rmse = np.sqrt(mse)
                mae = np.mean(np.abs(y_test - y_pred))
                
                # R²
                ss_res = np.sum((y_test - y_pred) ** 2)
                ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
                r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
                
                # 方向准确率
                direction_accuracy = np.mean(np.sign(y_pred) == np.sign(y_test))
                
                results[name] = {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2),
                    'direction_accuracy': float(direction_accuracy)
                }
                
            except Exception as e:
                logger.error(f"模型 {name} 评估失败: {e}")
                results[name] = {
                    'mse': float('inf'),
                    'rmse': float('inf'),
                    'mae': float('inf'),
                    'r2': 0.0,
                    'direction_accuracy': 0.5,
                    'error': str(e)
                }
        
        return results
    
    def _cross_validate(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Dict[str, float]]:
        """交叉验证"""
        cv_results = {}
        
        try:
            from sklearn.model_selection import cross_val_score
            from sklearn.ensemble import RandomForestRegressor
            
            # 只对随机森林进行交叉验证（作为示例）
            rf = RandomForestRegressor(n_estimators=50, random_state=self.random_state)
            
            # 计算不同指标的交叉验证分数
            cv_scores = cross_val_score(rf, X, y, cv=min(self.cv_folds, len(X)//10), scoring='r2')
            cv_mse = -cross_val_score(rf, X, y, cv=min(self.cv_folds, len(X)//10), scoring='neg_mean_squared_error')
            
            cv_results['random_forest'] = {
                'cv_r2_mean': float(np.mean(cv_scores)),
                'cv_r2_std': float(np.std(cv_scores)),
                'cv_mse_mean': float(np.mean(cv_mse)),
                'cv_mse_std': float(np.std(cv_mse))
            }
            
        except ImportError:
            logger.warning("sklearn不可用，跳过交叉验证")
            cv_results = {'note': 'sklearn不可用，无法进行交叉验证'}
        except Exception as e:
            logger.error(f"交叉验证失败: {e}")
            cv_results = {'error': str(e)}
        
        return cv_results
    
    def _get_feature_importance(self, models: Dict[str, Any], feature_names: List[str]) -> Dict[str, Dict[str, float]]:
        """获取特征重要性"""
        importance_results = {}
        
        for name, model in models.items():
            try:
                if hasattr(model, 'feature_importances_'):
                    # 树模型的特征重要性
                    importance = dict(zip(feature_names, model.feature_importances_))
                    # 按重要性排序
                    importance_results[name] = dict(sorted(importance.items(), key=lambda x: x[1], reverse=True))
                
                elif hasattr(model, 'coef_'):
                    # 线性模型的系数
                    importance = dict(zip(feature_names, np.abs(model.coef_)))
                    importance_results[name] = dict(sorted(importance.items(), key=lambda x: x[1], reverse=True))
                
                else:
                    importance_results[name] = {'note': '该模型不支持特征重要性计算'}
                    
            except Exception as e:
                logger.error(f"模型 {name} 特征重要性计算失败: {e}")
                importance_results[name] = {'error': str(e)}
        
        return importance_results
    
    def _select_best_model(self, results: Dict[str, Dict[str, float]]) -> str:
        """选择最佳模型"""
        try:
            best_model = None
            best_score = float('-inf')
            
            for name, metrics in results.items():
                if 'error' not in metrics:
                    # 综合评分：R² + 方向准确率 - 标准化MSE
                    r2 = metrics.get('r2', 0)
                    direction_acc = metrics.get('direction_accuracy', 0.5)
                    mse = metrics.get('mse', float('inf'))
                    
                    # 标准化MSE（避免除零）
                    normalized_mse = mse / (1 + mse) if mse != float('inf') else 1
                    
                    score = r2 + direction_acc - normalized_mse
                    
                    if score > best_score:
                        best_score = score
                        best_model = name
            
            return best_model or 'none'
            
        except Exception as e:
            logger.error(f"最佳模型选择失败: {e}")
            return 'none'
    
    def predict(self, features: pd.DataFrame, model_name: str = None) -> np.ndarray:
        """使用训练好的模型进行预测"""
        try:
            if not self.trained_models:
                raise ValueError("没有训练好的模型")
            
            # 选择模型
            if model_name is None:
                model_name = list(self.trained_models.keys())[0]
            
            if model_name not in self.trained_models:
                raise ValueError(f"模型 {model_name} 不存在")
            
            model = self.trained_models[model_name]
            
            # 数据预处理
            X = features.select_dtypes(include=[np.number])
            X = X.fillna(X.mean())
            X = X.replace([np.inf, -np.inf], np.nan).fillna(X.mean())
            
            # 预测
            if hasattr(model, 'predict'):
                predictions = model.predict(X)
            else:
                predictions = np.full(len(X), model.mean_value)
            
            return predictions
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.zeros(len(features))


class SimpleMeanModel:
    """简单的均值模型（当sklearn不可用时使用）"""
    
    def __init__(self, mean_value: float):
        self.mean_value = mean_value
    
    def predict(self, X):
        return np.full(len(X), self.mean_value)