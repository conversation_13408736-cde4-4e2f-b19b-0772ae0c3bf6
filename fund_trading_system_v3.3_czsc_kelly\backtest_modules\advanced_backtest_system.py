"""
高级回测系统
Advanced Backtest System
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class AdvancedBacktestSystem:
    """高级回测系统"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化高级回测系统
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.data_preprocessor = None
        self.feature_engineer = None
        self.model_trainer = None
        self.backtest_engine = None
        self.risk_manager = None
        
        # 初始化各个组件
        self._initialize_components()
        
        logger.info("AdvancedBacktestSystem 初始化完成")
    
    def _initialize_components(self):
        """初始化各个组件"""
        try:
            # 尝试导入并初始化各个组件
            from data_preprocessor import DataPreprocessor
            self.data_preprocessor = DataPreprocessor(self.config.get('data_preprocessor', {}))
            logger.info("DataPreprocessor 模块加载成功")
        except ImportError as e:
            logger.warning(f"DataPreprocessor 不可用，使用简化版本: {e}")
            self.data_preprocessor = SimpleDataPreprocessor()
        
        try:
            from feature_engineer import FeatureEngineer
            self.feature_engineer = FeatureEngineer(self.config.get('feature_engineer', {}))
            logger.info("FeatureEngineer 模块加载成功")
        except ImportError as e:
            logger.warning(f"FeatureEngineer 不可用，使用简化版本: {e}")
            self.feature_engineer = SimpleFeatureEngineer()
        
        try:
            from model_trainer import ModelTrainer
            self.model_trainer = ModelTrainer(self.config.get('model_trainer', {}))
            logger.info("ModelTrainer 模块加载成功")
        except ImportError as e:
            logger.warning(f"ModelTrainer 不可用，使用简化版本: {e}")
            self.model_trainer = SimpleModelTrainer()
        
        try:
            from backtest_engine import BacktestEngine
            self.backtest_engine = BacktestEngine(self.config.get('backtest_engine', {}))
            logger.info("BacktestEngine 模块加载成功")
        except ImportError as e:
            logger.warning(f"BacktestEngine 不可用，使用简化版本: {e}")
            self.backtest_engine = SimpleBacktestEngine(self.config.get('backtest_engine', {}))
        
        try:
            from risk_manager import RiskManager
            self.risk_manager = RiskManager(self.config.get('risk_manager', {}))
            logger.info("RiskManager 模块加载成功")
        except ImportError as e:
            logger.warning(f"RiskManager 不可用，使用简化版本: {e}")
            self.risk_manager = SimpleRiskManager(self.config.get('risk_manager', {}))
    
    def run_complete_pipeline(self, data: pd.DataFrame, target_col: str = 'future_return', 
                            time_col: str = 'dt', price_col: str = 'close') -> Dict[str, Any]:
        """
        运行完整的回测流水线
        
        Args:
            data: 输入数据
            target_col: 目标列名
            time_col: 时间列名
            price_col: 价格列名
            
        Returns:
            回测结果字典
        """
        logger.info("开始运行完整回测流水线")
        
        results = {
            'pipeline_results': {},
            'performance_metrics': {},
            'risk_metrics': {},
            'execution_time': {}
        }
        
        try:
            # 1. 数据预处理
            start_time = datetime.now()
            processed_data = self.data_preprocessor.preprocess(data)
            results['execution_time']['preprocessing'] = (datetime.now() - start_time).total_seconds()
            results['pipeline_results']['preprocessing'] = {
                'original_shape': data.shape,
                'processed_shape': processed_data.shape,
                'missing_values_handled': True
            }
            logger.info(f"数据预处理完成: {data.shape} -> {processed_data.shape}")
            
            # 2. 特征工程
            start_time = datetime.now()
            features = self.feature_engineer.engineer_features(processed_data)
            results['execution_time']['feature_engineering'] = (datetime.now() - start_time).total_seconds()
            results['pipeline_results']['feature_engineering'] = {
                'feature_count': features.shape[1] if hasattr(features, 'shape') else len(features.columns),
                'feature_names': list(features.columns) if hasattr(features, 'columns') else []
            }
            logger.info(f"特征工程完成: 生成 {len(features.columns)} 个特征")
            
            # 3. 模型训练
            start_time = datetime.now()
            if target_col in processed_data.columns:
                model_results = self.model_trainer.train_and_evaluate(features, processed_data[target_col])
                results['execution_time']['model_training'] = (datetime.now() - start_time).total_seconds()
                results['pipeline_results']['model_training'] = model_results
                logger.info("模型训练完成")
            else:
                logger.warning(f"目标列 {target_col} 不存在，跳过模型训练")
                results['pipeline_results']['model_training'] = {'skipped': True, 'reason': f'Target column {target_col} not found'}
            
            # 4. 回测执行
            start_time = datetime.now()
            backtest_results = self.backtest_engine.run_backtest(
                processed_data, 
                time_col=time_col, 
                price_col=price_col
            )
            results['execution_time']['backtesting'] = (datetime.now() - start_time).total_seconds()
            results['pipeline_results']['backtest'] = backtest_results
            logger.info("回测执行完成")
            
            # 5. 风险评估
            start_time = datetime.now()
            risk_results = self.risk_manager.evaluate_risk(backtest_results)
            results['execution_time']['risk_evaluation'] = (datetime.now() - start_time).total_seconds()
            results['risk_metrics'] = risk_results
            logger.info("风险评估完成")
            
            # 6. 性能指标计算
            results['performance_metrics'] = self._calculate_performance_metrics(backtest_results)
            
            logger.info("完整回测流水线执行完成")
            
        except Exception as e:
            logger.error(f"回测流水线执行失败: {e}")
            results['error'] = str(e)
        
        return results
    
    def _calculate_performance_metrics(self, backtest_results: Dict[str, Any]) -> Dict[str, float]:
        """计算性能指标"""
        metrics = {}
        
        try:
            if 'returns' in backtest_results:
                returns = np.array(backtest_results['returns'])
                
                # 基础指标
                metrics['total_return'] = np.prod(1 + returns) - 1
                metrics['annualized_return'] = (1 + metrics['total_return']) ** (252 / len(returns)) - 1
                metrics['volatility'] = np.std(returns) * np.sqrt(252)
                metrics['sharpe_ratio'] = metrics['annualized_return'] / metrics['volatility'] if metrics['volatility'] > 0 else 0
                
                # 回撤指标
                cumulative_returns = np.cumprod(1 + returns)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdown = (cumulative_returns - running_max) / running_max
                metrics['max_drawdown'] = np.min(drawdown)
                
                # 胜率
                metrics['win_rate'] = np.sum(returns > 0) / len(returns)
                
            else:
                # 默认值
                metrics = {
                    'total_return': 0.0,
                    'annualized_return': 0.0,
                    'volatility': 0.0,
                    'sharpe_ratio': 0.0,
                    'max_drawdown': 0.0,
                    'win_rate': 0.5
                }
                
        except Exception as e:
            logger.error(f"性能指标计算失败: {e}")
            metrics = {
                'total_return': 0.0,
                'annualized_return': 0.0,
                'volatility': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.5
            }
        
        return metrics


# 简化版本的组件类
class SimpleDataPreprocessor:
    """简化的数据预处理器"""
    
    def preprocess(self, data: pd.DataFrame) -> pd.DataFrame:
        """简单的数据预处理"""
        processed_data = data.copy()
        
        # 处理缺失值
        processed_data = processed_data.fillna(method='ffill').fillna(method='bfill')
        
        # 处理异常值（简单的3σ规则）
        for col in processed_data.select_dtypes(include=[np.number]).columns:
            mean = processed_data[col].mean()
            std = processed_data[col].std()
            processed_data[col] = processed_data[col].clip(mean - 3*std, mean + 3*std)
        
        return processed_data


class SimpleFeatureEngineer:
    """简化的特征工程器"""
    
    def engineer_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """简单的特征工程"""
        features = data.copy()
        
        # 添加一些基础技术指标
        if 'close' in features.columns:
            # 移动平均
            features['ma5'] = features['close'].rolling(5).mean()
            features['ma20'] = features['close'].rolling(20).mean()
            
            # 价格变化率
            features['price_change'] = features['close'].pct_change()
            features['price_change_5d'] = features['close'].pct_change(5)
            
            # 波动率
            features['volatility_5d'] = features['price_change'].rolling(5).std()
            features['volatility_20d'] = features['price_change'].rolling(20).std()
        
        if 'volume' in features.columns:
            # 成交量指标
            features['volume_ma5'] = features['volume'].rolling(5).mean()
            features['volume_ratio'] = features['volume'] / features['volume_ma5']
        
        # 删除包含NaN的行
        features = features.dropna()
        
        return features


class SimpleModelTrainer:
    """简化的模型训练器"""
    
    def train_and_evaluate(self, features: pd.DataFrame, target: pd.Series) -> Dict[str, Any]:
        """简单的模型训练和评估"""
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import mean_squared_error, r2_score
            
            # 对齐数据
            common_index = features.index.intersection(target.index)
            X = features.loc[common_index]
            y = target.loc[common_index]
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # 训练模型
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X_train, y_train)
            
            # 预测和评估
            y_pred = model.predict(X_test)
            
            results = {
                'model_type': 'RandomForestRegressor',
                'train_size': len(X_train),
                'test_size': len(X_test),
                'mse': mean_squared_error(y_test, y_pred),
                'r2_score': r2_score(y_test, y_pred),
                'feature_importance': dict(zip(X.columns, model.feature_importances_))
            }
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            results = {
                'model_type': 'None',
                'error': str(e),
                'train_size': 0,
                'test_size': 0,
                'mse': float('inf'),
                'r2_score': 0.0
            }
        
        return results


class SimpleBacktestEngine:
    """简化的回测引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.initial_capital = config.get('initial_capital', 100000)
        self.transaction_cost = config.get('transaction_cost', 0.001)
    
    def run_backtest(self, data: pd.DataFrame, time_col: str = 'dt', 
                    price_col: str = 'close') -> Dict[str, Any]:
        """运行简单回测"""
        try:
            # 生成简单的买卖信号（基于移动平均）
            signals = self._generate_signals(data, price_col)
            
            # 计算收益
            returns = self._calculate_returns(data, signals, price_col)
            
            # 计算资金曲线
            capital_curve = self._calculate_capital_curve(returns)
            
            results = {
                'signals': signals,
                'returns': returns,
                'capital_curve': capital_curve,
                'total_trades': np.sum(np.abs(np.diff(signals))),
                'metrics': {
                    'total_return': capital_curve[-1] / self.initial_capital - 1,
                    'annualized_return': (capital_curve[-1] / self.initial_capital) ** (252 / len(returns)) - 1,
                    'max_drawdown': self._calculate_max_drawdown(capital_curve),
                    'sharpe_ratio': np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0,
                    'win_rate': np.sum(np.array(returns) > 0) / len(returns)
                }
            }
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            results = {
                'error': str(e),
                'signals': [],
                'returns': [],
                'capital_curve': [self.initial_capital],
                'total_trades': 0,
                'metrics': {
                    'total_return': 0.0,
                    'annualized_return': 0.0,
                    'max_drawdown': 0.0,
                    'sharpe_ratio': 0.0,
                    'win_rate': 0.5
                }
            }
        
        return results
    
    def _generate_signals(self, data: pd.DataFrame, price_col: str) -> List[int]:
        """生成交易信号"""
        signals = []
        
        if 'ma5' in data.columns and 'ma20' in data.columns:
            # 基于移动平均的信号
            for i in range(len(data)):
                if i == 0:
                    signals.append(0)
                else:
                    ma5_curr = data['ma5'].iloc[i]
                    ma20_curr = data['ma20'].iloc[i]
                    ma5_prev = data['ma5'].iloc[i-1]
                    ma20_prev = data['ma20'].iloc[i-1]
                    
                    if pd.notna(ma5_curr) and pd.notna(ma20_curr) and pd.notna(ma5_prev) and pd.notna(ma20_prev):
                        if ma5_prev <= ma20_prev and ma5_curr > ma20_curr:
                            signals.append(1)  # 买入信号
                        elif ma5_prev >= ma20_prev and ma5_curr < ma20_curr:
                            signals.append(-1)  # 卖出信号
                        else:
                            signals.append(signals[-1])  # 保持前一个信号
                    else:
                        signals.append(0)
        else:
            # 随机信号作为示例
            np.random.seed(42)
            signals = np.random.choice([-1, 0, 1], size=len(data), p=[0.1, 0.8, 0.1]).tolist()
        
        return signals
    
    def _calculate_returns(self, data: pd.DataFrame, signals: List[int], price_col: str) -> List[float]:
        """计算收益率"""
        returns = []
        
        for i in range(1, len(data)):
            if i < len(signals):
                signal = signals[i-1]  # 使用前一期的信号
                price_return = data[price_col].iloc[i] / data[price_col].iloc[i-1] - 1
                
                if signal == 1:  # 多头
                    strategy_return = price_return - self.transaction_cost
                elif signal == -1:  # 空头
                    strategy_return = -price_return - self.transaction_cost
                else:  # 空仓
                    strategy_return = 0
                
                returns.append(strategy_return)
            else:
                returns.append(0)
        
        return returns
    
    def _calculate_capital_curve(self, returns: List[float]) -> List[float]:
        """计算资金曲线"""
        capital_curve = [self.initial_capital]
        
        for ret in returns:
            new_capital = capital_curve[-1] * (1 + ret)
            capital_curve.append(new_capital)
        
        return capital_curve
    
    def _calculate_max_drawdown(self, capital_curve: List[float]) -> float:
        """计算最大回撤"""
        peak = capital_curve[0]
        max_dd = 0
        
        for value in capital_curve:
            if value > peak:
                peak = value
            
            drawdown = (peak - value) / peak
            if drawdown > max_dd:
                max_dd = drawdown
        
        return max_dd


class SimpleRiskManager:
    """简化的风险管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_position_size = config.get('max_position_size', 0.1)
        self.stop_loss_pct = config.get('stop_loss_pct', 0.05)
        self.take_profit_pct = config.get('take_profit_pct', 0.15)
        self.max_drawdown_limit = config.get('max_drawdown_limit', 0.2)
    
    def evaluate_risk(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """评估风险"""
        risk_metrics = {}
        
        try:
            if 'capital_curve' in backtest_results:
                capital_curve = backtest_results['capital_curve']
                
                # VaR计算
                if 'returns' in backtest_results:
                    returns = np.array(backtest_results['returns'])
                    risk_metrics['var_95'] = np.percentile(returns, 5)
                    risk_metrics['var_99'] = np.percentile(returns, 1)
                    risk_metrics['cvar_95'] = np.mean(returns[returns <= risk_metrics['var_95']])
                
                # 最大回撤
                risk_metrics['max_drawdown'] = self._calculate_max_drawdown(capital_curve)
                
                # 风险评级
                if risk_metrics['max_drawdown'] > self.max_drawdown_limit:
                    risk_metrics['risk_level'] = 'HIGH'
                elif risk_metrics['max_drawdown'] > self.max_drawdown_limit * 0.5:
                    risk_metrics['risk_level'] = 'MEDIUM'
                else:
                    risk_metrics['risk_level'] = 'LOW'
                
                # 风险调整收益
                if 'metrics' in backtest_results:
                    metrics = backtest_results['metrics']
                    risk_metrics['risk_adjusted_return'] = metrics.get('total_return', 0) / max(risk_metrics['max_drawdown'], 0.01)
            
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            risk_metrics = {
                'var_95': 0.0,
                'var_99': 0.0,
                'cvar_95': 0.0,
                'max_drawdown': 0.0,
                'risk_level': 'UNKNOWN',
                'risk_adjusted_return': 0.0,
                'error': str(e)
            }
        
        return risk_metrics
    
    def _calculate_max_drawdown(self, capital_curve: List[float]) -> float:
        """计算最大回撤"""
        peak = capital_curve[0]
        max_dd = 0
        
        for value in capital_curve:
            if value > peak:
                peak = value
            
            drawdown = (peak - value) / peak
            if drawdown > max_dd:
                max_dd = drawdown
        
        return max_dd