"""
Tushare数据提供器基本使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tushare_data_provider import TushareDataProvider, DataRequest, FIVE_YEAR_CONFIG, REALTIME_CONFIG
from datetime import datetime, timed<PERSON><PERSON>


def example_basic_usage():
    """基本使用示例"""
    print("=== Tushare数据提供器基本使用示例 ===")
    
    # 初始化数据提供器
    provider = TushareDataProvider()
    
    # 测试股票代码
    test_codes = ['000001.SZ', '600000.SH', '000002.SZ']
    
    print(f"\n1. 获取实时数据")
    print("-" * 50)
    
    # 获取实时数据
    realtime_response = provider.get_realtime_data(test_codes)
    
    if realtime_response.success:
        print(f"成功获取 {realtime_response.data_count} 条实时数据")
        
        # 显示第一条数据
        if realtime_response.data:
            first_data = realtime_response.data[0]
            print(f"股票代码: {first_data.ts_code}")
            print(f"交易日期: {first_data.trade_date}")
            print(f"收盘价: {first_data.market_data.close}")
            print(f"涨跌幅: {first_data.market_data.pct_chg}%")
            print(f"成交量: {first_data.market_data.vol}")
            print(f"RSI: {first_data.technical_indicators.rsi}")
            print(f"MACD: {first_data.technical_indicators.macd_dif}")
            
            # 转换为特征向量
            feature_vector = first_data.to_feature_vector()
            print(f"特征向量维度: {len(feature_vector)}")
            print(f"前10个特征: {feature_vector[:10]}")
    else:
        print(f"获取实时数据失败: {realtime_response.error_message}")


def example_historical_data():
    """历史数据获取示例"""
    print("\n2. 获取历史数据")
    print("-" * 50)
    
    # 使用5年配置
    provider = TushareDataProvider(FIVE_YEAR_CONFIG)
    
    # 测试股票代码（减少数量以加快测试）
    test_codes = ['000001.SZ']
    
    # 获取最近1个月的数据进行测试
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
    
    request = DataRequest(
        ts_codes=test_codes,
        start_date=start_date,
        end_date=end_date,
        include_basic=True,
        include_technical=True,
        include_macro=True,
        include_news=False,
        include_fund_flow=True
    )
    
    response = provider.get_comprehensive_data(request)
    
    if response.success:
        print(f"成功获取 {response.data_count} 条历史数据")
        
        # 转换为DataFrame
        df = response.to_dataframe()
        if df is not None:
            print(f"DataFrame形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            
            # 显示统计信息
            if response.data:
                dates = [item.trade_date for item in response.data]
                print(f"日期范围: {min(dates)} 到 {max(dates)}")
                
                # 计算一些基本统计
                closes = [item.market_data.close for item in response.data]
                print(f"收盘价范围: {min(closes):.2f} - {max(closes):.2f}")
                
                # 技术指标统计
                rsi_values = [item.technical_indicators.rsi for item in response.data if item.technical_indicators.rsi is not None]
                if rsi_values:
                    print(f"RSI范围: {min(rsi_values):.2f} - {max(rsi_values):.2f}")
    else:
        print(f"获取历史数据失败: {response.error_message}")


def example_custom_request():
    """自定义请求示例"""
    print("\n3. 自定义数据请求")
    print("-" * 50)
    
    provider = TushareDataProvider()
    
    # 自定义请求 - 只获取基础数据和技术指标
    request = DataRequest(
        ts_codes=['000001.SZ', '600000.SH'],
        start_date='20240101',
        end_date='20240131',
        include_basic=True,
        include_technical=True,
        include_macro=False,  # 不获取宏观数据
        include_news=False,   # 不获取新闻数据
        include_fund_flow=False  # 不获取资金流向
    )
    
    response = provider.get_comprehensive_data(request)
    
    if response.success:
        print(f"成功获取 {response.data_count} 条自定义数据")
        
        # 分析数据结构
        if response.data:
            sample_data = response.data[0]
            print(f"市场数据字段: {len(sample_data.market_data.__dict__)} 个")
            print(f"技术指标字段: {len(sample_data.technical_indicators.__dict__)} 个")
            
            # 检查哪些字段有数据
            tech_fields_with_data = []
            for field, value in sample_data.technical_indicators.__dict__.items():
                if value is not None and field not in ['ts_code', 'trade_date']:
                    tech_fields_with_data.append(field)
            
            print(f"有数据的技术指标: {tech_fields_with_data}")
    else:
        print(f"获取自定义数据失败: {response.error_message}")


def example_feature_vector():
    """特征向量示例"""
    print("\n4. 特征向量转换示例")
    print("-" * 50)
    
    provider = TushareDataProvider()
    
    # 获取少量数据进行特征向量测试
    request = DataRequest(
        ts_codes=['000001.SZ'],
        start_date='20240120',
        end_date='20240125',
        include_basic=True,
        include_technical=True,
        include_macro=True,
        include_news=True,
        include_fund_flow=True
    )
    
    response = provider.get_comprehensive_data(request)
    
    if response.success and response.data:
        print(f"获取到 {len(response.data)} 条数据")
        
        # 转换所有数据为特征向量
        feature_matrix = []
        for data_item in response.data:
            feature_vector = data_item.to_feature_vector()
            feature_matrix.append(feature_vector)
        
        import numpy as np
        feature_matrix = np.array(feature_matrix)
        
        print(f"特征矩阵形状: {feature_matrix.shape}")
        print(f"特征统计:")
        print(f"  均值: {np.mean(feature_matrix, axis=0)[:10]}")  # 前10个特征的均值
        print(f"  标准差: {np.std(feature_matrix, axis=0)[:10]}")   # 前10个特征的标准差
        
        # 检查缺失值
        nan_count = np.isnan(feature_matrix).sum()
        print(f"缺失值数量: {nan_count}")
        
        # 特征名称映射（前10个）
        feature_names = [
            'open', 'high', 'low', 'close', 'vol', 'amount',  # 基础数据
            'ma5', 'ma10', 'ma20', 'ma60'  # 技术指标开始
        ]
        
        print(f"前10个特征及其值:")
        for i, name in enumerate(feature_names):
            print(f"  {name}: {feature_matrix[0][i]:.4f}")
    else:
        print("获取特征向量数据失败")


def example_cache_usage():
    """缓存使用示例"""
    print("\n5. 缓存使用示例")
    print("-" * 50)
    
    provider = TushareDataProvider()
    
    # 第一次请求
    print("第一次请求...")
    start_time = datetime.now()
    response1 = provider.get_realtime_data(['000001.SZ'])
    time1 = (datetime.now() - start_time).total_seconds()
    print(f"第一次请求耗时: {time1:.2f} 秒")
    
    # 第二次请求（应该使用缓存）
    print("第二次请求...")
    start_time = datetime.now()
    response2 = provider.get_realtime_data(['000001.SZ'])
    time2 = (datetime.now() - start_time).total_seconds()
    print(f"第二次请求耗时: {time2:.2f} 秒")
    
    # 显示缓存信息
    cache_info = provider.get_cache_info()
    print(f"缓存大小: {cache_info['cache_size']}")
    
    # 清空缓存
    provider.clear_cache()
    print("缓存已清空")
    
    cache_info = provider.get_cache_info()
    print(f"清空后缓存大小: {cache_info['cache_size']}")


if __name__ == "__main__":
    try:
        # 运行所有示例
        example_basic_usage()
        example_historical_data()
        example_custom_request()
        example_feature_vector()
        example_cache_usage()
        
        print("\n=== 所有示例运行完成 ===")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        import traceback
        traceback.print_exc()