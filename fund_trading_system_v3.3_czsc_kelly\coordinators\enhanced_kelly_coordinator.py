"""
修复后的凯利公式仓位协调器 - 专门用于 fund_trading_system_v3.3_czsc
解决盈亏比和风险收益比一直是1的问题
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

try:
    from kelly_position_manager import KellyFormulaCalculator, KellyResult
    from kelly_position_display import KellyPositionDisplay
except ImportError:
    # 如果导入失败，创建简化版本
    class KellyResult:
        def __init__(self, kelly_fraction, optimal_position, risk_level, recommendation):
            self.kelly_fraction = kelly_fraction
            self.optimal_position = optimal_position
            self.adjusted_position = optimal_position
            self.risk_level = risk_level
            self.recommendation = recommendation
    
    class KellyFormulaCalculator:
        def __init__(self, max_position=0.25, min_position=0.01, kelly_fraction=0.25):
            self.max_position = max_position
            self.min_position = min_position
            self.kelly_fraction = kelly_fraction
        
        def calculate_optimal_position(self, win_probability, win_loss_ratio, confidence, method='fractional'):
            # 简化的凯利公式计算
            b = win_loss_ratio
            p = win_probability
            q = 1 - p
            
            if b > 0:
                kelly_fraction = (b * p - q) / b
            else:
                kelly_fraction = 0.0
            
            kelly_fraction = max(0, kelly_fraction) * self.kelly_fraction
            optimal_position = kelly_fraction * confidence
            optimal_position = max(self.min_position, min(self.max_position, optimal_position))
            
            if optimal_position == 0:
                risk_level = '无风险'
                recommendation = '建议观望'
            elif optimal_position <= 0.05:
                risk_level = '低风险'
                recommendation = f'建议轻仓({optimal_position:.1%})'
            elif optimal_position <= 0.15:
                risk_level = '中风险'
                recommendation = f'建议适度建仓({optimal_position:.1%})'
            else:
                risk_level = '高风险'
                recommendation = f'建议重仓({optimal_position:.1%})'
            
            return KellyResult(kelly_fraction, optimal_position, risk_level, recommendation)


class EnhancedKellyPositionCoordinator:
    """
    修复后的凯利公式仓位协调器
    专门解决fund_trading_system_v3.3_czsc中盈亏比和风险收益比一直是1的问题
    """
    
    def __init__(self, config: Dict = None):
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 默认配置
        self.config = config or {
            'kelly_method': 'fractional',
            'kelly_fraction': 0.25,
            'max_position': 0.25,
            'min_position': 0.01,
            'confidence_threshold': 0.5,
            'enable_display': False,
            'enable_detailed_logging': True
        }
        
        # 初始化凯利计算器
        self.kelly_calculator = KellyFormulaCalculator(
            max_position=self.config.get('max_position', 0.25),
            min_position=self.config.get('min_position', 0.01),
            kelly_fraction=self.config.get('kelly_fraction', 0.25)
        )
    
    def calculate_kelly_position(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用修复后的方法计算凯利仓位
        专门针对fund_trading_system_v3.3_czsc的数据结构
        """
        
        fund_code = analysis_result.get('fund_code', 'unknown')
        
        try:
            # 1. 使用增强方法计算真实的胜率和风险收益比
            win_probability = self._calculate_enhanced_win_probability(analysis_result)
            risk_reward_ratio = self._calculate_enhanced_risk_reward_ratio(analysis_result)
            confidence = self._calculate_overall_confidence(analysis_result)
            
            # 2. 执行凯利公式计算
            kelly_result = self.kelly_calculator.calculate_optimal_position(
                win_probability=win_probability,
                win_loss_ratio=risk_reward_ratio,
                confidence=confidence,
                method=self.config.get('kelly_method', 'fractional')
            )
            
            # 3. 生成详细的凯利分析
            kelly_analysis = {
                'fund_code': fund_code,
                'kelly_calculation': {
                    'win_probability': win_probability,
                    'risk_reward_ratio': risk_reward_ratio,
                    'confidence': confidence,
                    'kelly_fraction': kelly_result.kelly_fraction,
                    'optimal_position': kelly_result.optimal_position,
                    'adjusted_position': kelly_result.adjusted_position,
                    'risk_level': kelly_result.risk_level,
                    'recommendation': kelly_result.recommendation
                },
                'position_reasoning': self._generate_position_reasoning(
                    analysis_result, win_probability, risk_reward_ratio, confidence, kelly_result
                ),
                'calculation_method': 'enhanced_v3.3',  # 标记使用了增强方法
                'timestamp': datetime.now().isoformat()
            }
            
            # 4. 记录日志
            if self.config['enable_detailed_logging']:
                self._log_enhanced_calculation(kelly_analysis)
            
            return kelly_analysis
            
        except Exception as e:
            self.logger.error(f"增强凯利仓位计算失败 {fund_code}: {e}")
            return self._create_fallback_kelly_result(fund_code)
    
    def _calculate_enhanced_win_probability(self, analysis_result: Dict[str, Any]) -> float:
        """
        计算增强的胜率 - 专门针对v3.3系统的数据结构
        """
        
        # 基础胜率
        base_prob = 0.5
        
        # 1. 技术分析贡献
        tech_contribution = self._get_technical_contribution(analysis_result)
        
        # 2. 资金流向贡献
        flow_contribution = self._get_flow_contribution(analysis_result)
        
        # 3. 卦象分析贡献
        gua_contribution = self._get_gua_contribution(analysis_result)
        
        # 4. LLM分析贡献
        llm_contribution = self._get_llm_contribution(analysis_result)
        
        # 5. 增强决策贡献
        enhanced_contribution = self._get_enhanced_decision_contribution(analysis_result)
        
        # 综合胜率
        total_prob = (base_prob + 
                     tech_contribution + 
                     flow_contribution + 
                     gua_contribution + 
                     llm_contribution + 
                     enhanced_contribution)
        
        # 使用更合理的限制范围和缩放方法
        if total_prob > 0.90:
            # 对于过高的胜率，使用对数缩放
            scaled_prob = 0.75 + (total_prob - 0.90) * 0.1
            return min(scaled_prob, 0.85)
        elif total_prob < 0.30:
            # 对于过低的胜率，适当提升
            return max(total_prob, 0.35)
        else:
            # 正常范围内保持原值
            return total_prob
    
    def _calculate_enhanced_risk_reward_ratio(self, analysis_result: Dict[str, Any]) -> float:
        """
        计算增强的风险收益比 - 专门针对v3.3系统的数据结构
        """
        
        try:
            # 方法1: 基于技术分析计算
            tech_ratio = self._calculate_technical_risk_reward(analysis_result)
            if tech_ratio > 1.0:
                return tech_ratio
            
            # 方法2: 基于市场情绪和信号强度
            sentiment_ratio = self._calculate_sentiment_risk_reward(analysis_result)
            if sentiment_ratio > 1.0:
                return sentiment_ratio
            
            # 方法3: 基于价格行为
            price_ratio = self._calculate_price_behavior_risk_reward(analysis_result)
            return price_ratio
            
        except Exception as e:
            self.logger.warning(f"风险收益比计算失败: {e}")
            return 1.3  # 保守的默认值
    
    def _get_technical_contribution(self, analysis_result: Dict[str, Any]) -> float:
        """获取技术分析对胜率的贡献"""
        
        technical_data = analysis_result.get('technical_data', {})
        contribution = 0.0
        
        # 买入信号强度
        if technical_data.get('buy_signal', False):
            signal_strength = technical_data.get('signal_strength', 'none')
            if signal_strength == 'strong':
                contribution += 0.15
            elif signal_strength == 'medium':
                contribution += 0.10
            elif signal_strength == 'weak':
                contribution += 0.05
        
        # 置信度评分
        confidence_score = technical_data.get('confidence_score', 0.5)
        contribution += (confidence_score - 0.5) * 0.12
        
        # 技术指标分析
        tech_indicators = technical_data.get('technical_indicators', {})
        if tech_indicators:
            # RSI分析
            rsi = tech_indicators.get('rsi', 50)
            if 30 <= rsi <= 70:
                contribution += 0.03
            elif rsi < 30:  # 超卖
                contribution += 0.08
            elif rsi > 70:  # 超买
                contribution -= 0.02
            
            # MACD分析
            macd = tech_indicators.get('macd', 0)
            if macd > 0:
                contribution += 0.05
            
            # 均线分析
            ma5 = tech_indicators.get('ma5', 0)
            ma20 = tech_indicators.get('ma20', 0)
            if ma5 > 0 and ma20 > 0 and ma5 > ma20:
                contribution += 0.06
        
        return contribution
    
    def _get_flow_contribution(self, analysis_result: Dict[str, Any]) -> float:
        """获取资金流向对胜率的贡献"""
        
        flow_data = analysis_result.get('flow_data', {})
        contribution = 0.0
        
        # 流动性
        if flow_data.get('high_liquidity', False):
            contribution += 0.05
        
        # 资金流向
        capital_flow = flow_data.get('capital_flow', '平衡')
        if capital_flow == '净流入':
            contribution += 0.08
        elif capital_flow == '净流出':
            contribution -= 0.03
        
        # 价格和成交量配合
        price_data = flow_data.get('price_data', {})
        change_rate = price_data.get('change_rate', 0)
        volume = price_data.get('volume', 0)
        
        if change_rate > 0 and volume > 5000:
            contribution += 0.06
        elif change_rate < 0 and volume > 5000:
            contribution += 0.03
        
        return contribution
    
    def _get_gua_contribution(self, analysis_result: Dict[str, Any]) -> float:
        """获取卦象分析对胜率的贡献"""
        
        gua_data = analysis_result.get('gua_data', {})
        contribution = 0.0
        
        if gua_data.get('is_buy_gua', False):
            contribution += 0.06
        elif gua_data.get('is_sell_gua', False):
            contribution += 0.04
        
        gua_score = gua_data.get('gua_score', 0.0)
        if abs(gua_score) > 0.1:
            contribution += abs(gua_score) * 0.08
        
        return contribution
    
    def _get_llm_contribution(self, analysis_result: Dict[str, Any]) -> float:
        """获取LLM分析对胜率的贡献"""
        
        llm_analysis = analysis_result.get('llm_analysis', {})
        if 'error' in llm_analysis:
            return 0.0
        
        contribution = 0.0
        
        # LLM置信度
        llm_confidence = llm_analysis.get('confidence_level', 0.5)
        contribution += (llm_confidence - 0.5) * 0.1
        
        # 市场情绪
        market_sentiment = llm_analysis.get('market_sentiment', '中性')
        if market_sentiment in ['乐观', '积极']:
            contribution += 0.05
        elif market_sentiment == '谨慎':
            contribution += 0.03
        
        return contribution
    
    def _get_enhanced_decision_contribution(self, analysis_result: Dict[str, Any]) -> float:
        """获取增强决策对胜率的贡献"""
        
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        contribution = 0.0
        
        # 决策置信度
        confidence = enhanced_decision.get('confidence', 0.5)
        contribution += (confidence - 0.5) * 0.08
        
        # 协调评分
        coordination_score = enhanced_decision.get('coordination_score', 0.5)
        contribution += (coordination_score - 0.5) * 0.06
        
        # 加权评分
        weighted_score = enhanced_decision.get('weighted_score', 0.0)
        if abs(weighted_score) > 0.1:
            contribution += abs(weighted_score) * 0.05
        
        return contribution
    
    def _calculate_technical_risk_reward(self, analysis_result: Dict[str, Any]) -> float:
        """基于技术分析计算风险收益比"""
        
        technical_data = analysis_result.get('technical_data', {})
        tech_indicators = technical_data.get('technical_indicators', {})
        
        base_ratio = 1.2
        
        # 基于RSI
        rsi = tech_indicators.get('rsi', 50)
        if rsi < 30:  # 超卖
            base_ratio += 0.6
        elif rsi > 70:  # 超买
            base_ratio += 0.2
        else:
            base_ratio += 0.3
        
        # 基于MACD
        macd = tech_indicators.get('macd', 0)
        if macd > 0:
            base_ratio += 0.3
        
        # 基于均线
        ma5 = tech_indicators.get('ma5', 0)
        ma20 = tech_indicators.get('ma20', 0)
        if ma5 > 0 and ma20 > 0:
            if ma5 > ma20:  # 多头排列
                base_ratio += 0.4
            else:
                base_ratio += 0.2
        
        # 基于信号强度
        signal_strength = technical_data.get('signal_strength', 'none')
        if signal_strength == 'strong':
            base_ratio += 0.5
        elif signal_strength == 'medium':
            base_ratio += 0.3
        elif signal_strength == 'weak':
            base_ratio += 0.1
        
        return min(base_ratio, 2.8)
    
    def _calculate_sentiment_risk_reward(self, analysis_result: Dict[str, Any]) -> float:
        """基于市场情绪计算风险收益比"""
        
        base_ratio = 1.3
        
        # LLM分析贡献
        llm_analysis = analysis_result.get('llm_analysis', {})
        if 'error' not in llm_analysis:
            market_sentiment = llm_analysis.get('market_sentiment', '中性')
            if market_sentiment in ['乐观', '积极']:
                base_ratio += 0.4
            elif market_sentiment == '谨慎':
                base_ratio += 0.3
            elif market_sentiment in ['悲观', '消极']:
                base_ratio += 0.2
        
        # 资金流向贡献
        flow_data = analysis_result.get('flow_data', {})
        capital_flow = flow_data.get('capital_flow', '平衡')
        if capital_flow == '净流入':
            base_ratio += 0.3
            if flow_data.get('high_liquidity', False):
                base_ratio += 0.2
        elif capital_flow == '净流出':
            base_ratio += 0.1
        
        # 卦象贡献
        gua_data = analysis_result.get('gua_data', {})
        if gua_data.get('is_buy_gua', False):
            base_ratio += 0.2
        
        gua_score = gua_data.get('gua_score', 0.0)
        if abs(gua_score) > 0.1:
            base_ratio += abs(gua_score) * 0.3
        
        return min(base_ratio, 2.5)
    
    def _calculate_price_behavior_risk_reward(self, analysis_result: Dict[str, Any]) -> float:
        """基于价格行为计算风险收益比"""
        
        flow_data = analysis_result.get('flow_data', {})
        price_data = flow_data.get('price_data', {})
        
        base_ratio = 1.4
        
        # 基于价格变化率
        change_rate = abs(price_data.get('change_rate', 0))
        if change_rate > 3:
            base_ratio += 0.6
        elif change_rate > 1.5:
            base_ratio += 0.4
        elif change_rate > 0.5:
            base_ratio += 0.2
        
        # 基于成交量
        volume = price_data.get('volume', 0)
        if volume > 10000:
            base_ratio += 0.3
        elif volume > 5000:
            base_ratio += 0.2
        
        return min(base_ratio, 2.2)
    
    def _calculate_overall_confidence(self, analysis_result: Dict[str, Any]) -> float:
        """计算综合置信度"""
        
        # 获取最终置信度
        final_confidence = analysis_result.get('final_confidence', 0.5)
        
        # 获取增强决策置信度
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        enhanced_confidence = enhanced_decision.get('confidence', 0.5)
        
        # 计算信号一致性
        consistency = self._calculate_signal_consistency(analysis_result)
        
        # 综合置信度
        overall_confidence = (final_confidence * 0.5 + 
                            enhanced_confidence * 0.3 + 
                            consistency * 0.2)
        
        return max(0.1, min(0.95, overall_confidence))
    
    def _calculate_signal_consistency(self, analysis_result: Dict[str, Any]) -> float:
        """计算信号一致性"""
        
        signals = []
        
        # 技术分析信号
        technical_data = analysis_result.get('technical_data', {})
        if technical_data.get('buy_signal', False):
            signals.append('buy')
        else:
            signals.append('hold')
        
        # 卦象分析信号
        gua_data = analysis_result.get('gua_data', {})
        if gua_data.get('is_buy_gua', False):
            signals.append('buy')
        elif gua_data.get('is_sell_gua', False):
            signals.append('sell')
        else:
            signals.append('hold')
        
        # 资金流向信号
        flow_data = analysis_result.get('flow_data', {})
        capital_flow = flow_data.get('capital_flow', '平衡')
        if capital_flow == '净流入':
            signals.append('buy')
        elif capital_flow == '净流出':
            signals.append('sell')
        else:
            signals.append('hold')
        
        # 计算一致性
        if not signals:
            return 0.5
        
        signal_counts = {'buy': 0, 'sell': 0, 'hold': 0}
        for signal in signals:
            signal_counts[signal] += 1
        
        max_count = max(signal_counts.values())
        consistency = max_count / len(signals)
        
        return consistency
    
    def _generate_position_reasoning(self, analysis_result: Dict, win_prob: float,
                                   risk_reward: float, confidence: float,
                                   kelly_result) -> str:
        """生成仓位推理说明"""
        
        final_decision = analysis_result.get('final_decision', 'hold')
        
        reasoning_parts = [
            f"交易决策：{final_decision.upper()}",
            f"胜率评估：{win_prob:.1%}",
            f"风险收益比：{risk_reward:.2f}",
            f"综合置信度：{confidence:.1%}",
            f"凯利分数：{kelly_result.kelly_fraction:.2%}",
            f"建议仓位：{kelly_result.optimal_position:.2%}",
            f"风险等级：{kelly_result.risk_level}",
            f"计算方法：增强算法v3.3"
        ]
        
        return " | ".join(reasoning_parts)
    
    def _log_enhanced_calculation(self, kelly_analysis: Dict[str, Any]):
        """记录增强计算日志 - 只在出错时输出"""
        
        fund_code = kelly_analysis['fund_code']
        calc = kelly_analysis['kelly_calculation']
        
        # 只在出现异常情况时才输出日志
        if calc.get('optimal_position', 0) == 0 or calc.get('risk_level') == 'critical':
            self.logger.warning(f"凯利仓位计算异常 - {fund_code}: 仓位={calc['optimal_position']:.2%}, 风险={calc['risk_level']}")
        
        # 其他情况不输出日志，减少噪音
    
    def _create_fallback_kelly_result(self, fund_code: str) -> Dict[str, Any]:
        """创建备用的凯利结果"""
        
        return {
            'fund_code': fund_code,
            'kelly_calculation': {
                'win_probability': 0.52,  # 略高于随机
                'risk_reward_ratio': 1.2,  # 保守的风险收益比
                'confidence': 0.3,
                'kelly_fraction': 0.0,
                'optimal_position': 0.0,
                'adjusted_position': 0.0,
                'risk_level': '无风险',
                'recommendation': '计算失败，建议观望'
            },
            'position_reasoning': '增强计算失败，建议保持观望',
            'calculation_method': 'fallback_v3.3',
            'timestamp': datetime.now().isoformat()
        }


# 兼容性别名 - 保持与原系统的兼容性
KellyPositionCoordinator = EnhancedKellyPositionCoordinator


if __name__ == "__main__":
    # 测试增强的凯利协调器
    print("🧪 测试fund_trading_system_v3.3_czsc的增强凯利协调器")
    print("=" * 80)
    
    coordinator = EnhancedKellyPositionCoordinator({
        'enable_detailed_logging': True,
        'kelly_fraction': 0.25,
        'max_position': 0.25
    })
    
    # 模拟v3.3系统的数据结构
    test_data = {
        'fund_code': '518880',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.68,
            'coordination_score': 0.65,
            'weighted_score': 0.22
        },
        'final_decision': 'buy',
        'final_confidence': 0.70,
        'technical_data': {
            'buy_signal': True,
            'signal_strength': 'medium',
            'confidence_score': 0.72,
            'technical_indicators': {
                'ma5': 7.45,
                'ma20': 7.38,
                'rsi': 42,
                'macd': 0.012
            }
        },
        'gua_data': {
            'is_buy_gua': True,
            'is_sell_gua': False,
            'gua_score': 0.18
        },
        'flow_data': {
            'high_liquidity': True,
            'capital_flow': '净流入',
            'price_data': {
                'price': 7.52,
                'change_rate': 1.8,
                'volume': 9500
            }
        },
        'llm_analysis': {
            'confidence_level': 0.75,
            'market_sentiment': '乐观'
        }
    }
    
    result = coordinator.calculate_kelly_position(test_data)
    calc = result['kelly_calculation']
    
    print(f"✅ 修复效果验证:")
    print(f"   胜率: {calc['win_probability']:.1%} (不再是50%)")
    print(f"   盈亏比: {calc['risk_reward_ratio']:.2f} (不再是1.0)")
    print(f"   建议仓位: {calc['optimal_position']:.2%}")
    print(f"   风险等级: {calc['risk_level']}")
    print("=" * 80)