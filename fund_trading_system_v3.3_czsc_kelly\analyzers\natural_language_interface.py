"""
自然语言查询接口
允许用户使用自然语言查询市场状态和投资建议
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
import json
import re

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .llm_market_analyzer import LLMMarketAnalyzer


class NaturalLanguageInterface:
    """
    @class NaturalLanguageInterface
    @brief 自然语言查询接口
    @details 处理用户的自然语言查询，返回相应的市场分析和投资建议
    """
    
    def __init__(self, llm_analyzer: LLMMarketAnalyzer = None):
        self.name = "NaturalLanguageInterface"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 使用传入的LLM分析器或创建新的
        self.llm_analyzer = llm_analyzer or LLMMarketAnalyzer()
        
        # 预定义查询模式
        self.query_patterns = {
            'market_status': [
                r'市场.*状态', r'当前.*市场', r'市场.*情况', r'行情.*如何',
                r'market.*status', r'current.*market'
            ],
            'investment_advice': [
                r'投资.*建议', r'买入.*建议', r'卖出.*建议', r'操作.*建议',
                r'investment.*advice', r'buy.*recommendation', r'sell.*recommendation'
            ],
            'risk_analysis': [
                r'风险.*分析', r'风险.*评估', r'有.*风险', r'安全.*吗',
                r'risk.*analysis', r'risk.*assessment'
            ],
            'fund_performance': [
                r'基金.*表现', r'基金.*收益', r'基金.*怎么样', r'.*基金.*如何',
                r'fund.*performance', r'fund.*return'
            ],
            'technical_analysis': [
                r'技术.*分析', r'指标.*分析', r'趋势.*分析',
                r'technical.*analysis', r'indicator.*analysis'
            ]
        }
    
    def process_query(self, query: str, context_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        @brief 处理自然语言查询
        @param query: 用户查询文本
        @param context_data: 上下文数据（市场数据、基金信息等）
        @return: 查询结果
        """
        try:
            # 识别查询类型
            query_type = self._identify_query_type(query)
            
            # 提取查询中的关键信息
            extracted_info = self._extract_query_info(query)
            
            # 根据查询类型生成响应
            response = self._generate_response(query, query_type, extracted_info, context_data)
            
            result = {
                'query': query,
                'query_type': query_type,
                'extracted_info': extracted_info,
                'response': response,
                'timestamp': datetime.now().isoformat(),
                'success': True
            }
            
            self.logger.info(f"自然语言查询处理成功: {query_type}")
            return result
            
        except Exception as e:
            self.logger.error(f"自然语言查询处理失败: {str(e)}")
            return {
                'query': query,
                'error': str(e),
                'response': "抱歉，我无法理解您的查询，请尝试重新表述。",
                'timestamp': datetime.now().isoformat(),
                'success': False
            }
    
    def _identify_query_type(self, query: str) -> str:
        """识别查询类型"""
        query_lower = query.lower()
        
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return query_type
        
        return 'general_inquiry'
    
    def _extract_query_info(self, query: str) -> Dict[str, Any]:
        """提取查询中的关键信息"""
        extracted = {
            'fund_codes': [],
            'time_period': None,
            'specific_metrics': [],
            'sentiment': 'neutral'
        }
        
        # 提取基金代码（6位数字）
        fund_code_pattern = r'\b\d{6}\b'
        fund_codes = re.findall(fund_code_pattern, query)
        extracted['fund_codes'] = fund_codes
        
        # 提取时间相关信息
        time_patterns = [
            r'今天', r'昨天', r'本周', r'上周', r'本月', r'上月',
            r'近.*天', r'近.*周', r'近.*月', r'最近'
        ]
        for pattern in time_patterns:
            if re.search(pattern, query):
                extracted['time_period'] = re.search(pattern, query).group()
                break
        
        # 提取具体指标
        metric_patterns = {
            '收益率': r'收益|回报|盈利',
            '风险': r'风险|波动|亏损',
            '趋势': r'趋势|方向|走势',
            '流动性': r'流动性|成交量'
        }
        
        for metric, pattern in metric_patterns.items():
            if re.search(pattern, query):
                extracted['specific_metrics'].append(metric)
        
        # 简单情感分析
        positive_words = ['好', '优秀', '不错', '推荐', '买入']
        negative_words = ['差', '不好', '风险', '卖出', '担心']
        
        positive_count = sum(1 for word in positive_words if word in query)
        negative_count = sum(1 for word in negative_words if word in query)
        
        if positive_count > negative_count:
            extracted['sentiment'] = 'positive'
        elif negative_count > positive_count:
            extracted['sentiment'] = 'negative'
        
        return extracted
    
    def _generate_response(self, query: str, query_type: str, 
                          extracted_info: Dict[str, Any], 
                          context_data: Dict[str, Any] = None) -> str:
        """生成响应"""
        if not self.llm_analyzer.client:
            return self._generate_fallback_response(query_type, extracted_info)
        
        try:
            # 构建上下文信息
            context_prompt = self._build_context_prompt(context_data, extracted_info)
            
            # 构建查询提示词
            system_prompt = self._get_query_system_prompt(query_type)
            user_prompt = f"""用户查询: {query}

查询类型: {query_type}
提取信息: {json.dumps(extracted_info, ensure_ascii=False)}

{context_prompt}

请基于以上信息，用自然、友好的语言回答用户的查询。要求：
1. 回答要准确、有用
2. 语言要通俗易懂
3. 如果涉及投资建议，要包含风险提示
4. 回答长度控制在200字以内
"""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            completion = self.llm_analyzer.client.chat.completions.create(
                model="kimi-k2-0711-preview",
                messages=messages,
                temperature=0.4,
            )
            
            response = completion.choices[0].message.content
            return response
            
        except Exception as e:
            self.logger.error(f"LLM响应生成失败: {str(e)}")
            return self._generate_fallback_response(query_type, extracted_info)
    
    def _build_context_prompt(self, context_data: Dict[str, Any], 
                             extracted_info: Dict[str, Any]) -> str:
        """构建上下文提示词"""
        if not context_data:
            return "当前没有可用的市场数据。"
        
        context_prompt = "当前市场数据:\n"
        
        # 添加基金信息
        if extracted_info.get('fund_codes') and 'fund_data' in context_data:
            for fund_code in extracted_info['fund_codes']:
                if fund_code in context_data['fund_data']:
                    fund_info = context_data['fund_data'][fund_code]
                    context_prompt += f"基金 {fund_code}: 当前净值 {fund_info.get('nav', 'N/A')}, "
                    context_prompt += f"涨跌幅 {fund_info.get('change_pct', 'N/A')}%\n"
        
        # 添加市场分析结果
        if 'market_analysis' in context_data:
            analysis = context_data['market_analysis']
            context_prompt += f"市场情绪: {analysis.get('market_sentiment', '中性')}\n"
            context_prompt += f"主要驱动因素: {', '.join(analysis.get('market_drivers', []))}\n"
            context_prompt += f"风险点: {', '.join(analysis.get('risk_points', []))}\n"
        
        # 添加技术指标
        if 'technical_indicators' in context_data:
            indicators = context_data['technical_indicators']
            context_prompt += f"技术指标: {json.dumps(indicators, ensure_ascii=False)}\n"
        
        return context_prompt
    
    def _get_query_system_prompt(self, query_type: str) -> str:
        """获取查询类型对应的系统提示词"""
        prompts = {
            'market_status': "你是一个专业的市场分析师，擅长解读市场状态和趋势。",
            'investment_advice': "你是一个专业的投资顾问，提供客观的投资建议。注意：所有建议都要包含风险提示。",
            'risk_analysis': "你是一个风险管理专家，专注于识别和评估投资风险。",
            'fund_performance': "你是一个基金分析师，擅长评估基金表现和特点。",
            'technical_analysis': "你是一个技术分析专家，擅长解读技术指标和图表模式。",
            'general_inquiry': "你是一个专业的金融助手，能够回答各种投资相关问题。"
        }
        
        return prompts.get(query_type, prompts['general_inquiry'])
    
    def _generate_fallback_response(self, query_type: str, 
                                   extracted_info: Dict[str, Any]) -> str:
        """生成兜底响应"""
        responses = {
            'market_status': "当前市场数据显示，建议关注主要技术指标的变化。由于AI服务暂时不可用，建议查看详细的技术分析报告。",
            'investment_advice': "基于当前数据，建议谨慎操作。请注意：投资有风险，决策需谨慎。建议咨询专业投资顾问。",
            'risk_analysis': "风险评估需要综合考虑多个因素。当前建议保持谨慎态度，密切关注市场变化。",
            'fund_performance': "基金表现需要结合历史数据和市场环境综合评估。建议查看基金的详细报告。",
            'technical_analysis': "技术分析需要结合多个指标综合判断。建议查看完整的技术分析图表。",
            'general_inquiry': "感谢您的查询。由于AI服务暂时不可用，建议查看相关的分析报告或咨询专业人士。"
        }
        
        base_response = responses.get(query_type, responses['general_inquiry'])
        
        # 根据提取的信息添加个性化内容
        if extracted_info.get('fund_codes'):
            fund_codes = ', '.join(extracted_info['fund_codes'])
            base_response += f" 您询问的基金代码: {fund_codes}。"
        
        return base_response
    
    def get_suggested_queries(self) -> List[str]:
        """获取建议的查询示例"""
        return [
            "当前市场状态如何？",
            "基金000001的表现怎么样？",
            "现在适合买入吗？",
            "有什么投资风险需要注意？",
            "技术指标显示什么信号？",
            "最近一周的市场趋势如何？",
            "这只基金的风险大吗？",
            "现在应该卖出还是持有？"
        ]