"""
测试凯利公式修复在整个系统中的集成效果
验证盈亏比和风险收益比不再是1的问题是否解决
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from coordinators import KellyPositionCoordinator
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3


def test_kelly_fix_in_system():
    """测试凯利修复在整个系统中的效果"""
    
    print("🧪 测试凯利公式修复在fund_trading_system_v3.3_czsc中的集成效果")
    print("=" * 80)
    
    # 测试1: 直接测试凯利协调器
    print("📊 测试1: 直接测试凯利协调器")
    print("-" * 50)
    
    kelly_coordinator = KellyPositionCoordinator({
        'enable_detailed_logging': True,
        'kelly_fraction': 0.25,
        'max_position': 0.25
    })
    
    # 模拟系统数据结构
    test_analysis_result = {
        'fund_code': '518880',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.72,
            'coordination_score': 0.68,
            'weighted_score': 0.25
        },
        'final_decision': 'buy',
        'final_confidence': 0.74,
        'technical_data': {
            'buy_signal': True,
            'signal_strength': 'strong',
            'confidence_score': 0.76,
            'technical_indicators': {
                'ma5': 7.48,
                'ma20': 7.35,
                'rsi': 38,  # 偏超卖，有反弹机会
                'macd': 0.018
            }
        },
        'gua_data': {
            'is_buy_gua': True,
            'is_sell_gua': False,
            'gua_score': 0.22,
            'main_gua': '需'
        },
        'flow_data': {
            'high_liquidity': True,
            'capital_flow': '净流入',
            'price_data': {
                'price': 7.55,
                'change_rate': 2.3,  # 较大涨幅
                'volume': 15000     # 高成交量
            }
        },
        'llm_analysis': {
            'confidence_level': 0.78,
            'market_sentiment': '乐观',
            'strategy_suggestion': '技术指标显示强烈买入信号，MA5大幅上穿MA20，MACD金叉扩大，RSI从超卖区域快速回升，资金大幅净流入，建议积极做多。'
        }
    }
    
    try:
        kelly_result = kelly_coordinator.calculate_kelly_position(test_analysis_result)
        calc = kelly_result['kelly_calculation']
        
        print(f"✅ 凯利计算结果:")
        print(f"   基金代码: {kelly_result['fund_code']}")
        print(f"   胜率: {calc['win_probability']:.1%}")
        print(f"   盈亏比: {calc['risk_reward_ratio']:.2f}")
        print(f"   置信度: {calc['confidence']:.1%}")
        print(f"   凯利分数: {calc['kelly_fraction']:.2%}")
        print(f"   建议仓位: {calc['optimal_position']:.2%}")
        print(f"   风险等级: {calc['risk_level']}")
        
        # 验证修复效果
        win_prob_fixed = calc['win_probability'] != 0.5
        risk_reward_fixed = calc['risk_reward_ratio'] != 1.0
        position_reasonable = calc['optimal_position'] > 0
        
        print(f"\n🔍 修复效果验证:")
        print(f"   {'✅' if win_prob_fixed else '❌'} 胜率修复: {calc['win_probability']:.1%} {'(不再是50%)' if win_prob_fixed else '(仍是50%)'}")
        print(f"   {'✅' if risk_reward_fixed else '❌'} 盈亏比修复: {calc['risk_reward_ratio']:.2f} {'(不再是1.0)' if risk_reward_fixed else '(仍是1.0)'}")
        print(f"   {'✅' if position_reasonable else '❌'} 仓位计算: {calc['optimal_position']:.2%} {'(合理)' if position_reasonable else '(异常)'}")
        
        test1_success = win_prob_fixed and risk_reward_fixed and position_reasonable
        
    except Exception as e:
        print(f"❌ 测试1失败: {e}")
        test1_success = False
    
    # 测试2: 测试多智能体协调器中的凯利集成
    print(f"\n📊 测试2: 测试多智能体协调器中的凯利集成")
    print("-" * 50)
    
    try:
        # 初始化多智能体协调器
        multi_coordinator = MultiAgentCoordinatorV3()
        
        # 检查凯利协调器是否正确集成
        kelly_in_multi = hasattr(multi_coordinator, 'kelly_coordinator')
        kelly_type_correct = type(multi_coordinator.kelly_coordinator).__name__ == 'EnhancedKellyPositionCoordinator'
        
        print(f"   {'✅' if kelly_in_multi else '❌'} 凯利协调器集成: {'已集成' if kelly_in_multi else '未集成'}")
        print(f"   {'✅' if kelly_type_correct else '❌'} 协调器类型: {type(multi_coordinator.kelly_coordinator).__name__}")
        
        test2_success = kelly_in_multi and kelly_type_correct
        
    except Exception as e:
        print(f"❌ 测试2失败: {e}")
        test2_success = False
    
    # 测试3: 测试不同场景下的计算结果
    print(f"\n📊 测试3: 测试不同场景下的计算结果")
    print("-" * 50)
    
    test_scenarios = [
        {
            'name': '强买入场景',
            'data': {
                'fund_code': '513030',
                'final_decision': 'buy',
                'final_confidence': 0.85,
                'technical_data': {
                    'buy_signal': True,
                    'signal_strength': 'strong',
                    'confidence_score': 0.88,
                    'technical_indicators': {'rsi': 25, 'macd': 0.025}  # 超卖+强势金叉
                },
                'flow_data': {
                    'capital_flow': '净流入',
                    'high_liquidity': True,
                    'price_data': {'change_rate': 3.5, 'volume': 20000}  # 大涨+放量
                },
                'llm_analysis': {'market_sentiment': '乐观', 'confidence_level': 0.82}
            }
        },
        {
            'name': '观望场景',
            'data': {
                'fund_code': '159567',
                'final_decision': 'hold',
                'final_confidence': 0.55,
                'technical_data': {
                    'buy_signal': False,
                    'signal_strength': 'none',
                    'confidence_score': 0.52,
                    'technical_indicators': {'rsi': 55, 'macd': -0.005}  # 中性
                },
                'flow_data': {
                    'capital_flow': '平衡',
                    'high_liquidity': False,
                    'price_data': {'change_rate': 0.3, 'volume': 3000}  # 小幅波动
                },
                'llm_analysis': {'market_sentiment': '中性', 'confidence_level': 0.58}
            }
        }
    ]
    
    scenario_results = []
    
    for scenario in test_scenarios:
        try:
            result = kelly_coordinator.calculate_kelly_position(scenario['data'])
            calc = result['kelly_calculation']
            
            print(f"   📋 {scenario['name']}:")
            print(f"      胜率: {calc['win_probability']:.1%}")
            print(f"      盈亏比: {calc['risk_reward_ratio']:.2f}")
            print(f"      建议仓位: {calc['optimal_position']:.2%}")
            
            # 验证结果合理性
            reasonable = (
                calc['win_probability'] != 0.5 and 
                calc['risk_reward_ratio'] != 1.0 and
                0.35 <= calc['win_probability'] <= 0.85 and
                1.0 <= calc['risk_reward_ratio'] <= 3.0
            )
            
            scenario_results.append(reasonable)
            print(f"      {'✅ 结果合理' if reasonable else '❌ 结果异常'}")
            
        except Exception as e:
            print(f"      ❌ 计算失败: {e}")
            scenario_results.append(False)
    
    test3_success = all(scenario_results)
    
    # 汇总测试结果
    print(f"\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    all_tests_passed = test1_success and test2_success and test3_success
    
    print(f"测试1 - 凯利协调器直接测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"测试2 - 多智能体集成测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"测试3 - 多场景计算测试: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    if all_tests_passed:
        print(f"\n🎉 所有测试通过！凯利公式修复成功集成到fund_trading_system_v3.3_czsc系统中")
        print(f"💡 现在运行main.py时，盈亏比和风险收益比将不再固定为1")
        print(f"📈 系统将提供更准确的胜率评估和仓位建议")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查集成")
    
    print("=" * 80)
    
    return all_tests_passed


def test_import_compatibility():
    """测试导入兼容性"""
    
    print("\n🔧 测试导入兼容性")
    print("-" * 40)
    
    try:
        # 测试各种导入方式
        from coordinators import KellyPositionCoordinator
        from coordinators.enhanced_kelly_coordinator import EnhancedKellyPositionCoordinator
        
        # 验证别名是否正确
        alias_correct = KellyPositionCoordinator == EnhancedKellyPositionCoordinator
        
        print(f"✅ 导入测试通过")
        print(f"✅ 别名设置正确: {alias_correct}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 fund_trading_system_v3.3_czsc 凯利公式修复集成测试")
    print("🎯 目标: 验证盈亏比和风险收益比不再固定为1")
    print("=" * 80)
    
    # 测试导入兼容性
    import_success = test_import_compatibility()
    
    if import_success:
        # 测试系统集成
        integration_success = test_kelly_fix_in_system()
        
        if integration_success:
            print(f"\n🎊 恭喜！修复完全成功！")
            print(f"🚀 现在可以运行 python fund_trading_system_v3.3_czsc/main.py")
            print(f"📊 系统将显示真实的胜率和风险收益比，不再是固定值")
        else:
            print(f"\n⚠️ 集成测试失败，请检查配置")
    else:
        print(f"\n❌ 导入测试失败，请检查文件路径")