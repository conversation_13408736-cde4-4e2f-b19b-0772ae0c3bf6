"""
LLM驱动的市场分析器
基于大语言模型进行市场叙事和情绪分析
"""

import logging
import sys
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import json
from dotenv import load_dotenv
import os
load_dotenv("file.env")

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入kimi AI客户端
from pathlib import Path
from openai import OpenAI

from core.data_structures import DimensionEvaluationResult


class LLMMarketAnalyzer:
    """
    @class LLMMarketAnalyzer
    @brief LLM驱动的市场分析器
    @details 使用大语言模型分析市场叙事和情绪，提供智能决策建议
    """
    
    def __init__(self, config=None):
        self.name = "LLMMarketAnalyzer"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 导入配置管理
        try:
            from config.ai_config import get_ai_config
            self.ai_config = config or get_ai_config()
        except ImportError:
            self.logger.warning("无法导入AI配置，使用默认设置")
            self.ai_config = None
        
        # 初始化LLM客户端
        self.client = self._initialize_llm_client()
    
    def _initialize_llm_client(self):
        """初始化LLM客户端"""
        try:
            if self.ai_config:
                # 使用配置管理
                provider_config = self.ai_config.get_llm_config("moonshot")
                if provider_config and provider_config.get("enabled", False):
                    api_key = provider_config.get("api_key", "")
                    base_url = provider_config.get("base_url", "https://api.moonshot.cn/v1")
                    
                    if api_key:
                        client = OpenAI(api_key=api_key, base_url=base_url)
                        self.logger.info("LLM客户端初始化成功（使用配置管理）")
                        return client
                    else:
                        self.logger.warning("API密钥未配置")
                else:
                    self.logger.warning("Moonshot提供商未启用或配置不存在")
            
            # 兜底：尝试使用环境变量
            api_key = os.getenv("MOONSHOT_API_KEY")
            if api_key:
                client = OpenAI(
                    api_key=api_key,
                    base_url="https://api.moonshot.cn/v1"
                )
                self.logger.info("LLM客户端初始化成功（使用环境变量）")
                return client
            else:
                self.logger.warning("未找到MOONSHOT_API_KEY环境变量")
            
            return None
            
        except Exception as e:
            self.logger.error(f"LLM客户端初始化失败: {str(e)}")
            return None
    
    def analyze_market_narrative(self, market_data: Dict[str, Any], 
                               fund_code: str = None) -> Dict[str, Any]:
        """
        @brief 分析市场叙事和情绪
        @param market_data: 市场数据，包含技术指标、资金流向等
        @param fund_code: 基金代码
        @return: 市场分析结果
        """
        if not self.client:
            return self._get_fallback_analysis(market_data)
        
        try:
            # 构建分析提示词
            prompt = self._build_analysis_prompt(market_data, fund_code)
            
            # 调用LLM进行分析
            messages = [
                {
                    "role": "system",
                    "content": self._get_system_prompt()
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ]
            
            completion = self.client.chat.completions.create(
                model="kimi-k2-0711-preview",
                messages=messages,
                temperature=0.3,
            )
            
            # 解析LLM响应
            llm_response = completion.choices[0].message.content
            analysis_result = self._parse_llm_response(llm_response, market_data)
            
            self.logger.info(f"LLM市场分析完成，基金代码: {fund_code}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"LLM市场分析失败: {str(e)}")
            return self._get_fallback_analysis(market_data, error=str(e))
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的量化投资分析师，擅长分析基于CZSC缠论技术分析的市场数据并提供投资建议。

你将接收到以下类型的分析数据：
1. CZSC缠论技术指标分析 - 包括移动平均线、震荡指标、波动性指标等
2. CZSC缠论卦象分析 - 基于价格行为的卦象模式识别
3. 资金流向分析 - 基于实时交易数据的流动性分析
4. 六大维度评估 - 多维度综合评估结果

请基于这些CZSC缠论分析数据，从以下角度进行分析：
1. 市场主要驱动因素分析（明确提及CZSC技术指标的作用）
2. 潜在风险点识别（结合卦象分析结果）
3. 投资机会评估（基于缠论信号）
4. 操作策略建议（融合传统技术分析和缠论理念）

要求：
- 分析要客观理性，基于CZSC缠论数据说话
- 在分析中明确提及使用了CZSC缠论技术分析
- 风险提示要明确具体，结合卦象分析
- 建议要可操作性强，体现缠论思维
- 回答要结构化，便于程序解析

请以JSON格式返回分析结果，包含以下字段：
{
    "market_drivers": ["基于CZSC技术指标的驱动因素1", "驱动因素2"],
    "risk_points": ["结合卦象分析的风险点1", "风险点2"],
    "opportunities": ["基于缠论信号的机会1", "机会2"],
    "strategy_suggestion": "融合CZSC缠论理念的具体操作建议",
    "confidence_level": 0.8,
    "market_sentiment": "积极/中性/谨慎",
    "key_insights": "基于CZSC缠论分析的核心洞察"
}"""
    
    def _build_analysis_prompt(self, market_data: Dict[str, Any], fund_code: str = None) -> str:
        """构建分析提示词"""
        prompt = f"""请分析以下基金的市场情况：

基金代码: {fund_code or '未指定'}
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        # 检查数据可用性
        has_real_data = False
        
        # 添加技术分析数据
        technical_analysis = market_data.get('technical_analysis', {})
        if technical_analysis and 'error' not in technical_analysis:
            indicators = technical_analysis.get('indicators', {})
            if indicators:
                prompt += "技术指标分析 (基于CZSC缠论技术分析):\n"
                
                # 分类显示技术指标
                ma_indicators = {k: v for k, v in indicators.items() if 'ma' in k.lower()}
                oscillator_indicators = {k: v for k, v in indicators.items() if k.lower() in ['rsi', 'macd', 'macd_signal']}
                volatility_indicators = {k: v for k, v in indicators.items() if 'bb' in k.lower() or 'atr' in k.lower()}
                volume_indicators = {k: v for k, v in indicators.items() if 'volume' in k.lower()}
                
                if ma_indicators:
                    prompt += "  移动平均线指标:\n"
                    for indicator, value in ma_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator.upper()}: {value:.4f}\n"
                            has_real_data = True
                
                if oscillator_indicators:
                    prompt += "  震荡指标:\n"
                    for indicator, value in oscillator_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator.upper()}: {value:.4f}\n"
                            has_real_data = True
                
                if volatility_indicators:
                    prompt += "  波动性指标:\n"
                    for indicator, value in volatility_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator}: {value:.4f}\n"
                            has_real_data = True
                
                if volume_indicators:
                    prompt += "  成交量指标:\n"
                    for indicator, value in volume_indicators.items():
                        if value is not None and value != 'N/A':
                            prompt += f"  - {indicator}: {value:.4f}\n"
                            has_real_data = True
                
                prompt += "\n"
        
        # 添加卦象分析数据 (基于CZSC缠论)
        gua_data = market_data.get('gua_data', {})
        if gua_data and 'error' not in gua_data:
            gua_score = gua_data.get('gua_score', 0)
            main_gua = gua_data.get('main_gua', '')
            gua_pattern = gua_data.get('gua_pattern', '')
            
            prompt += f"CZSC缠论卦象分析:\n"
            prompt += f"- 卦象评分: {gua_score:.3f}\n"
            if main_gua:
                prompt += f"- 主卦: {main_gua}\n"
            if gua_pattern:
                prompt += f"- 卦象模式: {gua_pattern}\n"
            prompt += f"- 是否选择卦: {gua_data.get('is_select_gua', False)}\n"
            prompt += f"- 买入信号: {gua_data.get('is_buy_gua', False)}\n"
            prompt += f"- 卖出信号: {gua_data.get('is_sell_gua', False)}\n"
            
            # 添加卦象解释
            gua_interpretation = gua_data.get('gua_interpretation', '')
            if gua_interpretation:
                prompt += f"- 卦象解释: {gua_interpretation}\n"
            
            prompt += "\n"
            has_real_data = True
        
        # 添加资金流向数据
        flow_data = market_data.get('flow_data', {})
        if flow_data and 'error' not in flow_data:
            liquidity_score = flow_data.get('liquidity_score', 0)
            if liquidity_score > 0:
                prompt += f"资金流向分析:\n"
                prompt += f"- 流动性评分: {liquidity_score:.3f}\n"
                prompt += f"- 高流动性: {flow_data.get('high_liquidity', False)}\n\n"
                has_real_data = True
        
        # 添加价格数据（如果有的话）
        price_data = market_data.get('price_data', {})
        if price_data and any(v not in [None, 'N/A', ''] for v in price_data.values()):
            prompt += f"价格数据:\n"
            current_price = price_data.get('current_price')
            change_pct = price_data.get('change_pct')
            volume = price_data.get('volume')
            
            if current_price not in [None, 'N/A', '']:
                prompt += f"- 当前价格: {current_price}\n"
                has_real_data = True
            if change_pct not in [None, 'N/A', '']:
                prompt += f"- 涨跌幅: {change_pct}%\n"
                has_real_data = True
            if volume not in [None, 'N/A', '']:
                prompt += f"- 成交量: {volume}\n"
                has_real_data = True
            prompt += "\n"
        
        # 添加维度评估结果
        if 'evaluations' in market_data:
            prompt += "六大维度评估结果:\n"
            for dim_name, eval_result in market_data['evaluations'].items():
                if isinstance(eval_result, DimensionEvaluationResult):
                    prompt += f"- {dim_name}: 评分={eval_result.score:.3f}, 信号={eval_result.signal}, 置信度={eval_result.confidence:.3f}\n"
                    if eval_result.details:
                        prompt += f"  详情: {eval_result.details}\n"
                    has_real_data = True
            prompt += "\n"
        
        # 添加市场分类结果
        if 'classification' in market_data:
            classification = market_data['classification']
            prompt += f"当前市场分类: {classification.get('primary_classification', '未分类')}\n"
            prompt += f"分类置信度: {classification.get('classification_confidence', 0):.3f}\n"
            prompt += f"市场特征: {', '.join(classification.get('market_characteristics', []))}\n\n"
            has_real_data = True
        
        # 添加CZSC结构分析数据
        czsc_structure = market_data.get('czsc_structure', {})
        if czsc_structure:
            prompt += "CZSC缠论结构分析:\n"
            
            # 添加分型(FX)数据
            fx_list = czsc_structure.get('fx_list', [])
            if fx_list:
                prompt += "  分型(FX)数据:\n"
                for i, fx in enumerate(fx_list[-5:], 1):  # 显示最近5个分型
                    fx_type = "顶分型" if fx['fx_mark'] == 'g' else "底分型"
                    prompt += f"  - 分型{i}: {fx_type}, 价格={fx['fx_price']:.4f}, 时间={fx['dt']}\n"
                    has_real_data = True
            
            # 添加笔(BI)数据
            bi_list = czsc_structure.get('bi_list', [])
            if bi_list:
                prompt += "  笔(BI)数据:\n"
                for i, bi in enumerate(bi_list[-5:], 1):  # 显示最近5个笔
                    prompt += f"  - 笔{i}: {bi['direction']}, 价格={bi['bi_price']:.4f}, 时间={bi['dt']}\n"
                    has_real_data = True
            
            # 添加线段(XD)数据
            xd_list = czsc_structure.get('xd_list', [])
            if xd_list:
                prompt += "  线段(XD)数据:\n"
                for i, xd in enumerate(xd_list[-3:], 1):  # 显示最近3个线段
                    prompt += f"  - 线段{i}: {xd['direction']}, 价格={xd['xd_price']:.4f}, 时间={xd['dt']}\n"
                    has_real_data = True
            
            # 添加结构分析
            structure_analysis = czsc_structure.get('structure_analysis', {})
            if structure_analysis:
                prompt += "  结构分析:\n"
                prompt += f"  - 当前趋势: {structure_analysis.get('current_trend', '未知')}\n"
                prompt += f"  - 结构强度: {structure_analysis.get('structure_strength', 0.5):.2f}\n"
                
                # 关键价位
                key_levels = structure_analysis.get('key_levels', [])
                if key_levels:
                    prompt += "  - 关键价位:\n"
                    for level in key_levels[-3:]:  # 显示最近3个关键价位
                        prompt += f"    * {level['type']}: {level['price']:.4f}\n"
                
                # 结构信号
                structure_signals = structure_analysis.get('structure_signals', [])
                if structure_signals:
                    prompt += f"  - 结构信号: {', '.join(structure_signals)}\n"
                
                has_real_data = True
            
            prompt += "\n"
        
        # 根据数据可用性调整分析要求
        if has_real_data:
            if czsc_structure:
                prompt += """请基于以上CZSC缠论技术分析数据进行综合分析，特别关注：
1. 分型(FX)和笔(BI)的结构关系
2. 当前市场所处的缠论结构位置
3. 基于笔和线段的趋势判断
4. 结合传统技术指标和缠论结构的综合建议

请给出专业的缠论分析和投资建议。"""
            else:
                prompt += "请基于以上数据进行综合分析，并给出投资建议。"
        else:
            prompt += f"""注意：当前缺乏实时市场数据，这可能是由于数据源暂时不可用。

请基于{fund_code}的基本情况进行分析：
1. 这是一只ETF基金，请分析其一般投资特点
2. 在数据缺失的情况下，给出谨慎的投资建议
3. 建议投资者等待更多数据后再做决策

请提供保守的分析意见。"""
        
        return prompt
    
    def _parse_llm_response(self, llm_response: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON格式的响应
            if '{' in llm_response and '}' in llm_response:
                json_start = llm_response.find('{')
                json_end = llm_response.rfind('}') + 1
                json_str = llm_response[json_start:json_end]
                parsed_result = json.loads(json_str)
            else:
                # 如果不是JSON格式，创建结构化结果
                parsed_result = {
                    "market_drivers": ["LLM分析结果"],
                    "risk_points": ["需要进一步分析"],
                    "opportunities": ["待识别"],
                    "strategy_suggestion": llm_response[:200] + "..." if len(llm_response) > 200 else llm_response,
                    "confidence_level": 0.6,
                    "market_sentiment": "中性",
                    "key_insights": "详见完整分析"
                }
            
            # 添加元数据
            result = {
                "analysis_type": "llm_market_analysis",
                "analysis_time": datetime.now().isoformat(),
                "llm_model": "kimi-k2-0711-preview",
                "raw_response": llm_response,
                **parsed_result
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"解析LLM响应失败: {str(e)}")
            return {
                "analysis_type": "llm_market_analysis",
                "analysis_time": datetime.now().isoformat(),
                "error": f"解析失败: {str(e)}",
                "raw_response": llm_response,
                "market_drivers": ["解析错误"],
                "risk_points": ["响应解析失败"],
                "opportunities": [],
                "strategy_suggestion": "建议使用传统分析方法",
                "confidence_level": 0.1,
                "market_sentiment": "未知",
                "key_insights": "LLM响应解析出错"
            }
    
    def _get_fallback_analysis(self, market_data: Dict[str, Any], error: str = None) -> Dict[str, Any]:
        """获取兜底分析结果"""
        return {
            "analysis_type": "fallback_analysis",
            "analysis_time": datetime.now().isoformat(),
            "error": error,
            "market_drivers": ["技术指标驱动"],
            "risk_points": ["LLM服务不可用"],
            "opportunities": ["基于传统指标分析"],
            "strategy_suggestion": "建议使用传统技术分析方法进行决策",
            "confidence_level": 0.3,
            "market_sentiment": "中性",
            "key_insights": "LLM分析服务暂时不可用，建议依赖传统分析方法"
        }
    
    def generate_decision_explanation(self, decision_data: Dict[str, Any]) -> str:
        """
        @brief 生成决策解释
        @param decision_data: 决策相关数据
        @return: 决策解释文本
        """
        if not self.client:
            return self._get_fallback_explanation(decision_data)
        
        try:
            prompt = f"""请为以下投资决策生成清晰的解释：

决策类型: {decision_data.get('decision_type', '未知')}
基金代码: {decision_data.get('fund_code', '未指定')}
决策时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

决策依据:
{json.dumps(decision_data, ensure_ascii=False, indent=2)}

请用通俗易懂的语言解释这个决策的原因，包括：
1. 为什么做出这个决策
2. 主要考虑因素
3. 预期效果
4. 风险提示

要求简洁明了，不超过200字。"""

            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的投资顾问，擅长用简洁明了的语言解释投资决策。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            completion = self.client.chat.completions.create(
                model="kimi-k2-0711-preview",
                messages=messages,
                temperature=0.2,
            )
            
            explanation = completion.choices[0].message.content
            self.logger.info("决策解释生成成功")
            return explanation
            
        except Exception as e:
            self.logger.error(f"决策解释生成失败: {str(e)}")
            return self._get_fallback_explanation(decision_data, error=str(e))
    
    def _get_fallback_explanation(self, decision_data: Dict[str, Any], error: str = None) -> str:
        """获取兜底决策解释"""
        decision_type = decision_data.get('decision_type', '未知决策')
        fund_code = decision_data.get('fund_code', '未指定基金')
        
        explanation = f"基于技术分析对{fund_code}做出{decision_type}决策。"
        
        if error:
            explanation += f" (注：AI解释服务暂时不可用: {error})"
        
        return explanation