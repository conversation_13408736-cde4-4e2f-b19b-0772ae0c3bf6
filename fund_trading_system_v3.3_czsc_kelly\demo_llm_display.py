#!/usr/bin/env python3
"""
演示完整的LLM分析显示效果
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from system.enhanced_trading_system import EnhancedFundTradingSystemV3

def demo_llm_display():
    """演示LLM分析显示效果"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🎯 演示完整LLM分析显示效果")
    print("=" * 60)
    
    try:
        # 初始化交易系统
        trading_system = EnhancedFundTradingSystemV3()
        
        # 测试基金代码
        test_fund_code = '513500'
        
        print(f"📊 分析基金: {test_fund_code}")
        print("-" * 60)
        
        # 执行完整分析
        analysis_result = trading_system.analyze_fund_v3(test_fund_code)
        
        if 'error' not in analysis_result:
            # 使用新的显示方法展示详细分析结果
            trading_system.display_detailed_analysis_result(analysis_result)
            
            # 额外显示完整的LLM分析内容
            print("\n" + "="*60)
            print("🤖 完整LLM分析详情:")
            print("="*60)
            
            llm_analysis = analysis_result.get('llm_analysis', {})
            if llm_analysis and 'error' not in llm_analysis:
                
                # 显示所有LLM分析字段
                for key, value in llm_analysis.items():
                    if key in ['analysis_type', 'analysis_time', 'llm_model', 'raw_response']:
                        continue  # 跳过技术字段
                    
                    print(f"\n📋 {key.replace('_', ' ').title()}:")
                    if isinstance(value, list):
                        for i, item in enumerate(value, 1):
                            print(f"   {i}. {item}")
                    elif isinstance(value, str) and len(value) > 100:
                        # 长文本分行显示
                        lines = trading_system._wrap_text(value, 80)
                        for line in lines:
                            print(f"   {line}")
                    else:
                        print(f"   {value}")
                
                # 显示原始LLM响应（如果需要调试）
                if llm_analysis.get('raw_response'):
                    print(f"\n🔍 LLM原始响应:")
                    print("-" * 40)
                    raw_response = llm_analysis['raw_response']
                    if len(raw_response) > 500:
                        print(f"{raw_response[:500]}...")
                        print(f"[响应总长度: {len(raw_response)} 字符]")
                    else:
                        print(raw_response)
            else:
                print("❌ LLM分析失败或无数据")
                
        else:
            print(f"❌ 基金分析失败: {analysis_result['error']}")
            
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n🏁 LLM显示演示完成")

if __name__ == '__main__':
    demo_llm_display()