"""
风险管理器
Risk Manager
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class RiskManager:
    """风险管理器"""
    
    def __init__(self, max_position_size: float = 0.1, stop_loss_pct: float = 0.05,
                 take_profit_pct: float = 0.15, max_drawdown_limit: float = 0.2,
                 max_daily_loss: float = 0.02, position_concentration_limit: float = 0.3,
                 **kwargs):
        """
        初始化风险管理器
        
        Args:
            max_position_size: 最大仓位大小
            stop_loss_pct: 止损百分比
            take_profit_pct: 止盈百分比
            max_drawdown_limit: 最大回撤限制
            max_daily_loss: 最大日损失
            position_concentration_limit: 仓位集中度限制
        """
        self.max_position_size = max_position_size
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.max_drawdown_limit = max_drawdown_limit
        self.max_daily_loss = max_daily_loss
        self.position_concentration_limit = position_concentration_limit
        
        # 风险监控状态
        self.current_drawdown = 0.0
        self.daily_loss = 0.0
        self.position_sizes = {}
        self.risk_warnings = []
        
        logger.info("RiskManager 初始化完成")
    
    def calculate_position_size(self, signal_strength: float, confidence: float, 
                              current_price: float, portfolio_value: float) -> float:
        """
        计算建议仓位大小
        
        Args:
            signal_strength: 信号强度 (0-1)
            confidence: 置信度 (0-1)
            current_price: 当前价格
            portfolio_value: 组合价值
            
        Returns:
            建议仓位大小 (0-1)
        """
        try:
            # 基础仓位计算
            base_position = min(signal_strength * confidence, self.max_position_size)
            
            # 风险调整
            risk_adjustment = self._calculate_risk_adjustment()
            adjusted_position = base_position * risk_adjustment
            
            # 确保不超过限制
            final_position = min(adjusted_position, self.max_position_size)
            
            logger.debug(f"仓位计算: 基础={base_position:.3f}, 风险调整={risk_adjustment:.3f}, 最终={final_position:.3f}")
            
            return final_position
            
        except Exception as e:
            logger.error(f"仓位计算失败: {e}")
            return 0.0
    
    def calculate_stop_loss_level(self, entry_price: float, signal_direction: int) -> float:
        """
        计算止损位
        
        Args:
            entry_price: 入场价格
            signal_direction: 信号方向 (1为多头, -1为空头)
            
        Returns:
            止损价格
        """
        if signal_direction > 0:  # 多头
            stop_loss = entry_price * (1 - self.stop_loss_pct)
        else:  # 空头
            stop_loss = entry_price * (1 + self.stop_loss_pct)
        
        return stop_loss
    
    def calculate_take_profit_level(self, entry_price: float, signal_direction: int) -> float:
        """
        计算止盈位
        
        Args:
            entry_price: 入场价格
            signal_direction: 信号方向 (1为多头, -1为空头)
            
        Returns:
            止盈价格
        """
        if signal_direction > 0:  # 多头
            take_profit = entry_price * (1 + self.take_profit_pct)
        else:  # 空头
            take_profit = entry_price * (1 - self.take_profit_pct)
        
        return take_profit
    
    def evaluate_risk_level(self, signal_strength: float, confidence: float, 
                           market_volatility: float = 0.2) -> str:
        """
        评估风险等级
        
        Args:
            signal_strength: 信号强度
            confidence: 置信度
            market_volatility: 市场波动率
            
        Returns:
            风险等级 ('LOW', 'MEDIUM', 'HIGH')
        """
        try:
            # 综合风险评分
            risk_score = 0.0
            
            # 信号质量风险
            signal_risk = 1 - (signal_strength * confidence)
            risk_score += signal_risk * 0.4
            
            # 市场波动风险
            volatility_risk = min(market_volatility / 0.3, 1.0)  # 标准化到0-1
            risk_score += volatility_risk * 0.3
            
            # 当前回撤风险
            drawdown_risk = min(abs(self.current_drawdown) / self.max_drawdown_limit, 1.0)
            risk_score += drawdown_risk * 0.3
            
            # 风险等级判断
            if risk_score < 0.3:
                return 'LOW'
            elif risk_score < 0.7:
                return 'MEDIUM'
            else:
                return 'HIGH'
                
        except Exception as e:
            logger.error(f"风险等级评估失败: {e}")
            return 'HIGH'  # 出错时返回高风险
    
    def check_risk_limits(self, proposed_position: float, current_positions: Dict[str, float],
                         portfolio_value: float) -> Tuple[bool, List[str]]:
        """
        检查风险限制
        
        Args:
            proposed_position: 建议仓位
            current_positions: 当前持仓
            portfolio_value: 组合价值
            
        Returns:
            (是否通过检查, 警告信息列表)
        """
        warnings = []
        passed = True
        
        try:
            # 检查单个仓位限制
            if abs(proposed_position) > self.max_position_size:
                warnings.append(f"仓位超限: {proposed_position:.2%} > {self.max_position_size:.2%}")
                passed = False
            
            # 检查总仓位集中度
            total_position = sum(abs(pos) for pos in current_positions.values()) + abs(proposed_position)
            if total_position > self.position_concentration_limit:
                warnings.append(f"仓位集中度过高: {total_position:.2%} > {self.position_concentration_limit:.2%}")
                passed = False
            
            # 检查回撤限制
            if abs(self.current_drawdown) > self.max_drawdown_limit:
                warnings.append(f"回撤超限: {self.current_drawdown:.2%} > {self.max_drawdown_limit:.2%}")
                passed = False
            
            # 检查日损失限制
            if self.daily_loss > self.max_daily_loss:
                warnings.append(f"日损失超限: {self.daily_loss:.2%} > {self.max_daily_loss:.2%}")
                passed = False
            
        except Exception as e:
            logger.error(f"风险限制检查失败: {e}")
            warnings.append(f"风险检查错误: {str(e)}")
            passed = False
        
        return passed, warnings
    
    def update_risk_status(self, current_portfolio_value: float, peak_value: float,
                          daily_pnl: float):
        """
        更新风险状态
        
        Args:
            current_portfolio_value: 当前组合价值
            peak_value: 历史最高价值
            daily_pnl: 日盈亏
        """
        try:
            # 更新回撤
            self.current_drawdown = (current_portfolio_value - peak_value) / peak_value
            
            # 更新日损失
            if daily_pnl < 0:
                self.daily_loss = abs(daily_pnl) / current_portfolio_value
            else:
                self.daily_loss = 0.0
            
            # 生成风险警告
            self._generate_risk_warnings()
            
        except Exception as e:
            logger.error(f"风险状态更新失败: {e}")
    
    def get_risk_warnings(self) -> List[str]:
        """获取当前风险警告"""
        return self.risk_warnings.copy()
    
    def evaluate_risk(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估回测结果的风险指标
        
        Args:
            backtest_results: 回测结果
            
        Returns:
            风险评估结果
        """
        risk_metrics = {}
        
        try:
            if 'capital_curve' in backtest_results:
                capital_curve = np.array(backtest_results['capital_curve'])
                
                # 计算回撤序列
                peak = np.maximum.accumulate(capital_curve)
                drawdown = (capital_curve - peak) / peak
                
                # 基础风险指标
                risk_metrics['max_drawdown'] = np.min(drawdown)
                risk_metrics['avg_drawdown'] = np.mean(drawdown[drawdown < 0]) if np.any(drawdown < 0) else 0.0
                risk_metrics['drawdown_duration'] = self._calculate_drawdown_duration(drawdown)
                
                # 收益率相关风险
                if 'returns' in backtest_results:
                    returns = np.array(backtest_results['returns'])
                    
                    # VaR和CVaR
                    risk_metrics['var_95'] = np.percentile(returns, 5)
                    risk_metrics['var_99'] = np.percentile(returns, 1)
                    risk_metrics['cvar_95'] = np.mean(returns[returns <= risk_metrics['var_95']]) if np.any(returns <= risk_metrics['var_95']) else 0.0
                    
                    # 下行风险
                    negative_returns = returns[returns < 0]
                    risk_metrics['downside_deviation'] = np.std(negative_returns) if len(negative_returns) > 0 else 0.0
                    risk_metrics['sortino_ratio'] = np.mean(returns) / risk_metrics['downside_deviation'] if risk_metrics['downside_deviation'] > 0 else 0.0
                    
                    # 尾部风险
                    risk_metrics['skewness'] = self._calculate_skewness(returns)
                    risk_metrics['kurtosis'] = self._calculate_kurtosis(returns)
                
                # 风险等级评定
                risk_metrics['risk_level'] = self._assess_overall_risk_level(risk_metrics)
                
            else:
                # 默认风险指标
                risk_metrics = self._get_default_risk_metrics()
                
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            risk_metrics = self._get_default_risk_metrics()
            risk_metrics['error'] = str(e)
        
        return risk_metrics
    
    def _calculate_risk_adjustment(self) -> float:
        """计算风险调整系数"""
        adjustment = 1.0
        
        # 回撤调整
        if abs(self.current_drawdown) > self.max_drawdown_limit * 0.5:
            adjustment *= 0.5
        elif abs(self.current_drawdown) > self.max_drawdown_limit * 0.8:
            adjustment *= 0.2
        
        # 日损失调整
        if self.daily_loss > self.max_daily_loss * 0.5:
            adjustment *= 0.7
        
        return max(adjustment, 0.1)  # 最小调整系数为0.1
    
    def _generate_risk_warnings(self):
        """生成风险警告"""
        self.risk_warnings.clear()
        
        if abs(self.current_drawdown) > self.max_drawdown_limit * 0.8:
            self.risk_warnings.append("回撤接近限制")
        
        if self.daily_loss > self.max_daily_loss * 0.8:
            self.risk_warnings.append("日损失接近限制")
        
        if abs(self.current_drawdown) > self.max_drawdown_limit:
            self.risk_warnings.append("回撤超过限制")
        
        if self.daily_loss > self.max_daily_loss:
            self.risk_warnings.append("日损失超过限制")
    
    def _calculate_drawdown_duration(self, drawdown: np.ndarray) -> int:
        """计算最大回撤持续期"""
        try:
            max_duration = 0
            current_duration = 0
            
            for dd in drawdown:
                if dd < 0:
                    current_duration += 1
                    max_duration = max(max_duration, current_duration)
                else:
                    current_duration = 0
            
            return max_duration
        except:
            return 0
    
    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """计算偏度"""
        try:
            mean = np.mean(returns)
            std = np.std(returns)
            if std == 0:
                return 0.0
            return np.mean(((returns - mean) / std) ** 3)
        except:
            return 0.0
    
    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """计算峰度"""
        try:
            mean = np.mean(returns)
            std = np.std(returns)
            if std == 0:
                return 0.0
            return np.mean(((returns - mean) / std) ** 4) - 3
        except:
            return 0.0
    
    def _assess_overall_risk_level(self, risk_metrics: Dict[str, float]) -> str:
        """评估整体风险等级"""
        try:
            risk_score = 0.0
            
            # 最大回撤风险
            max_dd = abs(risk_metrics.get('max_drawdown', 0))
            if max_dd > 0.2:
                risk_score += 3
            elif max_dd > 0.1:
                risk_score += 2
            elif max_dd > 0.05:
                risk_score += 1
            
            # VaR风险
            var_95 = abs(risk_metrics.get('var_95', 0))
            if var_95 > 0.05:
                risk_score += 2
            elif var_95 > 0.03:
                risk_score += 1
            
            # 下行偏差风险
            downside_dev = risk_metrics.get('downside_deviation', 0)
            if downside_dev > 0.03:
                risk_score += 1
            
            # 风险等级判断
            if risk_score >= 5:
                return 'HIGH'
            elif risk_score >= 3:
                return 'MEDIUM'
            else:
                return 'LOW'
                
        except:
            return 'MEDIUM'
    
    def validate_position_size(self, position_size: float, current_price: float, 
                              portfolio_value: float) -> Tuple[bool, List[str]]:
        """
        验证仓位大小
        
        Args:
            position_size: 建议仓位大小
            current_price: 当前价格
            portfolio_value: 组合价值
            
        Returns:
            (是否有效, 警告信息列表)
        """
        warnings = []
        is_valid = True
        
        try:
            # 检查仓位大小限制
            if abs(position_size) > self.max_position_size:
                warnings.append(f"仓位超限: {position_size:.2%} > {self.max_position_size:.2%}")
                is_valid = False
            
            # 检查最小仓位
            if abs(position_size) < 0.001:
                warnings.append("仓位过小")
                is_valid = False
            
            # 检查当前回撤状态
            if abs(self.current_drawdown) > self.max_drawdown_limit * 0.8:
                warnings.append("当前回撤较大，建议减少仓位")
                is_valid = False
            
        except Exception as e:
            warnings.append(f"仓位验证错误: {str(e)}")
            is_valid = False
        
        return is_valid, warnings
    
    def _get_default_risk_metrics(self) -> Dict[str, Any]:
        """获取默认风险指标"""
        return {
            'max_drawdown': 0.0,
            'avg_drawdown': 0.0,
            'drawdown_duration': 0,
            'var_95': 0.0,
            'var_99': 0.0,
            'cvar_95': 0.0,
            'downside_deviation': 0.0,
            'sortino_ratio': 0.0,
            'skewness': 0.0,
            'kurtosis': 0.0,
            'risk_level': 'UNKNOWN'
        }