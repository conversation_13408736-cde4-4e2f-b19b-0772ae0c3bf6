"""
基金特性分析器
负责分析基金的流动性、波动性、规模和关联性等特征
"""

import logging
import sys
import os
import json
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.utils import *


class FundCharacteristicsAnalyzer:
    """
    @class FundCharacteristicsAnalyzer
    @brief 基金特性分析器
    @details 负责分析基金的流动性、波动性、规模和关联性等特征
    """
    
    def __init__(self):
        self.fund_profiles = {}
        self.feature_weights = {
            'liquidity': 0.4, 'volatility': 0.3, 'scale': 0.2, 'correlation': 0.1
        }
        # 基础基金分类映射
        self.fund_categories = {
            '513500': 'large_cap_equity', '513300': 'large_cap_equity',
            '513030': 'developed_equity', '513080': 'developed_equity',
            '513520': 'emerging_equity', '513850': 'emerging_equity',
            '159329': 'china_equity', '159561': 'china_equity',
            '520830': 'bond_fund', '159567': 'commodity_fund',
            '518880': 'gold_etf', '601398': 'bank_stock'  # 补充缺失的基金
        }

        # 基金代码前缀分类规则
        self.prefix_rules = {
            '5130': 'large_cap_equity',    # 沪深300等大盘ETF
            '5133': 'large_cap_equity',    # 创业板ETF
            '5135': 'emerging_equity',     # 新兴市场ETF
            '5138': 'sector_equity',       # 行业ETF
            '1593': 'china_equity',        # 中国相关ETF
            '1595': 'china_equity',        # 中国相关ETF
            '5208': 'bond_fund',           # 债券基金
            '5188': 'gold_etf',            # 黄金ETF
            '6013': 'bank_stock',          # 银行股票
            '6000': 'large_cap_stock',     # 大盘股票
            '0000': 'small_cap_stock'      # 小盘股票
        }
    
    def analyze_fund_characteristics(self, fund_code: str, df: pd.DataFrame, fund_volume: float) -> Dict[str, float]:
        """分析基金特性"""
        try:
            liquidity_score = self.calculate_liquidity_score(fund_code, fund_volume)
            volatility_score = self.calculate_volatility_score(df)
            scale_score = self.calculate_scale_score(fund_volume)
            correlation_score = self.calculate_correlation_score(fund_code)
            
            category = self.get_fund_category(fund_code)
            category_adjustment = self._get_category_adjustment(category)
            
            characteristics = {
                'liquidity_score': liquidity_score,
                'volatility_score': volatility_score,
                'scale_score': scale_score,
                'correlation_score': correlation_score,
                'category': category,
                'category_adjustment': category_adjustment,
                'composite_score': (
                    liquidity_score * self.feature_weights['liquidity'] +
                    volatility_score * self.feature_weights['volatility'] +
                    scale_score * self.feature_weights['scale'] +
                    correlation_score * self.feature_weights['correlation']
                )
            }
            
            self.fund_profiles[fund_code] = characteristics
            return characteristics
            
        except Exception as e:
            logging.error(f"基金特性分析失败 {fund_code}: {e}")
            return self._get_default_characteristics()
    
    def calculate_liquidity_score(self, fund_code: str, fund_volume: float) -> float:
        """计算流动性评分"""
        try:
            scale_factor = min(1.0, fund_volume / 10_000_000_000)
            try:
                if CZSC_FUNC_AVAILABLE:
                    df = get_kline(fund_code, freq='D')
                    if not df.empty and len(df) >= 5:
                        recent_volumes = df['vol'].tail(5).mean()
                        turnover_rate = recent_volumes * 100 / fund_volume if fund_volume > 0 else 0
                        turnover_factor = min(1.0, turnover_rate / 5.0)
                    else:
                        turnover_factor = 0.5
                else:
                    turnover_factor = 0.5
            except:
                turnover_factor = 0.5
            
            liquidity_score = scale_factor * 0.6 + turnover_factor * 0.4
            return max(0.1, min(1.0, liquidity_score))
        except Exception:
            return 0.5
    
    def calculate_volatility_score(self, df: pd.DataFrame) -> float:
        """计算波动性评分"""
        try:
            if df.empty or len(df) < 20:
                return 0.5
            
            returns = df['close'].pct_change().dropna()
            if len(returns) < 10:
                return 0.5
            
            volatility = returns.tail(20).std()
            annualized_vol = volatility * np.sqrt(252)
            volatility_score = min(1.0, annualized_vol / 0.3)
            return max(0.1, min(1.0, volatility_score))
        except Exception:
            return 0.5
    
    def calculate_scale_score(self, fund_volume: float) -> float:
        """计算规模评分"""
        try:
            scale_score = min(1.0, fund_volume / 50_000_000_000)
            return max(0.1, scale_score)
        except Exception:
            return 0.5
    
    def calculate_correlation_score(self, fund_code: str) -> float:
        """计算关联性评分"""
        try:
            category = self.get_fund_category(fund_code)
            correlation_map = {
                'large_cap_equity': 0.8, 'developed_equity': 0.7, 'emerging_equity': 0.6,
                'china_equity': 0.9, 'bond_fund': 0.3, 'commodity_fund': 0.4,
                'gold_etf': 0.2, 'bank_stock': 0.7, 'sector_equity': 0.6,
                'large_cap_stock': 0.8, 'small_cap_stock': 0.5
            }
            return correlation_map.get(category, 0.5)
        except Exception:
            return 0.5
    
    def get_fund_category(self, fund_code: str) -> str:
        """获取基金类别 - 支持动态识别"""
        # 首先检查预定义的分类
        if fund_code in self.fund_categories:
            return self.fund_categories[fund_code]

        # 使用前缀规则进行动态分类
        category = self._classify_by_prefix(fund_code)
        if category != 'unknown':
            # 自动添加到分类映射中
            self.fund_categories[fund_code] = category
            return category

        # 使用名称模式进行分类（如果可用）
        category = self._classify_by_pattern(fund_code)
        if category != 'unknown':
            self.fund_categories[fund_code] = category
            return category

        return 'unknown'

    def _classify_by_prefix(self, fund_code: str) -> str:
        """基于代码前缀进行分类"""
        for prefix, category in self.prefix_rules.items():
            if fund_code.startswith(prefix):
                return category
        return 'unknown'

    def _classify_by_pattern(self, fund_code: str) -> str:
        """基于代码模式进行分类"""
        # ETF基金通常以51开头
        if fund_code.startswith('51'):
            return 'equity_etf'
        # 债券基金通常以52开头
        elif fund_code.startswith('52'):
            return 'bond_fund'
        # 股票代码
        elif fund_code.startswith('60') or fund_code.startswith('00') or fund_code.startswith('30'):
            if fund_code.startswith('60'):
                return 'large_cap_stock'
            else:
                return 'small_cap_stock'
        return 'unknown'
    
    def _get_category_adjustment(self, category: str) -> Dict[str, float]:
        """获取类别调整系数"""
        adjustments = {
            'large_cap_equity': {'sensitivity': -0.1, 'smoothness': 0.1},
            'developed_equity': {'sensitivity': 0.0, 'smoothness': 0.0},
            'emerging_equity': {'sensitivity': 0.1, 'smoothness': -0.1},
            'china_equity': {'sensitivity': 0.0, 'smoothness': 0.0},
            'bond_fund': {'sensitivity': -0.2, 'smoothness': 0.2},
            'commodity_fund': {'sensitivity': 0.15, 'smoothness': -0.15},
            'gold_etf': {'sensitivity': -0.1, 'smoothness': 0.15},
            'bank_stock': {'sensitivity': -0.05, 'smoothness': 0.05},
            'sector_equity': {'sensitivity': 0.05, 'smoothness': -0.05},
            'large_cap_stock': {'sensitivity': -0.1, 'smoothness': 0.1},
            'small_cap_stock': {'sensitivity': 0.2, 'smoothness': -0.2}
        }
        return adjustments.get(category, {'sensitivity': 0.0, 'smoothness': 0.0})
    
    def _get_default_characteristics(self) -> Dict[str, float]:
        """获取默认特性"""
        return {
            'liquidity_score': 0.5, 'volatility_score': 0.5, 'scale_score': 0.5,
            'correlation_score': 0.5, 'category': 'unknown',
            'category_adjustment': {'sensitivity': 0.0, 'smoothness': 0.0},
            'composite_score': 0.5
        }
    
    def get_fund_profile(self, fund_code: str) -> Dict[str, Any]:
        """获取基金档案"""
        return self.fund_profiles.get(fund_code, self._get_default_characteristics())
    
    def update_fund_category(self, fund_code: str, category: str) -> None:
        """更新基金类别"""
        self.fund_categories[fund_code] = category
    
    def get_all_categories(self) -> Dict[str, str]:
        """获取所有基金类别"""
        return self.fund_categories.copy()
    
    def get_category_stats(self) -> Dict[str, int]:
        """获取类别统计"""
        category_counts = {}
        for category in self.fund_categories.values():
            category_counts[category] = category_counts.get(category, 0) + 1
        return category_counts

    def sync_with_fund_list(self, fund_list: List[str]) -> Dict[str, str]:
        """与基金列表同步，自动分类新基金"""
        new_classifications = {}

        for fund_code in fund_list:
            if fund_code not in self.fund_categories:
                # 使用动态分类
                category = self._classify_by_prefix(fund_code)
                if category == 'unknown':
                    category = self._classify_by_pattern(fund_code)

                self.fund_categories[fund_code] = category
                new_classifications[fund_code] = category

        return new_classifications

    def load_categories_from_config(self, config_path: str = None) -> bool:
        """从配置文件加载基金分类"""
        try:
            if config_path is None:
                config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'fund_categories.json')

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    external_categories = json.load(f)

                # 合并外部配置
                self.fund_categories.update(external_categories)
                return True

        except Exception as e:
            logging.warning(f"Failed to load fund categories from config: {e}")

        return False

    def save_categories_to_config(self, config_path: str = None) -> bool:
        """保存基金分类到配置文件"""
        try:
            if config_path is None:
                config_dir = os.path.join(os.path.dirname(__file__), '..', 'config')
                os.makedirs(config_dir, exist_ok=True)
                config_path = os.path.join(config_dir, 'fund_categories.json')

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.fund_categories, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            logging.warning(f"Failed to save fund categories to config: {e}")
            return False
