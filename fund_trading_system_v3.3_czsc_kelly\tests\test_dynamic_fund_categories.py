"""
测试动态基金分类功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from optimizers.fund_analyzer import FundCharacteristicsAnalyzer


def test_dynamic_fund_categories():
    """测试动态基金分类功能"""
    print("🧪 测试动态基金分类功能")
    print("=" * 60)
    
    # 创建分析器
    analyzer = FundCharacteristicsAnalyzer()
    
    # 测试基金列表（包含新的基金代码）
    test_fund_list = [
        '513030', '513080', '513500', '513520', '513300', 
        '513850', '159329', '159561', '520830', '159567',
        '518880', '601398',  # 这两个之前缺失
        '513100',  # 新的ETF
        '600036',  # 新的银行股
        '000001'   # 新的小盘股
    ]
    
    print("1. 测试原有基金分类:")
    for fund_code in ['513500', '518880', '601398']:
        category = analyzer.get_fund_category(fund_code)
        print(f"   {fund_code}: {category}")
    
    print("\n2. 测试动态分类:")
    for fund_code in ['513100', '600036', '000001']:
        category = analyzer.get_fund_category(fund_code)
        print(f"   {fund_code}: {category} (动态识别)")
    
    print("\n3. 测试与基金列表同步:")
    new_classifications = analyzer.sync_with_fund_list(test_fund_list)
    if new_classifications:
        print("   新分类的基金:")
        for fund_code, category in new_classifications.items():
            print(f"   {fund_code}: {category}")
    else:
        print("   所有基金都已分类")
    
    print("\n4. 测试类别统计:")
    stats = analyzer.get_category_stats()
    for category, count in stats.items():
        print(f"   {category}: {count}只基金")
    
    print("\n5. 测试关联性评分:")
    for fund_code in ['518880', '601398', '513100']:
        score = analyzer.calculate_correlation_score(fund_code)
        category = analyzer.get_fund_category(fund_code)
        print(f"   {fund_code} ({category}): 关联性评分 {score}")
    
    print("\n6. 测试类别调整系数:")
    for fund_code in ['518880', '601398']:
        category = analyzer.get_fund_category(fund_code)
        adjustment = analyzer._get_category_adjustment(category)
        print(f"   {fund_code} ({category}): {adjustment}")
    
    print("\n✅ 动态基金分类功能测试完成!")
    print("=" * 60)
    
    return analyzer


def test_config_functionality():
    """测试配置文件功能"""
    print("\n🔧 测试配置文件功能")
    print("-" * 40)
    
    analyzer = FundCharacteristicsAnalyzer()
    
    # 添加一些测试分类
    analyzer.fund_categories['999999'] = 'test_category'
    
    # 测试保存配置
    config_path = os.path.join(current_dir, 'test_fund_categories.json')
    success = analyzer.save_categories_to_config(config_path)
    print(f"保存配置文件: {'成功' if success else '失败'}")
    
    # 创建新的分析器并加载配置
    new_analyzer = FundCharacteristicsAnalyzer()
    success = new_analyzer.load_categories_from_config(config_path)
    print(f"加载配置文件: {'成功' if success else '失败'}")
    
    if success:
        test_category = new_analyzer.get_fund_category('999999')
        print(f"测试分类加载: 999999 -> {test_category}")
    
    # 清理测试文件
    try:
        if os.path.exists(config_path):
            os.remove(config_path)
            print("清理测试文件: 成功")
    except:
        print("清理测试文件: 失败")
    
    print("✅ 配置文件功能测试完成!")


if __name__ == "__main__":
    try:
        analyzer = test_dynamic_fund_categories()
        test_config_functionality()
        
        print("\n🎉 所有测试完成!")
        print("✅ 动态基金分类系统已准备就绪")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
