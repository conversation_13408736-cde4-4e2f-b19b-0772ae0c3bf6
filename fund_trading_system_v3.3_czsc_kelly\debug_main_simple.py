"""
简化的主系统调试
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3


def debug_simple():
    """简化调试"""
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        print(f"\n🔍 简化调试主系统")
        print("-" * 60)
        
        # 直接使用协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 分析一个基金
        fund_code = '518880'
        print(f"\n📊 分析基金: {fund_code}")
        
        # 获取分析结果
        analysis_result = coordinator.coordinate_analysis(fund_code)
        
        # 检查凯利分析结果
        kelly_analysis = analysis_result.get('kelly_analysis', {})
        if kelly_analysis:
            kelly_calc = kelly_analysis.get('kelly_calculation', {})
            
            optimal_position = kelly_calc.get('optimal_position', 0)
            risk_level = kelly_calc.get('risk_level', '未知')
            win_probability = kelly_calc.get('win_probability', 0)
            risk_reward_ratio = kelly_calc.get('risk_reward_ratio', 0)
            
            print(f"\n✅ 凯利分析结果:")
            print(f"   建议仓位: {optimal_position:.2%}")
            print(f"   风险等级: {risk_level}")
            print(f"   胜率: {win_probability:.1%}")
            print(f"   盈亏比: {risk_reward_ratio:.2f}")
            
            # 模拟主系统的显示逻辑
            position_icon = "💰" if optimal_position > 0 else "⚪"
            print(f"\n🖥️ 主系统显示格式:")
            print(f"   {position_icon} 凯利仓位: {optimal_position:.2%} | 风险等级: {risk_level}")
            print(f"   📊 概率分析: 胜率={win_probability:.1%} | 盈亏比={risk_reward_ratio:.2f}")
        else:
            print(f"\n❌ 没有找到凯利分析结果")
        
    except Exception as e:
        print(f"\n❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    debug_simple()