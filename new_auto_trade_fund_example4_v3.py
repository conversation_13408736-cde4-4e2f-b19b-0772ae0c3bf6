"""
增强版基金交易多智能体系统 V3
集成enhanced_soros_agent7.py的六大维度评估体系

V3版本新增功能：
1. 六大维度市场评估框架（趋势、波动性、流动性、情绪、结构、转换）
2. 分型质量评估系统
3. 智能信号冲突解决器
4. 动态权重管理器
5. 多维度综合决策机制
6. 结构化分析摘要系统
"""

import os
import sys
import time
import random
import logging
import numpy as np
import pandas as pd
import talib as ta
from datetime import datetime, timedelta
from typing import Dict, List, Union, Optional, Tuple, Any, Callable
from collections import OrderedDict, deque
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
# 导入真实数据分析模块
from czsc_func import get_kline, get_realtime_quote
from enhanced_czsc_func import (
    get_enhanced_technical_indicators,
    calculate_real_fund_flow_strength,
    calculate_market_sentiment_indicators,
    calculate_multi_timeframe_coordination,
    get_real_confidence_metrics,
    analyze_real_gua_from_price_action,
    get_volume_profile_analysis,
    calculate_trend_strength_metrics,
    validate_data_quality
)
import json
from scipy.stats import norm
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

# 导入puppet交易库
import puppet

# 导入CZSC库
from czsc_master.czsc import CZSC, Freq, RawBar as CzscRawBar
from czsc_master.czsc.analyze import BI, FX # Bi, Xd

# 检查是否有增强版功能
try:
    from czsc.utils.corr import cross_correlation  # 测试特定功能
    CZSC_ENHANCED_AVAILABLE = True
except ImportError:
    CZSC_ENHANCED_AVAILABLE = False

# =============================================================================
# V3新增：市场状态和信号强度枚举
# =============================================================================

class TrendState(Enum):
    STRONG_UPTREND = "strong_uptrend"
    WEAK_UPTREND = "weak_uptrend"
    SIDEWAYS = "sideways"
    WEAK_DOWNTREND = "weak_downtrend"
    STRONG_DOWNTREND = "strong_downtrend"

class VolatilityState(Enum):
    EXTREMELY_LOW = "extremely_low"
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    EXTREMELY_HIGH = "extremely_high"

class LiquidityState(Enum):
    EXTREMELY_LOW = "extremely_low"
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    SUPER_HIGH = "super_high"

class SentimentState(Enum):
    EXTREME_PANIC = "extreme_panic"
    PANIC = "panic"
    NEUTRAL = "neutral"
    GREED = "greed"
    EXTREME_GREED = "extreme_greed"

class StructuralState(Enum):
    BREAKDOWN = "breakdown"
    WEAKENING = "weakening"
    STABLE = "stable"
    STRENGTHENING = "strengthening"
    BREAKOUT = "breakout"

class TransitionState(Enum):
    NO_TRANSITION = "no_transition"
    EARLY_TRANSITION = "early_transition"
    MID_TRANSITION = "mid_transition"
    LATE_TRANSITION = "late_transition"
    TRANSITION_COMPLETE = "transition_complete"

class SignalStrength(Enum):
    VERY_WEAK = 0.1
    WEAK = 0.3
    MEDIUM = 0.5
    STRONG = 0.7
    VERY_STRONG = 0.9

class MarketRegime(Enum):
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

# =============================================================================
# V3新增：增强数据结构
# =============================================================================

@dataclass
class RawBar:
    """原始K线数据结构"""
    symbol: str
    dt: datetime
    id: int
    freq: str
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float

@dataclass
class DimensionEvaluationResult:
    """维度评估结果"""
    dimension_name: str
    state: Any  # 对应的枚举状态
    score: float
    confidence: float
    signals: List[str]
    data_quality: str
    details: Optional[Dict[str, Any]] = None
    indicators: Optional[Dict[str, float]] = None

@dataclass
class MultiDimensionalMarketState:
    """多维度市场状态"""
    timestamp: datetime
    trend: DimensionEvaluationResult
    volatility: DimensionEvaluationResult
    liquidity: DimensionEvaluationResult
    sentiment: DimensionEvaluationResult
    structural: DimensionEvaluationResult
    transition: DimensionEvaluationResult
    composite_score: float
    overall_confidence: float
    market_regime: MarketRegime
    recommended_action: str
    risk_level: str

@dataclass
class SignalConflictResult:
    """信号冲突处理结果"""
    final_signal: int  # -1, 0, 1
    final_strength: float
    final_confidence: float
    conflict_reason: str
    resolution_method: str
    contributing_signals: List[Dict[str, Any]]

@dataclass
class DynamicWeights:
    """动态权重结果"""
    trend_weight: float
    volatility_weight: float
    liquidity_weight: float
    sentiment_weight: float
    structural_weight: float
    transition_weight: float
    adjustment_reason: str
    market_conditions: Dict[str, float]

@dataclass
class EnhancedAnalysisResult:
    """增强分析结果"""
    symbol: str
    timestamp: datetime
    market_state: MultiDimensionalMarketState
    signal_conflict_result: SignalConflictResult
    dynamic_weights: DynamicWeights
    composite_score: float
    recommendation: str
    confidence: float
    risk_assessment: Dict[str, Any]
    execution_details: Dict[str, Any]

@dataclass
class TimeframeCycleDivergence:
    """时间周期背驰分析结果"""
    timeframe: str
    divergence_type: str
    divergence_strength: float
    macd_area_ratio: float
    dif_peak_comparison: Dict[str, float]
    confirmation_bars: int
    cycle_position: str

@dataclass
class MultiTimeframeResonance:
    """多时间框架共振结果"""
    short_term: TimeframeCycleDivergence
    medium_term: TimeframeCycleDivergence
    long_term: TimeframeCycleDivergence
    resonance_score: float
    consensus_direction: str
    confidence_level: float
    transition_signal: str

@dataclass
class CzscBiStructure:
    """缠论笔结构数据"""
    bi_direction: str
    bi_start_price: float
    bi_end_price: float
    bi_amplitude: float
    bi_duration: int
    macd_area: float
    dif_peak: float

@dataclass
class TimeframeCycleDivergence:
    """时间周期背驰分析结果"""
    timeframe: str  # 时间框架
    divergence_type: str  # 背驰类型 
    divergence_strength: float  # 背驰强度 (0-1)
    macd_area_ratio: float  # MACD面积比率
    dif_peak_comparison: Dict[str, float]  # DIF峰值比较
    confirmation_bars: int  # 确认K线数量
    cycle_position: str  # 周期位置: early/middle/late

@dataclass  
class MultiTimeframeResonance:
    """多时间框架共振分析结果"""
    short_term: TimeframeCycleDivergence
    medium_term: TimeframeCycleDivergence  
    long_term: TimeframeCycleDivergence
    resonance_score: float  # 共振评分 (0-1)
    consensus_direction: str  # 共识方向: bullish/bearish/neutral
    confidence_level: float  # 置信水平 (0-1)
    transition_signal: str  # 变盘信号强度: strong/medium/weak/none

# =============================================================================
# 配置日志系统
# =============================================================================

class SafeFormatter(logging.Formatter):
    """安全的日志格式化器，避免编码问题"""
    def format(self, record):
        # 移除或替换可能导致编码问题的字符
        if hasattr(record, 'msg'):
            # 将emoji和特殊字符替换为简单文本
            msg = str(record.msg)
            # 替换常见的emoji
            msg = msg.replace('🔄', '[CYCLE]')
            msg = msg.replace('✅', '[OK]')
            msg = msg.replace('❌', '[ERROR]')
            msg = msg.replace('⚠️', '[WARNING]')
            msg = msg.replace('🎯', '[TARGET]')
            msg = msg.replace('📊', '[CHART]')
            msg = msg.replace('🚀', '[ROCKET]')
            msg = msg.replace('🔧', '[TOOL]')
            msg = msg.replace('💡', '[IDEA]')
            record.msg = msg
        return super().format(record)

# 配置文件日志（保留emoji）
file_handler = logging.FileHandler('fund_trading_v3.log', encoding='utf-8')
file_handler.setLevel(logging.INFO)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)

# 配置控制台日志（移除emoji）
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    handlers=[file_handler, console_handler]
)

logger = logging.getLogger(__name__) 

# =============================================================================
# 基础智能体架构
# =============================================================================

class BaseAgent(ABC):
    """
    @class BaseAgent
    @brief 智能体基类
    @details 定义智能体的基本接口和通用功能
    """
    
    def __init__(self, name: str, role: str):
        """
        @brief 初始化智能体
        @param name: 智能体名称
        @param role: 智能体角色
        """
        self.name = name
        self.role = role
        self.logger = logging.getLogger(f"{self.__class__.__name__}_{name}")
        self.memory = {}
        self.message_queue = deque(maxlen=100)
        
    @abstractmethod
    def process(self, data: Any) -> Any:
        """
        @brief 处理数据的抽象方法
        @param data: 输入数据
        @return: 处理结果
        """
        pass
        
    def communicate(self, message: Dict[str, Any]) -> None:
        """
        @brief 发送消息给其他智能体
        @param message: 消息内容
        """
        self.logger.info(f"Sending message: {message}")
        
    def receive_message(self, message: Dict[str, Any]) -> None:
        """
        @brief 接收来自其他智能体的消息
        @param message: 消息内容
        """
        self.message_queue.append(message)
        self.logger.info(f"Received message: {message}")
        
    def store_in_memory(self, key: str, value: Any) -> None:
        """
        @brief 存储信息到内存
        @param key: 键
        @param value: 值
        """
        self.memory[key] = value
        
    def retrieve_from_memory(self, key: str) -> Optional[Any]:
        """
        @brief 从内存中检索信息
        @param key: 键
        @return: 存储的值，如果不存在则返回None
        """
        return self.memory.get(key)

# =============================================================================
# 传统智能体类（保留原有功能）
# =============================================================================

class TechnicalAgent(BaseAgent):
    """
    @class TechnicalAgent
    @brief 技术分析智能体
    @details 负责基金的技术分析，包括趋势判断、买卖信号生成等
    """
    
    def __init__(self, name: str = "TechnicalAgent"):
        super().__init__(name, "technical_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 处理技术分析请求
        @param data: 包含基金代码的数据
        @return: 技术分析结果
        """
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            analysis_result = self.analyze_fund_trend(fund_code)
            return analysis_result
        except Exception as e:
            self.logger.error(f"Technical analysis failed for {fund_code}: {str(e)}")
            return {'error': str(e)}
    
    def analyze_fund_trend(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 分析基金趋势 - 完全基于真实数据
        @param fund_code: 基金代码
        @return: 分析结果
        """
        try:
            # 获取增强版技术指标
            tech_data = get_enhanced_technical_indicators(fund_code, 'D')
            indicators = tech_data['indicators']
            
            # 数据质量验证
            quality_result = validate_data_quality(fund_code, tech_data)
            if not quality_result['overall_quality']:
                raise ValueError(f"Data quality issues: {quality_result['issues']}")
            
            # 计算置信度
            confidence_score = get_real_confidence_metrics(fund_code, indicators)
            
            # 生成买卖信号 - 基于多个真实指标确认
            signal_conditions = {
                'ma_trend': indicators['ma5'] > indicators['ma20'],
                'rsi_condition': 30 < indicators['rsi'] < 70,  # RSI不在极值区域
                'macd_bullish': indicators['macd_bullish'],
                'volume_confirmation': indicators['volume_ratio'] > 1.2,
                'bb_position': 0.2 < indicators['bb_position'] < 0.8  # 布林带中性区域
            }
            
            # 计算信号强度
            positive_signals = sum([
                signal_conditions['ma_trend'],
                signal_conditions['macd_bullish'],
                signal_conditions['volume_confirmation']
            ])
            
            # 风险控制信号
            risk_signals = sum([
                not signal_conditions['rsi_condition'],  # RSI极值
                not signal_conditions['bb_position']     # 布林带极值
            ])
            
            # 综合判断
            if positive_signals >= 2 and risk_signals == 0:
                buy_signal = True
                if positive_signals == 3 and indicators['volume_ratio'] > 2.0:
                    signal_strength = 'strong'
                else:
                    signal_strength = 'medium'
            elif positive_signals >= 1 and risk_signals <= 1:
                buy_signal = True
                signal_strength = 'weak'
            else:
                buy_signal = False
                signal_strength = 'none'
            
            # 趋势强度分析
            trend_data = calculate_trend_strength_metrics(fund_code)
            
            return {
                'fund_code': fund_code,
                'buy_signal': buy_signal,
                'signal_strength': signal_strength,
                'confidence_score': confidence_score,
                'technical_indicators': {
                    'ma5': indicators['ma5'],
                    'ma20': indicators['ma20'],
                    'ma60': indicators['ma60'],
                    'rsi': indicators['rsi'],
                    'macd': indicators['macd'],
                    'macd_signal': indicators['macd_signal'],
                    'bb_position': indicators['bb_position'],
                    'volume_ratio': indicators['volume_ratio'],
                    'atr_ratio': indicators['atr_ratio']
                },
                'trend_analysis': trend_data,
                'signal_details': signal_conditions,
                'analysis_time': datetime.now().isoformat(),
                'data_quality': quality_result['quality_score'],
                'data_source': 'real_quantaxis'
            }

        except Exception as e:
            self.logger.error(f"Failed to analyze fund trend for {fund_code}: {str(e)}")
            # 不再使用模拟数据，直接返回错误
            return {
                'fund_code': fund_code,
                'error': str(e),
                'buy_signal': False,
                'signal_strength': 'none',
                'confidence_score': 0.0,
                'data_source': 'error'
            }
    


class GuaAnalysisAgent(BaseAgent):
    """
    @class GuaAnalysisAgent
    @brief 卦象分析智能体
    @details 负责基于易经卦象的市场分析
    """
    
    def __init__(self, name: str = "GuaAnalysisAgent"):
        super().__init__(name, "gua_analysis")
        # 定义64卦
        self.gua_64 = [
            "乾", "坤", "屯", "蒙", "需", "讼", "师", "比",
            "小畜", "履", "泰", "否", "同人", "大有", "谦", "豫",
            "随", "蛊", "临", "观", "噬嗑", "贲", "剥", "复",
            "无妄", "大畜", "颐", "大过", "坎", "离", "咸", "恒",
            "遁", "大壮", "晋", "明夷", "家人", "睽", "蹇", "解",
            "损", "益", "夬", "姤", "萃", "升", "困", "井",
            "革", "鼎", "震", "艮", "渐", "归妹", "丰", "旅",
            "巽", "兑", "涣", "节", "中孚", "小过", "既济", "未济"
        ]
        # 定义选股卦象（吉卦）
        self.select_gua_list = ["乾", "坤", "泰", "大有", "谦", "豫", "临", "益", "升", "井", "震", "既济"]
        # 定义买入卦象
        self.buy_gua_list = ["需", "比", "小畜", "履", "同人", "随", "观", "复", "大畜", "离", "咸", "解", "萃", "鼎", "渐", "丰", "巽", "涣", "中孚"]
        # 定义卖出卦象
        self.sell_gua_list = ["讼", "师", "否", "蛊", "噬嗑", "剥", "无妄", "遁", "明夷", "睽", "蹇", "损", "夬", "困", "革", "艮", "归妹", "旅", "兑", "节", "小过", "未济"]
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理卦象分析请求"""
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            gua_result = self.analyze_gua(fund_code)
            return gua_result
        except Exception as e:
            self.logger.error(f"Gua analysis failed for {fund_code}: {str(e)}")
            return {'error': str(e)}
    
    def analyze_gua(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 分析卦象 - 基于真实价格行为
        @param fund_code: 基金代码
        @return: 卦象分析结果
        """
        try:
            # 使用真实价格行为分析卦象
            gua_result = analyze_real_gua_from_price_action(fund_code)
            
            # 验证数据质量
            quality_result = validate_data_quality(fund_code, gua_result)
            if not quality_result['overall_quality']:
                raise ValueError(f"Gua analysis data quality issues: {quality_result['issues']}")
            
            # 提取卦象信息
            main_gua = gua_result['main_gua']
            gua_lines = gua_result['gua_lines']
            
            # 判断卦象类型（基于真实分析结果）
            is_select_gua = gua_result['is_select_gua']
            is_buy_gua = gua_result['is_buy_gua']
            is_sell_gua = gua_result['is_sell_gua']
            
            # 基于真实数据的卦象评分
            gua_score = 0.0
            if is_select_gua:
                # 强势卦象，加权真实强度
                gua_score = gua_result['strength_score'] * 1.2
            elif is_buy_gua:
                gua_score = gua_result['strength_score'] * 0.8
            elif is_sell_gua:
                gua_score = -gua_result['strength_score'] * 0.8
            else:
                gua_score = 0.0
            
            # 生成配卦（基于主卦的变化）
            gua_list = [main_gua]
            if len(gua_lines) >= 3:
                # 根据阴阳爻组合生成配卦
                pattern_variants = self._generate_pattern_variants(gua_lines)
                gua_list.extend(pattern_variants[:2])  # 最多3个卦
            
            return {
                'fund_code': fund_code,
                'gua_list': gua_list,
                'main_gua': main_gua,
                'gua_pattern': gua_result['gua_pattern'],
                'gua_lines': gua_lines,
                'is_select_gua': is_select_gua,
                'is_buy_gua': is_buy_gua,
                'is_sell_gua': is_sell_gua,
                'gua_score': gua_score,
                'strength_score': gua_result['strength_score'],
                'price_momentum': gua_result['price_momentum'],
                'volume_momentum': gua_result['volume_momentum'],
                'analysis_time': gua_result['analysis_time'],
                'gua_interpretation': f"主卦：{main_gua}，价格动量：{gua_result['price_momentum']:.2%}",
                'confidence': gua_result['confidence'],
                'data_quality': quality_result['quality_score'],
                'data_source': 'real_price_action'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze gua for {fund_code}: {str(e)}")
            # 不再使用随机卦象，直接返回错误
            return {
                'fund_code': fund_code,
                'error': str(e),
                'gua_score': 0.0,
                'is_select_gua': False,
                'is_buy_gua': False,
                'is_sell_gua': False,
                'data_source': 'error'
            }
    
    def _generate_pattern_variants(self, gua_lines: List[str]) -> List[str]:
        """基于阴阳爻生成配卦变体"""
        try:
            variants = []
            if len(gua_lines) >= 3:
                # 简化的配卦生成逻辑
                base_pattern = ''.join(gua_lines[:3])
                
                # 生成互卦（中间三爻的变化）
                if len(gua_lines) >= 5:
                    mutual_pattern = ''.join(gua_lines[1:4])
                    mutual_gua = self._pattern_to_gua(mutual_pattern)
                    if mutual_gua and mutual_gua != '未知卦象':
                        variants.append(mutual_gua)
                
                # 生成综卦（阴阳互换）
                inverted_pattern = ''.join(['阴' if line == '阳' else '阳' for line in gua_lines[:3]])
                inverted_gua = self._pattern_to_gua(inverted_pattern)
                if inverted_gua and inverted_gua != '未知卦象':
                    variants.append(inverted_gua)
            
            return variants
        except Exception:
            return []
    
    def _pattern_to_gua(self, pattern: str) -> str:
        """将阴阳爻模式转换为卦名"""
        try:
            # 简化的卦象映射
            pattern_mapping = {
                '阳阳阳': '乾为天',
                '阴阴阴': '坤为地', 
                '阳阴阳': '离为火',
                '阴阳阴': '坎为水',
                '阳阳阴': '兑为泽',
                '阴阴阳': '艮为山',
                '阳阴阴': '震为雷',
                '阴阳阳': '巽为风',
                '阳阴阳': '履',
                '阴阳阳': '泰',
                '阳阳阴': '否',
                '阴阴阳': '同人'
            }
            return pattern_mapping.get(pattern, '未知卦象')
        except Exception:
            return '未知卦象'

class FundFlowAgent(BaseAgent):
    """
    @class FundFlowAgent
    @brief 资金流向分析智能体
    @details 负责分析基金的资金流向和实时价格
    """
    
    def __init__(self, name: str = "FundFlowAgent"):
        super().__init__(name, "fund_flow_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理资金流向分析请求"""
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            # 获取资金流向数据
            flow_data = self.analyze_fund_flow(fund_code)
            return flow_data
        except Exception as e:
            self.logger.error(f"Fund flow analysis failed for {fund_code}: {str(e)}")
            return {'error': str(e)}
    
    def get_fund_change_rate(self, fund_code: str) -> float:
        """
        @brief 获取基金涨跌幅
        @param fund_code: 基金代码
        @return: 涨跌幅（百分比）
        """
        try:
            # 实际涨跌幅数据获取需要在这里实现
            raise NotImplementedError("Real change rate data source required")
        except Exception as e:
            self.logger.error(f"Failed to get change rate for {fund_code}: {str(e)}")
            return 0.0
    
    def get_real_time_price(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 获取实时价格数据 - 仅使用真实数据
        @param fund_code: 基金代码
        @return: 价格数据字典
        """
        try:
            # 优先获取实时行情数据
            try:
                quote_data = get_realtime_quote(fund_code)
                if 'error' not in quote_data:
                    return {
                        'fund_code': fund_code,
                        'price': quote_data['price'],
                        'change_rate': quote_data['change_rate'],
                        'volume': quote_data['volume'],
                        'amount': quote_data.get('amount', 0),
                        'bid1': quote_data.get('bid1', quote_data['price']),
                        'ask1': quote_data.get('ask1', quote_data['price']),
                        'timestamp': quote_data['timestamp'],
                        'data_source': 'real_time_quote'
                    }
            except Exception as quote_error:
                self.logger.warning(f"Real-time quote failed for {fund_code}: {quote_error}")
            
            # 备用：获取K线数据
            df = get_kline(fund_code, freq='D')
            if df.empty or len(df) < 1:
                raise ValueError(f"No historical data available for {fund_code}")
                
            latest_data = df.iloc[-1]
            current_price = float(latest_data['close'])
            
            # 计算涨跌幅
            if len(df) >= 2:
                prev_price = float(df.iloc[-2]['close'])
                change_rate = ((current_price - prev_price) / prev_price) * 100
            else:
                change_rate = 0.0
            
            # 估算买卖盘（基于高低价）
            day_range = float(latest_data['high']) - float(latest_data['low'])
            bid_estimate = current_price - (day_range * 0.001)  # 估算买一价
            ask_estimate = current_price + (day_range * 0.001)  # 估算卖一价
            
            return {
                'fund_code': fund_code,
                'price': current_price,
                'change_rate': change_rate,
                'volume': float(latest_data.get('vol', 0)),
                'amount': float(latest_data.get('amount', 0)),
                'high': float(latest_data['high']),
                'low': float(latest_data['low']),
                'open': float(latest_data['open']),
                'bid1': bid_estimate,
                'ask1': ask_estimate,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'historical_kline'
            }

        except Exception as e:
            self.logger.error(f"Failed to get real-time price for {fund_code}: {str(e)}")
            # 不再提供模拟数据，直接返回错误
            return {
                'fund_code': fund_code,
                'price': 0.0,
                'error': str(e),
                'data_source': 'error'
            }
    

    
    def analyze_fund_flow(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 分析基金资金流向 - 完全基于真实数据
        @param fund_code: 基金代码
        @return: 资金流向分析结果
        """
        try:
            # 获取真实价格数据
            price_data = self.get_real_time_price(fund_code)

            if 'error' in price_data:
                raise ValueError(f"Failed to get price data: {price_data['error']}")
            
            # 计算真实资金流向强度
            flow_strength = calculate_real_fund_flow_strength(fund_code)
            
            # 获取成交量分析
            volume_analysis = get_volume_profile_analysis(fund_code)
            
            # 数据质量验证
            quality_result = validate_data_quality(fund_code, {
                'symbol': fund_code,
                'timestamp': price_data['timestamp'],
                'price_data': price_data,
                'flow_data': flow_strength
            })
            
            if not quality_result['overall_quality']:
                self.logger.warning(f"Data quality issues for {fund_code}: {quality_result['issues']}")
            
            # 综合流动性评估
            current_volume = price_data.get('volume', 0)
            current_amount = price_data.get('amount', 0)
            change_rate = price_data.get('change_rate', 0.0)
            
            # 基于真实指标判断流动性
            high_liquidity = (
                flow_strength['liquidity_score'] > 0.6 and
                volume_analysis['relative_volume'] > 1.2 and
                abs(change_rate) < 5.0  # 价格变动不过于剧烈
            )
            
            # 资金流向方向判断
            if flow_strength['net_flow_strength'] > 0.3:
                capital_flow_direction = '净流入'
            elif flow_strength['net_flow_strength'] < -0.3:
                capital_flow_direction = '净流出'
            else:
                capital_flow_direction = '平衡'
            
            # 成交量水平分类（基于真实数据）
            volume_level = volume_analysis['volume_quality']
            
            # 买卖压力分析
            buy_pressure = flow_strength['buy_pressure']
            sell_pressure = 1.0 - buy_pressure
            
            return {
                'fund_code': fund_code,
                'price_data': price_data,
                'flow_strength': flow_strength,
                'volume_analysis': volume_analysis,
                'current_volume': current_volume,
                'current_amount': current_amount,
                'volume_ratio': volume_analysis['relative_volume'],
                'volume_level': volume_level,
                'capital_flow': capital_flow_direction,
                'net_flow_strength': flow_strength['net_flow_strength'],
                'buy_pressure': buy_pressure,
                'sell_pressure': sell_pressure,
                'high_liquidity': high_liquidity,
                'liquidity_score': flow_strength['liquidity_score'],
                'flow_ratio': flow_strength['flow_ratio'],
                'volume_activity': flow_strength['volume_activity'],
                'change_rate': change_rate,
                'data_quality': quality_result['quality_score'],
                'analysis_time': datetime.now().isoformat(),
                'data_source': 'real_flow_analysis'
            }

        except Exception as e:
            self.logger.error(f"Failed to analyze fund flow for {fund_code}: {str(e)}")
            # 不再提供模拟数据，直接返回错误
            return {
                'fund_code': fund_code,
                'error': str(e),
                'high_liquidity': False,
                'liquidity_score': 0.0,
                'data_source': 'error'
            }
    


# =============================================================================
# V3核心功能：六大维度评估器
# =============================================================================

class TrendEvaluator:
    """
    @class TrendEvaluator
    @brief 趋势维度评估器
    @details 负责评估市场趋势的强度、方向和可持续性
    """
    
    def __init__(self):
        self.name = "TrendEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估趋势维度
        @param data: 市场数据
        @return: 趋势评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            technical_data = data.get('technical_data', {})
            
            # 获取技术指标数据
            ma5 = technical_data.get('ma5', 0)
            ma20 = technical_data.get('ma20', 0)
            rsi = technical_data.get('rsi', 50)
            macd = technical_data.get('macd', 0)
            
            current_price = price_data.get('price', 0)
            change_rate = price_data.get('change_rate', 0)
            
            # 趋势强度计算
            trend_signals = []
            trend_score = 0.0
            
            # 1. 移动平均线趋势判断
            if ma5 > ma20:
                ma_trend = "上涨"
                ma_strength = min(1.0, (ma5 - ma20) / ma20 * 10)
                trend_score += ma_strength * 0.3
                trend_signals.append(f"MA5>MA20(+{ma_strength:.2f})")
            elif ma5 < ma20:
                ma_trend = "下跌"
                ma_strength = min(1.0, (ma20 - ma5) / ma20 * 10)
                trend_score -= ma_strength * 0.3
                trend_signals.append(f"MA5<MA20(-{ma_strength:.2f})")
            else:
                ma_trend = "横盘"
                trend_signals.append("MA5=MA20(0)")
            
            # 2. RSI趋势确认
            if rsi > 70:
                rsi_signal = "超买"
                rsi_strength = min(1.0, (rsi - 70) / 30)
                trend_score += rsi_strength * 0.2
                trend_signals.append(f"RSI超买({rsi:.1f})")
            elif rsi < 30:
                rsi_signal = "超卖"
                rsi_strength = min(1.0, (30 - rsi) / 30)
                trend_score -= rsi_strength * 0.2
                trend_signals.append(f"RSI超卖({rsi:.1f})")
            else:
                rsi_signal = "正常"
                trend_signals.append(f"RSI正常({rsi:.1f})")
            
            # 3. MACD趋势确认
            if macd > 0:
                macd_signal = "向上"
                macd_strength = min(1.0, abs(macd) * 100)
                trend_score += macd_strength * 0.25
                trend_signals.append(f"MACD向上(+{macd:.3f})")
            elif macd < 0:
                macd_signal = "向下"
                macd_strength = min(1.0, abs(macd) * 100)
                trend_score -= macd_strength * 0.25
                trend_signals.append(f"MACD向下({macd:.3f})")
            else:
                macd_signal = "中性"
                trend_signals.append("MACD中性")
            
            # 4. 价格变化趋势
            if abs(change_rate) > 2:
                price_signal = "强势" if change_rate > 0 else "弱势"
                price_strength = min(1.0, abs(change_rate) / 5)
                if change_rate > 0:
                    trend_score += price_strength * 0.25
                else:
                    trend_score -= price_strength * 0.25
                trend_signals.append(f"价格{price_signal}({change_rate:+.2f}%)")
            else:
                price_signal = "稳定"
                trend_signals.append(f"价格稳定({change_rate:+.2f}%)")
            
            # 趋势状态判断
            if trend_score >= 0.7:
                trend_state = TrendState.STRONG_UPTREND
            elif trend_score >= 0.3:
                trend_state = TrendState.WEAK_UPTREND
            elif trend_score >= -0.3:
                trend_state = TrendState.SIDEWAYS
            elif trend_score >= -0.7:
                trend_state = TrendState.WEAK_DOWNTREND
            else:
                trend_state = TrendState.STRONG_DOWNTREND
            
            # 置信度计算
            signal_consistency = len([s for s in [ma_trend, rsi_signal, macd_signal, price_signal] 
                                    if any(word in s for word in ['上涨', '向上', '超买', '强势'])])
            confidence = min(0.95, max(0.1, signal_consistency / 4.0 + abs(trend_score) * 0.3))
            
            # 数据质量评估
            data_quality = "good" if all([ma5, ma20, current_price]) else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="趋势",
                state=trend_state,
                score=trend_score,
                confidence=confidence,
                signals=trend_signals,
                data_quality=data_quality,
                details={
                    'ma_trend': ma_trend,
                    'rsi_signal': rsi_signal,
                    'macd_signal': macd_signal,
                    'price_signal': price_signal
                },
                indicators={
                    'ma5': ma5,
                    'ma20': ma20,
                    'rsi': rsi,
                    'macd': macd,
                    'change_rate': change_rate
                }
            )
            
        except Exception as e:
            self.logger.error(f"Trend evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="趋势",
                state=TrendState.SIDEWAYS,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )

class VolatilityEvaluator:
    """
    @class VolatilityEvaluator
    @brief 波动性维度评估器
    @details 负责评估市场波动性水平和风险程度
    """
    
    def __init__(self):
        self.name = "VolatilityEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估波动性维度
        @param data: 市场数据
        @return: 波动性评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            
            # 获取价格变化数据
            change_rate = abs(price_data.get('change_rate', 0))
            volume_ratio = data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            
            # 波动性评分计算
            volatility_signals = []
            volatility_score = 0.0
            
            # 1. 价格波动度
            if change_rate >= 5:
                price_volatility = "极高"
                vol_strength = min(1.0, change_rate / 10)
                volatility_score += vol_strength
                volatility_signals.append(f"价格波动极高({change_rate:.2f}%)")
            elif change_rate >= 3:
                price_volatility = "高"
                vol_strength = min(1.0, change_rate / 5)
                volatility_score += vol_strength * 0.8
                volatility_signals.append(f"价格波动高({change_rate:.2f}%)")
            elif change_rate >= 1:
                price_volatility = "正常"
                vol_strength = change_rate / 3
                volatility_score += vol_strength * 0.5
                volatility_signals.append(f"价格波动正常({change_rate:.2f}%)")
            elif change_rate >= 0.5:
                price_volatility = "低"
                vol_strength = change_rate / 1
                volatility_score += vol_strength * 0.3
                volatility_signals.append(f"价格波动低({change_rate:.2f}%)")
            else:
                price_volatility = "极低"
                volatility_score += 0.1
                volatility_signals.append(f"价格波动极低({change_rate:.2f}%)")
            
            # 2. 成交量波动
            if volume_ratio >= 2.0:
                volume_volatility = "异常放量"
                vol_vol_strength = min(1.0, (volume_ratio - 1) / 2)
                volatility_score += vol_vol_strength * 0.5
                volatility_signals.append(f"异常放量({volume_ratio:.2f}倍)")
            elif volume_ratio >= 1.5:
                volume_volatility = "放量"
                vol_vol_strength = (volume_ratio - 1) / 1
                volatility_score += vol_vol_strength * 0.3
                volatility_signals.append(f"放量({volume_ratio:.2f}倍)")
            elif volume_ratio <= 0.5:
                volume_volatility = "缩量"
                vol_vol_strength = (1 - volume_ratio) / 0.5
                volatility_score += vol_vol_strength * 0.2
                volatility_signals.append(f"缩量({volume_ratio:.2f}倍)")
            else:
                volume_volatility = "正常"
                volatility_signals.append(f"成交量正常({volume_ratio:.2f}倍)")
            
            # 波动性状态判断
            if volatility_score >= 0.9:
                volatility_state = VolatilityState.EXTREMELY_HIGH
            elif volatility_score >= 0.7:
                volatility_state = VolatilityState.HIGH
            elif volatility_score >= 0.4:
                volatility_state = VolatilityState.NORMAL
            elif volatility_score >= 0.2:
                volatility_state = VolatilityState.LOW
            else:
                volatility_state = VolatilityState.EXTREMELY_LOW
            
            # 置信度计算（基于数据可靠性）
            confidence = min(0.95, max(0.3, 0.8 - abs(volatility_score - 0.5)))
            
            # 数据质量评估
            data_quality = "good" if change_rate >= 0 and volume_ratio > 0 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="波动性",
                state=volatility_state,
                score=volatility_score,
                confidence=confidence,
                signals=volatility_signals,
                data_quality=data_quality,
                details={
                    'price_volatility': price_volatility,
                    'volume_volatility': volume_volatility
                },
                indicators={
                    'change_rate': change_rate,
                    'volume_ratio': volume_ratio,
                    'volatility_score': volatility_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Volatility evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="波动性",
                state=VolatilityState.NORMAL,
                score=0.5,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )

class LiquidityEvaluator:
    """
    @class LiquidityEvaluator
    @brief 流动性维度评估器
    @details 负责评估市场流动性水平和交易活跃度
    """
    
    def __init__(self):
        self.name = "LiquidityEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估流动性维度
        @param data: 市场数据
        @return: 流动性评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            flow_data = data.get('flow_data', {})
            
            # 获取流动性相关数据
            high_liquidity = flow_data.get('high_liquidity', False)
            volume = flow_data.get('price_data', {}).get('volume', 0)
            volume_ratio = flow_data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            
            # 流动性评分计算
            liquidity_signals = []
            liquidity_score = 0.0
            
            # 1. 基础流动性判断
            if high_liquidity:
                base_liquidity = "高"
                liquidity_score += 0.7
                liquidity_signals.append("基础流动性高")
            else:
                base_liquidity = "低"
                liquidity_score += 0.3
                liquidity_signals.append("基础流动性低")
            
            # 2. 成交量水平
            if volume >= 8000:
                volume_level = "超高"
                vol_strength = min(1.0, volume / 10000)
                liquidity_score += vol_strength * 0.3
                liquidity_signals.append(f"成交量超高({volume})")
            elif volume >= 5000:
                volume_level = "高"
                vol_strength = volume / 8000
                liquidity_score += vol_strength * 0.25
                liquidity_signals.append(f"成交量高({volume})")
            elif volume >= 2000:
                volume_level = "正常"
                vol_strength = volume / 5000
                liquidity_score += vol_strength * 0.2
                liquidity_signals.append(f"成交量正常({volume})")
            elif volume >= 1000:
                volume_level = "低"
                vol_strength = volume / 2000
                liquidity_score += vol_strength * 0.1
                liquidity_signals.append(f"成交量低({volume})")
            else:
                volume_level = "极低"
                liquidity_score += 0.05
                liquidity_signals.append(f"成交量极低({volume})")
            
            # 3. 成交量活跃度
            if volume_ratio >= 1.5:
                activity_level = "活跃"
                activity_strength = min(1.0, (volume_ratio - 1) / 1)
                liquidity_score += activity_strength * 0.2
                liquidity_signals.append(f"交易活跃({volume_ratio:.2f}倍)")
            elif volume_ratio >= 0.8:
                activity_level = "正常"
                liquidity_signals.append(f"交易正常({volume_ratio:.2f}倍)")
            else:
                activity_level = "不活跃"
                liquidity_score *= 0.8  # 降低整体分数
                liquidity_signals.append(f"交易不活跃({volume_ratio:.2f}倍)")
            
            # 流动性状态判断
            if liquidity_score >= 0.9:
                liquidity_state = LiquidityState.SUPER_HIGH
            elif liquidity_score >= 0.7:
                liquidity_state = LiquidityState.HIGH
            elif liquidity_score >= 0.5:
                liquidity_state = LiquidityState.NORMAL
            elif liquidity_score >= 0.3:
                liquidity_state = LiquidityState.LOW
            else:
                liquidity_state = LiquidityState.EXTREMELY_LOW
            
            # 置信度计算
            confidence = min(0.95, max(0.2, 0.6 + liquidity_score * 0.3))
            
            # 数据质量评估
            data_quality = "good" if volume > 0 and volume_ratio > 0 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="流动性",
                state=liquidity_state,
                score=liquidity_score,
                confidence=confidence,
                signals=liquidity_signals,
                data_quality=data_quality,
                details={
                    'base_liquidity': base_liquidity,
                    'volume_level': volume_level,
                    'activity_level': activity_level
                },
                indicators={
                    'volume': volume,
                    'volume_ratio': volume_ratio,
                    'high_liquidity': high_liquidity,
                    'liquidity_score': liquidity_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Liquidity evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="流动性",
                state=LiquidityState.NORMAL,
                score=0.5,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )

class SentimentEvaluator:
    """
    @class SentimentEvaluator
    @brief 情绪维度评估器
    @details 负责评估市场情绪和投资者心理状态
    """
    
    def __init__(self):
        self.name = "SentimentEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估情绪维度
        @param data: 市场数据
        @return: 情绪评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            technical_data = data.get('technical_data', {})
            gua_data = data.get('gua_data', {})
            
            # 获取情绪相关数据
            change_rate = price_data.get('change_rate', 0)
            rsi = technical_data.get('rsi', 50)
            volume_ratio = data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            gua_score = gua_data.get('gua_score', 0)
            
            # 情绪评分计算
            sentiment_signals = []
            sentiment_score = 0.0
            
            # 1. RSI情绪指标
            if rsi >= 80:
                rsi_sentiment = "极度贪婪"
                rsi_strength = min(1.0, (rsi - 80) / 20)
                sentiment_score += 0.9 + rsi_strength * 0.1
                sentiment_signals.append(f"RSI极度贪婪({rsi:.1f})")
            elif rsi >= 70:
                rsi_sentiment = "贪婪"
                rsi_strength = (rsi - 70) / 10
                sentiment_score += 0.7 + rsi_strength * 0.2
                sentiment_signals.append(f"RSI贪婪({rsi:.1f})")
            elif rsi <= 20:
                rsi_sentiment = "极度恐慌"
                rsi_strength = min(1.0, (20 - rsi) / 20)
                sentiment_score -= 0.9 + rsi_strength * 0.1
                sentiment_signals.append(f"RSI极度恐慌({rsi:.1f})")
            elif rsi <= 30:
                rsi_sentiment = "恐慌"
                rsi_strength = (30 - rsi) / 10
                sentiment_score -= 0.7 + rsi_strength * 0.2
                sentiment_signals.append(f"RSI恐慌({rsi:.1f})")
            else:
                rsi_sentiment = "中性"
                neutral_factor = 1.0 - abs(rsi - 50) / 20
                sentiment_score += (neutral_factor - 0.5) * 0.2
                sentiment_signals.append(f"RSI中性({rsi:.1f})")
            
            # 2. 价格变化情绪
            if change_rate >= 4:
                price_sentiment = "狂热"
                price_strength = min(1.0, change_rate / 8)
                sentiment_score += 0.3 + price_strength * 0.2
                sentiment_signals.append(f"价格狂热(+{change_rate:.2f}%)")
            elif change_rate >= 2:
                price_sentiment = "乐观"
                price_strength = change_rate / 4
                sentiment_score += 0.1 + price_strength * 0.2
                sentiment_signals.append(f"价格乐观(+{change_rate:.2f}%)")
            elif change_rate <= -4:
                price_sentiment = "恐慌"
                price_strength = min(1.0, abs(change_rate) / 8)
                sentiment_score -= 0.3 + price_strength * 0.2
                sentiment_signals.append(f"价格恐慌({change_rate:.2f}%)")
            elif change_rate <= -2:
                price_sentiment = "悲观"
                price_strength = abs(change_rate) / 4
                sentiment_score -= 0.1 + price_strength * 0.2
                sentiment_signals.append(f"价格悲观({change_rate:.2f}%)")
            else:
                price_sentiment = "平静"
                sentiment_signals.append(f"价格平静({change_rate:+.2f}%)")
            
            # 3. 成交量情绪
            if volume_ratio >= 2.0:
                volume_sentiment = "高涨"
                vol_strength = min(1.0, (volume_ratio - 1) / 2)
                sentiment_score += vol_strength * 0.15
                sentiment_signals.append(f"成交高涨({volume_ratio:.2f}倍)")
            elif volume_ratio <= 0.5:
                volume_sentiment = "低迷"
                vol_strength = (1 - volume_ratio) / 0.5
                sentiment_score -= vol_strength * 0.15
                sentiment_signals.append(f"成交低迷({volume_ratio:.2f}倍)")
            else:
                volume_sentiment = "正常"
                sentiment_signals.append(f"成交正常({volume_ratio:.2f}倍)")
            
            # 4. 卦象情绪（传统智慧）
            if gua_score >= 0.7:
                gua_sentiment = "吉"
                sentiment_score += gua_score * 0.1
                sentiment_signals.append(f"卦象吉利({gua_score:.2f})")
            elif gua_score <= -0.7:
                gua_sentiment = "凶"
                sentiment_score += gua_score * 0.1
                sentiment_signals.append(f"卦象凶险({gua_score:.2f})")
            else:
                gua_sentiment = "中性"
                sentiment_signals.append(f"卦象中性({gua_score:.2f})")
            
            # 情绪状态判断
            if sentiment_score >= 0.8:
                sentiment_state = SentimentState.EXTREME_GREED
            elif sentiment_score >= 0.3:
                sentiment_state = SentimentState.GREED
            elif sentiment_score >= -0.3:
                sentiment_state = SentimentState.NEUTRAL
            elif sentiment_score >= -0.8:
                sentiment_state = SentimentState.PANIC
            else:
                sentiment_state = SentimentState.EXTREME_PANIC
            
            # 置信度计算
            signal_strength = abs(sentiment_score)
            confidence = min(0.95, max(0.3, 0.5 + signal_strength * 0.4))
            
            # 数据质量评估
            data_quality = "good" if all([rsi, change_rate is not None]) else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="情绪",
                state=sentiment_state,
                score=sentiment_score,
                confidence=confidence,
                signals=sentiment_signals,
                data_quality=data_quality,
                details={
                    'rsi_sentiment': rsi_sentiment,
                    'price_sentiment': price_sentiment,
                    'volume_sentiment': volume_sentiment,
                    'gua_sentiment': gua_sentiment
                },
                indicators={
                    'rsi': rsi,
                    'change_rate': change_rate,
                    'volume_ratio': volume_ratio,
                    'gua_score': gua_score,
                    'sentiment_score': sentiment_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Sentiment evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="情绪",
                state=SentimentState.NEUTRAL,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )

class StructuralEvaluator:
    """
    @class StructuralEvaluator
    @brief 结构维度评估器
    @details 负责评估市场结构的稳定性和变化趋势
    """
    
    def __init__(self):
        self.name = "StructuralEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估结构维度
        @param data: 市场数据
        @return: 结构评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            technical_data = data.get('technical_data', {})
            price_data = data.get('price_data', {})
            
            # 获取结构相关数据
            ma5 = technical_data.get('ma5', 0)
            ma20 = technical_data.get('ma20', 0)
            current_price = price_data.get('price', 0)
            change_rate = price_data.get('change_rate', 0)
            
            # 结构评分计算
            structural_signals = []
            structural_score = 0.0
            
            # 1. 移动平均线结构
            if ma5 > 0 and ma20 > 0:
                ma_ratio = ma5 / ma20
                if ma_ratio >= 1.05:
                    ma_structure = "强势上升"
                    ma_strength = min(1.0, (ma_ratio - 1) / 0.1)
                    structural_score += ma_strength * 0.4
                    structural_signals.append(f"MA结构强势上升({ma_ratio:.3f})")
                elif ma_ratio >= 1.02:
                    ma_structure = "温和上升"
                    ma_strength = (ma_ratio - 1) / 0.05
                    structural_score += ma_strength * 0.3
                    structural_signals.append(f"MA结构温和上升({ma_ratio:.3f})")
                elif ma_ratio >= 0.98:
                    ma_structure = "横盘整理"
                    structural_score += 0.1
                    structural_signals.append(f"MA结构横盘整理({ma_ratio:.3f})")
                elif ma_ratio >= 0.95:
                    ma_structure = "温和下降"
                    ma_strength = (1 - ma_ratio) / 0.05
                    structural_score -= ma_strength * 0.3
                    structural_signals.append(f"MA结构温和下降({ma_ratio:.3f})")
                else:
                    ma_structure = "弱势下降"
                    ma_strength = min(1.0, (1 - ma_ratio) / 0.1)
                    structural_score -= ma_strength * 0.4
                    structural_signals.append(f"MA结构弱势下降({ma_ratio:.3f})")
            else:
                ma_structure = "数据不足"
                structural_signals.append("MA数据不足")
            
            # 2. 价格位置结构
            if current_price > 0 and ma20 > 0:
                price_position = current_price / ma20
                if price_position >= 1.1:
                    position_structure = "高位运行"
                    pos_strength = min(1.0, (price_position - 1) / 0.2)
                    structural_score += pos_strength * 0.3
                    structural_signals.append(f"价格高位运行({price_position:.3f})")
                elif price_position >= 1.05:
                    position_structure = "中高位"
                    pos_strength = (price_position - 1) / 0.1
                    structural_score += pos_strength * 0.2
                    structural_signals.append(f"价格中高位({price_position:.3f})")
                elif price_position >= 0.95:
                    position_structure = "中位运行"
                    structural_score += 0.05
                    structural_signals.append(f"价格中位运行({price_position:.3f})")
                elif price_position >= 0.9:
                    position_structure = "中低位"
                    pos_strength = (1 - price_position) / 0.1
                    structural_score -= pos_strength * 0.2
                    structural_signals.append(f"价格中低位({price_position:.3f})")
                else:
                    position_structure = "低位运行"
                    pos_strength = min(1.0, (1 - price_position) / 0.2)
                    structural_score -= pos_strength * 0.3
                    structural_signals.append(f"价格低位运行({price_position:.3f})")
            else:
                position_structure = "数据不足"
                structural_signals.append("价格位置数据不足")
            
            # 3. 变化稳定性
            if abs(change_rate) <= 1:
                stability = "高稳定性"
                stab_strength = 1.0 - abs(change_rate)
                structural_score += stab_strength * 0.3
                structural_signals.append(f"高稳定性({change_rate:+.2f}%)")
            elif abs(change_rate) <= 3:
                stability = "中等稳定性"
                stab_strength = 1.0 - abs(change_rate) / 3
                structural_score += stab_strength * 0.2
                structural_signals.append(f"中等稳定性({change_rate:+.2f}%)")
            else:
                stability = "低稳定性"
                instab_strength = min(1.0, abs(change_rate) / 5)
                structural_score -= instab_strength * 0.3
                structural_signals.append(f"低稳定性({change_rate:+.2f}%)")
            
            # 结构状态判断
            if structural_score >= 0.7:
                structural_state = StructuralState.BREAKOUT
            elif structural_score >= 0.3:
                structural_state = StructuralState.STRENGTHENING
            elif structural_score >= -0.3:
                structural_state = StructuralState.STABLE
            elif structural_score >= -0.7:
                structural_state = StructuralState.WEAKENING
            else:
                structural_state = StructuralState.BREAKDOWN
            
            # 置信度计算
            data_completeness = len([x for x in [ma5, ma20, current_price] if x > 0]) / 3
            confidence = min(0.95, max(0.2, data_completeness * 0.8 + abs(structural_score) * 0.2))
            
            # 数据质量评估
            data_quality = "good" if data_completeness >= 0.8 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="结构",
                state=structural_state,
                score=structural_score,
                confidence=confidence,
                signals=structural_signals,
                data_quality=data_quality,
                details={
                    'ma_structure': ma_structure,
                    'position_structure': position_structure,
                    'stability': stability
                },
                indicators={
                    'ma5': ma5,
                    'ma20': ma20,
                    'current_price': current_price,
                    'change_rate': change_rate,
                    'structural_score': structural_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Structural evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="结构",
                state=StructuralState.STABLE,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )

class TransitionEvaluator:
    """
    @class TransitionEvaluator
    @brief 转换维度评估器
    @details 负责评估市场转换的可能性和阶段
    """
    
    def __init__(self):
        self.name = "TransitionEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估转换维度
        @param data: 市场数据
        @return: 转换评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            timeframe_divergence = data.get('timeframe_divergence', {})
            
            # 获取转换相关数据
            transition_probability = timeframe_divergence.get('transition_probability', 0.0)
            consensus_direction = timeframe_divergence.get('consensus_direction', 'neutral')
            transition_signal = timeframe_divergence.get('transition_signal', 'none')
            resonance_score = timeframe_divergence.get('resonance_score', 0.0)
            
            # 转换评分计算
            transition_signals = []
            transition_score = 0.0
            
            # 1. 基础转换概率
            if transition_probability >= 0.8:
                base_transition = "高概率转换"
                prob_strength = transition_probability
                transition_score += prob_strength * 0.5
                transition_signals.append(f"高概率转换({transition_probability:.2f})")
            elif transition_probability >= 0.6:
                base_transition = "中等转换概率"
                prob_strength = transition_probability
                transition_score += prob_strength * 0.4
                transition_signals.append(f"中等转换概率({transition_probability:.2f})")
            elif transition_probability >= 0.3:
                base_transition = "低转换概率"
                prob_strength = transition_probability
                transition_score += prob_strength * 0.3
                transition_signals.append(f"低转换概率({transition_probability:.2f})")
            else:
                base_transition = "无明显转换"
                transition_score += 0.1
                transition_signals.append(f"无明显转换({transition_probability:.2f})")
            
            # 2. 转换信号强度
            signal_strength_map = {
                'strong': 0.9, 'medium': 0.6, 'weak': 0.3, 'none': 0.0
            }
            signal_strength = signal_strength_map.get(transition_signal, 0.0)
            transition_score += signal_strength * 0.3
            transition_signals.append(f"信号强度{transition_signal}({signal_strength:.1f})")
            
            # 3. 方向一致性
            if consensus_direction == 'bullish':
                direction_factor = "看涨一致"
                consensus_strength = 0.8
                transition_score += consensus_strength * 0.2
                transition_signals.append("看涨方向一致")
            elif consensus_direction == 'bearish':
                direction_factor = "看跌一致"
                consensus_strength = 0.8
                transition_score += consensus_strength * 0.2
                transition_signals.append("看跌方向一致")
            else:
                direction_factor = "方向不明"
                consensus_strength = 0.2
                transition_score += consensus_strength * 0.1
                transition_signals.append("转换方向不明")
            
            # 4. 共振评分
            if resonance_score >= 0.7:
                resonance_level = "强共振"
                resonance_strength = resonance_score
                transition_score += resonance_strength * 0.2
                transition_signals.append(f"强共振({resonance_score:.2f})")
            elif resonance_score >= 0.5:
                resonance_level = "中等共振"
                resonance_strength = resonance_score
                transition_score += resonance_strength * 0.15
                transition_signals.append(f"中等共振({resonance_score:.2f})")
            else:
                resonance_level = "弱共振"
                resonance_strength = resonance_score
                transition_score += resonance_strength * 0.1
                transition_signals.append(f"弱共振({resonance_score:.2f})")
            
            # 转换状态判断
            if transition_score >= 0.8:
                transition_state = TransitionState.TRANSITION_COMPLETE
            elif transition_score >= 0.6:
                transition_state = TransitionState.LATE_TRANSITION
            elif transition_score >= 0.4:
                transition_state = TransitionState.MID_TRANSITION
            elif transition_score >= 0.2:
                transition_state = TransitionState.EARLY_TRANSITION
            else:
                transition_state = TransitionState.NO_TRANSITION
            
            # 置信度计算
            data_availability = 1.0 if any([transition_probability, transition_signal != 'none']) else 0.3
            confidence = min(0.95, max(0.2, data_availability * 0.7 + transition_score * 0.3))
            
            # 数据质量评估
            data_quality = "good" if transition_probability > 0 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="转换",
                state=transition_state,
                score=transition_score,
                confidence=confidence,
                signals=transition_signals,
                data_quality=data_quality,
                details={
                    'base_transition': base_transition,
                    'direction_factor': direction_factor,
                    'resonance_level': resonance_level
                },
                indicators={
                    'transition_probability': transition_probability,
                    'consensus_direction': consensus_direction,
                    'transition_signal': transition_signal,
                    'resonance_score': resonance_score,
                    'transition_score': transition_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Transition evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="转换",
                state=TransitionState.NO_TRANSITION,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            ) 

# =============================================================================
# V3核心功能：智能组件（冲突解决、权重管理、质量评估）
# =============================================================================

class SignalConflictResolver:
    """
    @class SignalConflictResolver
    @brief 信号冲突解决器
    @details 负责解决不同维度之间的信号冲突，提供智能决策建议
    """
    
    def __init__(self):
        self.name = "SignalConflictResolver"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 冲突解决权重配置
        self.conflict_weights = {
            "趋势": 0.25,    # 趋势权重
            "波动性": 0.15,  # 波动性权重
            "流动性": 0.20,  # 流动性权重
            "情绪": 0.15,    # 情绪权重
            "结构": 0.15,    # 结构权重
            "转换": 0.10     # 转换权重
        }
        
        # 冲突类型识别阈值
        self.conflict_threshold = 0.3
        
    def resolve_conflicts(self, evaluations: Dict[str, DimensionEvaluationResult], 
                         fund_code: str) -> Dict[str, Any]:
        """
        @brief 解决维度间信号冲突
        @param evaluations: 各维度评估结果字典
        @param fund_code: 基金代码
        @return: 冲突解决结果
        """
        try:
            # 1. 识别冲突
            conflicts = self._identify_conflicts(evaluations)
            
            # 2. 分析冲突严重程度
            conflict_severity = self._analyze_conflict_severity(conflicts, evaluations)
            
            # 3. 应用解决策略
            resolution_strategy = self._determine_resolution_strategy(conflicts, evaluations)
            
            # 4. 计算调整后的权重
            adjusted_weights = self._calculate_adjusted_weights(conflicts, evaluations)
            
            # 5. 生成最终建议
            final_recommendation = self._generate_final_recommendation(
                evaluations, adjusted_weights, conflicts
            )
            
            return {
                'fund_code': fund_code,
                'conflicts_detected': conflicts,
                'conflict_severity': conflict_severity,
                'resolution_strategy': resolution_strategy,
                'adjusted_weights': adjusted_weights,
                'final_recommendation': final_recommendation,
                'confidence': self._calculate_resolution_confidence(conflicts, evaluations),
                'resolution_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Conflict resolution failed for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'conflicts_detected': [],
                'final_recommendation': 'hold'
            }
    
    def _identify_conflicts(self, evaluations: Dict[str, DimensionEvaluationResult]) -> List[Dict]:
        """识别维度间冲突"""
        conflicts = []
        dimensions = list(evaluations.keys())
        
        for i in range(len(dimensions)):
            for j in range(i + 1, len(dimensions)):
                dim1, dim2 = dimensions[i], dimensions[j]
                
                eval1 = evaluations[dim1]
                eval2 = evaluations[dim2]
                
                # 检查评分差异
                score_diff = abs(eval1.score - eval2.score)
                if score_diff >= self.conflict_threshold:
                    conflict_type = self._classify_conflict_type(eval1, eval2)
                    
                    conflicts.append({
                        'dimensions': [dim1, dim2],
                        'conflict_type': conflict_type,
                        'score_difference': score_diff,
                        'severity': 'high' if score_diff >= 0.6 else 'medium' if score_diff >= 0.4 else 'low'
                    })
        
        return conflicts
    
    def _classify_conflict_type(self, eval1: DimensionEvaluationResult, 
                               eval2: DimensionEvaluationResult) -> str:
        """分类冲突类型"""
        if eval1.score > 0 and eval2.score < 0:
            return "方向性冲突"
        elif eval1.score < 0 and eval2.score > 0:
            return "方向性冲突"
        elif abs(eval1.score - eval2.score) >= 0.6:
            return "强度冲突"
        else:
            return "微弱冲突"
    
    def _analyze_conflict_severity(self, conflicts: List[Dict], 
                                  evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """分析冲突严重程度"""
        if not conflicts:
            return "无冲突"
        
        high_severity_count = sum(1 for c in conflicts if c['severity'] == 'high')
        medium_severity_count = sum(1 for c in conflicts if c['severity'] == 'medium')
        
        if high_severity_count >= 2:
            return "严重冲突"
        elif high_severity_count >= 1 or medium_severity_count >= 3:
            return "中等冲突"
        else:
            return "轻微冲突"
    
    def _determine_resolution_strategy(self, conflicts: List[Dict], 
                                     evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """确定解决策略"""
        if not conflicts:
            return "无需解决"
        
        # 计算高置信度维度数量
        high_confidence_dims = sum(1 for eval_result in evaluations.values() 
                                  if eval_result.confidence >= 0.8)
        
        if high_confidence_dims >= 3:
            return "权重优先策略"  # 根据置信度调整权重
        elif len(conflicts) >= 3:
            return "保守策略"      # 倾向于保守决策
        else:
            return "平衡策略"      # 平衡各维度建议
    
    def _calculate_adjusted_weights(self, conflicts: List[Dict], 
                                   evaluations: Dict[str, DimensionEvaluationResult]) -> Dict[str, float]:
        """计算调整后的权重"""
        adjusted_weights = self.conflict_weights.copy()
        
        # 根据置信度调整权重
        for dim_name, eval_result in evaluations.items():
            if dim_name in adjusted_weights:
                confidence_factor = eval_result.confidence
                data_quality_factor = 1.0 if eval_result.data_quality == "good" else 0.7
                
                # 调整权重
                adjustment_factor = confidence_factor * data_quality_factor
                adjusted_weights[dim_name] *= adjustment_factor
        
        # 标准化权重
        total_weight = sum(adjusted_weights.values())
        if total_weight > 0:
            for dim_name in adjusted_weights:
                adjusted_weights[dim_name] /= total_weight
        
        return adjusted_weights
    
    def _generate_final_recommendation(self, evaluations: Dict[str, DimensionEvaluationResult],
                                     adjusted_weights: Dict[str, float],
                                     conflicts: List[Dict]) -> tuple:
        """生成最终推荐，并返回加权分数"""
        # 计算加权综合评分
        weighted_score = 0.0
        for dim_name, eval_result in evaluations.items():
            if dim_name in adjusted_weights:
                weighted_score += eval_result.score * adjusted_weights[dim_name]
        # 根据冲突情况调整决策阈值
        decision_threshold = 0.3
        if len(conflicts) >= 2:
            decision_threshold = 0.5  # 有冲突时提高决策阈值
        # 生成推荐
        if weighted_score >= decision_threshold:
            return ("buy", weighted_score)
        elif weighted_score <= -decision_threshold:
            return ("sell", weighted_score)
        else:
            return ("hold", weighted_score)
    
    def _calculate_resolution_confidence(self, conflicts: List[Dict], 
                                       evaluations: Dict[str, DimensionEvaluationResult]) -> float:
        """计算解决方案置信度"""
        if not conflicts:
            return 0.9
        
        # 基础置信度
        base_confidence = 0.7
        
        # 根据冲突数量调整
        conflict_penalty = min(0.3, len(conflicts) * 0.1)
        
        # 根据数据质量调整
        good_quality_count = sum(1 for eval_result in evaluations.values() 
                               if eval_result.data_quality == "good")
        quality_bonus = (good_quality_count / len(evaluations)) * 0.2
        
        final_confidence = base_confidence - conflict_penalty + quality_bonus
        return max(0.1, min(0.95, final_confidence))

class DynamicWeightManager:
    """
    @class DynamicWeightManager
    @brief 动态权重管理器
    @details 根据市场环境和数据质量动态调整各维度权重
    """
    
    def __init__(self):
        self.name = "DynamicWeightManager"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 基础权重配置
        self.base_weights = {
            "趋势": 0.25,
            "波动性": 0.15,
            "流动性": 0.20,
            "情绪": 0.15,
            "结构": 0.15,
            "转换": 0.10
        }
        
        # 市场环境权重调整策略
        self.market_condition_adjustments = {
            "bull_market": {"趋势": 1.3, "情绪": 1.2, "结构": 0.8},
            "bear_market": {"波动性": 1.2, "流动性": 1.3, "情绪": 0.8},
            "volatile_market": {"波动性": 1.4, "趋势": 0.8, "转换": 1.2},
            "stable_market": {"结构": 1.3, "趋势": 1.1, "波动性": 0.7}
        }
        
    def calculate_dynamic_weights(self, evaluations: Dict[str, DimensionEvaluationResult],
                                 market_condition: str = "neutral") -> Dict[str, float]:
        """
        @brief 计算动态权重
        @param evaluations: 各维度评估结果
        @param market_condition: 市场环境
        @return: 动态权重字典
        """
        try:
            # 1. 从基础权重开始
            dynamic_weights = self.base_weights.copy()
            
            # 2. 根据市场环境调整
            if market_condition in self.market_condition_adjustments:
                adjustments = self.market_condition_adjustments[market_condition]
                for dim_name, factor in adjustments.items():
                    if dim_name in dynamic_weights:
                        dynamic_weights[dim_name] *= factor
            
            # 3. 根据数据质量调整
            for dim_name, eval_result in evaluations.items():
                if dim_name in dynamic_weights:
                    quality_factor = self._calculate_quality_factor(eval_result)
                    dynamic_weights[dim_name] *= quality_factor
            
            # 4. 根据置信度调整
            for dim_name, eval_result in evaluations.items():
                if dim_name in dynamic_weights:
                    confidence_factor = self._calculate_confidence_factor(eval_result.confidence)
                    dynamic_weights[dim_name] *= confidence_factor
            
            # 5. 标准化权重
            dynamic_weights = self._normalize_weights(dynamic_weights)
            
            return dynamic_weights
            
        except Exception as e:
            self.logger.error(f"Dynamic weight calculation failed: {str(e)}")
            return self.base_weights.copy()
    
    def _calculate_quality_factor(self, eval_result: DimensionEvaluationResult) -> float:
        """计算数据质量因子"""
        quality_mapping = {
            "good": 1.0,
            "fair": 0.8,
            "poor": 0.6,
            "error": 0.3
        }
        return quality_mapping.get(eval_result.data_quality, 0.5)
    
    def _calculate_confidence_factor(self, confidence: float) -> float:
        """计算置信度因子"""
        # 置信度越高，权重调整越大
        if confidence >= 0.8:
            return 1.2
        elif confidence >= 0.6:
            return 1.0
        elif confidence >= 0.4:
            return 0.8
        else:
            return 0.6
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """标准化权重"""
        total_weight = sum(weights.values())
        if total_weight > 0:
            return {dim: weight / total_weight for dim, weight in weights.items()}
        else:
            return self.base_weights.copy()
    
    def detect_market_condition(self, evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """
        @brief 检测市场环境
        @param evaluations: 各维度评估结果
        @return: 市场环境类型
        """
        try:
            # 获取各维度评分
            trend_score = evaluations.get("趋势", DimensionEvaluationResult("趋势", "SIDEWAYS", 0.0, 0.0, [], "low")).score
            volatility_score = evaluations.get("波动性", DimensionEvaluationResult("波动性", "NORMAL", 0.5, 0.0, [], "low")).score
            sentiment_score = evaluations.get("情绪", DimensionEvaluationResult("情绪", "NEUTRAL", 0.0, 0.0, [], "low")).score
            
            # 市场环境判断逻辑
            if trend_score >= 0.5 and sentiment_score >= 0.3:
                return "bull_market"
            elif trend_score <= -0.5 and sentiment_score <= -0.3:
                return "bear_market"
            elif volatility_score >= 0.7:
                return "volatile_market"
            elif abs(trend_score) <= 0.2 and volatility_score <= 0.3:
                return "stable_market"
            else:
                return "neutral"
                
        except Exception as e:
            self.logger.error(f"Market condition detection failed: {str(e)}")
            return "neutral"

class FractalValidator:
    """
    @class FractalValidator
    @brief 分型质量评估器
    @details 负责评估市场分型的质量和可靠性
    """
    
    def __init__(self):
        self.name = "FractalValidator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def validate_fractal_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 验证分型质量
        @param data: 市场数据
        @return: 分型质量评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            technical_data = data.get('technical_data', {})
            flow_data = data.get('flow_data', {})
            
            # 四个评估维度
            amplitude_score = self._calculate_amplitude_score(price_data)
            volume_score = self._calculate_volume_score(flow_data)
            independence_score = self._calculate_independence_score(technical_data)
            volatility_score = self._calculate_volatility_score(price_data)
            
            # 综合质量评分
            quality_scores = {
                'amplitude_score': amplitude_score,
                'volume_score': volume_score,
                'independence_score': independence_score,
                'volatility_score': volatility_score
            }
            
            overall_quality = np.mean(list(quality_scores.values()))
            quality_level = self._determine_quality_level(overall_quality)
            
            return {
                'fund_code': fund_code,
                'quality_scores': quality_scores,
                'overall_quality': overall_quality,
                'quality_level': quality_level,
                'validation_signals': self._generate_validation_signals(quality_scores),
                'confidence': min(0.95, max(0.3, overall_quality)),
                'validation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Fractal validation failed for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'overall_quality': 0.0,
                'quality_level': 'poor'
            }
    
    def _calculate_amplitude_score(self, price_data: Dict[str, Any]) -> float:
        """计算振幅得分"""
        try:
            change_rate = abs(price_data.get('change_rate', 0))
            
            # 振幅评分逻辑
            if change_rate >= 3:
                return min(1.0, change_rate / 5)  # 高振幅
            elif change_rate >= 1:
                return 0.5 + (change_rate - 1) / 4  # 中等振幅
            else:
                return change_rate / 2  # 低振幅
                
        except Exception:
            return 0.0
    
    def _calculate_volume_score(self, flow_data: Dict[str, Any]) -> float:
        """计算成交量得分"""
        try:
            volume = flow_data.get('price_data', {}).get('volume', 0)
            volume_ratio = flow_data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            
            # 成交量评分逻辑
            volume_factor = min(1.0, volume / 5000)  # 标准化成交量
            ratio_factor = min(1.0, volume_ratio / 1.5)  # 标准化成交量比率
            
            return (volume_factor + ratio_factor) / 2
            
        except Exception:
            return 0.0
    
    def _calculate_independence_score(self, technical_data: Dict[str, Any]) -> float:
        """计算独立性得分"""
        try:
            rsi = technical_data.get('rsi', 50)
            macd = technical_data.get('macd', 0)
            
            # 独立性评分（基于技术指标的分离度）
            rsi_independence = abs(rsi - 50) / 50  # RSI偏离中性程度
            macd_independence = min(1.0, abs(macd) * 100)  # MACD信号强度
            
            return (rsi_independence + macd_independence) / 2
            
        except Exception:
            return 0.0
    
    def _calculate_volatility_score(self, price_data: Dict[str, Any]) -> float:
        """计算波动性得分"""
        try:
            change_rate = abs(price_data.get('change_rate', 0))
            
            # 波动性评分（适度波动最佳）
            if 1 <= change_rate <= 3:
                return 1.0  # 理想波动范围
            elif change_rate < 1:
                return change_rate  # 波动过小
            else:
                return max(0.3, 1.0 - (change_rate - 3) / 7)  # 波动过大
                
        except Exception:
            return 0.0
    
    def _determine_quality_level(self, overall_quality: float) -> str:
        """确定质量等级"""
        if overall_quality >= 0.8:
            return "excellent"
        elif overall_quality >= 0.6:
            return "good"
        elif overall_quality >= 0.4:
            return "fair"
        elif overall_quality >= 0.2:
            return "poor"
        else:
            return "very_poor"
    
    def _generate_validation_signals(self, quality_scores: Dict[str, float]) -> List[str]:
        """生成验证信号"""
        signals = []
        
        for score_type, score in quality_scores.items():
            if score >= 0.8:
                signals.append(f"{score_type}优秀({score:.2f})")
            elif score >= 0.6:
                signals.append(f"{score_type}良好({score:.2f})")
            elif score >= 0.4:
                signals.append(f"{score_type}一般({score:.2f})")
            else:
                signals.append(f"{score_type}较差({score:.2f})")
        
        return signals

class MultiDimensionalMarketClassifier:
    """
    @class MultiDimensionalMarketClassifier
    @brief 多维度市场分类器
    @details 基于六大维度评估结果对市场进行智能分类
    """
    
    def __init__(self):
        self.name = "MultiDimensionalMarketClassifier"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 市场分类规则
        self.classification_rules = {
            "强势突破": {
                "conditions": {"趋势": (">", 0.7), "结构": (">", 0.6), "流动性": (">", 0.5)},
                "priority": 1
            },
            "趋势确认": {
                "conditions": {"趋势": (">", 0.5), "情绪": (">", 0.3), "波动性": ("<", 0.7)},
                "priority": 2
            },
            "震荡整理": {
                "conditions": {"趋势": ("between", -0.3, 0.3), "波动性": ("<", 0.5)},
                "priority": 3
            },
            "风险警示": {
                "conditions": {"波动性": (">", 0.8), "情绪": ("<", -0.5)},
                "priority": 1
            }
        }
        
    def classify_market(self, evaluations: Dict[str, DimensionEvaluationResult]) -> Dict[str, Any]:
        """
        @brief 对市场进行分类
        @param evaluations: 各维度评估结果
        @return: 市场分类结果
        """
        try:
            # 提取评分
            scores = {dim: eval_result.score for dim, eval_result in evaluations.items()}
            
            # 匹配分类规则
            matched_classifications = []
            for class_name, rule in self.classification_rules.items():
                if self._check_conditions(scores, rule["conditions"]):
                    matched_classifications.append({
                        'classification': class_name,
                        'priority': rule["priority"],
                        'confidence': self._calculate_match_confidence(scores, rule["conditions"])
                    })
            
            # 排序并选择最佳分类
            matched_classifications.sort(key=lambda x: (x['priority'], -x['confidence']))
            
            primary_classification = matched_classifications[0] if matched_classifications else {
                'classification': '未知市场',
                'priority': 999,
                'confidence': 0.1
            }
            
            return {
                'primary_classification': primary_classification['classification'],
                'classification_confidence': primary_classification['confidence'],
                'all_matches': matched_classifications,
                'market_characteristics': self._extract_characteristics(evaluations),
                'classification_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Market classification failed: {str(e)}")
            return {
                'primary_classification': '分类错误',
                'classification_confidence': 0.0,
                'error': str(e)
            }
    
    def _check_conditions(self, scores: Dict[str, float], conditions: Dict[str, tuple]) -> bool:
        """检查分类条件"""
        for dimension, condition in conditions.items():
            if dimension not in scores:
                continue
                
            score = scores[dimension]
            operator, *params = condition
            
            if operator == ">":
                if not (score > params[0]):
                    return False
            elif operator == "<":
                if not (score < params[0]):
                    return False
            elif operator == "between":
                if not (params[0] <= score <= params[1]):
                    return False
        
        return True
    
    def _calculate_match_confidence(self, scores: Dict[str, float], conditions: Dict[str, tuple]) -> float:
        """计算匹配置信度"""
        total_confidence = 0.0
        condition_count = 0
        
        for dimension, condition in conditions.items():
            if dimension not in scores:
                continue
                
            score = scores[dimension]
            operator, *params = condition
            
            if operator == ">":
                confidence = min(1.0, max(0.0, (score - params[0]) / (1.0 - params[0])))
            elif operator == "<":
                confidence = min(1.0, max(0.0, (params[0] - score) / (params[0] + 1.0)))
            elif operator == "between":
                mid_point = (params[0] + params[1]) / 2
                confidence = 1.0 - abs(score - mid_point) / ((params[1] - params[0]) / 2)
            else:
                confidence = 0.5
                
            total_confidence += confidence
            condition_count += 1
        
        return total_confidence / max(1, condition_count)
    
    def _extract_characteristics(self, evaluations: Dict[str, DimensionEvaluationResult]) -> List[str]:
        """提取市场特征"""
        characteristics = []
        
        for dim_name, eval_result in evaluations.items():
            score = eval_result.score
            
            if score >= 0.7:
                characteristics.append(f"{dim_name}强势")
            elif score >= 0.3:
                characteristics.append(f"{dim_name}偏强")
            elif score <= -0.7:
                characteristics.append(f"{dim_name}弱势")
            elif score <= -0.3:
                characteristics.append(f"{dim_name}偏弱")
            else:
                characteristics.append(f"{dim_name}中性")
        
        return characteristics 

# =============================================================================
# V3核心：增强版决策智能体
# =============================================================================

class EnhancedDecisionAgentV3(BaseAgent):
    """
    @class EnhancedDecisionAgentV3
    @brief 增强版决策智能体 V3
    @details 集成六大维度评估体系的高级决策智能体，提供全方位的基金投资决策支持
    """
    
    def __init__(self, name: str = "EnhancedDecisionAgentV3"):
        super().__init__(name, "enhanced_decision_v3")
        
        # 初始化六大维度评估器
        self.trend_evaluator = TrendEvaluator()
        self.volatility_evaluator = VolatilityEvaluator()
        self.liquidity_evaluator = LiquidityEvaluator()
        self.sentiment_evaluator = SentimentEvaluator()
        self.structural_evaluator = StructuralEvaluator()
        self.transition_evaluator = TransitionEvaluator()
        
        # 初始化智能组件
        self.conflict_resolver = SignalConflictResolver()
        self.weight_manager = DynamicWeightManager()
        self.fractal_validator = FractalValidator()
        self.market_classifier = MultiDimensionalMarketClassifier()
        
        # 决策历史记录
        self.decision_history = deque(maxlen=100)
        
        # 性能指标
        self.performance_metrics = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'accuracy_rate': 0.0,
            'last_update': datetime.now()
        }
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 处理增强决策请求
        @param data: 包含基金代码和各类数据的字典
        @return: 增强版决策结果
        """
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            # 执行增强版决策流程
            enhanced_result = self.make_enhanced_decision_v3(data)
            
            # 记录决策历史
            self._record_decision(enhanced_result)
            
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"Enhanced decision V3 failed for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'final_decision': 'hold',
                'confidence': 0.0
            }
    
    def make_enhanced_decision_v3(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 执行增强版决策流程 V3
        @param data: 综合市场数据
        @return: 完整的增强版决策结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            start_time = datetime.now()
            
            # ========================================
            # 第一阶段：数据预处理和验证
            # ========================================
            
            # 1. 数据完整性检查
            data_quality_report = self._assess_data_quality(data)
            
            # 2. 分型质量验证
            fractal_quality = self.fractal_validator.validate_fractal_quality(data)
            
            # ========================================
            # 第二阶段：六大维度全面评估
            # ========================================
            
            # 执行六大维度评估
            evaluations = {}
            
            # 趋势维度评估
            evaluations["趋势"] = self.trend_evaluator.evaluate(data)
            
            # 波动性维度评估
            evaluations["波动性"] = self.volatility_evaluator.evaluate(data)
            
            # 流动性维度评估
            evaluations["流动性"] = self.liquidity_evaluator.evaluate(data)
            
            # 情绪维度评估
            evaluations["情绪"] = self.sentiment_evaluator.evaluate(data)
            
            # 结构维度评估
            evaluations["结构"] = self.structural_evaluator.evaluate(data)
            
            # 变盘维度评估  
            evaluations["变盘"] = self.transition_evaluator.evaluate(data)
            
            # 时间框架背驰分析（V2版本集成）
            timeframe_analysis = self._analyze_timeframe_divergence(fund_code)
            
            # ========================================
            # 第三阶段：智能分析和冲突解决
            # ========================================
            
            # 1. 市场环境分类
            market_classification = self.market_classifier.classify_market(evaluations)
            
            # 2. 检测市场环境
            market_condition = self.weight_manager.detect_market_condition(evaluations)
            
            # 3. 计算动态权重
            dynamic_weights = self.weight_manager.calculate_dynamic_weights(
                evaluations, market_condition
            )
            
            # 4. 解决信号冲突
            conflict_resolution = self.conflict_resolver.resolve_conflicts(
                evaluations, fund_code
            )
            
            # ========================================
            # 第四阶段：综合决策生成
            # ========================================
            
            # 1. 计算维度协调评分
            coordination_score = self._calculate_coordination_score(evaluations)
            
            # 2. 生成最终决策（集成时间框架背驰分析）
            final_decision_dict = self._generate_final_decision(
                evaluations, 
                dynamic_weights, 
                conflict_resolution,
                coordination_score,
                timeframe_analysis
            )
            final_decision = final_decision_dict.get('final_decision', 'hold')
            weighted_score = final_decision_dict.get('weighted_score', 0.0)
            final_score = final_decision_dict.get('final_score', 0.0)
            buy_threshold = final_decision_dict.get('buy_threshold', 0.3)
            sell_threshold = final_decision_dict.get('sell_threshold', -0.3)
            
            # 3. 计算决策置信度
            decision_confidence = self._calculate_decision_confidence(
                evaluations,
                conflict_resolution,
                coordination_score,
                fractal_quality
            )
            
            # 4. 生成风险评估
            risk_assessment = self._generate_risk_assessment(evaluations, final_decision)
            
            # ========================================
            # 第五阶段：结构化分析摘要
            # ========================================
            
            # 生成分析摘要
            analysis_summary = self._generate_analysis_summary_v3(
                evaluations,
                market_classification,
                conflict_resolution,
                risk_assessment
            )
            
            # 生成操作建议
            operation_suggestions = self._generate_operation_suggestions(
                final_decision,
                decision_confidence,
                risk_assessment,
                market_condition
            )
            
            # ========================================
            # 生成详细信号原因说明
            # ========================================
            
            detailed_signal_reason = self._generate_detailed_signal_reason(
                final_decision,
                evaluations,
                dynamic_weights,
                conflict_resolution,
                coordination_score,
                timeframe_analysis
            )
            
            # ========================================
            # 结果汇总
            # ========================================
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            enhanced_result = {
                # 基础信息
                'fund_code': fund_code,
                'decision_time': start_time.isoformat(),
                'processing_time_seconds': processing_time,
                'system_version': 'V3.0',
                
                # 核心决策结果
                'final_decision': final_decision,
                'weighted_score': weighted_score,
                'final_score': final_score,
                'buy_threshold': buy_threshold,
                'sell_threshold': sell_threshold,
                'decision_confidence': decision_confidence,
                'coordination_score': coordination_score,
                'detailed_signal_reason': detailed_signal_reason,  # 🔥 新增详细信号原因
                
                # 六大维度评估结果
                'dimensional_evaluations': {
                    dim: {
                        'state': eval_result.state,
                        'score': eval_result.score,
                        'confidence': eval_result.confidence,
                        'signals': eval_result.signals,
                        'data_quality': eval_result.data_quality
                    } for dim, eval_result in evaluations.items()
                },
                
                # 智能分析结果
                'market_classification': market_classification,
                'market_condition': market_condition,
                'dynamic_weights': dynamic_weights,
                'conflict_resolution': conflict_resolution,
                
                # 时间框架背驰分析（V2版本集成）
                'timeframe_divergence': timeframe_analysis,
                
                # 质量和风险评估
                'data_quality_report': data_quality_report,
                'fractal_quality': fractal_quality,
                'risk_assessment': risk_assessment,
                
                # 分析摘要和建议
                'analysis_summary': analysis_summary,
                'operation_suggestions': operation_suggestions,
                
                # 性能指标
                'performance_metrics': self.performance_metrics.copy()
            }
            
            # 更新性能指标
            self._update_performance_metrics(enhanced_result)
            
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"Enhanced decision V3 failed: {str(e)}")
            return {
                'fund_code': data.get('fund_code', 'UNKNOWN'),
                'error': str(e),
                'final_decision': 'hold',
                'decision_confidence': 0.0,
                'system_version': 'V3.0'
            }
    
    def _assess_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """评估数据质量"""
        try:
            quality_factors = {}
            
            # 检查价格数据质量
            price_data = data.get('price_data', {})
            if price_data.get('price', 0) > 0:
                quality_factors['price_data'] = 'good'
            else:
                quality_factors['price_data'] = 'poor'
            
            # 检查技术数据质量
            technical_data = data.get('technical_data', {})
            tech_fields = ['ma5', 'ma20', 'rsi', 'macd']
            tech_quality = sum(1 for field in tech_fields if technical_data.get(field) is not None)
            quality_factors['technical_data'] = 'good' if tech_quality >= 3 else 'fair' if tech_quality >= 2 else 'poor'
            
            # 检查流动性数据质量
            flow_data = data.get('flow_data', {})
            if flow_data.get('high_liquidity') is not None:
                quality_factors['flow_data'] = 'good'
            else:
                quality_factors['flow_data'] = 'poor'
            
            # 计算整体质量评分
            quality_scores = {'good': 1.0, 'fair': 0.6, 'poor': 0.3}
            overall_score = np.mean([quality_scores.get(q, 0.3) for q in quality_factors.values()])
            
            return {
                'quality_factors': quality_factors,
                'overall_score': overall_score,
                'overall_level': 'good' if overall_score >= 0.8 else 'fair' if overall_score >= 0.6 else 'poor'
            }
            
        except Exception as e:
            self.logger.error(f"Data quality assessment failed: {str(e)}")
            return {
                'quality_factors': {},
                'overall_score': 0.3,
                'overall_level': 'poor',
                'error': str(e)
            }
    
    def _calculate_coordination_score(self, evaluations: Dict[str, DimensionEvaluationResult]) -> float:
        """计算维度协调评分"""
        try:
            scores = [eval_result.score for eval_result in evaluations.values()]
            
            # 计算方差（协调性）
            variance = np.var(scores)
            
            # 计算均值（整体方向）
            mean_score = np.mean(scores)
            
            # 协调评分：低方差高均值最佳
            coordination = 1.0 - min(1.0, variance) + abs(mean_score) * 0.3
            
            return max(0.0, min(1.0, coordination))
            
        except Exception:
            return 0.5
    
    def _generate_final_decision(self, evaluations: Dict[str, DimensionEvaluationResult],
                               dynamic_weights: Dict[str, float],
                               conflict_resolution: Dict[str, Any],
                               coordination_score: float,
                               timeframe_analysis: Dict[str, Any] = None) -> dict:
        """生成最终决策，集成weighted_score、final_score和buy_threshold"""
        conflicts = conflict_resolution.get('conflicts_detected', [])
        action, weighted_score = self.conflict_resolver._generate_final_recommendation(
            evaluations, dynamic_weights, conflicts)
        # 计算final_score和buy_threshold（与原决策逻辑一致）
        # 计算加权综合评分
        weighted_score2 = 0.0
        total_weight = 0.0
        for dim_name, eval_result in evaluations.items():
            weight = dynamic_weights.get(dim_name, 0.0)
            weighted_score2 += eval_result.score * weight
            total_weight += weight
        if total_weight > 0:
            final_score = weighted_score2 / total_weight
        else:
            final_score = 0.0
        # 时间框架背驰调整
        if timeframe_analysis and not timeframe_analysis.get('error'):
            divergence_weight = 0.4
            transition_probability = timeframe_analysis.get('transition_probability', 0.0)
            consensus_direction = timeframe_analysis.get('consensus_direction', 'neutral')
            transition_signal = timeframe_analysis.get('transition_signal', 'none')
            divergence_adjustment = 0.0
            if consensus_direction == 'bullish' and transition_signal in ['strong', 'medium']:
                divergence_adjustment = transition_probability * 0.6
            elif consensus_direction == 'bearish' and transition_signal in ['strong', 'medium']:
                divergence_adjustment = -transition_probability * 0.6
            elif transition_signal == 'weak':
                if consensus_direction == 'bullish':
                    divergence_adjustment = transition_probability * 0.3
                elif consensus_direction == 'bearish':
                    divergence_adjustment = -transition_probability * 0.3
            final_score = final_score * (1 - divergence_weight) + divergence_adjustment * divergence_weight
        threshold_adjustment = (1.0 - coordination_score) * 0.2
        buy_threshold = 0.3 + threshold_adjustment
        sell_threshold = -0.3 - threshold_adjustment
        return {
            'final_decision': action,
            'weighted_score': weighted_score,
            'final_score': final_score,
            'buy_threshold': buy_threshold,
            'sell_threshold': sell_threshold
        }
    
    def _calculate_decision_confidence(self, evaluations: Dict[str, DimensionEvaluationResult],
                                     conflict_resolution: Dict[str, Any],
                                     coordination_score: float,
                                     fractal_quality: Dict[str, Any]) -> float:
        """计算决策置信度"""
        try:
            # 基础置信度（各维度置信度的加权平均）
            base_confidences = [eval_result.confidence for eval_result in evaluations.values()]
            base_confidence = np.mean(base_confidences)
            
            # 冲突解决置信度
            conflict_confidence = conflict_resolution.get('confidence', 0.5)
            
            # 协调性奖励
            coordination_bonus = coordination_score * 0.2
            
            # 数据质量奖励
            fractal_quality_score = fractal_quality.get('overall_quality', 0.5)
            quality_bonus = fractal_quality_score * 0.1
            
            # 综合置信度
            final_confidence = (
                base_confidence * 0.5 +
                conflict_confidence * 0.3 +
                coordination_bonus +
                quality_bonus
            )
            
            return max(0.1, min(0.95, final_confidence))
            
        except Exception:
            return 0.5
    
    def _generate_risk_assessment(self, evaluations: Dict[str, DimensionEvaluationResult],
                                final_decision: str) -> Dict[str, Any]:
        """生成风险评估"""
        try:
            risk_factors = []
            risk_level = "低风险"
            
            # 波动性风险
            volatility_score = evaluations.get("波动性", DimensionEvaluationResult("波动性", "NORMAL", 0.5, 0.5, [], "medium")).score
            if volatility_score >= 0.8:
                risk_factors.append("极高波动性风险")
                risk_level = "高风险"
            elif volatility_score >= 0.6:
                risk_factors.append("高波动性风险")
                if risk_level == "低风险":
                    risk_level = "中风险"
            
            # 流动性风险
            liquidity_score = evaluations.get("流动性", DimensionEvaluationResult("流动性", "NORMAL", 0.5, 0.5, [], "medium")).score
            if liquidity_score <= 0.3:
                risk_factors.append("流动性不足风险")
                if risk_level in ["低风险", "中风险"]:
                    risk_level = "中风险"
            
            # 情绪风险
            sentiment_score = evaluations.get("情绪", DimensionEvaluationResult("情绪", "NEUTRAL", 0.0, 0.5, [], "medium")).score
            if abs(sentiment_score) >= 0.8:
                risk_factors.append("极端情绪风险")
                risk_level = "高风险"
            
            # 结构风险
            structural_score = evaluations.get("结构", DimensionEvaluationResult("结构", "STABLE", 0.0, 0.5, [], "medium")).score
            if structural_score <= -0.6:
                risk_factors.append("结构性风险")
                if risk_level == "低风险":
                    risk_level = "中风险"
            
            return {
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'volatility_risk': volatility_score,
                'liquidity_risk': 1.0 - liquidity_score,
                'sentiment_risk': abs(sentiment_score),
                'structural_risk': max(0.0, -structural_score)
            }
            
        except Exception as e:
            self.logger.error(f"Risk assessment failed: {str(e)}")
            return {
                'risk_level': '评估失败',
                'risk_factors': [f'风险评估错误: {str(e)}'],
                'error': str(e)
            }
    
    def _generate_detailed_signal_reason(self, final_decision: str, 
                                       evaluations: Dict[str, DimensionEvaluationResult],
                                       dynamic_weights: Dict[str, float],
                                       conflict_resolution: Dict[str, Any],
                                       coordination_score: float,
                                       timeframe_analysis: Dict[str, Any] = None) -> str:
        """生成详细的多层次信号原因解释"""
        try:
            reasons = []
            
            # ===============================
            # 第一层：决策核心依据
            # ===============================
            if final_decision == 'buy':
                reasons.append("🟢 买入信号触发")
            elif final_decision == 'sell':
                reasons.append("🔴 卖出信号触发")
            else:
                reasons.append("🟡 持有观望信号")
            
            # ===============================
            # 第二层：维度分析详情
            # ===============================
            dimension_reasons = []
            for dim_name, eval_result in evaluations.items():
                if eval_result.score >= 0.6:
                    dimension_reasons.append(f"{dim_name}强势({eval_result.score:.2f})")
                elif eval_result.score <= -0.6:
                    dimension_reasons.append(f"{dim_name}弱势({eval_result.score:.2f})")
                elif abs(eval_result.score) >= 0.3:
                    direction = "偏多" if eval_result.score > 0 else "偏空"
                    dimension_reasons.append(f"{dim_name}{direction}({eval_result.score:.2f})")
                    
            if dimension_reasons:
                reasons.append(f"维度状态: {', '.join(dimension_reasons)}")
            
            # ===============================
            # 第三层：冲突解决机制
            # ===============================
            conflicts_detected = conflict_resolution.get('conflicts_detected', [])
            if conflicts_detected:
                conflict_details = []
                for conflict in conflicts_detected:
                    conflict_details.append(f"{conflict.get('dimension1', '')}vs{conflict.get('dimension2', '')}")
                reasons.append(f"冲突处理: {len(conflicts_detected)}项冲突({', '.join(conflict_details)})")
                reasons.append(f"解决策略: {conflict_resolution.get('resolution_strategy', '未知')}")
            else:
                reasons.append("维度协调: 各维度信号协调一致")
            
            # ===============================
            # 第四层：权重调整说明
            # ===============================
            weight_adjustments = []
            base_weight = 1.0 / len(dynamic_weights) if dynamic_weights else 0.2
            for dim_name, weight in dynamic_weights.items():
                if weight > base_weight + 0.1:
                    weight_adjustments.append(f"{dim_name}权重提升({weight:.2f})")
                elif weight < base_weight - 0.1:
                    weight_adjustments.append(f"{dim_name}权重降低({weight:.2f})")
                    
            if weight_adjustments:
                reasons.append(f"权重调整: {', '.join(weight_adjustments)}")
            
            # ===============================
            # 第五层：协调性评估
            # ===============================
            if coordination_score >= 0.8:
                reasons.append(f"协调性评估: 高度协调({coordination_score:.2f})")
            elif coordination_score >= 0.6:
                reasons.append(f"协调性评估: 中等协调({coordination_score:.2f})")
            else:
                reasons.append(f"协调性评估: 协调性较低({coordination_score:.2f})")
            
            # ===============================
            # 第六层：时间框架分析（如果可用）
            # ===============================
            if timeframe_analysis and 'transition_probability' in timeframe_analysis:
                transition_prob = timeframe_analysis.get('transition_probability', 0.0)
                consensus_dir = timeframe_analysis.get('consensus_direction', 'neutral')
                if transition_prob >= 0.7:
                    reasons.append(f"变盘信号: 高概率变盘({transition_prob:.2f}) - {consensus_dir}")
                elif transition_prob >= 0.4:
                    reasons.append(f"变盘信号: 中等变盘可能({transition_prob:.2f}) - {consensus_dir}")
                    
            # ===============================
            # 第七层：关键技术指标
            # ===============================
            key_indicators = []
            for dim_name, eval_result in evaluations.items():
                if eval_result.indicators:
                    for indicator_name, value in eval_result.indicators.items():
                        if isinstance(value, (int, float)) and abs(value) > 0.5:
                            key_indicators.append(f"{indicator_name}={value:.2f}")
                            
            if key_indicators:
                reasons.append(f"关键指标: {', '.join(key_indicators[:3])}")  # 只显示前3个
            
            return " | ".join(reasons)
            
        except Exception as e:
            self.logger.error(f"Detailed signal reason generation failed: {str(e)}")
            return f"信号原因生成失败: {str(e)}"

    def _generate_analysis_summary_v3(self, evaluations: Dict[str, DimensionEvaluationResult],
                                     market_classification: Dict[str, Any],
                                     conflict_resolution: Dict[str, Any],
                                     risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """生成V3版本的增强结构化分析摘要"""
        try:
            # 提取关键信息
            primary_classification = market_classification.get('primary_classification', '未知')
            conflicts_detected = len(conflict_resolution.get('conflicts_detected', []))
            risk_level = risk_assessment.get('risk_level', '未知')
            
            # ===============================
            # 增强版维度摘要
            # ===============================
            dimension_summary = {}
            dimension_insights = []
            
            for dim_name, eval_result in evaluations.items():
                # 基础信息
                dim_info = {
                    'state': eval_result.state,
                    'score': round(eval_result.score, 3),
                    'confidence': round(eval_result.confidence, 3),
                    'data_quality': eval_result.data_quality,
                    'key_signals': eval_result.signals[:3]
                }
                
                # 详细解读
                if eval_result.score >= 0.7:
                    dim_info['interpretation'] = f"{dim_name}表现强劲，支持积极操作"
                elif eval_result.score >= 0.3:
                    dim_info['interpretation'] = f"{dim_name}温和向好，可适度参与"
                elif eval_result.score <= -0.7:
                    dim_info['interpretation'] = f"{dim_name}显著疲弱，建议规避风险"
                elif eval_result.score <= -0.3:
                    dim_info['interpretation'] = f"{dim_name}偏向悲观，需谨慎操作"
                else:
                    dim_info['interpretation'] = f"{dim_name}中性偏平，静待明确方向"
                
                # 置信度解读
                if eval_result.confidence >= 0.8:
                    dim_info['confidence_level'] = "高"
                elif eval_result.confidence >= 0.6:
                    dim_info['confidence_level'] = "中"
                else:
                    dim_info['confidence_level'] = "低"
                
                dimension_summary[dim_name] = dim_info
                
                # 收集维度洞察
                if eval_result.confidence >= 0.7 and abs(eval_result.score) >= 0.5:
                    direction = "积极" if eval_result.score > 0 else "消极"
                    dimension_insights.append(f"{dim_name}高置信度{direction}信号")
            
            # ===============================
            # 深度市场洞察分析
            # ===============================
            deep_insights = []
            
            # 1. 趋势深度分析
            trend_eval = evaluations.get("趋势")
            if trend_eval:
                if trend_eval.score >= 0.6 and trend_eval.confidence >= 0.7:
                    deep_insights.append("📈 趋势动能强劲，具备持续上涨基础，适合趋势跟踪策略")
                elif trend_eval.score <= -0.6 and trend_eval.confidence >= 0.7:
                    deep_insights.append("📉 下跌趋势明确，空头力量占主导，建议回避或做空")
                elif abs(trend_eval.score) < 0.3:
                    deep_insights.append("🔄 趋势方向不明，市场处于横盘整理阶段，等待突破")
            
            # 2. 波动性深度分析
            volatility_eval = evaluations.get("波动性")
            if volatility_eval:
                if volatility_eval.score >= 0.6:
                    deep_insights.append("⚡ 波动率处于高位，市场活跃度高，短线机会丰富但风险也大")
                elif volatility_eval.score <= -0.6:
                    deep_insights.append("😴 波动率较低，市场相对平静，适合长线投资但短线机会有限")
            
            # 3. 流动性深度分析
            liquidity_eval = evaluations.get("流动性")
            if liquidity_eval:
                if liquidity_eval.score >= 0.6:
                    deep_insights.append("💧 流动性充裕，资金供给充足，有利于价格上涨")
                elif liquidity_eval.score <= -0.6:
                    deep_insights.append("🏜️ 流动性趋紧，资金面紧张，对价格形成压制")
            
            # 4. 情绪深度分析
            sentiment_eval = evaluations.get("情绪")
            if sentiment_eval:
                if sentiment_eval.score >= 0.7:
                    deep_insights.append("🚀 市场情绪极度乐观，但需警惕过度乐观带来的回调风险")
                elif sentiment_eval.score <= -0.7:
                    deep_insights.append("😰 市场情绪极度悲观，可能存在超跌反弹机会")
                elif 0.3 <= sentiment_eval.score < 0.7:
                    deep_insights.append("😊 市场情绪积极健康，有利于价格稳步上涨")
                elif -0.7 < sentiment_eval.score <= -0.3:
                    deep_insights.append("😟 市场情绪偏向谨慎，对涨势构成阻力")
            
            # 5. 结构性深度分析
            structural_eval = evaluations.get("结构")
            if structural_eval:
                if structural_eval.score >= 0.6:
                    deep_insights.append("🏗️ 市场结构健康，技术形态良好，支撑价格向上突破")
                elif structural_eval.score <= -0.6:
                    deep_insights.append("🏚️ 市场结构脆弱，技术形态恶化，价格面临破位风险")
            
            # 6. 变盘信号深度分析
            transition_eval = evaluations.get("变盘")
            if transition_eval:
                if transition_eval.score >= 0.6:
                    deep_insights.append("🔄 变盘信号强烈，市场可能迎来重要转折点")
                elif transition_eval.score <= -0.6:
                    deep_insights.append("📍 当前趋势延续性强，短期内变盘可能性较低")
            
            # ===============================
            # 协调性和冲突分析
            # ===============================
            coordination_analysis = self._analyze_dimensional_coordination(evaluations, conflicts_detected)
            
            # ===============================
            # 风险因素深度分析
            # ===============================
            risk_factor_analysis = self._analyze_risk_factors_detailed(evaluations, risk_assessment)
            
            # ===============================
            # 操作建议细化
            # ===============================
            tactical_recommendations = self._generate_tactical_recommendations(
                evaluations, primary_classification, risk_level
            )
            
            return {
                'market_overview': {
                    'classification': primary_classification,
                    'classification_confidence': market_classification.get('confidence', 0.5),
                    'risk_level': risk_level,
                    'conflicts_count': conflicts_detected,
                    'overall_market_tone': self._determine_market_tone(evaluations)
                },
                'dimension_summary': dimension_summary,
                'dimension_insights': dimension_insights,
                'deep_market_insights': deep_insights,
                'coordination_analysis': coordination_analysis,
                'risk_factor_analysis': risk_factor_analysis,
                'tactical_recommendations': tactical_recommendations,
                'market_sentiment_overview': self._generate_sentiment_overview(evaluations),
                'summary_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Enhanced analysis summary generation failed: {str(e)}")
            return {
                'error': str(e),
                'deep_market_insights': ['增强分析摘要生成失败'],
                'summary_time': datetime.now().isoformat()
            }

    def _generate_operation_suggestions(self, final_decision: str,
                                      decision_confidence: float,
                                      risk_assessment: Dict[str, Any],
                                      market_condition: str) -> List[str]:
        """生成操作建议"""
        suggestions = []
        
        try:
            # 基础建议
            if final_decision == 'buy':
                if decision_confidence >= 0.8:
                    suggestions.append("高置信度买入信号，建议分批建仓")
                elif decision_confidence >= 0.6:
                    suggestions.append("中等置信度买入信号，可小仓位试探")
                else:
                    suggestions.append("低置信度买入信号，建议谨慎观望")
            elif final_decision == 'sell':
                if decision_confidence >= 0.8:
                    suggestions.append("高置信度卖出信号，建议及时止损")
                elif decision_confidence >= 0.6:
                    suggestions.append("中等置信度卖出信号，可分批减仓")
                else:
                    suggestions.append("低置信度卖出信号，建议继续观察")
            else:
                suggestions.append("建议保持观望，等待更明确的信号")
            
            # 风险建议
            risk_level = risk_assessment.get('risk_level', '未知')
            if risk_level == '高风险':
                suggestions.append("当前市场风险较高，建议控制仓位")
            elif risk_level == '中风险':
                suggestions.append("市场存在一定风险，建议适度参与")
            
            # 市场环境建议
            if market_condition == 'volatile_market':
                suggestions.append("市场波动较大，建议采用短线策略")
            elif market_condition == 'stable_market':
                suggestions.append("市场相对稳定，可考虑中长线持有")
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Operation suggestions generation failed: {str(e)}")
            return [f"建议生成失败: {str(e)}"]

    def _analyze_dimensional_coordination(self, evaluations: Dict[str, DimensionEvaluationResult], 
                                        conflicts_count: int) -> Dict[str, Any]:
        """分析维度协调性"""
        try:
            # 计算维度一致性指标
            scores = [eval_result.score for eval_result in evaluations.values()]
            confidences = [eval_result.confidence for eval_result in evaluations.values()]
            
            # 方向一致性
            positive_count = sum(1 for score in scores if score > 0.3)
            negative_count = sum(1 for score in scores if score < -0.3)
            neutral_count = len(scores) - positive_count - negative_count
            
            # 协调性分类
            if positive_count >= len(scores) * 0.7:
                coordination_type = "多头主导"
                coordination_strength = "强"
            elif negative_count >= len(scores) * 0.7:
                coordination_type = "空头主导" 
                coordination_strength = "强"
            elif positive_count >= len(scores) * 0.5:
                coordination_type = "多头倾向"
                coordination_strength = "中"
            elif negative_count >= len(scores) * 0.5:
                coordination_type = "空头倾向"
                coordination_strength = "中"
            else:
                coordination_type = "分化明显"
                coordination_strength = "弱"
            
            # 置信度一致性
            avg_confidence = np.mean(confidences)
            confidence_std = np.std(confidences)
            
            return {
                'coordination_type': coordination_type,
                'coordination_strength': coordination_strength,
                'direction_distribution': {
                    'positive': positive_count,
                    'negative': negative_count,
                    'neutral': neutral_count
                },
                'avg_confidence': round(avg_confidence, 3),
                'confidence_consistency': "高" if confidence_std < 0.15 else "中" if confidence_std < 0.25 else "低",
                'conflicts_detected': conflicts_count,
                'coordination_summary': f"{coordination_type}，协调性{coordination_strength}，冲突{conflicts_count}项"
            }
        except Exception as e:
            return {'error': str(e), 'coordination_summary': '协调性分析失败'}

    def _analyze_risk_factors_detailed(self, evaluations: Dict[str, DimensionEvaluationResult],
                                     risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """详细风险因素分析"""
        try:
            risk_factors = []
            risk_mitigation = []
            
            # 波动性风险
            volatility_eval = evaluations.get("波动性")
            if volatility_eval and volatility_eval.score >= 0.6:
                risk_factors.append("高波动率风险：市场波动加剧，价格不稳定性增加")
                risk_mitigation.append("建议分批建仓，控制单次投入规模")
            
            # 流动性风险  
            liquidity_eval = evaluations.get("流动性")
            if liquidity_eval and liquidity_eval.score <= -0.4:
                risk_factors.append("流动性风险：资金面偏紧，流动性不足")
                risk_mitigation.append("避免大额交易，关注流动性改善信号")
            
            # 情绪风险
            sentiment_eval = evaluations.get("情绪")
            if sentiment_eval:
                if sentiment_eval.score >= 0.7:
                    risk_factors.append("情绪过热风险：市场情绪过度乐观，存在回调压力")
                    risk_mitigation.append("适当获利了结，避免追高")
                elif sentiment_eval.score <= -0.7:
                    risk_factors.append("情绪恐慌风险：市场情绪极度悲观，可能继续下探")
                    risk_mitigation.append("控制仓位，等待情绪修复")
            
            # 结构性风险
            structural_eval = evaluations.get("结构")
            if structural_eval and structural_eval.score <= -0.5:
                risk_factors.append("结构性风险：技术形态恶化，支撑位面临考验")
                risk_mitigation.append("设置止损位，关注关键技术位破位风险")
            
            # 变盘风险
            transition_eval = evaluations.get("变盘")
            if transition_eval and transition_eval.score >= 0.5:
                risk_factors.append("变盘风险：市场可能面临重要转折")
                risk_mitigation.append("保持灵活性，及时调整策略")
            
            # 综合风险等级评估
            risk_score = len(risk_factors) / 5.0  # 标准化到0-1
            if risk_score >= 0.6:
                overall_risk = "高风险"
            elif risk_score >= 0.4:
                overall_risk = "中风险"
            else:
                overall_risk = "低风险"
            
            return {
                'overall_risk_level': overall_risk,
                'risk_score': round(risk_score, 3),
                'identified_risks': risk_factors,
                'mitigation_strategies': risk_mitigation,
                'risk_count': len(risk_factors),
                'risk_summary': f"识别{len(risk_factors)}项风险因素，整体风险等级{overall_risk}"
            }
        except Exception as e:
            return {'error': str(e), 'risk_summary': '风险分析失败'}

    def _generate_tactical_recommendations(self, evaluations: Dict[str, DimensionEvaluationResult],
                                         market_classification: str, risk_level: str) -> List[str]:
        """生成战术操作建议"""
        try:
            recommendations = []
            
            # 基于市场分类的建议
            if market_classification == "trending_market":
                recommendations.append("🔄 趋势市场：采用趋势跟踪策略，关注突破信号")
            elif market_classification == "volatile_market":
                recommendations.append("⚡ 波动市场：采用区间操作策略，高抛低吸")
            elif market_classification == "stable_market":
                recommendations.append("📈 稳定市场：可考虑长线持有，分批建仓")
            
            # 基于风险等级的建议
            if risk_level == "高风险":
                recommendations.append("🚨 高风险环境：严格控制仓位，设置止损")
            elif risk_level == "中风险":
                recommendations.append("⚠️ 中风险环境：适度参与，保持谨慎")
            else:
                recommendations.append("✅ 低风险环境：可适当增加仓位")
            
            # 基于维度分析的建议
            trend_eval = evaluations.get("趋势")
            if trend_eval and trend_eval.score >= 0.6:
                recommendations.append("📈 趋势向好：可考虑顺势加仓")
            
            volatility_eval = evaluations.get("波动性")
            if volatility_eval and volatility_eval.score >= 0.6:
                recommendations.append("⚡ 高波动：分批操作，控制单次投入")
            
            liquidity_eval = evaluations.get("流动性")
            if liquidity_eval and liquidity_eval.score >= 0.6:
                recommendations.append("💧 流动性充裕：资金供给有保障")
            
            sentiment_eval = evaluations.get("情绪")
            if sentiment_eval:
                if sentiment_eval.score >= 0.7:
                    recommendations.append("🚀 情绪火热：注意获利了结")
                elif sentiment_eval.score <= -0.7:
                    recommendations.append("😰 情绪低迷：逢低布局机会")
            
            return recommendations[:6]  # 限制建议数量
            
        except Exception as e:
            return [f"战术建议生成失败: {str(e)}"]

    def _determine_market_tone(self, evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """确定整体市场基调"""
        try:
            scores = [eval_result.score for eval_result in evaluations.values()]
            avg_score = np.mean(scores)
            
            if avg_score >= 0.5:
                return "积极乐观"
            elif avg_score >= 0.2:
                return "温和向好"
            elif avg_score >= -0.2:
                return "中性震荡"
            elif avg_score >= -0.5:
                return "谨慎悲观"
            else:
                return "显著疲弱"
        except:
            return "未知"

    def _generate_sentiment_overview(self, evaluations: Dict[str, DimensionEvaluationResult]) -> Dict[str, Any]:
        """生成市场情绪概览"""
        try:
            sentiment_eval = evaluations.get("情绪")
            if not sentiment_eval:
                return {'status': '情绪数据不可用'}
            
            score = sentiment_eval.score
            confidence = sentiment_eval.confidence
            
            # 情绪分类
            if score >= 0.7:
                sentiment_type = "极度乐观"
                sentiment_icon = "🚀"
                sentiment_warning = "警惕过度乐观风险"
            elif score >= 0.3:
                sentiment_type = "温和乐观"
                sentiment_icon = "😊"
                sentiment_warning = "情绪健康向好"
            elif score >= -0.3:
                sentiment_type = "中性平静"
                sentiment_icon = "😐"
                sentiment_warning = "情绪相对稳定"
            elif score >= -0.7:
                sentiment_type = "温和悲观"
                sentiment_icon = "😟"
                sentiment_warning = "谨慎悲观情绪"
            else:
                sentiment_type = "极度悲观"
                sentiment_icon = "😰"
                sentiment_warning = "关注反弹机会"
            
            return {
                'sentiment_type': sentiment_type,
                'sentiment_icon': sentiment_icon,
                'sentiment_score': round(score, 3),
                'confidence': round(confidence, 3),
                'sentiment_warning': sentiment_warning,
                'sentiment_description': f"{sentiment_icon} {sentiment_type}(评分:{score:.2f}, 置信度:{confidence:.2f})"
            }
        except Exception as e:
            return {'error': str(e), 'sentiment_description': '情绪分析失败'}
    
    def _record_decision(self, decision_result: Dict[str, Any]) -> None:
        """记录决策历史"""
        try:
            decision_record = {
                'timestamp': datetime.now(),
                'fund_code': decision_result.get('fund_code'),
                'decision': decision_result.get('final_decision'),
                'confidence': decision_result.get('decision_confidence'),
                'processing_time': decision_result.get('processing_time_seconds')
            }
            
            self.decision_history.append(decision_record)
            
        except Exception as e:
            self.logger.error(f"Decision recording failed: {str(e)}")
    
    def _update_performance_metrics(self, decision_result: Dict[str, Any]) -> None:
        """更新性能指标"""
        try:
            self.performance_metrics['total_decisions'] += 1
            
            # 更新准确率（这里简化处理）
            if decision_result.get('final_decision') != 'hold':
                confidence = decision_result.get('decision_confidence', 0.0)
                if confidence >= 0.7:
                    self.performance_metrics['successful_decisions'] += 1
            
            # 计算准确率
            total = self.performance_metrics['total_decisions']
            successful = self.performance_metrics['successful_decisions']
            if total > 0:
                self.performance_metrics['accuracy_rate'] = successful / total
            
            self.performance_metrics['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Performance metrics update failed: {str(e)}")

    def _analyze_timeframe_divergence(self, fund_code: str) -> Dict[str, Any]:
        """时间框架背驰分析（从V2版本集成）"""
        try:
            # 创建时间周期背驰分析代理实例
            divergence_agent = TimeframeDivergenceAgent()
            
            # 进行背驰分析
            divergence_result = divergence_agent.process({'fund_code': fund_code})
            
            if 'error' in divergence_result:
                return {
                    'fund_code': fund_code,
                    'transition_probability': 0.0,
                    'consensus_direction': 'neutral',
                    'transition_signal': 'none',
                    'resonance_score': 0.0,
                    'confidence_level': 0.0,
                    'error': divergence_result['error'],
                    'timestamp': datetime.now().isoformat()
                }
            
            # 提取关键指标
            resonance_result = divergence_result.get('resonance_result')
            transition_probability = divergence_result.get('transition_probability', 0.0)
            
            # 获取算法统计信息
            algorithm_stats = {}
            if hasattr(divergence_agent, 'get_algorithm_stats'):
                algorithm_stats = divergence_agent.get_algorithm_stats()
            
            return {
                'fund_code': fund_code,
                'transition_probability': transition_probability,
                'consensus_direction': resonance_result.consensus_direction if resonance_result else 'neutral',
                'transition_signal': resonance_result.transition_signal if resonance_result else 'none',
                'resonance_score': resonance_result.resonance_score if resonance_result else 0.0,
                'confidence_level': resonance_result.confidence_level if resonance_result else 0.0,
                'divergence_results': divergence_result.get('divergence_results', {}),
                'algorithm_stats': algorithm_stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error in timeframe divergence analysis for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'transition_probability': 0.0,
                'consensus_direction': 'neutral',
                'transition_signal': 'none',
                'resonance_score': 0.0,
                'confidence_level': 0.0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# =============================================================================
# 多智能体协调系统和执行器
# =============================================================================

class MultiAgentCoordinatorV3:
    """
    @class MultiAgentCoordinatorV3
    @brief 多智能体协调器 V3版本
    @details 负责协调所有智能体的工作，集成传统智能体和增强版决策智能体
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 初始化传统智能体
        self.technical_agent = TechnicalAgent("TechnicalAgent_V3")
        self.gua_agent = GuaAnalysisAgent("GuaAgent_V3")
        self.flow_agent = FundFlowAgent("FlowAgent_V3")
        
        # 初始化增强版决策智能体
        self.enhanced_decision_agent = EnhancedDecisionAgentV3("EnhancedDecisionV3")
        
        # 协调历史
        self.coordination_history = deque(maxlen=50)
        
    def coordinate_analysis(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 协调所有智能体进行综合分析
        @param fund_code: 基金代码
        @return: 综合分析结果
        """
        try:
            start_time = datetime.now()
            self.logger.info(f"开始协调分析基金: {fund_code}")
            
            # ===============================
            # 第一阶段：传统智能体数据收集
            # ===============================
            
            # 技术分析
            technical_data = self.technical_agent.process({'fund_code': fund_code})
            
            # 卦象分析
            gua_data = self.gua_agent.process({'fund_code': fund_code})
            
            # 资金流向分析
            flow_data = self.flow_agent.process({'fund_code': fund_code})
            
            # ===============================
            # 第二阶段：数据整合和预处理
            # ===============================
            
            # 整合所有数据
            integrated_data = self._integrate_agent_data(
                fund_code, technical_data, gua_data, flow_data
            )
            
            # ===============================
            # 第三阶段：增强版决策分析
            # ===============================
            
            # 执行增强版决策分析
            enhanced_result = self.enhanced_decision_agent.process(integrated_data)
            
            # ===============================
            # 第四阶段：结果整合和输出
            # ===============================
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 整合最终结果
            final_result = {
                # 基础信息
                'fund_code': fund_code,
                'analysis_time': start_time.isoformat(),
                'total_processing_time': processing_time,
                'coordinator_version': 'V3.0',
                
                # 传统智能体结果
                'traditional_agents': {
                    'technical_analysis': technical_data,
                    'gua_analysis': gua_data,
                    'flow_analysis': flow_data
                },
                
                # 增强版决策结果
                'enhanced_decision': enhanced_result,
                
                # 协调摘要
                'coordination_summary': self._generate_coordination_summary(
                    technical_data, gua_data, flow_data, enhanced_result
                )
            }
            
            # 记录协调历史
            self._record_coordination(final_result)
            
            self.logger.info(f"基金 {fund_code} 分析完成，耗时 {processing_time:.3f} 秒")
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"协调分析失败 {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'analysis_time': datetime.now().isoformat(),
                'coordinator_version': 'V3.0'
            }
    
    def _integrate_agent_data(self, fund_code: str, 
                             technical_data: Dict, 
                             gua_data: Dict, 
                             flow_data: Dict) -> Dict[str, Any]:
        """整合各智能体数据"""
        try:
            # 构建增强版决策智能体需要的数据结构
            integrated_data = {
                'fund_code': fund_code,
                
                # 价格相关数据
                'price_data': flow_data.get('price_data', {}),
                
                # 技术指标数据
                'technical_data': technical_data.get('technical_indicators', {}),
                
                # 卦象数据
                'gua_data': {
                    'gua_score': gua_data.get('gua_score', 0.0),
                    'main_gua': gua_data.get('main_gua', ''),
                    'is_select_gua': gua_data.get('is_select_gua', False),
                    'is_buy_gua': gua_data.get('is_buy_gua', False),
                    'is_sell_gua': gua_data.get('is_sell_gua', False)
                },
                
                # 流动性数据
                'flow_data': flow_data,
                
                # 成交量分析
                'volume_analysis': flow_data.get('volume_analysis', {}),
                
                # 时间框架背驰分析数据（为V3决策智能体准备）
                'timeframe_divergence': {
                    'transition_probability': 0.0,  # 默认值，将由增强决策智能体计算
                    'consensus_direction': 'neutral',
                    'transition_signal': 'none',
                    'resonance_score': 0.0
                }
            }
            
            return integrated_data
            
        except Exception as e:
            self.logger.error(f"数据整合失败: {str(e)}")
            return {'fund_code': fund_code, 'error': str(e)}
    

    
    def _generate_coordination_summary(self, technical_data: Dict, 
                                     gua_data: Dict, 
                                     flow_data: Dict, 
                                     enhanced_result: Dict) -> Dict[str, Any]:
        """生成协调摘要"""
        try:
            # 传统智能体一致性检查
            traditional_signals = []
            
            # 技术分析信号
            if technical_data.get('buy_signal', False):
                traditional_signals.append('技术买入')
            
            # 卦象信号
            if gua_data.get('is_buy_gua', False):
                traditional_signals.append('卦象买入')
            elif gua_data.get('is_sell_gua', False):
                traditional_signals.append('卦象卖出')
            
            # 流动性信号
            if flow_data.get('high_liquidity', False):
                traditional_signals.append('高流动性')
            
            # 传统vs增强对比
            enhanced_decision = enhanced_result.get('final_decision', 'hold')
            traditional_consensus = self._determine_traditional_consensus(
                technical_data, gua_data, flow_data
            )
            
            agreement = enhanced_decision == traditional_consensus
            
            return {
                'traditional_signals': traditional_signals,
                'traditional_consensus': traditional_consensus,
                'enhanced_decision': enhanced_decision,
                'decision_agreement': agreement,
                'confidence_level': enhanced_result.get('decision_confidence', 0.0),
                'risk_level': enhanced_result.get('risk_assessment', {}).get('risk_level', '未知'),
                'market_classification': enhanced_result.get('market_classification', {}).get('primary_classification', '未知'),
                'coordination_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"协调摘要生成失败: {str(e)}")
            return {
                'error': str(e),
                'coordination_time': datetime.now().isoformat()
            }
    
    def _determine_traditional_consensus(self, technical_data: Dict, 
                                       gua_data: Dict, 
                                       flow_data: Dict) -> str:
        """确定传统智能体的一致意见"""
        try:
            buy_signals = 0
            sell_signals = 0
            
            # 技术分析投票
            if technical_data.get('buy_signal', False):
                buy_signals += 1
            
            # 卦象分析投票
            if gua_data.get('is_buy_gua', False):
                buy_signals += 1
            elif gua_data.get('is_sell_gua', False):
                sell_signals += 1
            
            # 流动性分析投票（影响权重较小）
            if flow_data.get('high_liquidity', False):
                buy_signals += 0.5
            
            # 决定一致意见
            if buy_signals >= 2:
                return 'buy'
            elif sell_signals >= 1.5:
                return 'sell'
            else:
                return 'hold'
                
        except Exception:
            return 'hold'
    
    def _record_coordination(self, result: Dict[str, Any]) -> None:
        """记录协调历史"""
        try:
            coordination_record = {
                'timestamp': datetime.now(),
                'fund_code': result.get('fund_code'),
                'enhanced_decision': result.get('enhanced_decision', {}).get('final_decision'),
                'confidence': result.get('enhanced_decision', {}).get('decision_confidence'),
                'processing_time': result.get('total_processing_time')
            }
            
            self.coordination_history.append(coordination_record)
            
        except Exception as e:
            self.logger.error(f"协调历史记录失败: {str(e)}")

class FundTradingExecutorV3:
    """
    @class FundTradingExecutorV3
    @brief 基金交易执行器 V3版本
    @details 负责执行基金交易决策，包含完整的风险控制和执行确认
    """
    
    def __init__(self, initial_capital: float = 100000.0):
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}  # 持仓记录
        self.transaction_history = []  # 交易历史
        self.risk_limits = {
            'max_position_size': 0.2,    # 单个基金最大仓位20%
            'max_daily_loss': 0.05,      # 单日最大亏损5%
            'min_confidence': 0.6        # 最小置信度要求
        }
        
    def execute_decision(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 执行交易决策
        @param analysis_result: 协调器分析结果
        @return: 执行结果
        """
        try:
            fund_code = analysis_result.get('fund_code', 'UNKNOWN')
            enhanced_decision = analysis_result.get('enhanced_decision', {})
            
            decision = enhanced_decision.get('final_decision', 'hold')
            confidence = enhanced_decision.get('decision_confidence', 0.0)
            
            self.logger.info(f"执行基金 {fund_code} 决策: {decision} (置信度: {confidence:.3f})")
            
            # 风险检查
            risk_check = self._perform_risk_check(fund_code, decision, confidence, enhanced_decision)
            
            if not risk_check['passed']:
                return {
                    'fund_code': fund_code,
                    'execution_status': 'rejected',
                    'reason': risk_check['reason'],
                    'original_decision': decision,
                    'confidence': confidence,
                    'execution_time': datetime.now().isoformat()
                }
            
            # 执行交易
            execution_result = self._execute_trade(fund_code, decision, confidence, enhanced_decision)
            
            # 记录交易
            self._record_transaction(fund_code, decision, execution_result)
            
            return execution_result
            
        except Exception as e:
            self.logger.error(f"决策执行失败: {str(e)}")
            return {
                'fund_code': analysis_result.get('fund_code', 'UNKNOWN'),
                'execution_status': 'error',
                'error': str(e),
                'execution_time': datetime.now().isoformat()
            }
    
    def _perform_risk_check(self, fund_code: str, decision: str, 
                           confidence: float, enhanced_decision: Dict) -> Dict[str, Any]:
        """执行风险检查"""
        try:
            # 置信度检查
            if confidence < self.risk_limits['min_confidence']:
                return {
                    'passed': False,
                    'reason': f'置信度过低 ({confidence:.3f} < {self.risk_limits["min_confidence"]})'
                }
            
            # 风险等级检查
            risk_assessment = enhanced_decision.get('risk_assessment', {})
            risk_level = risk_assessment.get('risk_level', '未知')
            
            if risk_level == '高风险' and decision in ['buy', 'sell']:
                return {
                    'passed': False,
                    'reason': f'高风险环境下限制交易 (风险等级: {risk_level})'
                }
            
            # 仓位检查
            current_position = self.positions.get(fund_code, 0.0)
            if decision == 'buy' and current_position >= self.risk_limits['max_position_size']:
                return {
                    'passed': False,
                    'reason': f'超过最大仓位限制 ({current_position:.1%} >= {self.risk_limits["max_position_size"]:.1%})'
                }
            
            return {'passed': True, 'reason': '风险检查通过'}
            
        except Exception as e:
            self.logger.error(f"风险检查失败: {str(e)}")
            return {'passed': False, 'reason': f'风险检查错误: {str(e)}'}
    
    def _execute_trade(self, fund_code: str, decision: str, 
                      confidence: float, enhanced_decision: Dict) -> Dict[str, Any]:
        """执行具体交易"""
        try:
            execution_time = datetime.now()
            
            # 获取价格数据
            price_data = enhanced_decision.get('traditional_agents', {}).get('flow_analysis', {}).get('price_data', {})
            current_price = price_data.get('price', 1.0)
            
            # 计算交易量（基于置信度调整）
            base_amount = self.current_capital * 0.1  # 基础投资10%
            confidence_adjustment = confidence * 1.5  # 置信度调整
            trade_amount = base_amount * confidence_adjustment
            
            # 执行不同类型的交易
            if decision == 'buy':
                result = self._execute_buy(fund_code, trade_amount, current_price)
            elif decision == 'sell':
                result = self._execute_sell(fund_code, current_price)
            else:  # hold
                result = {
                    'execution_status': 'hold',
                    'message': '保持现有仓位',
                    'current_position': self.positions.get(fund_code, 0.0)
                }
            
            # 添加通用信息
            result.update({
                'fund_code': fund_code,
                'decision': decision,
                'confidence': confidence,
                'execution_time': execution_time.isoformat(),
                'current_capital': self.current_capital,
                'price': current_price
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"交易执行失败: {str(e)}")
            return {
                'fund_code': fund_code,
                'execution_status': 'error',
                'error': str(e),
                'execution_time': datetime.now().isoformat()
            }
    
    def _execute_buy(self, fund_code: str, amount: float, price: float) -> Dict[str, Any]:
        """执行买入操作"""
        try:
            shares = amount / price
            
            # 更新持仓
            if fund_code in self.positions:
                self.positions[fund_code] += shares
            else:
                self.positions[fund_code] = shares
            
            # 更新资金
            self.current_capital -= amount
            
            return {
                'execution_status': 'buy_executed',
                'shares_bought': shares,
                'amount_invested': amount,
                'new_position': self.positions[fund_code],
                'message': f'成功买入 {shares:.2f} 份额，投资金额 {amount:.2f}'
            }
            
        except Exception as e:
            return {
                'execution_status': 'buy_failed',
                'error': str(e),
                'message': f'买入失败: {str(e)}'
            }
    
    def _execute_sell(self, fund_code: str, price: float) -> Dict[str, Any]:
        """执行卖出操作"""
        try:
            current_shares = self.positions.get(fund_code, 0.0)
            
            if current_shares <= 0:
                return {
                    'execution_status': 'sell_rejected',
                    'message': '无持仓，无法卖出',
                    'current_position': current_shares
                }
            
            # 卖出一半持仓
            shares_to_sell = current_shares * 0.5
            amount_received = shares_to_sell * price
            
            # 更新持仓
            self.positions[fund_code] -= shares_to_sell
            
            # 更新资金
            self.current_capital += amount_received
            
            return {
                'execution_status': 'sell_executed',
                'shares_sold': shares_to_sell,
                'amount_received': amount_received,
                'remaining_position': self.positions[fund_code],
                'message': f'成功卖出 {shares_to_sell:.2f} 份额，回收金额 {amount_received:.2f}'
            }
            
        except Exception as e:
            return {
                'execution_status': 'sell_failed',
                'error': str(e),
                'message': f'卖出失败: {str(e)}'
            }
    
    def _record_transaction(self, fund_code: str, decision: str, 
                           execution_result: Dict[str, Any]) -> None:
        """记录交易历史"""
        try:
            transaction_record = {
                'timestamp': datetime.now(),
                'fund_code': fund_code,
                'decision': decision,
                'execution_status': execution_result.get('execution_status'),
                'amount': execution_result.get('amount_invested', execution_result.get('amount_received', 0)),
                'shares': execution_result.get('shares_bought', execution_result.get('shares_sold', 0)),
                'price': execution_result.get('price', 0),
                'capital_after': self.current_capital
            }
            
            self.transaction_history.append(transaction_record)
            
        except Exception as e:
            self.logger.error(f"交易记录失败: {str(e)}")
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        try:
            total_value = self.current_capital
            for fund_code, shares in self.positions.items():
                # 简单估值（实际应获取实时价格）
                estimated_price = 1.5  # 假设价格
                total_value += shares * estimated_price
            
            return {
                'current_capital': self.current_capital,
                'total_value': total_value,
                'total_return': total_value - self.initial_capital,
                'return_rate': (total_value - self.initial_capital) / self.initial_capital,
                'positions': self.positions.copy(),
                'transaction_count': len(self.transaction_history),
                'summary_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"投资组合摘要生成失败: {str(e)}")
            return {
                'error': str(e),
                'summary_time': datetime.now().isoformat()
            }

# ==========================================================
# 实际交易代理（puppet库接口）
# ==========================================================

class TradingAgent(BaseAgent):
    """实际交易执行代理类 - 使用puppet库进行真实交易"""
    
    def __init__(self, name: str = "TradingAgent", title: str = ""):
        super().__init__(name, "trading_execution")

        self.account = puppet.Account(title=title)
        self.transaction_history = []
        self.all_money_list = []
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据并执行交易"""
        action = data.get('action')
        fund_code = data.get('fund_code')
        price = data.get('price')
        quantity = data.get('quantity', '20000')
        
        self.logger.info(f"Processing trade: {action} {fund_code} at {price}")
        
        if action == 'buy':
            result = self.execute_buy(fund_code, price, quantity)
        elif action == 'sell':
            result = self.execute_sell(fund_code, price, quantity)
        else:
            result = {
                'success': False,
                'message': f"Unknown action: {action}",
                'timestamp': datetime.now().isoformat()
            }
            
        # 记录交易历史
        transaction_record = {
            'action': action,
            'fund_code': fund_code,
            'price': price,
            'quantity': quantity,
            'result': result,
            'timestamp': datetime.now().isoformat()
        }
        self.transaction_history.append(transaction_record)
        
        return result
    
    def execute_buy(self, fund_code: str, price: float, quantity: str) -> Dict[str, Any]:
        """执行买入操作"""
        try:
            adjusted_price = round(price/10, 3) if price > 9 else round(price, 3)
            self.account.buy(fund_code, adjusted_price, quantity)

            return {
                'success': True,
                'message': f"Bought {fund_code} at {adjusted_price}",
                'fund_code': fund_code,
                'price': adjusted_price,
                'quantity': quantity,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Buy error for {fund_code}: {str(e)}")
            return {
                'success': False,
                'message': str(e),
                'fund_code': fund_code,
                'timestamp': datetime.now().isoformat()
            }
    
    def execute_sell(self, fund_code: str, price: float, quantity: str) -> Dict[str, Any]:
        """执行卖出操作"""
        try:
            adjusted_price = round(price/10, 3) if price > 9 else round(price, 3)
            self.account.sell(fund_code, adjusted_price, quantity)

            return {
                'success': True,
                'message': f"Sold {fund_code} at {adjusted_price}",
                'fund_code': fund_code,
                'price': adjusted_price,
                'quantity': quantity,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Sell error for {fund_code}: {str(e)}")
            return {
                'success': False,
                'message': str(e),
                'fund_code': fund_code,
                'timestamp': datetime.now().isoformat()
            }
    

    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            position = self.account.query('position')
            summary = self.account.query('summary')

            position_list = [x[2:-1] for x in position['symbol']]
            all_sum = sum(position['amount'])
            all_money = summary['equity']
            position_percentage = summary['position_pct']

            self.all_money_list.append(all_money)

            return {
                'position': position,
                'position_list': position_list,
                'all_sum': all_sum,
                'all_money': all_money,
                'position_percentage': position_percentage,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting account info: {str(e)}")
            return {'error': str(e)}
    


# ==========================================================
# 增强技术分析代理（时间周期背驰）
# ==========================================================

class EnhancedTechnicalAgent(BaseAgent):
    """增强版技术分析代理"""
    
    def __init__(self, name: str = "EnhancedTechnicalAgent"):
        super().__init__(name, "enhanced_technical_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据并进行增强技术分析"""
        fund_code = data.get('fund_code')
        self.logger.info(f"Processing enhanced technical analysis for {fund_code}")
        
        try:
            # 1. 传统技术分析
            traditional_analysis = self._analyze_traditional_signals(fund_code)
            
            # 2. 量化技术分析
            quantitative_analysis = self._analyze_quantitative_signals(fund_code)
            
            # 3. 时间周期背驰分析
            timeframe_divergence = self._analyze_timeframe_divergence(fund_code)
            
            # 4. 融合分析结果
            enhanced_result = self._fuse_analysis_results(traditional_analysis, quantitative_analysis, timeframe_divergence)
            
            self.store_in_memory(fund_code, enhanced_result)
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"Error in enhanced technical analysis for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'timestamp': datetime.now().isoformat(),
                'buy_signal': False,
                'error': str(e),
                'confidence_score': 0.0
            }
    
    def _analyze_traditional_signals(self, fund_code: str) -> Dict[str, Any]:
        """传统技术分析 - 基于真实数据"""
        try:
            # 获取真实技术指标
            tech_data = get_enhanced_technical_indicators(fund_code, 'D')
            indicators = tech_data['indicators']
            
            # 移动平均信号
            if indicators['ma5'] > indicators['ma20'] * 1.02:
                ma_signal = 'buy'
            elif indicators['ma5'] < indicators['ma20'] * 0.98:
                ma_signal = 'sell'
            else:
                ma_signal = 'hold'
            
            # RSI信号
            rsi = indicators['rsi']
            if rsi < 30:
                rsi_signal = 'buy'  # 超卖
            elif rsi > 70:
                rsi_signal = 'sell'  # 超买
            else:
                rsi_signal = 'hold'
            
            # MACD信号
            if indicators['macd_bullish'] and indicators['macd'] > 0.01:
                macd_signal = 'buy'
            elif not indicators['macd_bullish'] and indicators['macd'] < -0.01:
                macd_signal = 'sell'
            else:
                macd_signal = 'hold'
            
            # 计算置信度
            confidence = get_real_confidence_metrics(fund_code, indicators)
            
            return {
                'ma_signal': ma_signal,
                'rsi_signal': rsi_signal,
                'macd_signal': macd_signal,
                'confidence': confidence,
                'technical_indicators': indicators,
                'data_quality': tech_data.get('data_quality', 'unknown'),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error in traditional analysis for {fund_code}: {e}")
            return {'error': str(e)}
    
    def _analyze_quantitative_signals(self, fund_code: str) -> Dict[str, Any]:
        """量化技术分析 - 基于真实数据"""
        try:
            # 获取趋势强度指标
            trend_data = calculate_trend_strength_metrics(fund_code)
            
            # 获取市场情绪指标
            sentiment_data = calculate_market_sentiment_indicators(fund_code)
            
            # 获取成交量分析
            volume_data = get_volume_profile_analysis(fund_code)
            
            # 动量信号
            if trend_data['trend_direction'] == 'up' and trend_data['adx_strength'] > 25:
                momentum_signal = 'buy'
            elif trend_data['trend_direction'] == 'down' and trend_data['adx_strength'] > 25:
                momentum_signal = 'sell'
            else:
                momentum_signal = 'hold'
            
            # 波动率信号
            if sentiment_data['vix_like_indicator'] < 15 and sentiment_data['sentiment_direction'] == 'bullish':
                volatility_signal = 'buy'  # 低波动率上涨环境
            elif sentiment_data['vix_like_indicator'] > 30:
                volatility_signal = 'sell'  # 高波动率避险
            else:
                volatility_signal = 'hold'
            
            # 成交量信号
            if volume_data['volume_strength'] > 1 and volume_data['obv_trend'] > 0.1:
                volume_signal = 'buy'
            elif volume_data['volume_strength'] < -0.5 and volume_data['obv_trend'] < -0.1:
                volume_signal = 'sell'
            else:
                volume_signal = 'hold'
            
            # 综合置信度
            confidence = (
                abs(trend_data['adx_strength'] / 50) * 0.4 +
                sentiment_data['sentiment_strength'] * 0.3 +
                min(1.0, abs(volume_data['volume_strength'])) * 0.3
            )
            
            return {
                'momentum_signal': momentum_signal,
                'volatility_signal': volatility_signal,
                'volume_signal': volume_signal,
                'confidence': min(0.95, max(0.1, confidence)),
                'trend_analysis': trend_data,
                'sentiment_analysis': sentiment_data,
                'volume_analysis': volume_data,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error in quantitative analysis for {fund_code}: {e}")
            return {'error': str(e)}
    
    def _analyze_timeframe_divergence(self, fund_code: str) -> Dict[str, Any]:
        """时间周期背驰分析"""
        try:
            # 创建时间周期背驰分析代理实例
            divergence_agent = TimeframeDivergenceAgent()
            
            # 进行背驰分析
            divergence_result = divergence_agent.process({'fund_code': fund_code})
            
            if 'error' in divergence_result:
                return {
                    'transition_probability': 0.0,
                    'consensus_direction': 'neutral',
                    'transition_signal': 'none',
                    'error': divergence_result['error']
                }
            
            # 提取关键指标
            resonance_analysis = divergence_result.get('resonance_analysis', {})
            transition_probability = divergence_result.get('transition_probability', 0.0)
            
            # 获取算法统计信息
            algorithm_stats = {}
            if hasattr(divergence_agent, 'get_algorithm_stats'):
                algorithm_stats = divergence_agent.get_algorithm_stats()
            
            return {
                'transition_probability': transition_probability,
                'consensus_direction': resonance_analysis.get('consensus_direction', 'neutral'),
                'transition_signal': resonance_analysis.get('transition_signal', 'none'),
                'confidence_level': resonance_analysis.get('confidence_level', 0.0),
                'resonance_score': resonance_analysis.get('resonance_score', 0.0),
                'divergence_results': divergence_result.get('divergence_results', {}),
                'algorithm_stats': algorithm_stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error in timeframe divergence analysis for {fund_code}: {str(e)}")
            return {
                'transition_probability': 0.0,
                'consensus_direction': 'neutral',
                'transition_signal': 'none',
                'error': str(e)
            }
    
    def _fuse_analysis_results(self, traditional: Dict[str, Any], quantitative: Dict[str, Any], timeframe_divergence: Dict[str, Any] = None) -> Dict[str, Any]:
        """融合分析结果"""
        try:
            # 综合各分析结果
            signals = []
            confidences = []
            
            # 传统分析权重
            if 'error' not in traditional:
                if traditional.get('ma_signal') == 'buy':
                    signals.append(1)
                elif traditional.get('ma_signal') == 'sell':
                    signals.append(-1)
                else:
                    signals.append(0)
                confidences.append(traditional.get('confidence', 0.5))
            
            # 量化分析权重
            if 'error' not in quantitative:
                if quantitative.get('momentum_signal') == 'buy':
                    signals.append(1)
                elif quantitative.get('momentum_signal') == 'sell':
                    signals.append(-1)
                else:
                    signals.append(0)
                confidences.append(quantitative.get('confidence', 0.5))
            
            # 时间周期背驰权重
            if timeframe_divergence and 'error' not in timeframe_divergence:
                if timeframe_divergence.get('divergence_signal') == 'buy':
                    signals.append(1)
                elif timeframe_divergence.get('divergence_signal') == 'sell':
                    signals.append(-1)
                else:
                    signals.append(0)
                confidences.append(timeframe_divergence.get('confidence', 0.5))
            
            # 综合信号
            if signals:
                composite_signal = sum(signals) / len(signals)
                confidence_score = sum(confidences) / len(confidences)
            else:
                composite_signal = 0
                confidence_score = 0
            
            buy_signal = composite_signal > 0.3
            sell_signal = composite_signal < -0.3
            
            if buy_signal:
                signal_strength = 'strong' if composite_signal > 0.6 else 'medium'
            elif sell_signal:
                signal_strength = 'strong' if composite_signal < -0.6 else 'medium'
            else:
                signal_strength = 'weak'
            
            return {
                'buy_signal': buy_signal,
                'sell_signal': sell_signal,
                'composite_signal': composite_signal,
                'signal_strength': signal_strength,
                'confidence_score': confidence_score,
                'traditional_analysis': traditional,
                'quantitative_analysis': quantitative,
                'timeframe_divergence': timeframe_divergence,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error fusing analysis results: {e}")
            return {
                'buy_signal': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# ==========================================================
# 索罗斯反身性分析代理
# ==========================================================

class SorosReflexivityAgent(BaseAgent):
    """索罗斯反身性理论分析代理"""
    
    def __init__(self, name: str = "SorosReflexivityAgent"):
        super().__init__(name, "soros_reflexivity_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据并进行反身性分析"""
        fund_code = data.get('fund_code')
        self.logger.info(f"Processing reflexivity analysis for {fund_code}")
        
        try:
            # 简化的反身性分析
            market_data = self.fetch_market_data(fund_code)
            sentiment_data = self.fetch_sentiment_data(fund_code)
            feedback_loop = self.identify_feedback_loops(fund_code)
            judgment = self.generate_judgment(fund_code, feedback_loop)
            
            return judgment
            
        except Exception as e:
            self.logger.error(f"Error in reflexivity analysis for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'action': '观望',
                'error': str(e),
                'confidence': 0.0,
                'timestamp': datetime.now().isoformat()
            }
    
    def fetch_market_data(self, fund_code: str) -> pd.DataFrame:
        """获取市场数据 - 基于真实数据"""
        try:
            df = get_kline(fund_code, freq='D')
            if df.empty:
                raise ValueError(f"No market data available for {fund_code}")
            return df
        except Exception as e:
            self.logger.error(f"Failed to fetch market data for {fund_code}: {e}")
            return pd.DataFrame()
    
    def fetch_sentiment_data(self, fund_code: str) -> Dict[str, Any]:
        """获取情绪数据 - 基于真实数据"""
        try:
            sentiment_data = calculate_market_sentiment_indicators(fund_code)
            return sentiment_data
        except Exception as e:
            self.logger.error(f"Failed to fetch sentiment data for {fund_code}: {e}")
            return {}
    
    def identify_feedback_loops(self, fund_code: str) -> Dict[str, Any]:
        """识别反馈循环 - 基于真实数据"""
        try:
            # 获取市场数据
            market_df = self.fetch_market_data(fund_code)
            if market_df.empty:
                raise ValueError("No market data for feedback analysis")
            
            # 获取情绪数据
            sentiment = self.fetch_sentiment_data(fund_code)
            if not sentiment:
                raise ValueError("No sentiment data for feedback analysis")
            
            # 获取资金流向数据
            flow_data = calculate_real_fund_flow_strength(fund_code)
            
            # 分析反馈强度
            price_momentum = sentiment.get('momentum_sentiment', 0)
            sentiment_score = sentiment.get('composite_sentiment', 0)
            flow_strength = flow_data.get('net_flow_strength', 0)
            
            # 反馈强度：价格动量与情绪/资金流的协调程度
            feedback_strength = abs(price_momentum + sentiment_score + flow_strength) / 3
            
            # 反馈方向
            if sentiment_score > 0.2 and flow_strength > 0.2:
                feedback_direction = 'positive'  # 正反馈
                market_stage = '上涨趋势'
            elif sentiment_score < -0.2 and flow_strength < -0.2:
                feedback_direction = 'negative'  # 负反馈
                market_stage = '下跌趋势'
            else:
                feedback_direction = 'neutral'
                market_stage = '均衡过渡'
            
            return {
                'feedback_strength': min(1.0, max(0.0, feedback_strength)),
                'feedback_direction': feedback_direction,
                'market_stage': market_stage,
                'price_momentum': price_momentum,
                'sentiment_score': sentiment_score,
                'flow_strength': flow_strength
            }
        except Exception as e:
            self.logger.error(f"Failed to identify feedback loops for {fund_code}: {e}")
            # 返回中性结果而非随机结果
            return {
                'feedback_strength': 0.5,
                'feedback_direction': 'neutral',
                'market_stage': '均衡过渡',
                'error': str(e)
            }
    
    def generate_judgment(self, fund_code: str, feedback_loop: Dict[str, Any]) -> Dict[str, Any]:
        """生成反身性判断 - 基于真实数据"""
        try:
            feedback_strength = feedback_loop.get('feedback_strength', 0.5)
            feedback_direction = feedback_loop.get('feedback_direction', 'neutral')
            market_stage = feedback_loop.get('market_stage', '均衡过渡')
            
            # 基于反身性理论的决策逻辑
            if feedback_direction == 'positive' and feedback_strength > 0.6:
                # 强正反馈：趋势可能持续，但要警惕过度
                if feedback_strength > 0.8:
                    action = '观望'  # 过度正反馈，警惕泡沫
                    confidence = 0.6
                    reason = "强正反馈但警惕过度投机"
                else:
                    action = '买入'
                    confidence = 0.7
                    reason = "正反馈循环支持上涨趋势"
            elif feedback_direction == 'negative' and feedback_strength > 0.6:
                # 强负反馈：下跌趋势，但可能出现反转机会
                if feedback_strength > 0.8:
                    action = '买入'  # 过度负反馈，底部机会
                    confidence = 0.6
                    reason = "过度负反馈可能接近底部"
                else:
                    action = '卖出'
                    confidence = 0.7
                    reason = "负反馈循环确认下跌趋势"
            else:
                # 弱反馈或中性：观望为主
                action = '观望'
                confidence = 0.5
                reason = "反馈信号不明确，保持观望"
            
            return {
                'fund_code': fund_code,
                'action': action,
                'confidence': confidence,
                'market_stage': market_stage,
                'feedback_strength': feedback_strength,
                'feedback_direction': feedback_direction,
                'reason': reason,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Failed to generate judgment for {fund_code}: {e}")
            return {
                'fund_code': fund_code,
                'action': '观望',
                'confidence': 0.0,
                'market_stage': '未知',
                'reason': f"分析失败: {str(e)}",
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# ==========================================================
# 增强版基金交易系统V3（完整自动化交易）
# ==========================================================

class EnhancedFundTradingSystemV3:
    """
    增强版基金交易多智能体系统V3
    集成六大维度评估体系的完整自动化交易系统
    """
    
    def __init__(self, title: str = ""):
        # 配置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化所有代理
        self.technical_agent = TechnicalAgent()
        self.enhanced_technical_agent = EnhancedTechnicalAgent()
        self.gua_agent = GuaAnalysisAgent()
        self.fund_flow_agent = FundFlowAgent()
        self.soros_agent = SorosReflexivityAgent()
        self.trading_agent = TradingAgent(title=title)
        
        # 初始化V3新增组件
        self.coordinator = MultiAgentCoordinatorV3()
        self.executor = FundTradingExecutorV3()
        
        # 交易基金列表
        self.buy_fund_list = [
            '513030', '513080', '513500', '513520', '513300', 
            '513850', '159329', '159561', '520830', '159567',
            '518880', '601398'
        ]
        
        # 新闻和指数检查时间段
        self.news_index_time = [
            ['09:27:00', '09:32:00'], ['10:00:00', '10:06:00'], 
            ['10:30:00', '10:36:00'], ['11:00:00', '11:06:00'],
            ['13:00:00', '13:06:00'], ['13:30:00', '13:36:00'], 
            ['14:00:00', '14:06:00'], ['14:30:00', '14:36:00']
        ]
        
        # 并行处理配置
        self.max_workers = 4
        self.timeout = 30
        
        self.logger.info("Enhanced Fund Trading System V3 initialized")
        self.logger.info("🔄 V3版本特性: 六大维度评估 + 智能冲突解决 + 动态权重管理")

        
    def analyze_fund_v3(self, fund_code: str) -> Dict[str, Any]:
        """V3版本的增强基金分析"""
        self.logger.info(f"Starting V3 enhanced analysis for fund {fund_code}")
        
        # 使用V3协调器进行分析
        analysis_result = self.coordinator.coordinate_analysis(fund_code)
        
        # 获取账户信息
        account_data = self.trading_agent.get_account_info()
        analysis_result['account_data'] = account_data
        
        return analysis_result
    
    def execute_trading_decision_v3(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """V3版本的交易决策执行"""
        fund_code = analysis_result.get('fund_code')
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        
        # 获取决策信息
        final_decision = enhanced_decision.get('recommendation', 'hold')
        confidence = enhanced_decision.get('confidence', 0.0)
        
        if final_decision in ['buy', 'sell'] and confidence > 0.5:
            # 使用V3执行器进行交易
            execution_result = self.executor.execute_decision(analysis_result)
            
            # 如果V3执行器也支持，同时使用真实交易代理
            if execution_result.get('should_execute', False):
                # 准备交易数据
                trade_data = {
                    'action': final_decision,
                    'fund_code': fund_code,
                    'price': execution_result.get('execution_price', 1.0),
                    'quantity': execution_result.get('execution_quantity', '20000')
                }
                
                # 执行真实交易
                trade_result = self.trading_agent.process(trade_data)
                execution_result['real_trade_result'] = trade_result
                
                self.logger.info(f"Real trade executed for {fund_code}: {trade_result}")
            
            return execution_result
        else:
            return {
                'fund_code': fund_code,
                'action': 'hold',
                'reason': f"Decision: {final_decision}, Confidence: {confidence:.2f} (threshold: 0.5)",
                'timestamp': datetime.now().isoformat()
            }
    
    def run_trading_cycle_v3(self) -> List[Dict[str, Any]]:
        """运行V3版本的单次交易周期"""
        cycle_start_time = datetime.now()
        results = []
        
        # 测试基金列表
        fund_codes = self.buy_fund_list
        
        print(f"\n🔄 V3 交易周期开始 - {len(fund_codes)} 只基金 [{cycle_start_time.strftime('%H:%M:%S')}]")
        
        for fund_code in fund_codes:
            try:
                # 执行V3分析
                analysis_result = self.analyze_fund_v3(fund_code)
                
                if 'error' not in analysis_result:
                    # 展示详细分析结果
                    self.display_detailed_analysis_result(analysis_result)
                    
                    # 执行交易决策
                    execution_result = self.execute_trading_decision_v3(analysis_result)
                    result = {
                        'fund_code': fund_code,
                        'analysis_result': analysis_result,
                        'execution_result': execution_result
                    }
                else:
                    print(f"\n❌ 基金 {fund_code} 分析失败: {analysis_result['error']}")
                    result = {
                        'fund_code': fund_code,
                        'error': analysis_result['error']
                    }
                    
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Trading cycle error for {fund_code}: {e}")
                results.append({
                    'fund_code': fund_code,
                    'error': str(e)
                })
        
        # 周期结束摘要
        cycle_end_time = datetime.now()
        cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
        
        # 统计结果
        successful_analysis = len([r for r in results if 'error' not in r])
        failed_analysis = len([r for r in results if 'error' in r])
        
        # 统计决策类型
        decisions = {}
        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})
                enhanced_decision = analysis_result.get('enhanced_decision', {})
                decision = enhanced_decision.get('final_decision', 'hold')
                decisions[decision] = decisions.get(decision, 0) + 1
        
        # 简化输出
        decision_summary = " | ".join([f"{k.upper()}:{v}" for k, v in decisions.items()]) if decisions else "无决策"
        print(f"🏁 周期完成: 耗时{cycle_duration:.1f}s | 成功{successful_analysis}/{len(fund_codes)} | 决策分布: {decision_summary}\n")
        
        return results
    
    def display_detailed_analysis_result(self, analysis_result: Dict[str, Any]) -> None:
        """展示精简的分析结果"""
        try:
            fund_code = analysis_result.get('fund_code', 'UNKNOWN')
            enhanced_decision = analysis_result.get('enhanced_decision', {})
            
            # 核心决策结果 - 一行显示
            final_decision = enhanced_decision.get('final_decision', 'hold')
            decision_confidence = enhanced_decision.get('decision_confidence', 0)
            coordination_score = enhanced_decision.get('coordination_score', 0)
            weighted_score = enhanced_decision.get('weighted_score', None)
            final_score = enhanced_decision.get('final_score', None)
            buy_threshold = enhanced_decision.get('buy_threshold', None)
            
            decision_icon = "🟢" if final_decision == "buy" else ("🔴" if final_decision == "sell" else "🟡")
            # 统一所有信号类型的输出格式
            if weighted_score is not None and final_score is not None and buy_threshold is not None:
                print(f"\n{decision_icon} {fund_code}: {final_decision.upper()} (score={weighted_score:.3f}, final_score={final_score:.3f}, buy_th={buy_threshold:.3f}) | 置信度:{decision_confidence:.2f} | 协调:{coordination_score:.2f}")
            else:
                print(f"\n{decision_icon} {fund_code}: {final_decision.upper()} | 置信度:{decision_confidence:.2f} | 协调:{coordination_score:.2f}")
            
            # 关键信息 - 精简版
            analysis_summary = enhanced_decision.get('analysis_summary', {})
            if analysis_summary:
                
                # 关键洞察 - 只显示前2个
                deep_insights = analysis_summary.get('deep_insights', [])
                if deep_insights:
                    print(f"   📊 洞察: {' | '.join(deep_insights[:2])}")
                
                # 风险和建议 - 各显示1个最重要的
                risk_analysis = analysis_summary.get('risk_analysis', {})
                if risk_analysis:
                    risk_factors = risk_analysis.get('risk_factors', [])
                    mitigation_strategies = risk_analysis.get('mitigation_strategies', [])
                    
                    if risk_factors:
                        print(f"   ⚠️ 风险: {risk_factors[0]}")
                    if mitigation_strategies:
                        print(f"   🛡️ 策略: {mitigation_strategies[0]}")
                
                # 操作建议 - 只显示第一个
                operation_suggestions = enhanced_decision.get('operation_suggestions', [])
                if operation_suggestions:
                    print(f"   💡 建议: {operation_suggestions[0]}")
            
        except Exception as e:
            print(f"❌ {fund_code} 分析展示失败: {str(e)}")
    
    def run_trading_system_v3(self) -> None:
        """V3版本的自动化交易系统主循环"""
        self.logger.info("Starting V3 Enhanced Fund Trading System")
        
        try:
            # 主交易循环
            while ('06:27:00' <= datetime.now().strftime('%H:%M:%S') <= '11:39:00') or \
                  ('12:50:00' <= datetime.now().strftime('%H:%M:%S') <= '18:55:00'):
                  
                # 运行V3交易周期
                results = self.run_trading_cycle_v3()
                
                # 休息间隔
                time.sleep(15)
                
        except KeyboardInterrupt:
            self.logger.info("V3 Trading system interrupted by user")
        except Exception as e:
            self.logger.error(f"V3 Trading system error: {e}")
        finally:
            self.logger.info("V3 Enhanced trading system stopped")
    
    def diagnose_system_status_v3(self, fund_code: str = '513500') -> Dict[str, Any]:
        """V3版本的系统诊断"""
        self.logger.info(f"🔧 V3系统诊断开始 - 基金: {fund_code}")
        
        diagnosis = {
            'fund_code': fund_code,
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'issues_found': [],
            'recommendations': [],
            'component_status': {},
            'v3_features_status': {}
        }
        
        try:
            # 1. 测试V3增强分析
            self.logger.info("🔍 测试V3六大维度分析...")
            analysis_result = self.analyze_fund_v3(fund_code)
            if 'error' in analysis_result:
                diagnosis['issues_found'].append("V3 enhanced analysis failed")
                diagnosis['component_status']['v3_analysis'] = 'failed'
            else:
                diagnosis['component_status']['v3_analysis'] = 'working'
                
            # 2. 测试传统代理状态
            self.logger.info("🔍 测试传统代理状态...")
            for agent_name, agent in [
                ('technical', self.technical_agent),
                ('enhanced_technical', self.enhanced_technical_agent),
                ('gua', self.gua_agent),
                ('flow', self.fund_flow_agent),
                ('soros', self.soros_agent)
            ]:
                try:
                    result = agent.process({'fund_code': fund_code})
                    if 'error' in result:
                        diagnosis['component_status'][agent_name] = 'error'
                        diagnosis['issues_found'].append(f"{agent_name} agent has errors")
                    else:
                        diagnosis['component_status'][agent_name] = 'working'
                except Exception as e:
                    diagnosis['component_status'][agent_name] = 'failed'
                    diagnosis['issues_found'].append(f"{agent_name} agent failed: {str(e)}")
                    
            # 3. 测试交易功能

                
            # 4. V3特性检查
            diagnosis['v3_features_status']['six_dimension_analysis'] = 'available'
            diagnosis['v3_features_status']['signal_conflict_resolution'] = 'available'
            diagnosis['v3_features_status']['dynamic_weight_management'] = 'available'
            diagnosis['v3_features_status']['fractal_validation'] = 'available'
            
            # 确定整体状态
            if len(diagnosis['issues_found']) == 0:
                diagnosis['overall_status'] = 'healthy'
            elif len(diagnosis['issues_found']) <= 2:
                diagnosis['overall_status'] = 'warning'
            else:
                diagnosis['overall_status'] = 'critical'
                
            self.logger.info(f"✅ V3系统诊断完成 - 状态: {diagnosis['overall_status']}")
            
        except Exception as e:
            diagnosis['overall_status'] = 'critical'
            diagnosis['issues_found'].append(f"System diagnosis failed: {str(e)}")
            self.logger.error(f"V3 system diagnosis error: {e}")
            
        return diagnosis

# =============================================================================
# 日志配置
# =============================================================================

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('fund_trading_v3.log', encoding='utf-8')
        ]
    )

# =============================================================================
# 基金特性分析器和MACD参数优化器 (从V2版本集成)
# =============================================================================

class FundCharacteristicsAnalyzer:
    """基金特性分析器"""
    
    def __init__(self):
        self.fund_profiles = {}
        self.feature_weights = {
            'liquidity': 0.4, 'volatility': 0.3, 'scale': 0.2, 'correlation': 0.1
        }
        self.fund_categories = {
            '513500': 'large_cap_equity', '513300': 'large_cap_equity',
            '513030': 'developed_equity', '513080': 'developed_equity',
            '513520': 'emerging_equity', '513850': 'emerging_equity',
            '159329': 'china_equity', '159561': 'china_equity',
            '520830': 'bond_fund', '159567': 'commodity_fund'
        }
    
    def analyze_fund_characteristics(self, fund_code: str, df: pd.DataFrame, fund_volume: float) -> Dict[str, float]:
        """分析基金特性"""
        try:
            liquidity_score = self.calculate_liquidity_score(fund_code, fund_volume)
            volatility_score = self.calculate_volatility_score(df)
            scale_score = self.calculate_scale_score(fund_volume)
            correlation_score = self.calculate_correlation_score(fund_code)
            
            category = self.get_fund_category(fund_code)
            category_adjustment = self._get_category_adjustment(category)
            
            characteristics = {
                'liquidity_score': liquidity_score,
                'volatility_score': volatility_score,
                'scale_score': scale_score,
                'correlation_score': correlation_score,
                'category': category,
                'category_adjustment': category_adjustment,
                'composite_score': (
                    liquidity_score * self.feature_weights['liquidity'] +
                    volatility_score * self.feature_weights['volatility'] +
                    scale_score * self.feature_weights['scale'] +
                    correlation_score * self.feature_weights['correlation']
                )
            }
            
            self.fund_profiles[fund_code] = characteristics
            return characteristics
            
        except Exception as e:
            logging.error(f"基金特性分析失败 {fund_code}: {e}")
            return self._get_default_characteristics()
    
    def calculate_liquidity_score(self, fund_code: str, fund_volume: float) -> float:
        """计算流动性评分"""
        try:
            scale_factor = min(1.0, fund_volume / 10_000_000_000)
            try:
                df = get_kline(fund_code, freq='D')
                if not df.empty and len(df) >= 5:
                    recent_volumes = df['vol'].tail(5).mean()
                    turnover_rate = recent_volumes * 100 / fund_volume if fund_volume > 0 else 0
                    turnover_factor = min(1.0, turnover_rate / 5.0)
                else:
                    turnover_factor = 0.5
            except:
                turnover_factor = 0.5
            
            liquidity_score = scale_factor * 0.6 + turnover_factor * 0.4
            return max(0.1, min(1.0, liquidity_score))
        except Exception:
            return 0.5
    
    def calculate_volatility_score(self, df: pd.DataFrame) -> float:
        """计算波动性评分"""
        try:
            if df.empty or len(df) < 20:
                return 0.5
            
            returns = df['close'].pct_change().dropna()
            if len(returns) < 10:
                return 0.5
            
            volatility = returns.tail(20).std()
            annualized_vol = volatility * np.sqrt(252)
            volatility_score = min(1.0, annualized_vol / 0.3)
            return max(0.1, min(1.0, volatility_score))
        except Exception:
            return 0.5
    
    def calculate_scale_score(self, fund_volume: float) -> float:
        """计算规模评分"""
        try:
            scale_score = min(1.0, fund_volume / 50_000_000_000)
            return max(0.1, scale_score)
        except Exception:
            return 0.5
    
    def calculate_correlation_score(self, fund_code: str) -> float:
        """计算关联性评分"""
        try:
            category = self.get_fund_category(fund_code)
            correlation_map = {
                'large_cap_equity': 0.8, 'developed_equity': 0.7, 'emerging_equity': 0.6,
                'china_equity': 0.9, 'bond_fund': 0.3, 'commodity_fund': 0.4
            }
            return correlation_map.get(category, 0.5)
        except Exception:
            return 0.5
    
    def get_fund_category(self, fund_code: str) -> str:
        """获取基金类别"""
        return self.fund_categories.get(fund_code, 'unknown')
    
    def _get_category_adjustment(self, category: str) -> Dict[str, float]:
        """获取类别调整系数"""
        adjustments = {
            'large_cap_equity': {'sensitivity': -0.1, 'smoothness': 0.1},
            'developed_equity': {'sensitivity': 0.0, 'smoothness': 0.0},
            'emerging_equity': {'sensitivity': 0.1, 'smoothness': -0.1},
            'china_equity': {'sensitivity': 0.0, 'smoothness': 0.0},
            'bond_fund': {'sensitivity': -0.2, 'smoothness': 0.2},
            'commodity_fund': {'sensitivity': 0.15, 'smoothness': -0.15}
        }
        return adjustments.get(category, {'sensitivity': 0.0, 'smoothness': 0.0})
    
    def _get_default_characteristics(self) -> Dict[str, float]:
        """获取默认特性"""
        return {
            'liquidity_score': 0.5, 'volatility_score': 0.5, 'scale_score': 0.5,
            'correlation_score': 0.5, 'category': 'unknown',
            'category_adjustment': {'sensitivity': 0.0, 'smoothness': 0.0},
            'composite_score': 0.5
        }


class DynamicMACDOptimizer:
    """MACD参数动态优化器"""
    
    def __init__(self):
        self.base_params = {'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9}
        self.param_ranges = {'macd_fast': (6, 20), 'macd_slow': (20, 40), 'macd_signal': (6, 15)}
        self.optimization_stats = {'total_optimizations': 0, 'cache_hits': 0, 'parameter_adjustments': 0}
    
    def calculate_optimal_params(self, fund_features: Dict[str, float]) -> Dict[str, int]:
        """计算最优MACD参数"""
        try:
            self.optimization_stats['total_optimizations'] += 1
            
            liquidity_score = fund_features.get('liquidity_score', 0.5)
            volatility_score = fund_features.get('volatility_score', 0.5)
            category_adjustment = fund_features.get('category_adjustment', {})
            
            optimized_params = self.base_params.copy()
            optimized_params = self.optimize_for_liquidity(liquidity_score, optimized_params)
            optimized_params = self.optimize_for_volatility(volatility_score, optimized_params)
            optimized_params = self.apply_category_adjustment(optimized_params, category_adjustment)
            
            if self.validate_params(optimized_params):
                self.optimization_stats['parameter_adjustments'] += 1
                return optimized_params
            else:
                return self.base_params
        except Exception as e:
            logging.error(f"MACD参数优化失败: {e}")
            return self.base_params
    
    def optimize_for_liquidity(self, liquidity_score: float, base_params: Dict[str, int]) -> Dict[str, int]:
        """基于流动性优化参数"""
        try:
            params = base_params.copy()
            
            if liquidity_score > 0.7:
                adjustment = -2
            elif liquidity_score > 0.5:
                adjustment = -1
            elif liquidity_score < 0.3:
                adjustment = 2
            elif liquidity_score < 0.5:
                adjustment = 1
            else:
                adjustment = 0
            
            params['macd_fast'] = max(self.param_ranges['macd_fast'][0],
                                     min(self.param_ranges['macd_fast'][1], params['macd_fast'] + adjustment))
            params['macd_slow'] = max(self.param_ranges['macd_slow'][0],
                                     min(self.param_ranges['macd_slow'][1], params['macd_slow'] + adjustment * 2))
            return params
        except Exception:
            return base_params
    
    def optimize_for_volatility(self, volatility_score: float, base_params: Dict[str, int]) -> Dict[str, int]:
        """基于波动性优化参数"""
        try:
            params = base_params.copy()
            
            if volatility_score > 0.8:
                signal_adjustment = 3
            elif volatility_score > 0.6:
                signal_adjustment = 2
            elif volatility_score > 0.4:
                signal_adjustment = 1
            elif volatility_score < 0.2:
                signal_adjustment = -1
            else:
                signal_adjustment = 0
            
            params['macd_signal'] = max(self.param_ranges['macd_signal'][0],
                                       min(self.param_ranges['macd_signal'][1], params['macd_signal'] + signal_adjustment))
            return params
        except Exception:
            return base_params
    
    def apply_category_adjustment(self, params: Dict[str, int], category_adjustment: Dict[str, float]) -> Dict[str, int]:
        """应用类别调整"""
        try:
            adjusted_params = params.copy()
            sensitivity_adj = category_adjustment.get('sensitivity', 0.0)
            smoothness_adj = category_adjustment.get('smoothness', 0.0)
            
            if abs(sensitivity_adj) > 0.05:
                fast_adj = int(sensitivity_adj * -10)
                slow_adj = int(sensitivity_adj * -20)
                
                adjusted_params['macd_fast'] = max(self.param_ranges['macd_fast'][0],
                                                  min(self.param_ranges['macd_fast'][1], adjusted_params['macd_fast'] + fast_adj))
                adjusted_params['macd_slow'] = max(self.param_ranges['macd_slow'][0], 
                                                  min(self.param_ranges['macd_slow'][1], adjusted_params['macd_slow'] + slow_adj))
            
            if abs(smoothness_adj) > 0.05:
                signal_adj = int(smoothness_adj * 10)
                adjusted_params['macd_signal'] = max(self.param_ranges['macd_signal'][0],
                                                    min(self.param_ranges['macd_signal'][1], adjusted_params['macd_signal'] + signal_adj))
            
            return adjusted_params
        except Exception:
            return params
    
    def validate_params(self, params: Dict[str, int]) -> bool:
        """验证参数有效性"""
        try:
            for param_name, value in params.items():
                if param_name in self.param_ranges:
                    min_val, max_val = self.param_ranges[param_name]
                    if not (min_val <= value <= max_val):
                        return False
            
            if params['macd_fast'] >= params['macd_slow']:
                return False
            if params['macd_signal'] > params['macd_fast']:
                return False
            
            return True
        except Exception:
            return False
    
    def get_optimization_stats(self) -> Dict[str, int]:
        """获取优化统计信息"""
        return self.optimization_stats.copy()


class ParameterCacheManager:
    """参数缓存管理器"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_duration = 3600
        self.cache_stats = {'hits': 0, 'misses': 0, 'evictions': 0}
    
    def get_cached_params(self, fund_code: str) -> Optional[Dict[str, int]]:
        """获取缓存的参数"""
        try:
            if fund_code in self.cache and self.is_cache_valid(fund_code):
                self.cache_stats['hits'] += 1
                return self.cache[fund_code].copy()
            else:
                self.cache_stats['misses'] += 1
                return None
        except Exception:
            self.cache_stats['misses'] += 1
            return None
    
    def cache_params(self, fund_code: str, params: Dict[str, int]) -> None:
        """缓存参数"""
        try:
            self.cache[fund_code] = params.copy()
            self.cache_timestamps[fund_code] = time.time()
            self.cleanup_expired_cache()
        except Exception as e:
            logging.error(f"参数缓存失败 {fund_code}: {e}")
    
    def is_cache_valid(self, fund_code: str) -> bool:
        """检查缓存是否有效"""
        try:
            if fund_code not in self.cache_timestamps:
                return False
            cache_time = self.cache_timestamps[fund_code]
            current_time = time.time()
            return (current_time - cache_time) < self.cache_duration
        except Exception:
            return False
    
    def cleanup_expired_cache(self) -> None:
        """清理过期缓存"""
        try:
            current_time = time.time()
            expired_keys = []
            
            for fund_code, cache_time in self.cache_timestamps.items():
                if (current_time - cache_time) > self.cache_duration:
                    expired_keys.append(fund_code)
            
            for key in expired_keys:
                if key in self.cache:
                    del self.cache[key]
                if key in self.cache_timestamps:
                    del self.cache_timestamps[key]
                self.cache_stats['evictions'] += 1
        except Exception as e:
            logging.error(f"缓存清理失败: {e}")
    
    def clear_cache(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
        self.cache_timestamps.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / max(1, total_requests) * 100
        
        return {
            'cache_size': len(self.cache),
            'hit_rate': round(hit_rate, 2),
            'total_requests': total_requests,
            'stats': self.cache_stats.copy()
        }

# =============================================================================
# CZSC数据适配器 (从V2版本集成)  
# =============================================================================

class CzscDataAdapter:
    """CZSC数据适配器"""
    
    @staticmethod
    def freq_str_to_enum(freq_str: str):
        """将频率字符串转换为CZSC频率枚举"""
        try:
            freq_mapping = {
                '1min': Freq.F1,
                '5min': Freq.F5,
                '15min': Freq.F15,
                '30min': Freq.F30,
                '60min': Freq.F60,
                'D': Freq.D,
                'W': Freq.W,
                'M': Freq.M
            }
            return freq_mapping.get(freq_str, Freq.F1)
        except Exception:
            return Freq.F1  # 默认1分钟
    
    @staticmethod
    def pandas_to_rawbars(df: pd.DataFrame, symbol: str, freq: str) -> List[Any]:
        """将Pandas DataFrame转换为CZSC RawBar列表"""
        try:
            
            bars = []
            freq_enum = CzscDataAdapter.freq_str_to_enum(freq)
            
            for i, row in df.iterrows():
                bar = CzscRawBar(
                    symbol=symbol,
                    id=i,
                    freq=freq_enum, 
                    dt=pd.to_datetime(row['dt']) if 'dt' in row else pd.to_datetime(row.name),
                    open=float(row['open']),
                    close=float(row['close']),
                    high=float(row['high']),
                    low=float(row['low']),
                    vol=float(row.get('vol', row.get('volume', 0))),
                    amount=float(row.get('amount', 0))
                )
                bars.append(bar)
            
            return bars
        except Exception as e:
            logging.error(f"数据转换失败: {e}")
            return []
    
    @staticmethod
    def bi_to_czsc_structure(bi: Any, macd_data: Dict[str, np.ndarray], df: pd.DataFrame = None) -> CzscBiStructure:
        """将CZSC BI转换为系统的笔结构数据"""
        try:
            
            # 获取笔的基本信息
            bi_direction = "up" if bi.direction.value == "向上" else "down"
            bi_start_price = bi.fx_a.fx
            bi_end_price = bi.fx_b.fx
            bi_amplitude = abs(bi_end_price - bi_start_price)
            bi_duration = len(bi.bars)
            
            # 计算对应的MACD面积和DIF峰值
            start_idx = 0
            end_idx = len(macd_data['macd']) - 1
            
            if df is not None:
                try:
                    start_dt = bi.fx_a.dt
                    end_dt = bi.fx_b.dt
                    
                    df_with_dt = df.copy()
                    if 'dt' not in df_with_dt.columns:
                        df_with_dt.reset_index(inplace=True)
                    
                    df_with_dt['dt'] = pd.to_datetime(df_with_dt['dt'])
                    start_idx = df_with_dt[df_with_dt['dt'] <= start_dt].index[-1] if len(df_with_dt[df_with_dt['dt'] <= start_dt]) > 0 else 0
                    end_idx = df_with_dt[df_with_dt['dt'] <= end_dt].index[-1] if len(df_with_dt[df_with_dt['dt'] <= end_dt]) > 0 else len(df_with_dt) - 1
                except Exception:
                    # 如果时间匹配失败，使用默认索引
                    pass
            
            # 计算MACD面积
            macd_slice = macd_data['macd'][start_idx:end_idx+1]
            macd_area = np.sum(np.abs(macd_slice))
            
            # 计算DIF峰值
            dif_slice = macd_data['dif'][start_idx:end_idx+1]
            if bi_direction == "up":
                dif_peak = np.max(dif_slice)
            else:
                dif_peak = np.min(dif_slice)
            
            return CzscBiStructure(
                bi_direction=bi_direction,
                bi_start_price=bi_start_price,
                bi_end_price=bi_end_price,
                bi_amplitude=bi_amplitude,
                bi_duration=bi_duration,
                macd_area=macd_area,
                dif_peak=dif_peak
            )
            
        except Exception as e:
            logging.error(f"BI结构转换失败: {e}")
            # 返回默认结构
            return CzscBiStructure(
                bi_direction="unknown",
                bi_start_price=0.0,
                bi_end_price=0.0,
                bi_amplitude=0.0,
                bi_duration=0,
                macd_area=0.0,
                dif_peak=0.0
            )

# =============================================================================
# 时间框架背驰分析代理 (从V2版本集成)
# =============================================================================

class TimeframeDivergenceAgent(BaseAgent):
    """
    基于缠论背驰理论，实现多时间框架的背驰检测和变盘分析
    
    主要特性：
    - 🔄 动态MACD参数优化：基于基金特性自动调整参数
    - 📊 多维度基金特性分析：流动性、波动性、规模、关联性
    - 🚀 智能缓存机制：提高参数计算效率
    - 📈 专业CZSC分型算法：精确笔结构识别
    - 🎯 多时间框架共振分析：短中长期信号整合
    """
    
    def __init__(self, name: str = "TimeframeDivergenceAgent"):
        super().__init__(name, "timeframe_divergence_analysis")
        
        # 初始化优化组件
        self.fund_analyzer = FundCharacteristicsAnalyzer()
        self.macd_optimizer = DynamicMACDOptimizer()
        self.cache_manager = ParameterCacheManager()
        
        # 时间框架配置
        self.timeframes = ['1min', '5min', '30min']
        self.lookback_periods = {'1min': 100, '5min': 200, '30min': 300}
        
        # 性能统计
        self.performance_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'cache_hit_rate': 0.0,
            'avg_processing_time': 0.0,
            'fx_error_rates': {},
            'last_update': datetime.now()
        }
        
        # CZSC分析器缓存
        self.czsc_analyzers = {}
        self.czsc_cache_timestamps = {}
        self.czsc_cache_duration = 1800  # 30分钟缓存
        
        # 分型性能统计
        self.fx_performance_stats = {}
        
        self.logger.info("TimeframeDivergenceAgent initialized with enhanced features")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理时间框架背驰分析请求
        
        @param data: 包含基金代码的数据字典
        @return: 多时间框架背驰分析结果
        """
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
        
        start_time = time.time()
        self.performance_stats['total_analyses'] += 1
        
        try:
            self.logger.info(f"开始时间框架背驰分析: {fund_code}")
            
            # 执行多时间框架背驰分析
            divergence_results = {}
            
            for timeframe in self.timeframes:
                try:
                    divergence_result = self.detect_macd_divergence(fund_code, timeframe)
                    divergence_results[timeframe] = divergence_result
                    self.logger.debug(f"{timeframe} 背驰分析完成: {divergence_result.divergence_type}")
                except Exception as e:
                    self.logger.error(f"{timeframe} 背驰分析失败: {e}")
                    # 创建默认结果
                    divergence_results[timeframe] = TimeframeCycleDivergence(
                        timeframe=timeframe,
                        divergence_type="none", 
                        divergence_strength=0.0,
                        macd_area_ratio=1.0,
                        dif_peak_comparison={},
                        confirmation_bars=0,
                        cycle_position="unknown"
                    )
            
            # 分析多时间框架共振
            resonance_result = self.analyze_cycle_resonance(divergence_results)
            
            # 计算变盘概率
            transition_probability = self.calculate_transition_probability(resonance_result)
            
            # 处理时间统计
            processing_time = time.time() - start_time
            self.performance_stats['avg_processing_time'] = (
                (self.performance_stats['avg_processing_time'] * (self.performance_stats['total_analyses'] - 1) + processing_time) 
                / self.performance_stats['total_analyses']
            )
            
            # 构建返回结果
            result = {
                'fund_code': fund_code,
                'analysis_time': datetime.now().isoformat(),
                'processing_time': processing_time,
                'divergence_results': {
                    timeframe: {
                        'divergence_type': result.divergence_type,
                        'divergence_strength': result.divergence_strength,
                        'macd_area_ratio': result.macd_area_ratio,
                        'dif_peak_comparison': result.dif_peak_comparison,
                        'confirmation_bars': result.confirmation_bars,
                        'cycle_position': result.cycle_position
                    } for timeframe, result in divergence_results.items()
                },
                'resonance_analysis': {
                    'resonance_score': resonance_result.resonance_score,
                    'consensus_direction': resonance_result.consensus_direction,
                    'confidence_level': resonance_result.confidence_level,
                    'transition_signal': resonance_result.transition_signal
                },
                'transition_probability': transition_probability,
                'performance_stats': self.get_algorithm_stats()
            }
            
            self.performance_stats['successful_analyses'] += 1
            self.performance_stats['last_update'] = datetime.now()
            
            # 更新缓存统计
            cache_stats = self.cache_manager.get_cache_stats()
            self.performance_stats['cache_hit_rate'] = cache_stats['hit_rate']
            
            self.logger.info(f"时间框架背驰分析完成: {fund_code}, 耗时: {processing_time:.3f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"时间框架背驰分析失败 {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'analysis_time': datetime.now().isoformat()
            }
    
    def detect_macd_divergence(self, fund_code: str, timeframe: str) -> TimeframeCycleDivergence:
        """
        检测指定时间框架的MACD背驰
        
        @param fund_code: 基金代码
        @param timeframe: 时间框架
        @return: 时间周期背驰分析结果
        """
        try:
            self.logger.debug(f"检测MACD背驰: {fund_code} - {timeframe}")
            
            # 获取K线数据
            df = self._get_kline_data(fund_code, timeframe)
            if df.empty:
                raise ValueError(f"无法获取{timeframe}数据")
            
            # 获取或计算优化的MACD参数
            macd_params = self._get_optimized_params(fund_code, df)
            
            # 计算MACD指标
            macd_data = self._calculate_macd_indicators(df, macd_params)
            
            # 提取笔结构
            bi_structures = self._extract_bi_structures(df, macd_data)
            
            if len(bi_structures) < 3:
                self.logger.warning(f"笔结构数量不足: {len(bi_structures)}")
                return TimeframeCycleDivergence(
                    timeframe=timeframe,
                    divergence_type="insufficient_data",
                    divergence_strength=0.0,
                    macd_area_ratio=1.0,
                    dif_peak_comparison={},
                    confirmation_bars=0,
                    cycle_position="unknown"
                )
            
            # 分析背驰
            divergence_analysis = self._analyze_divergence(bi_structures)
            
            # 确定周期位置
            cycle_position = self._determine_cycle_position(df, macd_data)
            
            return TimeframeCycleDivergence(
                timeframe=timeframe,
                divergence_type=divergence_analysis['type'],
                divergence_strength=divergence_analysis['strength'],
                macd_area_ratio=divergence_analysis['area_ratio'],
                dif_peak_comparison=divergence_analysis['peak_comparison'],
                confirmation_bars=divergence_analysis['confirmation_bars'],
                cycle_position=cycle_position
            )
            
        except Exception as e:
            self.logger.error(f"MACD背驰检测失败 {fund_code}-{timeframe}: {e}")
            return TimeframeCycleDivergence(
                timeframe=timeframe,
                divergence_type="error",
                divergence_strength=0.0,
                macd_area_ratio=1.0,
                dif_peak_comparison={},
                confirmation_bars=0,
                cycle_position="unknown"
            )
    
    def analyze_cycle_resonance(self, divergence_results: Dict[str, Any]) -> MultiTimeframeResonance:
        """
        分析多时间框架共振
        
        @param divergence_results: 各时间框架的背驰结果
        @return: 多时间框架共振结果
        """
        try:
            # 提取各时间框架结果
            short_term = divergence_results.get('1min', divergence_results.get('5min'))
            medium_term = divergence_results.get('30min', divergence_results.get('1min'))
            long_term = divergence_results.get('30min', medium_term)
            
            # 计算共振评分
            resonance_score = self._calculate_resonance_score(short_term, medium_term, long_term)
            
            # 确定共识方向
            consensus_direction = self._determine_consensus_direction(short_term, medium_term, long_term)
            
            # 计算置信水平
            confidence_level = self._calculate_confidence_level(short_term, medium_term, long_term)
            
            # 确定变盘信号强度
            transition_signal = self._determine_transition_signal(resonance_score, confidence_level)
            
            return MultiTimeframeResonance(
                short_term=short_term,
                medium_term=medium_term,
                long_term=long_term,
                resonance_score=resonance_score,
                consensus_direction=consensus_direction,
                confidence_level=confidence_level,
                transition_signal=transition_signal
            )
            
        except Exception as e:
            self.logger.error(f"多时间框架共振分析失败: {e}")
            # 返回默认结果
            default_divergence = TimeframeCycleDivergence(
                timeframe="unknown",
                divergence_type="error",
                divergence_strength=0.0,
                macd_area_ratio=1.0,
                dif_peak_comparison={},
                confirmation_bars=0,
                cycle_position="unknown"
            )
            
            return MultiTimeframeResonance(
                short_term=default_divergence,
                medium_term=default_divergence,
                long_term=default_divergence,
                resonance_score=0.0,
                consensus_direction="neutral",
                confidence_level=0.0,
                transition_signal="none"
            )
    
    def calculate_transition_probability(self, resonance_result: MultiTimeframeResonance) -> float:
        """
        基于共振结果计算变盘概率
        
        @param resonance_result: 多时间框架共振结果
        @return: 变盘概率 (0-1)
        """
        try:
            base_probability = resonance_result.resonance_score * 0.6
            confidence_bonus = resonance_result.confidence_level * 0.3
            
            # 信号强度奖励
            signal_bonus = 0.0
            if resonance_result.transition_signal == "strong":
                signal_bonus = 0.2
            elif resonance_result.transition_signal == "medium":
                signal_bonus = 0.1
            elif resonance_result.transition_signal == "weak":
                signal_bonus = 0.05
            
            # 方向一致性奖励
            direction_bonus = 0.0
            if resonance_result.consensus_direction in ['bullish', 'bearish']:
                direction_bonus = 0.1
            
            total_probability = base_probability + confidence_bonus + signal_bonus + direction_bonus
            return max(0.0, min(1.0, total_probability))
            
        except Exception as e:
            self.logger.error(f"变盘概率计算失败: {e}")
            return 0.0
    
    def _get_kline_data(self, fund_code: str, timeframe: str) -> pd.DataFrame:
        """获取K线数据 - 仅使用真实数据"""
        try:
            # 直接使用czsc_func.py获取真实K线数据
            df = get_kline(fund_code, freq=timeframe)
            
            if df.empty:
                raise ValueError(f"No data available for {fund_code} on {timeframe} timeframe")
            
            # 验证数据质量
            required_columns = ['dt', 'open', 'close', 'high', 'low', 'vol']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # 确保数据完整性
            df = df.dropna(subset=['close', 'high', 'low', 'vol'])
            
            if len(df) < 30:
                raise ValueError(f"Insufficient data points: {len(df)} (minimum 30 required)")
            
            # 数据类型验证
            for col in ['open', 'close', 'high', 'low', 'vol']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 移除异常值
            df = df[df['close'] > 0]
            df = df[df['high'] >= df['low']]
            df = df[df['vol'] >= 0]
            
            # 确保有amount列（如果没有则计算）
            if 'amount' not in df.columns:
                df['amount'] = df['close'] * df['vol']
            
            self.logger.info(f"Successfully loaded {len(df)} data points for {fund_code} ({timeframe})")
            return df
            
        except Exception as e:
            self.logger.error(f"获取K线数据失败 {fund_code} ({timeframe}): {e}")
            # 不再提供模拟数据，直接返回空DataFrame
            return pd.DataFrame()
    
    def _calculate_macd_indicators(self, df: pd.DataFrame, params: Optional[Dict[str, int]] = None) -> Dict[str, np.ndarray]:
        """计算MACD指标"""
        try:
            if params is None:
                params = {'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9}
            
            close_prices = df['close'].values
            
            # 计算EMA
            ema_fast = self._calculate_ema(close_prices, params['macd_fast'])
            ema_slow = self._calculate_ema(close_prices, params['macd_slow'])
            
            # 计算DIF
            dif = ema_fast - ema_slow
            
            # 计算DEA (信号线)
            dea = self._calculate_ema(dif, params['macd_signal'])
            
            # 计算MACD柱状图
            macd = (dif - dea) * 2
            
            return {
                'dif': dif,
                'dea': dea,
                'macd': macd,
                'ema_fast': ema_fast,
                'ema_slow': ema_slow
            }
            
        except Exception as e:
            self.logger.error(f"MACD计算失败: {e}")
            return {'dif': np.array([]), 'dea': np.array([]), 'macd': np.array([])}
    
    def _calculate_ema(self, data: np.ndarray, period: int) -> np.ndarray:
        """计算指数移动平均"""
        try:
            if len(data) == 0:
                return np.array([])
            
            alpha = 2.0 / (period + 1)
            ema = np.zeros_like(data)
            ema[0] = data[0]
            
            for i in range(1, len(data)):
                ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]
            
            return ema
            
        except Exception as e:
            self.logger.error(f"EMA计算失败: {e}")
            return np.array([])
    
    def _extract_bi_structures(self, df: pd.DataFrame, macd_data: Dict[str, np.ndarray]) -> List[CzscBiStructure]:
        """提取笔结构"""

        return self._extract_bi_structures_czsc(df, macd_data)
    
    def _extract_bi_structures_czsc(self, df: pd.DataFrame, macd_data: Dict[str, np.ndarray]) -> List[CzscBiStructure]:
        """使用CZSC算法提取笔结构"""
        try:
            # 转换数据格式
            symbol = f"fund_{hash(str(df)) % 10000}"
            rawbars = CzscDataAdapter.pandas_to_rawbars(df, symbol, '1min')
            
            if not rawbars:
                return []
            
            # 创建CZSC分析器
            czsc_analyzer = CZSC(rawbars)
            
            # 提取笔结构
            bi_structures = []
            for bi in czsc_analyzer.bi_list[-10:]:  # 取最近10个笔
                czsc_structure = CzscDataAdapter.bi_to_czsc_structure(bi, macd_data, df)
                bi_structures.append(czsc_structure)
            
            return bi_structures
            
        except Exception as e:
            self.logger.error(f"CZSC笔结构提取失败: {e}")
            return self._extract_bi_structures_simple(df, macd_data)
    
    def _extract_bi_structures_simple(self, df: pd.DataFrame, macd_data: Dict[str, np.ndarray]) -> List[CzscBiStructure]:
        """简化版笔结构提取"""
        try:
            bi_structures = []
            prices = df['close'].values
            
            # 寻找极值点
            extremes = []
            for i in range(2, len(prices) - 2):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1] and \
                   prices[i] > prices[i-2] and prices[i] > prices[i+2]:
                    extremes.append((i, prices[i], 'high'))
                elif prices[i] < prices[i-1] and prices[i] < prices[i+1] and \
                     prices[i] < prices[i-2] and prices[i] < prices[i+2]:
                    extremes.append((i, prices[i], 'low'))
            
            # 构建笔结构
            for i in range(len(extremes) - 1):
                start_idx, start_price, start_type = extremes[i]
                end_idx, end_price, end_type = extremes[i + 1]
                
                if start_type != end_type:
                    direction = "up" if end_price > start_price else "down"
                    
                    # 计算MACD面积和DIF峰值
                    macd_slice = macd_data['macd'][start_idx:end_idx+1]
                    dif_slice = macd_data['dif'][start_idx:end_idx+1]
                    
                    bi_structure = CzscBiStructure(
                        bi_direction=direction,
                        bi_start_price=start_price,
                        bi_end_price=end_price,
                        bi_amplitude=abs(end_price - start_price),
                        bi_duration=end_idx - start_idx,
                        macd_area=np.sum(np.abs(macd_slice)),
                        dif_peak=np.max(dif_slice) if direction == "up" else np.min(dif_slice)
                    )
                    
                    bi_structures.append(bi_structure)
            
            return bi_structures[-10:]  # 返回最近10个笔
            
        except Exception as e:
            self.logger.error(f"简化笔结构提取失败: {e}")
            return []
    
    def _analyze_divergence(self, bi_structures: List[CzscBiStructure]) -> Dict[str, Any]:
        """分析背驰情况"""
        try:
            if len(bi_structures) < 3:
                return {
                    'type': 'insufficient_data',
                    'strength': 0.0,
                    'area_ratio': 1.0,
                    'peak_comparison': {},
                    'confirmation_bars': 0
                }
            
            # 分析最近三个笔的背驰情况
            recent_bis = bi_structures[-3:]
            
            # 计算面积比率
            area_ratios = []
            peak_comparisons = []
            
            for i in range(1, len(recent_bis)):
                prev_bi = recent_bis[i-1]
                curr_bi = recent_bis[i]
                
                # 同方向笔的比较
                if prev_bi.bi_direction == curr_bi.bi_direction:
                    if curr_bi.macd_area > 0 and prev_bi.macd_area > 0:
                        area_ratio = curr_bi.macd_area / prev_bi.macd_area
                        area_ratios.append(area_ratio)
                    
                    # DIF峰值比较
                    if prev_bi.bi_direction == "up":
                        peak_comparison = curr_bi.dif_peak / prev_bi.dif_peak if prev_bi.dif_peak != 0 else 1
                    else:
                        peak_comparison = prev_bi.dif_peak / curr_bi.dif_peak if curr_bi.dif_peak != 0 else 1
                    
                    peak_comparisons.append(peak_comparison)
            
            # 判断背驰类型和强度
            if area_ratios:
                avg_area_ratio = np.mean(area_ratios)
                avg_peak_ratio = np.mean(peak_comparisons) if peak_comparisons else 1.0
                
                # 背驰判断逻辑
                if avg_area_ratio < 0.618 and avg_peak_ratio < 0.8:
                    divergence_type = "strong_divergence"
                    strength = 1.0 - avg_area_ratio
                elif avg_area_ratio < 0.8 and avg_peak_ratio < 0.9:
                    divergence_type = "medium_divergence"
                    strength = (1.0 - avg_area_ratio) * 0.7
                elif avg_area_ratio < 1.0:
                    divergence_type = "weak_divergence"
                    strength = (1.0 - avg_area_ratio) * 0.5
                else:
                    divergence_type = "no_divergence"
                    strength = 0.0
                
                return {
                    'type': divergence_type,
                    'strength': min(1.0, strength),
                    'area_ratio': avg_area_ratio,
                    'peak_comparison': {
                        'avg_peak_ratio': avg_peak_ratio,
                        'individual_ratios': peak_comparisons
                    },
                    'confirmation_bars': len(recent_bis)
                }
            
            return {
                'type': 'no_data',
                'strength': 0.0,
                'area_ratio': 1.0,
                'peak_comparison': {},
                'confirmation_bars': 0
            }
            
        except Exception as e:
            self.logger.error(f"背驰分析失败: {e}")
            return {
                'type': 'error',
                'strength': 0.0,
                'area_ratio': 1.0,
                'peak_comparison': {},
                'confirmation_bars': 0
            }
    
    def _determine_cycle_position(self, df: pd.DataFrame, macd_data: Dict[str, np.ndarray]) -> str:
        """确定周期位置"""
        try:
            if len(macd_data['macd']) < 20:
                return "unknown"
            
            recent_macd = macd_data['macd'][-20:]
            macd_trend = np.mean(recent_macd[-5:]) - np.mean(recent_macd[-15:-10])
            
            if macd_trend > 0.02:
                return "early"
            elif macd_trend > -0.02:
                return "middle"
            else:
                return "late"
                
        except Exception:
            return "unknown"
    
    def _calculate_resonance_score(self, short: TimeframeCycleDivergence, 
                                 medium: TimeframeCycleDivergence, 
                                 long: TimeframeCycleDivergence) -> float:
        """计算共振评分"""
        try:
            scores = []
            
            # 收集各时间框架的背驰强度
            for frame_result in [short, medium, long]:
                if frame_result and frame_result.divergence_strength > 0:
                    scores.append(frame_result.divergence_strength)
            
            if not scores:
                return 0.0
            
            # 计算加权平均，长周期权重更高
            weights = [0.3, 0.4, 0.3] if len(scores) == 3 else [0.5, 0.5] if len(scores) == 2 else [1.0]
            weighted_score = sum(score * weight for score, weight in zip(scores, weights[:len(scores)]))
            
            return max(0.0, min(1.0, weighted_score))
            
        except Exception:
            return 0.0
    
    def _determine_consensus_direction(self, short: TimeframeCycleDivergence, 
                                     medium: TimeframeCycleDivergence, 
                                     long: TimeframeCycleDivergence) -> str:
        """确定共识方向"""
        try:
            directions = []
            
            for frame_result in [short, medium, long]:
                if frame_result and frame_result.divergence_type not in ['none', 'error', 'insufficient_data']:
                    if 'top' in frame_result.divergence_type:
                        directions.append('bearish')
                    elif 'bottom' in frame_result.divergence_type:
                        directions.append('bullish')
            
            if not directions:
                return "neutral"
            
            # 计算方向一致性
            bullish_count = directions.count('bullish')
            bearish_count = directions.count('bearish')
            
            if bullish_count > bearish_count:
                return "bullish"
            elif bearish_count > bullish_count:
                return "bearish"
            else:
                return "neutral"
                
        except Exception:
            return "neutral"
    
    def _calculate_confidence_level(self, short: TimeframeCycleDivergence, 
                                  medium: TimeframeCycleDivergence, 
                                  long: TimeframeCycleDivergence) -> float:
        """计算置信水平"""
        try:
            confidence_factors = []
            
            for frame_result in [short, medium, long]:
                if frame_result:
                    # 基于背驰强度和确认K线数量计算置信度
                    strength_factor = frame_result.divergence_strength
                    confirmation_factor = min(1.0, frame_result.confirmation_bars / 5.0)
                    frame_confidence = (strength_factor + confirmation_factor) / 2
                    confidence_factors.append(frame_confidence)
            
            if not confidence_factors:
                return 0.0
            
            # 加权平均置信度
            avg_confidence = np.mean(confidence_factors)
            
            # 一致性奖励
            if len(set(frame_result.divergence_type for frame_result in [short, medium, long] 
                     if frame_result and frame_result.divergence_type not in ['none', 'error'])) == 1:
                avg_confidence *= 1.2
            
            return max(0.0, min(1.0, avg_confidence))
            
        except Exception:
            return 0.0
    
    def _determine_transition_signal(self, resonance_score: float, confidence_level: float) -> str:
        """确定变盘信号强度"""
        try:
            combined_score = (resonance_score + confidence_level) / 2
            
            if combined_score >= 0.8:
                return "strong"
            elif combined_score >= 0.6:
                return "medium"
            elif combined_score >= 0.3:
                return "weak"
            else:
                return "none"
                
        except Exception:
            return "none"
    
    def _get_optimized_params(self, fund_code: str, df: pd.DataFrame) -> Dict[str, int]:
        """获取优化的MACD参数"""
        try:
            # 首先检查缓存
            cached_params = self.cache_manager.get_cached_params(fund_code)
            if cached_params:
                self._log_parameter_selection(fund_code, cached_params, "cache")
                return cached_params
            
            # 分析基金特性
            fund_volume = 1000000000  # 模拟基金规模
            fund_features = self.fund_analyzer.analyze_fund_characteristics(fund_code, df, fund_volume)
            
            # 计算最优参数
            optimized_params = self.macd_optimizer.calculate_optimal_params(fund_features)
            
            # 缓存参数
            self.cache_manager.cache_params(fund_code, optimized_params)
            
            self._log_parameter_selection(fund_code, optimized_params, "optimized", fund_features)
            
            return optimized_params
            
        except Exception as e:
            self.logger.error(f"参数优化失败 {fund_code}: {e}")
            default_params = {'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9}
            self._log_parameter_selection(fund_code, default_params, "default")
            return default_params
    
    def _log_parameter_selection(self, fund_code: str, params: Dict[str, int], source: str, features: Dict[str, Any] = None):
        """记录参数选择日志"""
        try:
            log_msg = f"基金 {fund_code} MACD参数: {params} (来源: {source})"
            
            if features:
                feature_summary = {
                    'liquidity': round(features.get('liquidity_score', 0), 3),
                    'volatility': round(features.get('volatility_score', 0), 3),
                    'category': features.get('category', 'unknown')
                }
                log_msg += f" 特性: {feature_summary}"
            
            self.logger.debug(log_msg)
            
        except Exception as e:
            self.logger.error(f"参数选择日志记录失败: {e}")
    
    def get_algorithm_stats(self) -> Dict[str, Any]:
        """获取算法统计信息"""
        try:
            cache_stats = self.cache_manager.get_cache_stats()
            optimization_stats = self.macd_optimizer.get_optimization_stats()
            
            success_rate = (
                self.performance_stats['successful_analyses'] / 
                max(1, self.performance_stats['total_analyses']) * 100
            )
            
            return {
                'algorithm_performance': {
                    'total_analyses': self.performance_stats['total_analyses'],
                    'success_rate': round(success_rate, 2),
                    'avg_processing_time': round(self.performance_stats['avg_processing_time'], 3),
                    'last_update': self.performance_stats['last_update'].isoformat()
                },
                'cache_performance': cache_stats,
                'optimization_stats': optimization_stats,
                'fx_performance': self.get_fx_performance_summary()
            }
            
        except Exception as e:
            self.logger.error(f"统计信息获取失败: {e}")
            return {
                'algorithm_performance': {'error': str(e)},
                'cache_performance': {},
                'optimization_stats': {},
                'fx_performance': {}
            }
    
    def get_fx_performance_summary(self) -> Dict[str, Any]:
        """获取分型性能摘要"""
        try:
            if not self.fx_performance_stats:
                return {'status': 'no_data'}
            
            total_analyses = sum(stats['total_analyses'] for stats in self.fx_performance_stats.values())
            total_errors = sum(stats['error_count'] for stats in self.fx_performance_stats.values())
            
            overall_error_rate = (total_errors / max(1, total_analyses)) * 100
            
            return {
                'overall_error_rate': round(overall_error_rate, 2),
                'total_analyses': total_analyses,
                'by_timeframe': {
                    key: {
                        'error_rate': round((stats['error_count'] / max(1, stats['total_analyses'])) * 100, 2),
                        'total_analyses': stats['total_analyses']
                    } for key, stats in self.fx_performance_stats.items()
                }
            }
            
        except Exception as e:
            self.logger.error(f"分型性能摘要获取失败: {e}")
            return {'error': str(e)}

# =============================================================================
# 主程序入口 - 自动化交易系统
# =============================================================================

if __name__ == '__main__':
    """增强版基金交易多智能体系统 V3.0 主程序入口"""
    import sys
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    try:
        # 完整自动化交易系统模式
        print(f"\n💰 启动完整自动化交易系统")
        print("-" * 60)
        logger.info("🚀 启动增强版基金交易多智能体系统 V3.0")
        logger.info("💰 自动化交易模式")

        # 初始化交易系统
        trading_system = EnhancedFundTradingSystemV3(title='')

        # 运行交易系统
        trading_system.run_trading_system_v3()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断系统运行")
        logger.info("⚠️ 用户中断交易系统")
    except Exception as e:
        print(f"\n❌ 系统运行异常: {str(e)}")
        logger.error(f"❌ 交易系统异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n👋 交易系统已退出")
        logger.info("👋 交易系统已退出")

