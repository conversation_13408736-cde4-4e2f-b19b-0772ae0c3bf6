"""
最终验证脚本 - 确认盈亏比和风险收益比修复效果
专门用于验证fund_trading_system_v3.3_czsc系统的修复
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from coordinators.enhanced_kelly_coordinator import EnhancedKellyPositionCoordinator


def final_verification():
    """最终验证修复效果"""
    
    print("🎯 fund_trading_system_v3.3_czsc 凯利公式修复最终验证")
    print("=" * 80)
    
    # 初始化修复后的协调器
    coordinator = EnhancedKellyPositionCoordinator({
        'enable_detailed_logging': False,  # 简化输出
        'kelly_fraction': 0.25,
        'max_position': 0.25
    })
    
    # 测试场景1: 强买入信号
    strong_buy_scenario = {
        'fund_code': '518880',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.82,
            'coordination_score': 0.78,
            'weighted_score': 0.35
        },
        'final_decision': 'buy',
        'final_confidence': 0.85,
        'technical_data': {
            'buy_signal': True,
            'signal_strength': 'strong',
            'confidence_score': 0.88,
            'technical_indicators': {
                'ma5': 7.55,
                'ma20': 7.35,
                'rsi': 28,  # 超卖
                'macd': 0.025
            }
        },
        'gua_data': {
            'is_buy_gua': True,
            'gua_score': 0.28
        },
        'flow_data': {
            'high_liquidity': True,
            'capital_flow': '净流入',
            'price_data': {
                'price': 7.58,
                'change_rate': 3.2,  # 大涨
                'volume': 25000     # 放量
            }
        },
        'llm_analysis': {
            'confidence_level': 0.85,
            'market_sentiment': '乐观'
        }
    }
    
    # 测试场景2: 中性观望
    neutral_scenario = {
        'fund_code': '513030',
        'enhanced_decision': {
            'decision': 'hold',
            'confidence': 0.55,
            'coordination_score': 0.52,
            'weighted_score': 0.08
        },
        'final_decision': 'hold',
        'final_confidence': 0.58,
        'technical_data': {
            'buy_signal': False,
            'signal_strength': 'none',
            'confidence_score': 0.53,
            'technical_indicators': {
                'ma5': 1.85,
                'ma20': 1.86,
                'rsi': 52,
                'macd': -0.003
            }
        },
        'gua_data': {
            'is_buy_gua': False,
            'gua_score': 0.05
        },
        'flow_data': {
            'high_liquidity': False,
            'capital_flow': '平衡',
            'price_data': {
                'price': 1.85,
                'change_rate': 0.1,
                'volume': 2800
            }
        },
        'llm_analysis': {
            'confidence_level': 0.60,
            'market_sentiment': '中性'
        }
    }
    
    # 测试场景3: 弱买入信号
    weak_buy_scenario = {
        'fund_code': '159567',
        'enhanced_decision': {
            'decision': 'buy',
            'confidence': 0.62,
            'coordination_score': 0.58,
            'weighted_score': 0.15
        },
        'final_decision': 'buy',
        'final_confidence': 0.65,
        'technical_data': {
            'buy_signal': True,
            'signal_strength': 'weak',
            'confidence_score': 0.58,
            'technical_indicators': {
                'ma5': 0.95,
                'ma20': 0.94,
                'rsi': 45,
                'macd': 0.005
            }
        },
        'gua_data': {
            'is_buy_gua': True,
            'gua_score': 0.12
        },
        'flow_data': {
            'high_liquidity': True,
            'capital_flow': '净流入',
            'price_data': {
                'price': 0.96,
                'change_rate': 1.2,
                'volume': 8500
            }
        },
        'llm_analysis': {
            'confidence_level': 0.68,
            'market_sentiment': '谨慎'
        }
    }
    
    scenarios = [
        ("强买入信号", strong_buy_scenario),
        ("中性观望", neutral_scenario),
        ("弱买入信号", weak_buy_scenario)
    ]
    
    results = []
    
    print("📊 测试不同场景下的修复效果:")
    print("-" * 80)
    
    for scenario_name, scenario_data in scenarios:
        try:
            result = coordinator.calculate_kelly_position(scenario_data)
            calc = result['kelly_calculation']
            
            print(f"\n🔸 {scenario_name} ({scenario_data['fund_code']}):")
            print(f"   胜率: {calc['win_probability']:.1%}")
            print(f"   盈亏比: {calc['risk_reward_ratio']:.2f}")
            print(f"   建议仓位: {calc['optimal_position']:.2%}")
            print(f"   风险等级: {calc['risk_level']}")
            
            # 验证修复效果
            win_prob_fixed = calc['win_probability'] != 0.5
            risk_reward_fixed = calc['risk_reward_ratio'] != 1.0
            reasonable_range = (
                0.35 <= calc['win_probability'] <= 0.85 and
                1.0 <= calc['risk_reward_ratio'] <= 3.0
            )
            
            scenario_success = win_prob_fixed and risk_reward_fixed and reasonable_range
            results.append(scenario_success)
            
            print(f"   {'✅ 修复成功' if scenario_success else '❌ 修复失败'}")
            
        except Exception as e:
            print(f"   ❌ 计算失败: {e}")
            results.append(False)
    
    # 汇总结果
    print(f"\n" + "=" * 80)
    print("🎯 最终验证结果")
    print("=" * 80)
    
    success_count = sum(results)
    total_count = len(results)
    success_rate = success_count / total_count * 100
    
    print(f"测试场景: {total_count}")
    print(f"成功修复: {success_count}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_count == total_count:
        print(f"\n🎉 恭喜！所有测试场景都修复成功！")
        print(f"✅ 盈亏比不再固定为1.0")
        print(f"✅ 胜率不再固定为50%")
        print(f"✅ 仓位计算更加精确")
        print(f"\n🚀 现在可以运行 python fund_trading_system_v3.3_czsc/main.py")
        print(f"📊 系统将显示真实的风险收益比和胜率计算结果")
        
        return True
    else:
        print(f"\n⚠️ 部分场景修复失败，成功率: {success_rate:.1f}%")
        return False


def show_before_after_comparison():
    """显示修复前后的对比"""
    
    print(f"\n📈 修复前后对比")
    print("=" * 80)
    
    print("修复前:")
    print("   胜率: 固定50%")
    print("   盈亏比: 固定1.0")
    print("   仓位: 计算不准确")
    print("   问题: 所有场景都显示相同的数值")
    
    print("\n修复后:")
    print("   胜率: 动态计算 35%-85%")
    print("   盈亏比: 动态计算 1.0-3.0")
    print("   仓位: 基于真实凯利公式")
    print("   优势: 根据技术分析、资金流向、市场情绪等多维度计算")
    
    print(f"\n🔧 修复原理:")
    print("1. 增强胜率计算: 基于技术指标、资金流向、卦象分析等")
    print("2. 增强风险收益比: 基于支撑阻力位、波动率、市场情绪等")
    print("3. 真实凯利公式: f = (bp - q) / b，其中b=盈亏比，p=胜率")
    print("4. 分数凯利: 使用25%凯利分数降低风险")
    print("5. 置信度调整: 根据综合置信度调整最终仓位")


if __name__ == "__main__":
    print("🚀 启动fund_trading_system_v3.3_czsc凯利公式修复最终验证")
    print("🎯 验证目标: 确认盈亏比和风险收益比不再固定为1")
    
    # 执行最终验证
    success = final_verification()
    
    # 显示对比
    show_before_after_comparison()
    
    if success:
        print(f"\n" + "🎊" * 20)
        print("🎊 修复完全成功！系统已准备就绪！ 🎊")
        print("🎊" * 20)
    else:
        print(f"\n⚠️ 需要进一步检查和调整")