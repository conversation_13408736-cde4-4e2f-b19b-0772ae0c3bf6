"""
测试数据管理器集成
"""

import sys
import os
sys.path.append('fund_trading_system_v3')

from fund_trading_system_v3.core.data_manager import DataManager
from datetime import datetime, timedelta


def test_data_manager_integration():
    """测试数据管理器集成功能"""
    print("=== 测试数据管理器集成功能 ===")
    
    try:
        # 初始化数据管理器
        data_manager = DataManager(config_type='default')
        print("✅ 数据管理器初始化成功")
        
        # 测试股票代码
        test_symbols = ['000001.SZ']  # 平安银行
        
        print(f"\n📊 测试获取市场数据")
        print("-" * 50)
        
        # 获取最近5天的市场数据
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=5)).strftime("%Y%m%d")
        
        market_data = data_manager.get_market_data(
            symbols=test_symbols,
            start_date=start_date,
            end_date=end_date
        )
        
        if market_data:
            symbol = test_symbols[0]
            if symbol in market_data:
                bars = market_data[symbol]
                print(f"✅ 成功获取 {len(bars)} 条K线数据")
                
                if bars:
                    latest_bar = bars[-1]
                    print(f"最新数据:")
                    print(f"  股票代码: {latest_bar.symbol}")
                    print(f"  交易时间: {latest_bar.dt}")
                    print(f"  开盘价: {latest_bar.open}")
                    print(f"  收盘价: {latest_bar.close}")
                    print(f"  最高价: {latest_bar.high}")
                    print(f"  最低价: {latest_bar.low}")
                    print(f"  成交量: {latest_bar.vol}")
                    print(f"  成交额: {latest_bar.amount}")
            else:
                print(f"❌ 未获取到 {symbol} 的数据")
        else:
            print("❌ 未获取到市场数据")
        
        print(f"\n📈 测试获取综合分析数据")
        print("-" * 50)
        
        # 获取综合分析数据
        comprehensive_data = data_manager.get_comprehensive_analysis_data(
            symbols=test_symbols,
            start_date=start_date,
            end_date=end_date
        )
        
        if comprehensive_data:
            symbol = test_symbols[0]
            if symbol in comprehensive_data:
                data_list = comprehensive_data[symbol]
                print(f"✅ 成功获取 {len(data_list)} 条综合数据")
                
                if data_list:
                    latest_data = data_list[-1]
                    print(f"最新综合数据:")
                    print(f"  股票代码: {latest_data.ts_code}")
                    print(f"  交易日期: {latest_data.trade_date}")
                    print(f"  收盘价: {latest_data.market_data.close}")
                    print(f"  MA5: {latest_data.technical_indicators.ma5}")
                    print(f"  MA20: {latest_data.technical_indicators.ma20}")
                    print(f"  RSI: {latest_data.technical_indicators.rsi}")
                    print(f"  MACD DIF: {latest_data.technical_indicators.macd_dif}")
                    
                    # 测试特征向量
                    feature_vector = latest_data.to_feature_vector()
                    print(f"  特征向量维度: {len(feature_vector)}")
                    print(f"  非零特征数: {(feature_vector != 0).sum()}")
            else:
                print(f"❌ 未获取到 {symbol} 的综合数据")
        else:
            print("❌ 未获取到综合分析数据")
        
        print(f"\n🔢 测试获取特征矩阵")
        print("-" * 50)
        
        # 获取特征矩阵
        feature_matrices = data_manager.get_feature_matrix(
            symbols=test_symbols,
            start_date=start_date,
            end_date=end_date
        )
        
        if feature_matrices:
            symbol = test_symbols[0]
            if symbol in feature_matrices:
                matrix = feature_matrices[symbol]
                print(f"✅ 成功获取特征矩阵")
                print(f"  形状: {matrix.shape}")
                print(f"  数据类型: {matrix.dtype}")
                print(f"  缺失值数量: {sum(sum(row) for row in (matrix != matrix))}")  # NaN检查
                print(f"  前5个特征统计:")
                for i in range(min(5, matrix.shape[1])):
                    col = matrix[:, i]
                    valid_values = col[~(col != col)]  # 排除NaN
                    if len(valid_values) > 0:
                        print(f"    特征{i}: 均值={valid_values.mean():.4f}, 标准差={valid_values.std():.4f}")
            else:
                print(f"❌ 未获取到 {symbol} 的特征矩阵")
        else:
            print("❌ 未获取到特征矩阵")
        
        print(f"\n🎯 测试多维度分析")
        print("-" * 50)
        
        # 获取多维度分析
        multidimensional_analysis = data_manager.get_multidimensional_analysis(
            symbols=test_symbols,
            start_date=start_date,
            end_date=end_date
        )
        
        if multidimensional_analysis:
            symbol = test_symbols[0]
            if symbol in multidimensional_analysis:
                analysis_list = multidimensional_analysis[symbol]
                print(f"✅ 成功获取 {len(analysis_list)} 条多维度分析")
                
                if analysis_list:
                    latest_analysis = analysis_list[-1]
                    print(f"最新多维度分析:")
                    print(f"  时间戳: {latest_analysis.timestamp}")
                    print(f"  综合得分: {latest_analysis.composite_score:.4f}")
                    print(f"  市场制度: {latest_analysis.market_regime}")
                    print(f"  推荐行动: {latest_analysis.recommended_action}")
                    print(f"  风险等级: {latest_analysis.risk_level}")
                    print(f"  整体置信度: {latest_analysis.overall_confidence:.4f}")
                    
                    print(f"  六大维度评估:")
                    print(f"    趋势: {latest_analysis.trend.state} (得分: {latest_analysis.trend.score:.4f})")
                    print(f"    波动性: {latest_analysis.volatility.state} (得分: {latest_analysis.volatility.score:.4f})")
                    print(f"    流动性: {latest_analysis.liquidity.state} (得分: {latest_analysis.liquidity.score:.4f})")
                    print(f"    情绪: {latest_analysis.sentiment.state} (得分: {latest_analysis.sentiment.score:.4f})")
                    print(f"    结构: {latest_analysis.structural.state} (得分: {latest_analysis.structural.score:.4f})")
                    print(f"    转换: {latest_analysis.transition.state} (得分: {latest_analysis.transition.score:.4f})")
            else:
                print(f"❌ 未获取到 {symbol} 的多维度分析")
        else:
            print("❌ 未获取到多维度分析")
        
        print(f"\n💾 测试缓存功能")
        print("-" * 50)
        
        # 获取缓存信息
        cache_info = data_manager.get_cache_info()
        print(f"缓存信息:")
        print(f"  管理器缓存大小: {cache_info['manager_cache_size']}")
        print(f"  提供器缓存大小: {cache_info['provider_cache_size']}")
        print(f"  更新记录数: {cache_info['last_update_count']}")
        
        # 清空缓存
        data_manager.clear_cache()
        print("✅ 缓存已清空")
        
        cache_info_after = data_manager.get_cache_info()
        print(f"清空后缓存信息:")
        print(f"  管理器缓存大小: {cache_info_after['manager_cache_size']}")
        print(f"  提供器缓存大小: {cache_info_after['provider_cache_size']}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def test_realtime_data():
    """测试实时数据获取"""
    print("\n=== 测试实时数据获取 ===")
    
    try:
        # 使用实时配置
        data_manager = DataManager(config_type='realtime')
        print("✅ 实时数据管理器初始化成功")
        
        test_symbols = ['000001.SZ', '600000.SH']
        
        # 获取实时数据
        realtime_data = data_manager.get_realtime_data(test_symbols)
        
        if realtime_data:
            print(f"✅ 成功获取 {len(realtime_data)} 只股票的实时数据")
            
            for symbol, data in realtime_data.items():
                print(f"\n{symbol} 实时数据:")
                print(f"  交易日期: {data.trade_date}")
                print(f"  收盘价: {data.market_data.close}")
                print(f"  涨跌幅: {data.market_data.pct_chg}%")
                print(f"  成交量: {data.market_data.vol}")
                print(f"  RSI: {data.technical_indicators.rsi}")
        else:
            print("❌ 未获取到实时数据")
            
    except Exception as e:
        print(f"❌ 实时数据测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 开始测试数据管理器集成")
    print("=" * 60)
    
    # 运行测试
    test_data_manager_integration()
    test_realtime_data()
    
    print("\n" + "=" * 60)
    print("🎉 数据管理器集成测试完成！")