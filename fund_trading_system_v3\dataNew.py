import pandas as pd
import simplejson as json
from functools import partial
import requests
import tushare as ts


def ts_pro():
    pro = ts.pro_api()
    pro._DataApi__http_url = 'https://apix.mega.tech/gate/ts'
    pro._DataApi__token = 'luo_I5FczI'
    return pro


if __name__ == '__main__':

    # pro = DataApiNew()
    pro = ts_pro()
    print(pro.stock_basic())
    print(pro.stk_mins(ts_code='600000.SH', start_date='20230901', end_date='20231026', freq='60min'))
    print(pro.fina_indicator_vip(period='20230930'))
