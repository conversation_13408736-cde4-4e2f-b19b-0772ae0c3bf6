"""
静默版基金交易系统启动器
完全消除调试信息和日志输出
"""

import sys
import os
import logging
import warnings

# 彻底禁用所有警告和日志
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

# 设置所有日志级别为CRITICAL，基本上不会有任何输出
logging.getLogger().setLevel(logging.CRITICAL)
for logger_name in [
    'httpx', 'httpcore', 'matplotlib', 'asyncio', 'openai', 'urllib3',
    'numexpr', 'QUANTAXIS', 'pytdx', 'requests', 'urllib3.connectionpool',
    'root', 'EnhancedFundTradingSystemV3', 'MultiAgentCoordinatorV3',
    'EnhancedKellyPositionCoordinator', 'LLMMarketAnalyzer', 'RiskControlAgent',
    'TradingAgent_TradingAgent', 'AIConfig'
]:
    logging.getLogger(logger_name).setLevel(logging.CRITICAL)

# 禁用matplotlib调试输出
try:
    import matplotlib
    matplotlib.set_loglevel("CRITICAL")
except:
    pass

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from system.enhanced_trading_system import EnhancedFundTradingSystemV3


def main():
    """静默主程序入口"""
    try:
        print("💰 启动完整自动化交易系统")
        print("-" * 60)
        
        # 初始化交易系统
        trading_system = EnhancedFundTradingSystemV3(title='')
        
        # 运行交易系统
        trading_system.run_trading_system_v3()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断系统运行")
    except Exception as e:
        print(f"\n❌ 系统运行异常: {str(e)}")
    finally:
        print("\n👋 交易系统已退出")


if __name__ == '__main__':
    main()