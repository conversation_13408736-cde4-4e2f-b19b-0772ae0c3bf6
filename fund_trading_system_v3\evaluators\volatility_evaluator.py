"""
波动性维度评估器
负责评估市场波动性水平和风险程度
"""

import logging
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult
from core.enums import VolatilityState


class VolatilityEvaluator:
    """
    @class VolatilityEvaluator
    @brief 波动性维度评估器
    @details 负责评估市场波动性水平和风险程度
    """
    
    def __init__(self):
        self.name = "VolatilityEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估波动性维度
        @param data: 市场数据
        @return: 波动性评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            
            # 获取价格变化数据
            change_rate = abs(price_data.get('change_rate', 0))
            volume_ratio = data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            
            # 波动性评分计算
            volatility_signals = []
            volatility_score = 0.0
            
            # 1. 价格波动度
            if change_rate >= 5:
                price_volatility = "极高"
                vol_strength = min(1.0, change_rate / 10)
                volatility_score += vol_strength
                volatility_signals.append(f"价格波动极高({change_rate:.2f}%)")
            elif change_rate >= 3:
                price_volatility = "高"
                vol_strength = min(1.0, change_rate / 5)
                volatility_score += vol_strength * 0.8
                volatility_signals.append(f"价格波动高({change_rate:.2f}%)")
            elif change_rate >= 1:
                price_volatility = "正常"
                vol_strength = change_rate / 3
                volatility_score += vol_strength * 0.5
                volatility_signals.append(f"价格波动正常({change_rate:.2f}%)")
            elif change_rate >= 0.5:
                price_volatility = "低"
                vol_strength = change_rate / 1
                volatility_score += vol_strength * 0.3
                volatility_signals.append(f"价格波动低({change_rate:.2f}%)")
            else:
                price_volatility = "极低"
                volatility_score += 0.1
                volatility_signals.append(f"价格波动极低({change_rate:.2f}%)")
            
            # 2. 成交量波动
            if volume_ratio >= 2.0:
                volume_volatility = "异常放量"
                vol_vol_strength = min(1.0, (volume_ratio - 1) / 2)
                volatility_score += vol_vol_strength * 0.5
                volatility_signals.append(f"异常放量({volume_ratio:.2f}倍)")
            elif volume_ratio >= 1.5:
                volume_volatility = "放量"
                vol_vol_strength = (volume_ratio - 1) / 1
                volatility_score += vol_vol_strength * 0.3
                volatility_signals.append(f"放量({volume_ratio:.2f}倍)")
            elif volume_ratio <= 0.5:
                volume_volatility = "缩量"
                vol_vol_strength = (1 - volume_ratio) / 0.5
                volatility_score += vol_vol_strength * 0.2
                volatility_signals.append(f"缩量({volume_ratio:.2f}倍)")
            else:
                volume_volatility = "正常"
                volatility_signals.append(f"成交量正常({volume_ratio:.2f}倍)")
            
            # 波动性状态判断
            if volatility_score >= 0.9:
                volatility_state = VolatilityState.EXTREMELY_HIGH
            elif volatility_score >= 0.7:
                volatility_state = VolatilityState.HIGH
            elif volatility_score >= 0.4:
                volatility_state = VolatilityState.NORMAL
            elif volatility_score >= 0.2:
                volatility_state = VolatilityState.LOW
            else:
                volatility_state = VolatilityState.EXTREMELY_LOW
            
            # 置信度计算（基于数据可靠性）
            confidence = min(0.95, max(0.3, 0.8 - abs(volatility_score - 0.5)))
            
            # 数据质量评估
            data_quality = "good" if change_rate >= 0 and volume_ratio > 0 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="波动性",
                state=volatility_state,
                score=volatility_score,
                confidence=confidence,
                signals=volatility_signals,
                data_quality=data_quality,
                details={
                    'price_volatility': price_volatility,
                    'volume_volatility': volume_volatility
                },
                indicators={
                    'change_rate': change_rate,
                    'volume_ratio': volume_ratio,
                    'volatility_score': volatility_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Volatility evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="波动性",
                state=VolatilityState.NORMAL,
                score=0.5,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )
