#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MetaTrader Algorithm Implementation
基于论文 "Your Offline Policy is Not Trustworthy: Bilevel Reinforcement Learning for Sequential Portfolio Optimization"

Core Components:
1. Partial-Offline RL with decoupled state spaces (market state + balance state)
2. Bilevel Actor-Critic optimization
3. Conservative TD learning with ensemble worst-case estimation
4. Financial data transformations for OOD simulation

Author: Research Mode Implementation
Date: 2024
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal
from typing import Dict, List, Tuple, Optional
import random
from collections import deque
import math


class FinancialDataTransformer:
    """金融数据变换器，模拟分布外市场条件"""
    
    def __init__(self, transform_types: List[str] = ['short_term', 'long_term', 'correlation']):
        self.transform_types = transform_types
    
    def short_term_randomness(self, price_data: torch.Tensor, intensity: float = 0.1) -> torch.Tensor:
        """模拟短期随机性变化
        
        Args:
            price_data: 价格数据 [batch, time, features]
            intensity: 变换强度
        
        Returns:
            变换后的价格数据
        """
        # 添加随机噪声模拟短期市场波动
        noise = torch.randn_like(price_data) * intensity * price_data.std(dim=1, keepdim=True)
        return price_data + noise
    
    def long_term_trend_shift(self, price_data: torch.Tensor, intensity: float = 0.1) -> torch.Tensor:
        """模拟长期趋势变化
        
        Args:
            price_data: 价格数据 [batch, time, features]
            intensity: 变换强度
        
        Returns:
            变换后的价格数据
        """
        batch_size, seq_len, num_features = price_data.shape
        
        # 生成趋势变化
        trend_direction = torch.randint(0, 2, (batch_size, 1, num_features)).float() * 2 - 1  # -1 or 1
        trend_magnitude = torch.rand(batch_size, 1, num_features) * intensity
        
        # 创建线性趋势
        time_steps = torch.arange(seq_len).float().unsqueeze(0).unsqueeze(-1)
        trend = trend_direction * trend_magnitude * time_steps / seq_len
        
        return price_data + trend.to(price_data.device)
    
    def correlation_shift(self, price_data: torch.Tensor, intensity: float = 0.1) -> torch.Tensor:
        """模拟股票间相关性变化
        
        Args:
            price_data: 价格数据 [batch, time, features]
            intensity: 变换强度
        
        Returns:
            变换后的价格数据
        """
        batch_size, seq_len, num_features = price_data.shape
        
        # 生成随机相关性矩阵
        correlation_matrix = torch.randn(num_features, num_features)
        correlation_matrix = correlation_matrix @ correlation_matrix.T  # 确保正定
        correlation_matrix = correlation_matrix / correlation_matrix.diag().sqrt().unsqueeze(1)
        correlation_matrix = correlation_matrix / correlation_matrix.diag().sqrt().unsqueeze(0)
        
        # 应用相关性变换
        transformed_data = torch.zeros_like(price_data)
        for b in range(batch_size):
            for t in range(seq_len):
                original_returns = price_data[b, t] - price_data[b, max(0, t-1)]
                transformed_returns = correlation_matrix @ original_returns * intensity
                transformed_data[b, t] = price_data[b, t] + transformed_returns
        
        return transformed_data
    
    def apply_transformation(self, price_data: torch.Tensor, transform_type: str, intensity: float = 0.1) -> torch.Tensor:
        """应用指定类型的变换"""
        if transform_type == 'short_term':
            return self.short_term_randomness(price_data, intensity)
        elif transform_type == 'long_term':
            return self.long_term_trend_shift(price_data, intensity)
        elif transform_type == 'correlation':
            return self.correlation_shift(price_data, intensity)
        else:
            raise ValueError(f"Unknown transformation type: {transform_type}")


class MarketStateEncoder(nn.Module):
    """市场状态编码器，提取关系、长期和短期特征"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 关系特征提取器（跨股票关系）
        self.relational_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 长期特征提取器
        self.long_term_encoder = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=2,
            batch_first=True,
            dropout=0.1
        )
        
        # 短期特征提取器
        self.short_term_encoder = nn.Sequential(
            nn.Conv1d(input_dim, hidden_dim, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.ReLU()
        )
    
    def forward(self, market_data: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """编码市场状态
        
        Args:
            market_data: 市场数据 [batch, seq_len, features]
        
        Returns:
            (relational_features, long_term_features, short_term_features)
        """
        batch_size, seq_len, features = market_data.shape
        
        # 关系特征：使用最新时刻的数据
        current_data = market_data[:, -1, :]  # [batch, features]
        relational_features = self.relational_encoder(current_data)
        
        # 长期特征：使用LSTM处理整个序列
        long_term_output, _ = self.long_term_encoder(market_data)
        long_term_features = long_term_output[:, -1, :]  # 取最后时刻的输出
        
        # 短期特征：使用CNN处理最近的数据
        # 转换维度用于Conv1d: [batch, features, seq_len]
        market_data_transposed = market_data.transpose(1, 2)
        short_term_output = self.short_term_encoder(market_data_transposed)
        short_term_features = short_term_output[:, :, -1]  # 取最后时刻的输出
        
        return relational_features, long_term_features, short_term_features


class MetaTraderActor(nn.Module):
    """MetaTrader Actor网络"""
    
    def __init__(self, market_state_dim: int, balance_state_dim: int, action_dim: int, hidden_dim: int = 256):
        super().__init__()
        
        total_state_dim = market_state_dim * 3 + balance_state_dim  # 3个市场特征 + 余额状态
        
        self.network = nn.Sequential(
            nn.Linear(total_state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim * 2)  # mean and log_std
        )
        
        self.action_dim = action_dim
    
    def forward(self, market_features: Tuple[torch.Tensor, torch.Tensor, torch.Tensor], 
                balance_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播
        
        Args:
            market_features: (relational, long_term, short_term) features
            balance_state: 余额状态
        
        Returns:
            (action_mean, action_log_std)
        """
        # 拼接所有特征
        relational, long_term, short_term = market_features
        combined_features = torch.cat([relational, long_term, short_term, balance_state], dim=-1)
        
        # 网络输出
        output = self.network(combined_features)
        
        # 分离均值和对数标准差
        action_mean = output[:, :self.action_dim]
        action_log_std = output[:, self.action_dim:]
        
        # 限制对数标准差的范围
        action_log_std = torch.clamp(action_log_std, -20, 2)
        
        return action_mean, action_log_std
    
    def sample_action(self, market_features: Tuple[torch.Tensor, torch.Tensor, torch.Tensor], 
                     balance_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """采样动作
        
        Returns:
            (action, log_prob)
        """
        action_mean, action_log_std = self.forward(market_features, balance_state)
        action_std = torch.exp(action_log_std)
        
        # 创建正态分布并采样
        dist = Normal(action_mean, action_std)
        action = dist.rsample()  # 重参数化采样
        log_prob = dist.log_prob(action).sum(dim=-1, keepdim=True)
        
        # 应用tanh激活并调整log_prob
        action = torch.tanh(action)
        log_prob -= torch.log(1 - action.pow(2) + 1e-6).sum(dim=-1, keepdim=True)
        
        return action, log_prob


class ConservativeCritic(nn.Module):
    """保守Critic网络，实现ensemble最小值估计"""
    
    def __init__(self, market_state_dim: int, balance_state_dim: int, action_dim: int, 
                 hidden_dim: int = 256, num_critics: int = 3):
        super().__init__()
        
        total_input_dim = market_state_dim * 3 + balance_state_dim + action_dim
        self.num_critics = num_critics
        
        # 创建多个Critic网络
        self.critics = nn.ModuleList([
            nn.Sequential(
                nn.Linear(total_input_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, 1)
            ) for _ in range(num_critics)
        ])
    
    def forward(self, market_features: Tuple[torch.Tensor, torch.Tensor, torch.Tensor], 
                balance_state: torch.Tensor, action: torch.Tensor, 
                conservative: bool = True) -> torch.Tensor:
        """前向传播
        
        Args:
            market_features: 市场特征
            balance_state: 余额状态
            action: 动作
            conservative: 是否使用保守估计
        
        Returns:
            Q值估计
        """
        # 拼接所有输入
        relational, long_term, short_term = market_features
        combined_input = torch.cat([relational, long_term, short_term, balance_state, action], dim=-1)
        
        # 计算所有Critic的Q值
        q_values = [critic(combined_input) for critic in self.critics]
        
        if conservative:
            # 保守估计：取最小值
            return torch.min(torch.stack(q_values), dim=0)[0]
        else:
            # 平均估计
            return torch.mean(torch.stack(q_values), dim=0)
    
    def get_all_q_values(self, market_features: Tuple[torch.Tensor, torch.Tensor, torch.Tensor], 
                        balance_state: torch.Tensor, action: torch.Tensor) -> List[torch.Tensor]:
        """获取所有Critic的Q值（用于训练）"""
        relational, long_term, short_term = market_features
        combined_input = torch.cat([relational, long_term, short_term, balance_state, action], dim=-1)
        
        return [critic(combined_input) for critic in self.critics]


class MetaTraderAgent:
    """MetaTrader主代理类"""
    
    def __init__(
        self,
        market_state_dim: int,
        balance_state_dim: int,
        action_dim: int,
        device: str = 'cpu',
        lr_actor: float = 3e-4,
        lr_critic: float = 3e-4,
        gamma: float = 0.99,
        tau: float = 0.005,
        alpha: float = 0.2,
        lambda_ood: float = 0.5
    ):
        self.device = device
        self.gamma = gamma
        self.tau = tau
        self.alpha = alpha
        self.lambda_ood = lambda_ood
        
        # 网络初始化
        self.market_encoder = MarketStateEncoder(market_state_dim).to(device)
        self.actor = MetaTraderActor(market_state_dim, balance_state_dim, action_dim).to(device)
        self.critic = ConservativeCritic(market_state_dim, balance_state_dim, action_dim).to(device)
        self.target_critic = ConservativeCritic(market_state_dim, balance_state_dim, action_dim).to(device)
        
        # 复制参数到目标网络
        self.hard_update(self.target_critic, self.critic)
        
        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=lr_actor)
        self.critic_optimizer = optim.Adam(
            list(self.critic.parameters()) + list(self.market_encoder.parameters()), 
            lr=lr_critic
        )
        
        # 数据变换器
        self.data_transformer = FinancialDataTransformer()
        
        # 训练统计
        self.training_stats = {
            'actor_loss': [],
            'critic_loss': [],
            'ood_loss': [],
            'total_loss': []
        }
    
    def hard_update(self, target_net: nn.Module, source_net: nn.Module):
        """硬更新目标网络"""
        for target_param, param in zip(target_net.parameters(), source_net.parameters()):
            target_param.data.copy_(param.data)
    
    def soft_update(self, target_net: nn.Module, source_net: nn.Module):
        """软更新目标网络"""
        for target_param, param in zip(target_net.parameters(), source_net.parameters()):
            target_param.data.copy_(target_param.data * (1.0 - self.tau) + param.data * self.tau)
    
    def compute_td_target(self, rewards: torch.Tensor, next_market_data: torch.Tensor, 
                         next_balance_state: torch.Tensor, dones: torch.Tensor) -> torch.Tensor:
        """计算TD目标值"""
        with torch.no_grad():
            # 编码下一状态的市场特征
            next_market_features = self.market_encoder(next_market_data)
            
            # 采样下一动作
            next_action, next_log_prob = self.actor.sample_action(next_market_features, next_balance_state)
            
            # 计算下一状态的Q值（使用目标网络）
            next_q_value = self.target_critic(next_market_features, next_balance_state, next_action, conservative=True)
            
            # SAC的熵正则化
            next_q_value -= self.alpha * next_log_prob
            
            # TD目标
            td_target = rewards + self.gamma * (1 - dones) * next_q_value
        
        return td_target
    
    def bilevel_update(
        self,
        market_data: torch.Tensor,
        balance_state: torch.Tensor,
        actions: torch.Tensor,
        rewards: torch.Tensor,
        next_market_data: torch.Tensor,
        next_balance_state: torch.Tensor,
        dones: torch.Tensor,
        transform_intensity: float = 0.1
    ) -> Dict[str, float]:
        """双层优化更新
        
        Args:
            market_data: 市场数据 [batch, seq_len, features]
            balance_state: 余额状态 [batch, balance_dim]
            actions: 动作 [batch, action_dim]
            rewards: 奖励 [batch, 1]
            next_market_data: 下一状态市场数据
            next_balance_state: 下一状态余额
            dones: 终止标志 [batch, 1]
            transform_intensity: 变换强度
        
        Returns:
            损失字典
        """
        # 编码原始市场特征
        market_features = self.market_encoder(market_data)
        
        # === Critic更新 ===
        # 计算原始数据的TD目标
        td_target_original = self.compute_td_target(rewards, next_market_data, next_balance_state, dones)
        
        # 计算原始数据的Q值
        current_q_values = self.critic.get_all_q_values(market_features, balance_state, actions)
        
        # 原始数据的Critic损失
        critic_loss_original = sum([F.mse_loss(q_val, td_target_original) for q_val in current_q_values])
        
        # OOD数据的Critic损失
        ood_critic_losses = []
        for transform_type in self.data_transformer.transform_types:
            # 应用数据变换
            transformed_market_data = self.data_transformer.apply_transformation(
                market_data, transform_type, transform_intensity
            )
            transformed_next_market_data = self.data_transformer.apply_transformation(
                next_market_data, transform_type, transform_intensity
            )
            
            # 编码变换后的市场特征
            transformed_market_features = self.market_encoder(transformed_market_data)
            
            # 计算变换数据的TD目标
            td_target_transformed = self.compute_td_target(
                rewards, transformed_next_market_data, next_balance_state, dones
            )
            
            # 计算变换数据的Q值
            transformed_q_values = self.critic.get_all_q_values(
                transformed_market_features, balance_state, actions
            )
            
            # 变换数据的Critic损失
            ood_loss = sum([F.mse_loss(q_val, td_target_transformed) for q_val in transformed_q_values])
            ood_critic_losses.append(ood_loss)
        
        # 总Critic损失：原始损失 + λ * 最大OOD损失
        max_ood_critic_loss = torch.max(torch.stack(ood_critic_losses))
        total_critic_loss = critic_loss_original + self.lambda_ood * max_ood_critic_loss
        
        # 更新Critic
        self.critic_optimizer.zero_grad()
        total_critic_loss.backward()
        self.critic_optimizer.step()
        
        # === Actor更新 ===
        # 采样新动作
        new_actions, log_probs = self.actor.sample_action(market_features, balance_state)
        
        # 计算Actor损失
        q_values_for_actor = self.critic(market_features, balance_state, new_actions, conservative=True)
        actor_loss = (self.alpha * log_probs - q_values_for_actor).mean()
        
        # 更新Actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()
        
        # 软更新目标网络
        self.soft_update(self.target_critic, self.critic)
        
        # 记录统计信息
        stats = {
            'actor_loss': actor_loss.item(),
            'critic_loss': critic_loss_original.item(),
            'ood_loss': max_ood_critic_loss.item(),
            'total_loss': total_critic_loss.item()
        }
        
        for key, value in stats.items():
            self.training_stats[key].append(value)
        
        return stats
    
    def select_action(self, market_data: torch.Tensor, balance_state: torch.Tensor, 
                     deterministic: bool = False) -> torch.Tensor:
        """选择动作
        
        Args:
            market_data: 市场数据
            balance_state: 余额状态
            deterministic: 是否确定性选择
        
        Returns:
            选择的动作
        """
        with torch.no_grad():
            market_features = self.market_encoder(market_data)
            
            if deterministic:
                action_mean, _ = self.actor(market_features, balance_state)
                return torch.tanh(action_mean)
            else:
                action, _ = self.actor.sample_action(market_features, balance_state)
                return action


def create_sample_trading_environment():
    """创建示例交易环境（简化版）"""
    print("[MODE: RESEARCH] MetaTrader算法实现完成")
    print("\n核心组件:")
    print("1. 金融数据变换器 - 模拟短期随机性、长期趋势、相关性变化")
    print("2. 市场状态编码器 - 提取关系、长期、短期特征")
    print("3. MetaTrader Actor - 基于市场和余额状态的策略网络")
    print("4. 保守Critic - ensemble最小值估计，避免价值高估")
    print("5. 双层优化框架 - 内层优化原始数据，外层优化OOD数据")
    
    # 示例参数
    market_state_dim = 50  # 市场特征维度
    balance_state_dim = 10  # 余额状态维度（现金+各股票持仓）
    action_dim = 5  # 动作维度（5只股票的交易量）
    
    # 创建MetaTrader代理
    agent = MetaTraderAgent(
        market_state_dim=market_state_dim,
        balance_state_dim=balance_state_dim,
        action_dim=action_dim,
        device='cpu',
        lambda_ood=0.5
    )
    
    print(f"\n模型参数统计:")
    total_params = sum(p.numel() for p in agent.actor.parameters()) + \
                  sum(p.numel() for p in agent.critic.parameters()) + \
                  sum(p.numel() for p in agent.market_encoder.parameters())
    print(f"总参数量: {total_params:,}")
    
    return agent


if __name__ == "__main__":
    # 创建示例环境
    agent = create_sample_trading_environment()
    
    # 示例数据
    batch_size = 32
    seq_len = 20
    market_data = torch.randn(batch_size, seq_len, 50)
    balance_state = torch.randn(batch_size, 10)
    
    # 测试动作选择
    action = agent.select_action(market_data, balance_state)
    print(f"\n示例动作输出形状: {action.shape}")
    print(f"动作值范围: [{action.min().item():.3f}, {action.max().item():.3f}]")